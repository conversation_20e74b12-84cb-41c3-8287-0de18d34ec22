<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.ideal</groupId>
        <artifactId>ieai-script-service</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>script-starter</artifactId>
    <packaging>jar</packaging>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

        <project.start.class>com.ideal.script.ScriptApplication</project.start.class>
        <maven.build.timestamp.format>yyyyMMddHHmmss</maven.build.timestamp.format>
    </properties>


    <dependencies>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jdbc</artifactId>
        </dependency>
        <!-- script 业务模块 -->
        <dependency>
            <groupId>com.ideal</groupId>
            <artifactId>system-button-permission</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ideal</groupId>
            <artifactId>script-biz</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>spring-boot-starter-actuator</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.ideal</groupId>
            <artifactId>script-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ideal</groupId>
            <artifactId>script-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ideal</groupId>
            <artifactId>tools-biz</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ideal</groupId>
            <artifactId>tools-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ideal</groupId>
            <artifactId>gateway-filter</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcpkix-jdk15to18</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>bcprov-jdk15on</artifactId>
                    <groupId>org.bouncycastle</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>bcpkix-jdk15on</artifactId>
                    <groupId>org.bouncycastle</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>



        <dependency>
            <groupId>org.liquibase</groupId>
            <artifactId>liquibase-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ideal</groupId>
            <artifactId>approval-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.internetitem</groupId>
            <artifactId>logback-elasticsearch-appender</artifactId>
        </dependency>
        <dependency>
            <groupId>net.logstash.logback</groupId>
            <artifactId>logstash-logback-encoder</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-toolkit-logback-1.x</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-toolkit-trace</artifactId>
        </dependency>
    </dependencies>


    <build>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>timestamp-property</id>
                        <goals>
                            <goal>timestamp-property</goal>
                        </goals>
                        <configuration>
                            <name>current.time</name>
                            <pattern>${maven.build.timestamp.format}</pattern>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <excludes>
                        <exclude>application.yml</exclude>
                        <exclude>rebel.xml</exclude>
                        <exclude>logback-spring.xml</exclude>
                        <exclude>compare_changeSets.py</exclude>
                        <exclude>sort_liquibaseChangeSets.py</exclude>
                    </excludes>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <additionalProperties>
                        <!--使用build-helper-maven-plugin插件生成的时间 -->
                        <!--suppress UnresolvedMavenProperty -->
                        <time>${current.time}</time>
                    </additionalProperties>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>build-info</goal>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>com.ideal</groupId>
                <artifactId>ideal-file-generator-plugin</artifactId>
                <executions>
                    <execution>
                        <phase>compile</phase>
                        <goals>
                            <goal>file-generator</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <fileConfigs>
                        <fileConfig>
                            <output>${project.basedir}/../build/bootstrap.yml</output>
                            <templatePath>${project.basedir}/../build/bootstrap.yml.ftl</templatePath>
                            <parameters>
                                <parameter>
                                    <name>appActive</name>
                                    <type>java.lang.String</type>
                                    <value>apt</value>
                                </parameter>
                                <parameter>
                                    <name>appName</name>
                                    <type>java.lang.String</type>
                                    <value>script</value>
                                </parameter>
                                <parameter>
                                    <name>configActive</name>
                                    <type>java.lang.String</type>
                                    <value>${config-center}</value>
                                </parameter>
                                <parameter>
                                    <name>discoverActive</name>
                                    <type>java.lang.String</type>
                                    <value>${discover-center}</value>
                                </parameter>
                            </parameters>
                        </fileConfig>
                        <fileConfig>
                            <output>${project.basedir}/../build/start.sh</output>
                            <templatePath>${project.basedir}/../build/start.sh.ftl</templatePath>
                            <parameters>
                                <parameter>
                                    <name>jvmOption</name>
                                    <type>java.lang.String</type>
                                    <value>-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=9422 \
                                        -Xms1024m -Xmx1024m -Xmn500m -XX:+UseG1GC \
                                        -XX:MaxGCPauseMillis=50 -XX:+PrintGCDetails \
                                        -XX:+PrintGCDateStamps -XX:-OmitStackTraceInFastThrow
                                    </value>
                                </parameter>
                                <parameter>
                                    <name>appName</name>
                                    <type>java.lang.String</type>
                                    <value>${project.build.finalName}.jar</value>
                                </parameter>
                            </parameters>
                        </fileConfig>
                    </fileConfigs>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-assembly-plugin</artifactId>
                <executions>
                    <execution>
                        <id>assemble</id>
                        <goals>
                            <goal>single</goal>
                        </goals>
                        <phase>package</phase>
                    </execution>
                </executions>
                <configuration>
                    <appendAssemblyId>false</appendAssemblyId>
                    <attach>false</attach>
                    <archive>
                        <manifest>
                            <mainClass>${project.start.class}</mainClass>
                        </manifest>
                    </archive>
                    <!-- maven assembly插件需要一个描述文件 来告诉插件包的结构以及打包所需的文件来自哪里 -->
                    <descriptors>
                        <descriptor>${project.basedir}/../build/assembly.xml</descriptor>
                    </descriptors>
                    <outputDirectory>${project.build.directory}</outputDirectory>
                    <!--suppress UnresolvedMavenProperty -->
                    <finalName>${project.artifactId}-${project.version}-${current.time}</finalName>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.liquibase</groupId>
                <artifactId>liquibase-maven-plugin</artifactId>
                <configuration>
                    <!--
                        每次执行diff命令生成出来的补丁的changeset的Id都是时间戳，是不同的
                        liquibase计算md5不包含id，如果id不同，但md5相同(内容相同)，会导致相同md5的记录执行多次,可能执行失败(比如加字段、加索引、建表)
                        diff命令生成的是带有时间戳的补丁文件，每个人用diff生成完后需要把临时补丁文件拷贝到对应模块文件夹的版本目录中
                        每个人执行完liquibase:diff生成补丁后，一定要把基线比对库执行liquibase:update操作升级，如果漏掉执行update操作，其他人执行diff命令生成的时候会导致带出额外的补丁文件
                    -->
                    <!--这个文件主要是执行非diff时使用，比如update-->
                    <changeLogFile>db/changelog/db.changelog-master.yaml</changeLogFile>
                    <!--
                        url：要执行liquibase命令的目标库
                        1.使用diff命令生成补丁的时候指的就是基线库
                        2.使用update操作指的就是要操作升级的哪个库
                    -->
                    <url>**************************************************************************************************************************************************</url>
                    <username>root</username>
                 <!--   <url>*****************************************************</url>
                    <username>postgres</username>-->
                    <password>ideal123</password>
                    <!--referenceUrl：源库，也就是包含变更的库，如开发库-->
                    <referenceUrl>*********************************************************************************************************************************************</referenceUrl>
                    <referenceUsername>root</referenceUsername>
                 <!--   <referenceUrl>*****************************************************</referenceUrl>
                    <referenceUsername>postgres</referenceUsername>-->
                    <referencePassword>ideal123</referencePassword>
                    <!--排除xxx开头的表，排除xxx开在头的索引，因为脚本服务化、工具箱在同一个服务下，所以需要分开生成,其他单模块服务不需要设置排除,生成的是临时变更文件，需要手动拷贝到对应的当前版本目录中-->
                    <diffExcludeObjects>table:ieai_tb.*,table:ieai_monitor.*,index:ieai_tools.*,index:IEAI_TB.*</diffExcludeObjects>
                    <!--diff命令生成的补丁文件完整路径，文件名追加了时间戳(UTC时间，没找到修改成东八区的方案)，每次执行完手动拷贝到当前版本的目录中-->
                    <!--suppress UnresolvedMavenProperty -->
                    <diffChangeLogFile>src/main/resources/db/changelog/${module.name}/changelog_dev_${maven.build.timestamp}.yaml</diffChangeLogFile>
                    <outputFileEncoding>UTF-8</outputFileEncoding>
                    <verbose>true</verbose>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
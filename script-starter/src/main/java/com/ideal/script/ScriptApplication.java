package com.ideal.script;

import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.mybatis.spring.annotation.MapperScan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.quartz.QuartzAutoConfiguration;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;

/**
 * 启动类
 *
 * <AUTHOR>
 */
@SpringBootApplication(scanBasePackages = {"com.ideal.script","com.ideal.tools"},exclude = { QuartzAutoConfiguration.class })
@EnableDubbo(scanBasePackages ={"com.ideal.script","com.ideal.tools"})
@MapperScan(basePackages = "com.ideal.**.mapper")
@ConfigurationPropertiesScan("com.ideal.script.config")
public class ScriptApplication {
    private static final Logger log = LoggerFactory.getLogger(ScriptApplication.class);

    public static void main(String[] args) {
        SpringApplication.run(ScriptApplication.class, args);
        log.info("script service has been started successfully!");
    }

}

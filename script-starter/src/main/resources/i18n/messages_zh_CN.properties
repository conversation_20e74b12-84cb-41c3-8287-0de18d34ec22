business.test=测试！
id.min=id 不能小于0！
name.empty=名称不能为空！
validate.error=验证异常！
list.success=查询数据成功！
list.fail=查询数据失败！
save.success=保存成功！
update.success=修改成功！
save.fail=保存失败！
remove.success=删除成功！
save.sys.permission.success=保存业务系统权限成功！
save.sys.permission.fail=保存业务系统权限失败！
###ģ�����Ƴ���У�鲻ͨ����
modelName.validata.length.error=模型名称长度校验不通过！
modelCon.validata.length.error=图标地址长度校验不通过！
unknown.script.type=未知的脚本类型！
script.type.error=脚本类型与适用平台不匹配！
script.type.length.error=脚本类型长度必须在1到20个字符之间！
paramName.not.null=参数名称不能为空！
script.published=脚本是发布版本，不允许二次发布！
no.available.script=无可用脚本，请启用脚本！
current.user.no.have.permission=当前用户没有操作权限！


read.file.failure=读取文件失败！
get.out.put.result.fail=获取输出结果失败！
save.taskIps.error=保存脚本任务IP失败！
upZipFile.fail=解压文件失败！
exportReleaseMedia.error=投产介质导出失败！
upload.file.empty=上传的文件为空！
import.release.media.error=投产介质导入失败！
import.release.media.same.script=脚本已经存在，无法导入！
import.categoryName.conflict=投产导入分类有冲突！
script.type.mismatch.error=脚本类型与平台不匹配！
schedule.job.execution.failure=接收到的定时任务执行失败！
file.format.error=文件格式不正确！必须以'投产介质_'开头的zip文件！
current.user.empty=当前用户为空！
data.not.found.for.ip.port=未找到给定IP和端口的数据！
release.media.exists=投产介质中已存在！
category.record.found.one.more=按级别、父ID、名称找到多条记录！
handle.json.file.error=投产介质导入时，处理JSON文件失败！
duplicate.category=分类重复！
duplicate.dangerCmd=关键命令重复！
duplicate.ruleName=验证规则名称重复
save.issue.record.storage.error=脚本下发记录存储失败！
xxjob.id.null.error=xxJob任务的id为空!
cannot.delete.published.script=发布状态的脚本不能删除！
double.check.submission.error=双人复核提交过程中出现错误！
error.script.receiveAudit=接收审核信息并更新脚本版本信息出错！
script.name.exists=脚本名已存在！
script.name.zh.exists=脚本中文名已存在！
error.import.attachment=投产介质导入过程中处理附件文件失败！
error.clean.tempFile=临时文件删除失败!
error.apply.script.taskInfoEmpty=任务申请提交审核时携带的taskInfo为空，无法进行申请操作！
error.apply.script.itsm.task=itsm单号异常，任务发起失败！
error.apply.script.itsm.telephone=ITSM中不存在当前手机号对应的账号，请输入正确的手机号！
error.eachnum.lessthan.agentcont=agent数量不能大于并发数
error.receive.audit.information=接收审核信息并更新脚本版本信息出错！
error.script.task.not.find=根据taskId未查询到脚本任务！
chosed.agent.users.empty=任务绑定服务器为空！
clone.script.task.error=创建任务失败！
clone.script.task.success=创建任务成功！
error.script.resGroupsEmpty=资源组为空！
storage.audit.relation.failed=存储双人复核与脚本服务化关系失败！
task.authority.updateUseState.failed=任务启禁权限修改失败！
error.check.unfinished.task.failed=查询是否存在未完成的任务失败！
no.agent.for.script.task=当前脚本任务未分配具体的代理信息！
error.exec.script.task.start=执行脚本任务异常！
insert.task.runtime.error=存储agent运行实例异常！
no.data.find.for.script.info=未找到脚本信息！
no.task.instance.record.find=未找到任务实例！
no.task.runtime.record.find=未找到agent运行实例！
no.task.data.find=脚本任务不存在！
save.task.params.error=存储脚本任务参数失败！
update.task.state.error=更新任务状态失败！
handle.script.execute.result.error=处理脚本执行结果异常！
error.json.missing.key=处理脚本执行结果返回的JSON缺少关键属性！
error.json.parsing.exception=处理脚本执行结果返回的JSON解析异常！
error.handle.script.send.result.taskRuntimeId.empty=从bizId中提取的taskRuntimeId为空！
error.handle.script.send.result=执行handleScriptSendResult方法时发生异常！
error.bizId.numberFormatException=bizId 提取runtimeId过程中发生了NumberFormatException异常！
error.bizId.empty=bizId为空！
fail.handle.script.error.result=处理管理服务返回的异常消息失败！
error.zeroPartsArrayLength=BizId parts数组长度为零！
category.delete.referenced.by.scripts=由于被脚本引用，无法删除选择的分类！
parameter.validation.message=请检查参数引用的校验规则的正则表达式是否正确！
script.time.task.switch.fail=定时、周期任务状态切换失败！
script.time.task.switch.success=定时、周期任务状态切换成功！
script.time.task.kill.fail=任务终止失败！
script.time.task.kill.success=任务终止成功！
script.time.task.cron.fail=任务更新周期失败！
script.time.task.cron.success=任务更新周期成功！
no.script.forbidden=不存在可以禁用的脚本！
no.script.enable=不存在可以启用的脚本！
cannot.delete.review.script=审核中状态的脚本不能删除！
com.ideal.script.annotation.ValidDataSize.message=配置的值：${validatedValue}，超过允许的最大值：{max}
file.too.large=文件过大！
category.not.exist=分类不存在
scriptExecAuditing.success=提交审核成功
exec.target.valid.fail=执行目标不能为空！
execTime.before.now=执行时间早于当前时间！
category.no.department.relation=分类未绑定部门不允许直接绑定用户！
invalid.category=当前分类未拥有当前部门权限，不允许保存！
createUser.not.exist=创建用户不存在！
Creation.of.sql.type.scripts.is.not.allowed=不允许创建sql类型脚本!
import.server.success=导入服务器成功！
import.server.fail=导入服务器失败！
error.execuser=执行用户与脚本配置的不一致！

tools.not.bound.agent=?????agent?
combined.tools.not.childs=????????????????
#脚本服务化实体参数
com.ideal.script.model.dto.AgentInfoDto.sysmAgentInfoId=sysmAgentInfoId
com.ideal.script.model.dto.AgentInfoDto.agentIp=代理IP
com.ideal.script.model.dto.AgentInfoDto.agentPort=agent端口
com.ideal.script.dto.AttachmentDto.name=附件名称
com.ideal.script.dto.BindFuncVarDto.bindObjId=函数/变量ID
com.ideal.script.dto.BindFuncVarDto.bindType=类型
com.ideal.script.dto.BindFuncVarDto.objName=函数/变量名称
com.ideal.script.dto.CategoryDto.parentId=父级分类ID
com.ideal.script.dto.CategoryDto.code=分类编码
com.ideal.script.dto.CategoryDto.name=分类名称
com.ideal.script.dto.CategoryDto.sort=分类排序
com.ideal.script.dto.CategoryDto.level=分类级别
com.ideal.script.dto.CategoryDto.description=分类描述
com.ideal.script.model.dto.DangerCmdDto.scriptCmd=关键命令
com.ideal.script.model.dto.DangerCmdDto.scriptType=脚本类型
com.ideal.script.model.dto.DangerCmdDto.scriptCmdLevel=校验级别
com.ideal.script.model.dto.DangerCmdDto.scriptCmdRemark=命令说明
com.ideal.script.model.dto.FunctionpublishDto.name=函数名称
com.ideal.script.model.dto.FunctionpublishDto.desc=描述
com.ideal.script.model.dto.FunctionpublishDto.functionMd=唯一标识
com.ideal.script.dto.ScriptInfoDto.scriptNameZh=脚本中文名称
com.ideal.script.dto.ScriptInfoDto.scriptName=脚本名称
com.ideal.script.dto.ScriptInfoDto.scriptType=脚本类型
com.ideal.script.dto.ScriptInfoDto.execuser=执行用户
com.ideal.script.dto.ScriptInfoDto.scriptLabel=标签
com.ideal.script.dto.ScriptInfoDto.categoryId=分类ID
com.ideal.script.dto.ScriptInfoDto.platforms=适用平台
com.ideal.script.dto.ScriptVersionDto.version=版本号
com.ideal.script.dto.ScriptVersionDto.description=功能描述
com.ideal.script.dto.ScriptVersionDto.expectLastline=预期结果值
com.ideal.script.dto.ScriptInfoDto.scriptVersionDto.expectType=预期类型
com.ideal.script.model.dto.InfoVersionTextDto.content=脚本内容
com.ideal.script.model.dto.ParameterCheckDto.ruleName=校验规则名称
com.ideal.script.model.dto.ParameterCheckDto.checkRule=校验规则内容
com.ideal.script.model.dto.ParameterCheckDto.ruleDes=校验规则描述
com.ideal.script.model.dto.ParameterDto.paramType=参数类型
com.ideal.script.model.dto.ParameterDto.paramDefaultValue=参数默认值
com.ideal.script.model.dto.ParameterDto.paramDesc=参数描述
com.ideal.script.model.dto.ParameterDto.paramOrder=参数顺序
com.ideal.script.model.dto.ParameterDto.paramName=参数名称
com.ideal.script.model.dto.ParameterManagerDto.paramName=枚举参数名称
com.ideal.script.model.dto.ParameterManagerDto.paramValue=枚举参数值
com.ideal.script.model.dto.ParameterManagerDto.paramDesc=枚举参数描述
com.ideal.script.model.dto.ParameterManagerDto.scope=适用范围
com.ideal.script.model.dto.ScriptExecAuditDto.execuser=执行用户
com.ideal.script.model.dto.ScriptExecAuditDto.auditUser=审核人
com.ideal.script.model.dto.ScriptExecAuditDto.auditUserId=审核人ID
com.ideal.script.model.dto.ScriptExecAuditDto.resGroupFlag=resGroupFlag
com.ideal.script.model.dto.ScriptExecAuditDto.startType=startType
com.ideal.script.model.dto.TaskDto.taskName=任务名称
com.ideal.script.model.dto.TaskDto.execUser=执行用户
com.ideal.script.model.dto.TaskDto.taskTime=任务执行时间
com.ideal.script.model.dto.TaskGroupsDto.cpname=资源组名称
com.ideal.script.controller.VariablePublishDto.name=变量名
com.ideal.script.controller.VariablePublishDto.desc=描述
com.ideal.script.model.dto.PlatformDto.name=平台名称
com.ideal.script.dto.AttachmentUploadDto.name=附件名称
com.ideal.script.dto.BussinessSystemDto.systemName=业务系统名称
com.ideal.script.dto.ParameterValidationDto.paramType=参数类型
com.ideal.script.dto.ParameterValidationDto.paramName=参数名称
com.ideal.script.dto.ParameterValidationDto.paramDefaultValue=参数默认值
com.ideal.script.dto.ParameterValidationDto.paramDesc=参数描述
com.ideal.script.dto.ParameterValidationDto.ruleName=校验规则名称
com.ideal.script.dto.ParameterValidationDto.checkRule=校验规则内容
com.ideal.script.dto.ParameterValidationDto.ruleDes=校验规则描述
com.ideal.script.dto.PublishDto.publicDesc=发布说明
com.ideal.script.dto.ScriptContentDto.content=脚本内容
com.ideal.script.dto.ScriptInfoDto.scriptVersionDto.timeout=超时时间
com.ideal.script.common.validation.NotBlankWhenPresentValidator.default.message=参数不为null时不允许为空字符串
enum.parameter.not.exist=参数不存在
id.min=id cannot be less than 0!
name.empty=name cannot be empty!
validate.error=validate error!
list.success=list success!
list.fail=list fail!
save.success=save success!
update.success=update success!
save.fail=save fail!
remove.success=remove success!
save.sys.permission.success=save sys permission success!
save.sys.permission.fail=save sys permission fail!
modelName.validata.length.error=model name length check failed!
modelCon.validata.length.error=model icon length check failed!
unknown.script.type=Unknown script type!
script.type.error=The script type does not match the platform！
script.type.length.error=The length of script type must be between 1 and 20 characters!


read.file.failure=Read file failure!
get.out.put.result.fail=get out put result fail!
save.taskIps.error=Error saving task Ips!
upZipFile.fail=Failed to unzip file!
exportReleaseMedia.error=Error occurred during exportReleaseMedia!
upload.file.empty=The uploaded file is empty!
import.release.media.error=Importing release media failed!
import.release.media.same.script=Importing release same script!
script.type.mismatch.error=The script type does not match the platform!
schedule.job.execution.failure=The scheduled job execution failed!
file.format.error=The file format is incorrect! It must be a zip file starting with '投产介质_'!
current.user.empty=The current user is empty!
data.not.found.for.ip.port=No data found for the given IP and port!
release.media.exists=Media already exists in production!
category.record.found.one.more=Find one more by level, parent ID, and name!
handle.json.file.error=Failed to handle JSON file during production media import!
duplicate.category=Duplicate category!
save.issue.record.storage.error=Failed to store script distribution record!
xxjob.id.null.error=Task xxJob id is null!
cannot.delete.published.script=Cannot delete script in published state!
double.check.submission.error=Error occurred during double check submission!
error.script.receiveAudit=Error occurred while receiving audit information and updating script version!
script.name.exists=Script name already exists!
script.name.zh.exists=script.name.zh.exists!
error.import.attachment=Failed to process attachment files during production media import!
error.clean.tempFile=Failed to delete temp files!
error.apply.script.taskInfoEmpty=Cannot proceed with script execution auditing due to empty taskInfo carried during task application submission!
error.apply.script.itsm.task=Cannot proceed with script execution get itsm work order number error!
error.apply.script.itsm.telephone=telephone number is error! please input right telephone number!
error.eachnum.lessthan.agentcont=Agent Count must be less than eachNum!
error.receive.audit.information=Error occurred while receiving audit information and updating script version!
error.script.task.not.find=not find script task based on the given taskId!
chosed.agent.users.empty=Server binding is empty for the task!
clone.script.task.error=Clone script task is error!
clone.script.task.success=Create script template task success!
error.script.resGroupsEmpty=The resource group list is empty!
error.storage.audit.relation.failed=Storage double review and script service relation failed!
task.authority.updateUseState.failed=Task permission modification failed!
error.check.unfinished.task.failed=Failed to check the existence of unfinished tasks!
no.agent.for.script.task=No specific agent information assigned to the current script task!
error.exec.script.task.start=Exception occurred while executing the script task!
insert.task.runtime.error=Exception occurred while storing task runtime!
no.data.find.for.script.info==Script information not found!
no.task.instance.record.find=task instance data not found!
no.task.runtime.record.find=task runtime data not found!
no.task.data.find=task data not found!
save.task.params.error=Failed to store script task parameters!
update.task.state.error=Failed to update task status!
handle.script.execute.result.error=the result of script execution processes error!
error.json.missing.key=The JSON returned from the script execution result is missing a key property!
error.json.parsing.exception=Exception occurred while parsing the JSON returned from the script execution result!
error.handle.script.send.result.taskRuntimeId.empty=The taskRuntimeId extracted from bizId is empty!
error.handle.script.send.result=An Exception occurred during the execution of the handleScriptSendResult method!
error.bizId.numberFormatException=A NumberFormatException occurred during the extraction of runtimeId from bizId!
error.bizId.empty=bizId is null or empty!
fail.handle.script.error.result=An Exception occurred during the script-error-result topic of the handleScriptErrorResult method！
error.zeroPartsArrayLength=BizId parts array length is zero!
category.delete.referenced.by.scripts=Unable to delete the selected category as it is referenced by scripts!
taskName.cannot.be.null=TaskName cannot be empty!
parameter.validation.message=Please check the regular expression for parameter references!
script.time.task.switch.fail=Failed to switch between scheduled and periodic tasks!
script.time.task.switch.success=The status of the scheduled and periodic tasks is successfully switched!
script.time.task.kill.fail=Task termination failed!
script.time.task.kill.success=The task was terminated successfully!
script.time.task.cron.fail=The task update cycle failed!
script.time.task.cron.success=The task update cycle is successful!
gain.fail=Acquisition failed
gain.success=Successfully obtained
get.ip.fail=Failed to obtain IP address
tool.not.exist=The tool does not exist
execute.audit.approved.and.execute=Execution approval has been approved and executed
refresh.current.state=Refresh current approval status
recall.success=Recall successful
recall.fail=Recall failed
execute.success=Execution successful
execute.fail=Execution failed
approval.not.passed=Approval not passed
com.ideal.script.annotation.ValidDataSize.message=Configured value: ${validatedValue}, exceeds the maximum allowed value: {max}
category.not.exist=category.not.exist
scriptExecAuditing.success=scriptExecAuditing success
exec.target.valid.fail=exec target valid fail
category.no.department.relation=category no department relation
invalid.category=invalid.category
createUser.not.exist=createUser not exist！
tools.not.bound.agent=The tool is not bound to an agent
combined.tools.not.childs=The current combination tool does not have any child nodes. Please check
Creation.of.sql.type.scripts.is.not.allowed=Creation.of.sql.type.scripts.is.not.allowed!
import.server.success=import server success!
import.server.fail=import server fail!
current.user.no.have.permission=the current user does not have permission！
error.execuser=Inconsistency between the execution user and the script configuration！


#Script service entity parameter
com.ideal.script.model.dto.sysmAgentInfoId=sysmAgentInfoId
com.ideal.script.model.dto.AgentInfoDto.agentIp=agentIp
com.ideal.script.model.dto.AgentInfoDto.agentPort=agentPort
com.ideal.script.dto.AttachmentDto.name=Attachment name
com.ideal.script.dto.BindFuncVarDto.bindObjId=function/variable ID
com.ideal.script.dto.BindFuncVarDto.bindType=type
com.ideal.script.dto.BindFuncVarDto.objName=function/variable name
com.ideal.script.dto.CategoryDto.parentId=parentId
com.ideal.script.dto.CategoryDto.code=category code
com.ideal.script.dto.CategoryDto.name=category name
com.ideal.script.dto.CategoryDto.sort=category sort
com.ideal.script.dto.CategoryDto.level=category level
com.ideal.script.dto.CategoryDto.description=category description
com.ideal.script.model.dto.DangerCmdDto.scriptCmd=key command
com.ideal.script.model.dto.DangerCmdDto.scriptType=scriptType
com.ideal.script.model.dto.DangerCmdDto.scriptCmdLevel=check level
com.ideal.script.model.dto.DangerCmdDto.scriptCmdRemark=command remark
com.ideal.script.model.dto.FunctionpublishDto.name=function name
com.ideal.script.model.dto.FunctionpublishDto.desc=description
com.ideal.script.model.dto.FunctionpublishDto.functionMd=functionMd
com.ideal.script.dto.ScriptInfoDto.scriptNameZh=script chinese name
com.ideal.script.dto.ScriptInfoDto.scriptName=scriptName
com.ideal.script.dto.ScriptInfoDto.scriptType=scriptType
com.ideal.script.dto.ScriptInfoDto.execuser=executive user
com.ideal.script.dto.ScriptInfoDto.scriptLabel=label
com.ideal.script.dto.ScriptInfoDto.categoryId=category ID
com.ideal.script.dto.ScriptInfoDto.platforms=platforms
com.ideal.script.dto.ScriptVersionDto.version=version
com.ideal.script.dto.ScriptVersionDto.description=function description
com.ideal.script.dto.ScriptVersionDto.expectLastline=expectLastline
com.ideal.script.dto.ScriptInfoDto.scriptVersionDto.expectType=expectType
com.ideal.script.model.dto.InfoVersionTextDto.content=script content
com.ideal.script.model.dto.ParameterCheckDto.ruleName=ruleName
com.ideal.script.model.dto.ParameterCheckDto.checkRule=checkRule
com.ideal.script.model.dto.ParameterCheckDto.ruleDes=check rule description
com.ideal.script.model.dto.ParameterDto.paramType=paramType
com.ideal.script.model.dto.ParameterDto.paramDefaultValue=paramDefaultValue
com.ideal.script.model.dto.ParameterDto.paramDesc=parameter description
com.ideal.script.model.dto.ParameterDto.paramOrder=parameter sequence
com.ideal.script.model.dto.ParameterDto.paramName=parameter name
com.ideal.script.model.dto.ParameterManagerDto.paramName=enumeration parameter name
com.ideal.script.model.dto.ParameterManagerDto.paramValue=enumeration parameter value
com.ideal.script.model.dto.ParameterManagerDto.paramDesc=enumeration parameter description
com.ideal.script.model.dto.ParameterManagerDto.scope=scope
com.ideal.script.model.dto.ScriptExecAuditDto.execuser=executive user
com.ideal.script.model.dto.ScriptExecAuditDto.auditUser=auditUser
com.ideal.script.model.dto.ScriptExecAuditDto.auditUserId=auditUserId
com.ideal.script.model.dto.ScriptExecAuditDto.resGroupFlag=resGroupFlag
com.ideal.script.model.dto.ScriptExecAuditDto.startType=startType
com.ideal.script.model.dto.TaskDto.taskName=taskName
com.ideal.script.model.dto.TaskDto.execUser=executive user
com.ideal.script.model.dto.TaskDto.taskTime=task execution time
com.ideal.script.model.dto.TaskGroupsDto.cpname=resource group name
com.ideal.script.controller.VariablePublishDto.name=variable name
com.ideal.script.controller.VariablePublishDto.desc=description
com.ideal.script.model.dto.PlatformDto.name=platform name
com.ideal.script.dto.AttachmentUploadDto.name=attachment name
com.ideal.script.dto.BussinessSystemDto.systemName=systemName
com.ideal.script.dto.ParameterValidationDto.paramType=paramType
com.ideal.script.dto.ParameterValidationDto.paramName=parameter name
com.ideal.script.dto.ParameterValidationDto.paramDefaultValue=paramDefaultValue
com.ideal.script.dto.ParameterValidationDto.paramDesc=parameter description
com.ideal.script.dto.ParameterValidationDto.ruleName=ruleName
com.ideal.script.dto.ParameterValidationDto.checkRule=checkRule
com.ideal.script.dto.ParameterValidationDto.ruleDes=check rule description
com.ideal.script.dto.PublishDto.publicDesc=public description
com.ideal.script.dto.ScriptContentDto.content=script content
com.ideal.script.dto.ScriptInfoDto.scriptVersionDto.timeout=timeout
com.ideal.script.common.validation.NotBlankWhenPresentValidator.default.message=An empty string is not allowed if the parameter is not null
enum.parameter.not.exist=enum parameter not exist
package com.ideal.script.dto;


import java.io.Serializable;


/**
 * 获取agent运行实例标准输出获取
 *
 * <AUTHOR>
 */
public class TaskRuntimeStdoutApiDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 任务id
     */
    private Long  callerTaskId;

    /**
     * agentIp
     */
    private String agentIp;

    /**
     * agent端口
     */
    private Integer agentPort;

    public Long getCallerTaskId() {
        return callerTaskId;
    }

    public void setCallerTaskId(Long callerTaskId) {
        this.callerTaskId = callerTaskId;
    }

    public String getAgentIp() {
        return agentIp;
    }

    public void setAgentIp(String agentIp) {
        this.agentIp = agentIp;
    }

    public Integer getAgentPort() {
        return agentPort;
    }

    public void setAgentPort(Integer agentPort) {
        this.agentPort = agentPort;
    }

}

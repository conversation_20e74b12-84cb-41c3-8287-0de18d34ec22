package com.ideal.script.dto;

import java.io.Serializable;

/**
 * 其它模块重试任务apiDto
 * <AUTHOR>
 */
public class RetryScriptInstanceApiDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 任务实例id
     */
    private Long callerTaskId;

    /**
     * agentIp
     */
    private String agentIp;

    /**
     * agent端口
     */
    private Integer agentPort;

    /**
     * 用户信息实体类
     */
    private CurrentUserDto currentUserDto;

    public Long getCallerTaskId() {
        return callerTaskId;
    }

    public void setCallerTaskId(Long callerTaskId) {
        this.callerTaskId = callerTaskId;
    }

    public String getAgentIp() {
        return agentIp;
    }

    public void setAgentIp(String agentIp) {
        this.agentIp = agentIp;
    }

    public Integer getAgentPort() {
        return agentPort;
    }

    public void setAgentPort(Integer agentPort) {
        this.agentPort = agentPort;
    }

    public CurrentUserDto getCurrentUserDto() {
        return currentUserDto;
    }

    public void setCurrentUserDto(CurrentUserDto currentUserDto) {
        this.currentUserDto = currentUserDto;
    }
}

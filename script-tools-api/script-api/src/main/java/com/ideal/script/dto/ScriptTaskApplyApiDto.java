package com.ideal.script.dto;

import java.io.Serializable;
import java.util.List;

/**
 * 脚本信息查询Dto
 *
 * <AUTHOR>
 */
public class ScriptTaskApplyApiDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 执行用户
     */
    private String execuser;

    /**
     * Agent集合
     */
    private List<ScriptTaskApplyAgentApiDto> chosedAgentUsers;

    /**
     * 执行参数
     */
    private List<String> params;

    /**
     * 脚本的uuid
     */
    private String scriptUuid;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     *  任务来源：0脚本服务化，1工具箱，2定时任务
     */
    private Integer startType;

    /**
     * 调用方任务id，截止到********版本，只有定时任务有这个值
     */
    private Long callerTaskId;

    public Long getCallerTaskId() {
        return callerTaskId;
    }

    public void setCallerTaskId(Long callerTaskId) {
        this.callerTaskId = callerTaskId;
    }

    public Integer getStartType() {
        return startType;
    }

    public void setStartType(Integer startType) {
        this.startType = startType;
    }

    public String getExecuser() {
        return execuser;
    }

    public void setExecuser(String execuser) {
        this.execuser = execuser;
    }

    public List<ScriptTaskApplyAgentApiDto> getChosedAgentUsers() {
        return chosedAgentUsers;
    }

    public void setChosedAgentUsers(List<ScriptTaskApplyAgentApiDto> chosedAgentUsers) {
        this.chosedAgentUsers = chosedAgentUsers;
    }

    public List<String> getParams() {
        return params;
    }

    public void setParams(List<String> params) {
        this.params = params;
    }

    public String getScriptUuid() {
        return scriptUuid;
    }

    public void setScriptUuid(String scriptUuid) {
        this.scriptUuid = scriptUuid;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }
}

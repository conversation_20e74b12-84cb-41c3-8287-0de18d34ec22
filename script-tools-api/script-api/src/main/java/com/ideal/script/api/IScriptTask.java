package com.ideal.script.api;

import com.ideal.script.dto.*;
import com.ideal.script.exception.ScriptException;

import java.util.List;
import java.util.Map;

/**
 * 获取脚本信息接口
 *
 * <AUTHOR>
 */
public interface IScriptTask {


    /**
     * 脚本服务化任务申请dubbo接口
     *
     * @param scriptTaskApplyDto 脚本信息
     * @return Map<String,String>
     * <AUTHOR>
     * @throws ScriptException 脚本服务化异常
     */
    Long scriptTaskApply(ScriptTaskApplyApiDto scriptTaskApplyDto) throws ScriptException;

    /**
     * 脚本服务化启动任务dubbo接口
     *
     * @param taskStartApiDto 任务信息
     * @return Map<String,String>
     * <AUTHOR>
     */
    Map<String,String> startScriptTask(TaskStartApiDto taskStartApiDto);


    /**
     * 根据任务实例id终止任务
     * @param taskInstanceId 任务实例id
     * <AUTHOR>
     * @throws ScriptException 脚本服务化异常
     * @return StopScriptTasksApiDto 返回值dto
     */
    StopScriptTasksApiDto stopScriptTaskByTaskInstanceId(List<Long> taskInstanceId) throws ScriptException;

    /**
     * 重试脚本服务化任务
     * @param retryScriptInstanceApiDto 重试任务相关参数
     */
    void reTryScriptTask(List<RetryScriptInstanceApiDto> retryScriptInstanceApiDto) throws ScriptException;

    /**
     * 终止其它模块脚本任务
     * @param stopCallerScriptTaskApiDtos 参数
     * @return 终止成功的agent地址
     * @throws ScriptException 脚本异常信息
     */
    List<String> stopScriptTaskByCallerTaskIdAndAgent(List<RetryScriptInstanceApiDto> stopCallerScriptTaskApiDtos) throws ScriptException;

}

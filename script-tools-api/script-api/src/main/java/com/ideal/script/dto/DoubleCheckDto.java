package com.ideal.script.dto;

import java.io.Serializable;

/**
 * 插叙是否是审核打回
 *
 * <AUTHOR>
 */
public class DoubleCheckDto implements Serializable {
    /**
     * 脚本版本id
     */
    private Long[] scriptInfoVersionId;
    /**
     * 审核类型
     */
    private Integer auditType;

    public Long[] getScriptInfoVersionId() {
        return scriptInfoVersionId;
    }

    public void setScriptInfoVersionId(Long[] scriptInfoVersionId) {
        this.scriptInfoVersionId = scriptInfoVersionId;
    }

    public Integer getAuditType() {
        return auditType;
    }

    public void setAuditType(Integer auditType) {
        this.auditType = auditType;
    }
}

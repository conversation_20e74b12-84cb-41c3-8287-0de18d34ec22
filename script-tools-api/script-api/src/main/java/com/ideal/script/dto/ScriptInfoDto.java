package com.ideal.script.dto;

import com.ideal.script.common.validation.Create;
import com.ideal.script.common.validation.Update;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

/**
 * 【请填写功能名称】对象 ieai_script_info
 *
 * <AUTHOR>
 */
public class ScriptInfoDto implements Serializable{
    private static final long serialVersionUID = 1L;


    /**
     * 脚本中文名称
     */
    @Size(min = 1, max = 255, groups = {Create.class, Update.class})
    @NotNull(groups = Create.class)
//    @NotNull(groups = {Create.class, Update.class}, message = "scriptNameZh 不能为空")
//    @NotBlank(groups = {Create.class, Update.class}, message = "scriptNameZh 不能为空")
    private String scriptNameZh;

    /**
     * 脚本名称
     */
    @Pattern(regexp = "^[a-zA-Z0-9_-]+$", groups = {Create.class, Update.class}, message = "脚本英文名只能包含数字|字母|下划线")
    @Size(min = 1, max = 255, groups = {Create.class, Update.class})
    @NotNull(groups = Create.class)
//    @NotNull(groups = {Create.class, Update.class}, message = "scriptName 不能为空")
//    @NotBlank(groups = {Create.class, Update.class}, message = "scriptName 不能为空")
    private String scriptName;

    /**
     * 脚本类型(shell、bat、perl、python、powershell,sql)
     */
    @Size(min = 1, max = 20, groups = {Create.class, Update.class})
    @NotNull(groups = Create.class)
//    @Size(min = 1, max = 20, groups = {Create.class, Update.class}, message = "scriptType 字段长度应在 1 到 20 之间")
//    @NotNull(groups = {Create.class, Update.class}, message = "scriptType 不能为空")
//    @NotBlank(groups = {Create.class, Update.class}, message = "scriptType 不能为空")
//    @Pattern(regexp = "^[a-zA-Z]+$", groups = {Create.class, Update.class}, message = "scriptType 只能是英文字母")
    private String scriptType;

    /**
     * 脚本的执行用户
     */
    @Size(max = 30, groups = {Create.class, Update.class})
//    @Size(max = 30, groups = {Create.class, Update.class}, message = "execuser 字段长度超长")
//    @NotNull(groups = {Create.class, Update.class}, message = "execuser 不能为空")
//    @NotBlank(groups = {Create.class, Update.class}, message = "execuser 不能为空")
    private String execuser;

    /**
     * 主键
     */
    @NotNull(groups = Update.class)
//    @NotNull(groups = Update.class, message = "id 不能为空")
//    @Digits(integer = 19, fraction = 0, groups = Update.class, message = "id 字段内容必须为数字")
//    @Min(value = 1000000000000000000L, groups = Update.class, message = "id 大小范围需要在 1000000000000000000 到 9223372036854775807 之间")
//    @Max(value = 9223372036854775807L, groups = Update.class, message = "id 大小范围需要在 1000000000000000000 到 9223372036854775807 之间")
    private Long id;

    /**
     * 主表唯一Uuid
     */
    @Size(min = 1, max = 50, groups = {Create.class, Update.class})
//    @Size(min = 1, max = 50, groups = {Create.class, Update.class}, message = "uniqueUuid 字段长度应在 1 到 50 之间")
//    @NotNull(groups = {Create.class, Update.class}, message = "uniqueUuid 不能为空")
//    @NotBlank(groups = {Create.class, Update.class}, message = "uniqueUuid 不能为空")
    private String uniqueUuid;

    /**
     * 编辑状态(0草稿、1发布)
     */
//    @NotNull(groups = {Create.class, Update.class}, message = "editState 不能为空")
//    @Digits(integer = 10, fraction = 0, groups = {Create.class, Update.class}, message = "editState 字段内容必须为数字")
//    @Min(value = 0, groups = {Create.class, Update.class}, message = "editState 的值必须是 0 或 1")
//    @Max(value = 1, groups = {Create.class, Update.class}, message = "editState 的值必须是 0 或 1")
//    @Pattern(regexp = "^[01]$", groups = {Create.class, Update.class}, message = "editState 的值必须是 0 或 1")
    private Integer editState;

    /**
     * 是否删除
     */
    private Integer deleted;

    /**
     * 标签
     */
    @Size(max = 1000, groups = {Create.class, Update.class})
//    @Size(min = 0, max = 1000, groups = {Create.class, Update.class}, message = "scriptLabel 字段长度应在 0 到 1000 之间")
//    @NotNull(groups = {Create.class, Update.class}, message = "scriptLabel 不能为空")
    private String scriptLabel;

    /**
     * 分类id
     */
    @NotNull(groups = Create.class)
//    @NotNull(groups = {Create.class, Update.class}, message = "categoryId 不能为空")
//    @Digits(integer = 19, fraction = 0, groups = {Create.class, Update.class}, message = "categoryId 字段内容必须为数字")
    private Long categoryId;



    /**
     * 运行前校验 1- 校验 0 - 不校验
     */
    private Integer checkBeforeExec;

    /**
     * 是否共享  1 - 已共享 0 - 未共享
     */
    private Integer share;

    /**
     * 脚本类别 0 - 脚本   1 - 白名单命令
     */
    private Integer whiteCommand;

    /**
     * 白名单命令可见类型  0 - 公有 1 - 私有
     */
    private Integer visibleType;


    /**
     * 适用平台  数据来源于ieai_platform_code表
     */
    @NotNull(groups = Create.class)
    private List<String> platforms;
//    @NotNull(groups = {Create.class, Update.class}, message = "platforms 不能为空")
//    @Size(min = 1, groups = {Create.class, Update.class}, message = "platforms 不能为空")
//    @Valid
//    private List<@Size(min = 1, max = 30, groups = {Create.class, Update.class}, message = "platforms 中元素长度应在 1 到 30 之间") @Pattern(regexp = "^[A-Za-z\\s\\-_]*$", groups = {Create.class, Update.class}, message = "platforms 中元素不能含有特殊符号或数字") String> platforms;

    /**
     * 是否应急  0 - 否 1 - 是
     */
    private Integer femscript;

    /**
     * 创建人id
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    @Size(min = 1, max = 50, groups = {Create.class, Update.class})
    private String creatorName;

    /**
     * 修改人id
     */
    private Long updatorId;

    /**
     * 修改人名称
     */
    @Size(min = 1, max = 50, groups = {Create.class, Update.class})
    private String updatorName;

    /**
     * 创建时间
     */
    private Timestamp createTime;

    /**
     * 修改时间
     */
    private Timestamp updateTime;

    /**
     * 投产介质导入导出使用
     */
    private String platform;


    /**
     * 脚本来源 0：脚本服务化，1：工具箱
     */
//    @NotNull(groups = {Create.class, Update.class}, message = "scriptSource 不能为空")
//    @Digits(integer = 10, fraction = 0, groups = {Create.class, Update.class}, message = "scriptSource 字段内容必须为数字")
//    @Min(value = 0, groups = {Create.class, Update.class}, message = "scriptSource 的值必须是 0 或 1")
//    @Max(value = 1, groups = {Create.class, Update.class}, message = "scriptSource 的值必须是 0 或 1")
//    @Pattern(regexp = "^[01]$", groups = {Create.class, Update.class}, message = "scriptSource 的值必须是 0 或 1")
    private Integer scriptSource;

    /**
     * 部门
     */
    private String orgCode;

    /**
     * 分类路径
     */
    private String categoryPath;

    /**
     * 分类信息实体Dto
     */

    private CategoryDto categoryDto;


    /**
     * 对应版本表Dto（单一脚本与版本是一对多）
     */
//    @NotNull(groups = {Create.class, Update.class}, message = "scriptVersionDto 不能为空")
    @Valid
    private ScriptVersionDto scriptVersionDto;


    /**
     * 是否忽略关键命令提醒继续 0 - 不忽略 1 - 忽略
     */
//    @NotNull(groups = {Create.class, Update.class}, message = "ignoreTipCmd 不能为空")
//    @Digits(integer = 10, fraction = 0, groups = {Create.class, Update.class}, message = "ignoreTipCmd 字段内容必须为数字")
//    @Min(value = 0, groups = {Create.class, Update.class}, message = "ignoreTipCmd 的值必须是 0 或 1")
//    @Max(value = 1, groups = {Create.class, Update.class}, message = "ignoreTipCmd 的值必须是 0 或 1")
//    @Pattern(regexp = "^[01]$", groups = {Create.class, Update.class}, message = "ignoreTipCmd 的值必须是 0 或 1")
    private Integer ignoreTipCmd;
    /**
     * 历史版本数量
     */
//    @NotNull(groups = {Create.class, Update.class}, message = "hisVersionCount 不能为空")
//    @Digits(integer = 10, fraction = 0, groups = {Create.class, Update.class}, message = "hisVersionCount 字段内容必须为数字")
//    @Min(value = 0, groups = {Create.class, Update.class}, message = "hisVersionCount 字段取值范围是 0 到 1000")
//    @Max(value = 1000, groups = {Create.class, Update.class}, message = "hisVersionCount 字段取值范围是 0 到 1000")
    private int hisVersionCount;



    /**
     * 实际新增的标签
     */
    private List<String> insertedLabels ;

    /**
     * 实际删除的标签
     */
    private List<String> removedLabels ;

    /**
     * 银行标识
     */
    private String customerName;

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public List<String> getInsertedLabels() {
        return insertedLabels;
    }

    public void setInsertedLabels(List<String> insertedLabels) {
        this.insertedLabels = insertedLabels;
    }

    public List<String> getRemovedLabels() {
        return removedLabels;
    }

    public void setRemovedLabels(List<String> removedLabels) {
        this.removedLabels = removedLabels;
    }

    public String getScriptNameZh() {
        return scriptNameZh;
    }

    public void setScriptNameZh(String scriptNameZh) {
        this.scriptNameZh = scriptNameZh;
    }

    public String getScriptName() {
        return scriptName;
    }

    public void setScriptName(String scriptName) {
        this.scriptName = scriptName;
    }

    public String getScriptType() {
        return scriptType;
    }

    public void setScriptType(String scriptType) {
        this.scriptType = scriptType;
    }

    public String getExecuser() {
        return execuser;
    }

    public void setExecuser(String execuser) {
        this.execuser = execuser;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUniqueUuid() {
        return uniqueUuid;
    }

    public void setUniqueUuid(String uniqueUuid) {
        this.uniqueUuid = uniqueUuid;
    }

    public Integer getEditState() {
        return editState;
    }

    public void setEditState(Integer editState) {
        this.editState = editState;
    }

    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    public String getScriptLabel() {
        return scriptLabel;
    }

    public void setScriptLabel(String scriptLabel) {
        this.scriptLabel = scriptLabel;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public Integer getCheckBeforeExec() {
        return checkBeforeExec;
    }

    public void setCheckBeforeExec(Integer checkBeforeExec) {
        this.checkBeforeExec = checkBeforeExec;
    }

    public Integer getShare() {
        return share;
    }

    public void setShare(Integer share) {
        this.share = share;
    }

    public Integer getWhiteCommand() {
        return whiteCommand;
    }

    public void setWhiteCommand(Integer whiteCommand) {
        this.whiteCommand = whiteCommand;
    }

    public Integer getVisibleType() {
        return visibleType;
    }

    public void setVisibleType(Integer visibleType) {
        this.visibleType = visibleType;
    }

    public List<String> getPlatforms() {
        return platforms;
    }

    public void setPlatforms(List<String> platforms) {
        this.platforms = platforms;
    }

    public Integer getFemscript() {
        return femscript;
    }

    public void setFemscript(Integer femscript) {
        this.femscript = femscript;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public Long getUpdatorId() {
        return updatorId;
    }

    public void setUpdatorId(Long updatorId) {
        this.updatorId = updatorId;
    }

    public String getUpdatorName() {
        return updatorName;
    }

    public void setUpdatorName(String updatorName) {
        this.updatorName = updatorName;
    }

    public Timestamp getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }

    public Timestamp getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Timestamp updateTime) {
        this.updateTime = updateTime;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public Integer getScriptSource() {
        return scriptSource;
    }

    public void setScriptSource(Integer scriptSource) {
        this.scriptSource = scriptSource;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getCategoryPath() {
        return categoryPath;
    }

    public void setCategoryPath(String categoryPath) {
        this.categoryPath = categoryPath;
    }

    public CategoryDto getCategoryDto() {
        return categoryDto;
    }

    public void setCategoryDto(CategoryDto categoryDto) {
        this.categoryDto = categoryDto;
    }

    public ScriptVersionDto getScriptVersionDto() {
        return scriptVersionDto;
    }

    public void setScriptVersionDto(ScriptVersionDto scriptVersionDto) {
        this.scriptVersionDto = scriptVersionDto;
    }

    public Integer getIgnoreTipCmd() {
        return ignoreTipCmd;
    }

    public void setIgnoreTipCmd(Integer ignoreTipCmd) {
        this.ignoreTipCmd = ignoreTipCmd;
    }

    public int getHisVersionCount() {
        return hisVersionCount;
    }

    public void setHisVersionCount(int hisVersionCount) {
        this.hisVersionCount = hisVersionCount;
    }

    @Override
    public String toString() {
        return "ScriptInfoDto{" +
                "scriptNameZh='" + scriptNameZh + '\'' +
                ", scriptName='" + scriptName + '\'' +
                ", scriptType='" + scriptType + '\'' +
                ", execuser='" + execuser + '\'' +
                ", id=" + id +
                ", uniqueUuid='" + uniqueUuid + '\'' +
                ", editState=" + editState +
                ", deleted=" + deleted +
                ", scriptLabel='" + scriptLabel + '\'' +
                ", categoryId=" + categoryId +
                ", checkBeforeExec=" + checkBeforeExec +
                ", share=" + share +
                ", whiteCommand=" + whiteCommand +
                ", visibleType=" + visibleType +
                ", platforms=" + platforms +
                ", femscript=" + femscript +
                ", creatorId=" + creatorId +
                ", creatorName='" + creatorName + '\'' +
                ", updatorId=" + updatorId +
                ", updatorName='" + updatorName + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", platform='" + platform + '\'' +
                ", scriptSource=" + scriptSource +
                ", orgCode='" + orgCode + '\'' +
                ", categoryPath='" + categoryPath + '\'' +
                ", categoryDto=" + categoryDto +
                ", scriptVersionDto=" + scriptVersionDto +
                ", ignoreTipCmd=" + ignoreTipCmd +
                ", hisVersionCount=" + hisVersionCount +
                '}';
    }
}

package com.ideal.script.dto;

import java.io.Serializable;
import java.util.List;

/**
 * 脚本信息查询Dto
 *
 * <AUTHOR>
 */
public class ScriptInfoQueryDto implements Serializable {

    //============脚本基本信息==============

    /**
     * 任务id
     */
    private Long taskId = null;
    /**
     * 脚本服务化基础信息表主键
     */
    private Long scriptInfoId;
    /**
     * 脚本中文名称
     */
    private String scriptNameZh;

    /**
     * 脚本名称
     */
    private String scriptName;

    /**
     * ieai_script_info表的唯一uuid
     */
    private String infoUniqueUuid;

    /**
     * 标签
     */
    private String scriptLabel;

    /**
     * 分类id
     */
    private Long categoryId;

    /**
     * 适用平台  数据来源于ieai_platform_code表
     */
    private String platform;

    /**
     * 脚本来源 0：脚本服务化，1：工具箱
     */
    private Integer scriptSource;

    /**
     * 分类全路径
     */
    private String categoryPath;

    /**
     * 编辑状态(0-草稿,1-发布)
     */
    private Integer editState;

    /**
     * 脚本类型(shell、bat、perl、python、powershell)
     */
    private String scriptType;



    //====================脚本版本信息=====================
    /**
     * 脚本版本id
     */
    private Long scriptInfoVersionId;

    /**
     * 每个版本的uuid
     */
    private String srcScriptUuid;

    //其他
    /**
     * 是否查询可用所有版本
     */
    private Boolean allVersions;

    /**
     * 是否排除默认版本，（true - 排除默认版本，false - 不排除）当allVersions存在时，此字段不允许生效
     */
    private Boolean excludeDefault;

    /**
     * 排除的脚本版本id集合
     */
    private List<Long> excludeVersionIdList;

    /**
     * 分页-页码
     */
    private Integer pageNum;

    /**
     * 分页-每页大小
     */
    private Integer pageSize;

    /**
     * 接口返回值是否携带脚本参数，true为携带，false为不携带，默认为true
     */
    private boolean queryScriptParamsFlag = true;

    /**
     * 接口返回值是否携带脚本附件，true为携带，false为不携带，默认为true
     */
    private boolean queryScriptAttachmentFlag = true;

    /**
     * 接口返回值是否携带脚本内容，true为携带，false为不携带，默认为true
     */
    private boolean queryScriptContentFlag = true;

    /**
     * 是否包含草稿脚本信息 true - 包含草稿 false - 不包含草稿
     */
    private Boolean draftFlag;

    /**
     * 是否为dubbo接口调用
     */
    private Boolean dubboFlag = true;

    /**
     * 用户信息实体类
     */
    private CurrentUserDto currentUserDto;

    private String from;


    /**
     * 关键词
     */
    private String keyword;

    /**
     * 是否返回默认版本的详情数据，true返回默认版本信息，false传什么版本就查什么版本，默认false
     */
    private Boolean showDefaultVersion = false;

    public Boolean getShowDefaultVersion() {
        return showDefaultVersion;
    }

    public void setShowDefaultVersion(Boolean showDefaultVersion) {
        this.showDefaultVersion = showDefaultVersion;
    }

    /**
     * 脚本级别，0白名单，1高风险，2中风险，3低风险
     */
    private Integer scriptLevel;

    public Integer getScriptLevel() {
        return scriptLevel;
    }

    public void setScriptLevel(Integer scriptLevel) {
        this.scriptLevel = scriptLevel;
    }


    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public String getFrom() {
        return from;
    }

    public void setFrom(String from) {
        this.from = from;
    }

    public CurrentUserDto getCurrentUserDto() {
        return currentUserDto;
    }

    public void setCurrentUserDto(CurrentUserDto currentUserDto) {
        this.currentUserDto = currentUserDto;
    }

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public Long getScriptInfoId() {
        return scriptInfoId;
    }

    public void setScriptInfoId(Long scriptInfoId) {
        this.scriptInfoId = scriptInfoId;
    }

    public Boolean getDubboFlag() {
        return dubboFlag;
    }

    public void setDubboFlag(Boolean dubboFlag) {
        this.dubboFlag = dubboFlag;
    }




    public Integer getScriptSource() {
        return scriptSource;
    }

    public void setScriptSource(Integer scriptSource) {
        this.scriptSource = scriptSource;
    }

    public boolean isQueryScriptParamsFlag() {
        return queryScriptParamsFlag;
    }

    public void setQueryScriptParamsFlag(boolean queryScriptParamsFlag) {
        this.queryScriptParamsFlag = queryScriptParamsFlag;
    }

    public boolean isQueryScriptAttachmentFlag() {
        return queryScriptAttachmentFlag;
    }

    public void setQueryScriptAttachmentFlag(boolean queryScriptAttachmentFlag) {
        this.queryScriptAttachmentFlag = queryScriptAttachmentFlag;
    }

    public boolean isQueryScriptContentFlag() {
        return queryScriptContentFlag;
    }

    public void setQueryScriptContentFlag(boolean queryScriptContentFlag) {
        this.queryScriptContentFlag = queryScriptContentFlag;
    }

    public String getScriptNameZh() {
        return scriptNameZh;
    }

    public void setScriptNameZh(String scriptNameZh) {
        this.scriptNameZh = scriptNameZh;
    }

    public String getScriptName() {
        return scriptName;
    }

    public void setScriptName(String scriptName) {
        this.scriptName = scriptName;
    }

    public String getScriptType() {
        return scriptType;
    }

    public void setScriptType(String scriptType) {
        this.scriptType = scriptType;
    }

    public String getScriptLabel() {
        return scriptLabel;
    }

    public void setScriptLabel(String scriptLabel) {
        this.scriptLabel = scriptLabel;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public Boolean getAllVersions() {
        return allVersions;
    }

    public void setAllVersions(Boolean allVersions) {
        this.allVersions = allVersions;
    }

    public Boolean getExcludeDefault() {
        return excludeDefault;
    }

    public void setExcludeDefault(Boolean excludeDefault) {
        this.excludeDefault = excludeDefault;
    }

    public String getSrcScriptUuid() {
        return srcScriptUuid;
    }

    public void setSrcScriptUuid(String srcScriptUuid) {
        this.srcScriptUuid = srcScriptUuid;
    }

    public Long getScriptInfoVersionId() {
        return scriptInfoVersionId;
    }

    public void setScriptInfoVersionId(Long scriptInfoVersionId) {
        this.scriptInfoVersionId = scriptInfoVersionId;
    }

    public List<Long> getExcludeVersionIdList() {
        return excludeVersionIdList;
    }

    public void setExcludeVersionIdList(List<Long> excludeVersionIdList) {
        this.excludeVersionIdList = excludeVersionIdList;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getInfoUniqueUuid() {
        return infoUniqueUuid;
    }

    public void setInfoUniqueUuid(String infoUniqueUuid) {
        this.infoUniqueUuid = infoUniqueUuid;
    }

    public String getCategoryPath() {
        return categoryPath;
    }

    public void setCategoryPath(String categoryPath) {
        this.categoryPath = categoryPath;
    }

    public Integer getEditState() {
        return editState;
    }

    public void setEditState(Integer editState) {
        this.editState = editState;
    }

    public Boolean getDraftFlag() {
        return draftFlag;
    }

    public void setDraftFlag(Boolean draftFlag) {
        this.draftFlag = draftFlag;
    }

    @Override
    public String toString() {
        return "ScriptInfoQueryDto{" +
                "scriptNameZh='" + scriptNameZh + '\'' +
                ", scriptName='" + scriptName + '\'' +
                ", infoUniqueUuid='" + infoUniqueUuid + '\'' +
                ", scriptLabel='" + scriptLabel + '\'' +
                ", categoryId=" + categoryId +
                ", platform='" + platform + '\'' +
                ", scriptSource=" + scriptSource +
                ", categoryPath='" + categoryPath + '\'' +
                ", editState=" + editState +
                ", scriptType='" + scriptType + '\'' +
                ", scriptInfoVersionId=" + scriptInfoVersionId +
                ", srcScriptUuid='" + srcScriptUuid + '\'' +
                ", allVersions=" + allVersions +
                ", excludeDefault=" + excludeDefault +
                ", excludeVersionIdList=" + excludeVersionIdList +
                ", pageNum=" + pageNum +
                ", pageSize=" + pageSize +
                ", queryScriptParamsFlag=" + queryScriptParamsFlag +
                ", queryScriptAttachmentFlag=" + queryScriptAttachmentFlag +
                ", queryScriptContentFlag=" + queryScriptContentFlag +
                ", draftFlag=" + draftFlag +
                '}';
    }
}

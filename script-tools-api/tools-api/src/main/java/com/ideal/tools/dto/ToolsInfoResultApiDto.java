package com.ideal.tools.dto;

import java.io.Serializable;
/**
 * 工具箱对接 流程编排 工具箱调用-工具列表
 *
 * <AUTHOR>
 * @date 2024-07-16
 */
public class ToolsInfoResultApiDto implements Serializable {
    private static final long serialVersionUID=1L;

    /** 主键ID */
    private Long id;
    /** 工具编码 */
    private String code;
    /** 工具名称 */
    private String name;
    /** 工具类型（1 描述工具，2 组合工具，3 脚本工具……） */
    private Integer type;
    /** 业务系统id */
    private Long businessSystemId;
    /** 业务系统code */
    private String businessSystemCode;
    /** 业务系统名称 */
    private String businessSystemName;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Long getBusinessSystemId() {
        return businessSystemId;
    }

    public void setBusinessSystemId(Long businessSystemId) {
        this.businessSystemId = businessSystemId;
    }

    public String getBusinessSystemName() {
        return businessSystemName;
    }

    public void setBusinessSystemName(String businessSystemName) {
        this.businessSystemName = businessSystemName;
    }

    public String getBusinessSystemCode() {
		return businessSystemCode;
	}

	public void setBusinessSystemCode(String businessSystemCode) {
		this.businessSystemCode = businessSystemCode;
	}

	@Override
    public String toString() {
        return "ToolsInfoResultApiDto{" +
                "id=" + id +
                ", code='" + code + '\'' +
                ", name='" + name + '\'' +
                ", type=" + type +
                ", businessSystemId=" + businessSystemId +
                ", businessSystemName='" + businessSystemName + '\'' +
                '}';
    }
}

package com.ideal.sc.util;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * IpUtils 类的单元测试
 *
 * <AUTHOR>
 */
public class IpUtilsTest {

    /**
     * 测试有效的IPv4地址
     */
    @Test
    public void testIsValidIPWithValidIPv4() {
        assertTrue(IpUtils.isValidIP("*******"));
    }

    /**
     * 测试无效的IP地址
     */
    @Test
    public void testIsValidIPWithInvalidIP() {
        assertFalse(IpUtils.isValidIP("256.256.256.256"));
    }

    /**
     * 测试有效的IPv4地址
     */
    @Test
    public void testIsIPv4WithValidIPv4() {
        assertTrue(IpUtils.isIPv4("***********"));
    }

    /**
     * 测试无效的IPv4地址
     */
    @Test
    public void testIsIPv4WithInvalidIPv4() {
        assertFalse(IpUtils.isIPv4("not.an.ip"));
    }

    /**
     * 测试有效的IPv6地址
     */
    @Test
    public void testIsIPv6WithValidIPv6() {
        assertTrue(IpUtils.isIPv6("2001:0db8:85a3:0000:0000:8a2e:0370:7334"));
    }

    /**
     * 测试无效的IPv6地址
     */
    @Test
    public void testIsIPv6WithInvalidIPv6() {
        assertFalse(IpUtils.isIPv6("not.an.ipv6"));
    }
}
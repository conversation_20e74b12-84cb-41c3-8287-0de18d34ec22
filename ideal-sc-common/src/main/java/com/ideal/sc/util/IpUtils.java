package com.ideal.sc.util;

import java.net.Inet4Address;
import java.net.Inet6Address;
import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * IP地址工具类
 *
 * <AUTHOR>
 */
public class IpUtils {

    /**
     * 判断是否为有效的IP地址
     *
     * @param ip IP地址
     * @return 是否为有效的IP地址
     */
    public static boolean isValidIP(String ip) {
        try {
            InetAddress address = InetAddress.getByName(ip);
            return address.getHostAddress() != null;
        } catch (UnknownHostException e) {
            return false;
        }
    }
    /**
     * 判断是否为有效的IPV4地址
     *
     * @param ip IPV4地址
     * @return 是否为有效的IPV4地址
     */
    public static boolean isIPv4(String ip) {
        try {
            InetAddress address = InetAddress.getByName(ip);
            return address instanceof Inet4Address;
        } catch (UnknownHostException e) {
            return false;
        }
    }

    /**
     * 判断是否为有效的IPV6地址
     *
     * @param ip IPV6地址
     * @return 是否为有效的IPV6地址
     */
    public static boolean isIPv6(String ip) {
        try {
            InetAddress address = InetAddress.getByName(ip);
            return address instanceof Inet6Address;
        } catch (UnknownHostException e) {
            return false;
        }
    }
}

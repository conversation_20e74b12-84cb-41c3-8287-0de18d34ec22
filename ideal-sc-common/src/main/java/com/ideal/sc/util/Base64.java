package com.ideal.sc.util;

import com.ideal.sc.constants.Constants;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;

/**
 * Base64 编码解码
 *
 * <AUTHOR>
 */
@SuppressWarnings("unused")
public class Base64 {
    private static final Logger log = LoggerFactory.getLogger(Base64.class);

    private static final char PADDING_CHARACTER = '=';

    private static final int FOUR = 4;

    private static final int THREE = 3;

    private Base64() {
        // 隐藏的私有构造函数
    }

    private static final char[] BASE64_ENCODE_CHARS = new char[]{'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H',
            'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y',
            'Z', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p',
            'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z', '0', '1', '2', '3', '4', '5', '6',
            '7', '8', '9', '+', '/'};

    private static final char[] CA = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"
            .toCharArray();
    private static final int[] IA = new int[256];

    static {
        Arrays.fill(IA, -1);
        for (int i = 0, iS = CA.length; i < iS; i++) {
            IA[CA[i]] = i;
        }
        IA['='] = 0;
    }

    public static String encode(byte[] data) {
        StringBuilder sb = new StringBuilder();
        int len = data.length;
        int i = 0;
        int b1;
        int b2;
        int b3;
        while (i < len) {
            b1 = data[i++] & 0xff;

            if (i == len) {
                sb.append(BASE64_ENCODE_CHARS[b1 >>> 2]);
                sb.append(BASE64_ENCODE_CHARS[(b1 & 0x03) << 4]);
                sb.append("==");
            } else {
                b2 = data[i++] & 0xff;
                sb.append(BASE64_ENCODE_CHARS[b1 >>> 2]);
                sb.append(BASE64_ENCODE_CHARS[((b1 & 0x03) << 4) | ((b2 & 0xf0) >>> 4)]);

                if (i == len) {
                    sb.append(BASE64_ENCODE_CHARS[(b2 & 0x0f) << 2]);
                    sb.append("=");
                } else {
                    b3 = data[i++] & 0xff;
                    sb.append(BASE64_ENCODE_CHARS[((b2 & 0x0f) << 2) | ((b3 & 0xc0) >>> 6)]);
                    sb.append(BASE64_ENCODE_CHARS[b3 & 0x3f]);
                }
            }
        }
        return sb.toString();
    }


    @Deprecated
    public static String[] decodeString(String[] param) {
        if (null == param) {
            return new String[0];
        }
        if (param.length == 0) {
            return param;
        } else {
            return decodeStringArr(param);
        }
    }
    @Deprecated
    private static String[] decodeStringArr(String[] param) {
        String[] reStringArray = new String[param.length];
        for (int i = 0; i < param.length; i++) {
            if (null == param[i]) {
                reStringArray[i] = null;
            } else if (param[i].startsWith(Constants.SHENANDOAH)) {
                reStringArray[i] = Base64.decodeString(param[i]);
            } else {
                reStringArray[i] = param[i];
            }
        }
        return reStringArray;
    }

    /**
     * 加密字符串
     */
    @Deprecated
    public static String encodeString(String jmq) {
        String encode = "";
        if (StringUtils.isNotBlank(jmq)) {
            byte[] bys1 = Base64.encodeToByte(jmq.getBytes(StandardCharsets.UTF_8));
            encode = new String(bys1, StandardCharsets.UTF_8);
            encode = Constants.SHENANDOAH + encode;
        }
        return encode;
    }


    /**
     * 解密字符串
     */
    @Deprecated
    public static String decodeString(String jmq) {
        String decode = "";
        if (StringUtils.isNotBlank(jmq) && jmq.startsWith(Constants.SHENANDOAH)) {
            jmq = jmq.substring("Constants.SHENANDOAH:".length());
            byte[] bys1 = Base64.decode(jmq.getBytes(StandardCharsets.UTF_8));
            decode = new String(bys1, StandardCharsets.UTF_8);
        }
        return decode;
    }

    public static String decodeStr(String jmq) {
        if(StringUtils.isNotBlank(jmq)){
            byte[] bytes = java.util.Base64.getDecoder().decode(jmq);
            return new String(bytes, StandardCharsets.UTF_8);
        }else{
            return null;
        }
    }

    public static String encodeStr(String jmq) {
        if(StringUtils.isNotBlank(jmq)){
            byte[] bytes = jmq.getBytes(StandardCharsets.UTF_8);
            return java.util.Base64.getEncoder().encodeToString(bytes);
        }else{
            return null;
        }

    }

    /**
     * Encodes a raw byte array into a BASE64 <code>char[]</code>
     * representation i accordance with RFC 2045.
     *
     * @param sArr    The bytes to convert. If <code>null</code> or length 0 an
     *                empty array will be returned.
     * @param lineSep Optional "\r\n" after 76 characters, unless end of file.<br>
     *                No line separator will be in breach of RFC 2045 which
     *                specifies max 76 per line but will be a little faster.
     * @return A BASE64 encoded array. Never <code>null</code>.
     */
    public static char[] encodeToChar(byte[] sArr, boolean lineSep) {
        // Check special case
        int sLen = sArr != null ? sArr.length : 0;
        if (sLen == 0) {
            return new char[0];
        }
        // Length of even 24-bits.
        int eLen = (sLen / 3) * 3;
        // Returned character count
        int cCnt = ((sLen - 1) / 3 + 1) << 2;
        // Length of
        int dLen = cCnt + (lineSep ? (cCnt - 1) / 76 << 1 : 0);
        // returned
        // array
        char[] dArr = new char[dLen];

        // Encode even 24-bits
        int cc = 0;
        int sIndex = 0;
        int dIndex = 0;
        while (sIndex < eLen) {
            // Copy next three bytes into lower 24 bits of int, paying attention
            // to sign.
            int val = toI(sArr[sIndex++]) << 16 | toI(sArr[sIndex++]) << 8 | toI(sArr[sIndex++]);

            // Encode the int into four chars
            dArr[dIndex++] = CA[(val >>> 18) & 0x3f];
            dArr[dIndex++] = CA[(val >>> 12) & 0x3f];
            dArr[dIndex++] = CA[(val >>> 6) & 0x3f];
            dArr[dIndex++] = CA[val & 0x3f];

            // Add optional line separator
            if (lineSep && ++cc == 19 && dIndex < dLen - 2) {
                dArr[dIndex++] = '\r';
                dArr[dIndex++] = '\n';
                cc = 0;
            }
        }

        // Pad and encode last bits if source isn't even 24 bits.
        // 0 - 2.
        int left = sLen - eLen;
        if (left > 0) {
            // Prepare the int
            int i = (toI(sArr[eLen]) << 10)
                    | (left == 2 ? (toI(sArr[sLen - 1]) << 2) : 0);

            // Set last four chars
            dArr[dLen - 4] = CA[i >> 12];
            dArr[dLen - 3] = CA[(i >>> 6) & 0x3f];
            dArr[dLen - 2] = left == 2 ? CA[i & 0x3f] : '=';
            dArr[dLen - 1] = '=';
        }
        return dArr;
    }


    // ****************************************************************************************
    // * byte[] version
    // ****************************************************************************************

    public static byte[] encodeToByte(byte[] sArr) {
        return encodeToByte(sArr, false);
    }

    /**
     * Encodes a raw byte array into a BASE64 <code>byte[]</code>
     * representation i accordance with RFC 2045.
     *
     * @param sArr    The bytes to convert. If <code>null</code> or length 0 an
     *                empty array will be returned.
     * @param lineSep Optional "\r\n" after 76 characters, unless end of file.<br>
     *                No line separator will be in breach of RFC 2045 which
     *                specifies max 76 per line but will be a little faster.
     * @return A BASE64 encoded array. Never <code>null</code>.
     */
    public static byte[] encodeToByte(byte[] sArr, boolean lineSep) {
        // Check special case
        int sLen = sArr != null ? sArr.length : 0;
        if (sLen == 0) {
            return new byte[0];
        }
        // Length of even 24-bits.
        int eLen = (sLen / 3) * 3;
        // Returned character count
        int cCnt = ((sLen - 1) / 3 + 1) << 2;
        // Length of
        int dLen = cCnt + (lineSep ? (cCnt - 1) / 76 << 1 : 0);
        // returned
        // array
        byte[] dArr = new byte[dLen];
        int s = 0;
        int d = 0;
        int cc = 0;
        // Encode even 24-bits
        for (; s < eLen; s += THREE) {
            // Copy next three bytes into lower 24 bits of int, paying attension
            // to sign.
            int i = toI(sArr[s++]) << 16 | toI(sArr[s++]) << 8 | toI(sArr[s++]);

            // Encode the int into four chars
            dArr[d++] = (byte) CA[(i >>> 18) & 0x3f];
            dArr[d++] = (byte) CA[(i >>> 12) & 0x3f];
            dArr[d++] = (byte) CA[(i >>> 6) & 0x3f];
            dArr[d++] = (byte) CA[i & 0x3f];

            // Add optional line separator
            if (lineSep && ++cc == 19 && d < dLen - 2) {
                dArr[d++] = '\r';
                dArr[d++] = '\n';
                cc = 0;
            }
        }

        // Pad and encode last bits if source isn't an even 24 bits.
        // 0 - 2.
        int left = sLen - eLen;
        if (left > 0) {
            // Prepare the int
            int i = (toI(sArr[eLen]) << 10)
                    | (left == 2 ? (toI(sArr[sLen - 1]) << 2) : 0);

            // Set last four chars
            dArr[dLen - 4] = (byte) CA[i >> 12];
            dArr[dLen - 3] = (byte) CA[(i >>> 6) & 0x3f];
            dArr[dLen - 2] = left == 2 ? (byte) CA[i & 0x3f] : (byte) '=';
            dArr[dLen - 1] = '=';
        }
        return dArr;
    }

    /**
     * Decodes a BASE64 encoded byte array. All illegal characters will be
     * ignored and can handle both arrays with and without line separators.
     *
     * @param sArr The source array. <code>null</code> or length 0 will return
     *             an empty array.
     * @return The decoded array of bytes. May be of length 0. Will be
     * <code>null</code> if the legal characters (including '=') isn't
     * divideable by 4. (I.e. corrupted).
     */
    public static byte[] decode(byte[] sArr) {
        int sLen = sArr != null ? sArr.length : 0;

        if (sLen == 0) {
            return new byte[0];
        }

        int sepCnt = countIllegalCharacters(sArr, sLen);

        if (!isLegalCharsDivisibleBy4(sLen, sepCnt)) {
            return new byte[0];
        }

        int pad = calculatePadding(sArr, sLen);

        int len = calculateDecodedLength(sLen, sepCnt, pad);

        byte[] dArr = new byte[len];

        assembleBytes(sArr, dArr, len);

        return dArr;
    }

    private static int countIllegalCharacters(byte[] sArr, int sLen) {
        int sepCnt = 0;
        for (int i = 0; i < sLen; i++) {
            byte b = sArr[i];
            if (b < 0 || IA[b] < 0) {
                sepCnt++;
            }
        }
        return sepCnt;
    }

    private static boolean isLegalCharsDivisibleBy4(int sLen, int sepCnt) {
        return (sLen - sepCnt) % 4 == 0;
    }

    private static int calculatePadding(byte[] sArr, int sLen) {
        int pad;
        if (sArr[sLen - 1] == PADDING_CHARACTER) {
            pad = (sArr[sLen - 2] == '=') ? 2 : 1;
        } else {
            pad = 0;
        }
        return pad;
    }

    private static int calculateDecodedLength(int sLen, int sepCnt, int pad) {
        return ((sLen - sepCnt) * 6 >> 3) - pad;
    }

    private static void assembleBytes(byte[] sArr, byte[] dArr, int len) {
        int s = 0;
        int d = 0;
        while (d < len) {
            int i = 0;
            int j = 0;
            int k = 0;

            while (k < FOUR) {
                int c = IA[sArr[s++]];

                if (c >= 0) {
                    i |= c << (18 - j * 6);
                    j++;
                    k++;
                }
            }

            dArr[d++] = (byte) (i >> 16);
            if (d < len) {
                dArr[d++] = (byte) (i >> 8);
                if (d < len) {
                    dArr[d++] = (byte) i;
                }
            }
        }
    }

    /**
     * 将字节数组编码为字符串
     *
     * @param sArr 字节数组
     * @return String
     */
    public static String encodeToString(byte[] sArr) {
        return encodeToString(sArr, false);
    }

    /**
     * Encodes a raw byte array into a BASE64 <code>String</code>
     * representation i accordance with RFC 2045.
     *
     * @param sArr    The bytes to convert. If <code>null</code> or length 0 an
     *                empty array will be returned.
     * @param lineSep Optional "\r\n" after 76 characters, unless end of file.<br>
     *                No line separator will be in breach of RFC 2045 which
     *                specifies max 76 per line but will be a little faster.
     * @return A BASE64 encoded array. Never <code>null</code>.
     */
    public static String encodeToString(byte[] sArr, boolean lineSep) {
        // Reuse char[] since we can't create a String incrementally anyway and
        // StringBuffer/Builder would be slower.
        return new String(encodeToChar(sArr, lineSep));
    }


    /**
     * If the byte is negative add 256.
     *
     * @param b The byte
     * @return a value 0..255.
     */
    private static int toI(byte b) {
        return b < 0 ? b + 256 : b;
    }


    /**
     * 解密（脚本服务化输出结果解析使用）
     *
     * @param s 密文
     * @return {@link String }
     */
    @SuppressWarnings({"java:S1191"})
    public static String getFromBase64(String s) {
         return decodeStr(s);
    }

}

package com.ideal.script.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ideal.common.util.FilePathValidator;
import com.ideal.sc.util.FileUtils;
import com.ideal.sc.util.ZipUtil;
import com.ideal.script.dto.AttachmentDto;
import com.ideal.script.dto.CategoryApiDto;
import com.ideal.script.dto.CategoryDto;
import com.ideal.script.dto.ScriptFileImportExportApiDto;
import com.ideal.script.dto.ScriptInfoDto;
import com.ideal.script.dto.ScriptVersionDto;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.mapper.CategoryMapper;
import com.ideal.script.mapper.InfoMapper;
import com.ideal.script.mapper.InfoVersionMapper;
import com.ideal.script.model.bean.ReleaseMediaBean;
import com.ideal.script.model.dto.InfoVersionTextDto;
import com.ideal.script.model.dto.ParameterCheckDto;
import com.ideal.script.model.dto.ParameterDto;
import com.ideal.script.model.dto.ParameterManagerDto;
import com.ideal.script.model.dto.ToProductDto;
import com.ideal.script.model.dto.ToProductQueryDto;
import com.ideal.script.model.dto.ToProductRelationDto;
import com.ideal.script.model.entity.Attachment;
import com.ideal.script.model.entity.Category;
import com.ideal.script.model.entity.Info;
import com.ideal.script.model.entity.InfoVersion;
import com.ideal.script.model.entity.InfoVersionText;
import com.ideal.script.model.entity.Parameter;
import com.ideal.script.model.entity.ParameterCheck;
import com.ideal.script.model.entity.ParameterManager;
import com.ideal.script.service.IAttachmentService;
import com.ideal.script.service.ICategoryService;
import com.ideal.script.service.IInfoService;
import com.ideal.script.service.IInfoVersionService;
import com.ideal.script.service.IInfoVersionTextService;
import com.ideal.script.service.IMyScriptService;
import com.ideal.script.service.IParameterCheckService;
import com.ideal.script.service.IParameterManagerService;
import com.ideal.script.service.IParameterService;
import com.ideal.script.service.IToProductRelationService;
import com.ideal.script.service.IToProductService;
import com.ideal.system.api.IUserInfo;
import com.ideal.system.common.component.model.CurrentUser;
import com.ideal.system.dto.UserInfoApiDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.io.Reader;
import java.lang.reflect.Method;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.Duration;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ReleaseMediaServiceImplTest {

    @Mock
    private IMyScriptService mockScriptService;
    @Mock
    private IParameterService mockParameterService;
    @Mock
    private IParameterCheckService mockParameterCheckService;
    @Mock
    private IParameterManagerService mockParameterManagerService;
    @Mock
    private IAttachmentService mockAttachmentService;
    @Mock
    private IInfoVersionService mockInfoVersionService;
    @Mock
    private IInfoVersionTextService mockInfoVersionTextService;
    @Mock
    private IInfoService mockInfoService;
    @Mock
    private InfoVersionMapper infoVersionMapper;
    @Mock
    private ICategoryService mockCategoryService;
    @Mock
    private InfoMapper mockInfoMapper;
    @Mock
    private IToProductService mockToProductService;
    @Mock
    private IToProductRelationService mockToProductRelationService;
    @Mock
    private CategoryMapper mockCategoryMapper;
    @Mock
    private File mockSubFolder;
    @Mock
    private IUserInfo mockUserInfoApi;

    private ReleaseMediaServiceImpl releaseMediaServiceImplUnderTest;

    @Mock
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() throws Exception {
        releaseMediaServiceImplUnderTest = new ReleaseMediaServiceImpl(mockScriptService, mockParameterService,
                mockParameterCheckService, mockParameterManagerService, mockAttachmentService, mockInfoVersionService,
                mockInfoVersionTextService, mockInfoService, mockCategoryService, mockInfoMapper, mockToProductService,
                mockToProductRelationService, mockCategoryMapper,infoVersionMapper,mockUserInfoApi,objectMapper );
    }

    @Test
    void testExportReleaseMedia() throws Exception {
        // Setup
        final MockHttpServletResponse response = new MockHttpServletResponse();

        // Configure IInfoVersionService.selectInfoVersionById(...).
        final ScriptVersionDto infoVersionDto = new ScriptVersionDto();
        infoVersionDto.setId(0L);
        infoVersionDto.setInfoUniqueUuid("infoUniqueUuid");
        infoVersionDto.setSrcScriptUuid("srcScriptUuid");
        infoVersionDto.setVersion("version");
        infoVersionDto.setEditState(0);
        when(mockInfoVersionService.selectInfoVersionById(0L)).thenReturn(infoVersionDto);

        // Configure IInfoVersionTextService.selectInfoVersionTextByScriptUuid(...).
        final InfoVersionTextDto infoVersionTextDto = new InfoVersionTextDto();
        infoVersionTextDto.setId(0L);
        infoVersionTextDto.setSrcScriptUuid("srcScriptUuid");
        infoVersionTextDto.setContent("content");
        infoVersionTextDto.setCreatorId(0L);
        infoVersionTextDto.setCreatorName("creatorName");
        when(mockInfoVersionTextService.selectInfoVersionTextByScriptUuid("srcScriptUuid"))
                .thenReturn(infoVersionTextDto);

        // Configure IInfoService.selectInfoByUniqueUuid(...).
        final ScriptInfoDto infoDto = new ScriptInfoDto();
        final Category category = new Category();
        final CategoryApiDto categoryApiDto = new CategoryApiDto();
        category.setChildren(Arrays.asList(categoryApiDto));
        category.setId(0L);
        category.setParentId(0L);
        category.setName("name");
        category.setLevel(0);
        infoDto.setCategoryDto(new CategoryDto());
        infoDto.setId(0L);
        infoDto.setUniqueUuid("infoUniqueUuid");
        infoDto.setScriptNameZh("scriptNameZh");
        infoDto.setScriptName("scriptName");
        infoDto.setCategoryId(0L);
        infoDto.setCreatorId(0L);
        infoDto.setCreatorName("creatorName");
        infoDto.setCategoryPath("categoryName");
        when(mockInfoService.selectInfoByUniqueUuid("infoUniqueUuid")).thenReturn(infoDto);

        // Configure CategoryMapper.selectCategoryById(...).
        final Category category1 = new Category();
        final CategoryApiDto categoryApiDto1 = new CategoryApiDto();
        category1.setChildren(Arrays.asList(categoryApiDto1));
        category1.setId(0L);
        category1.setParentId(0L);
        category1.setName("name");
        category1.setLevel(0);
        when(mockCategoryMapper.selectCategoryById(0L)).thenReturn(category1);

        // Configure ICategoryService.setChildrenForCategoryAndParent(...).
        final Category category2 = new Category();
        final CategoryApiDto categoryApiDto2 = new CategoryApiDto();
        category2.setChildren(Arrays.asList(categoryApiDto2));
        category2.setId(0L);
        category2.setParentId(0L);
        category2.setName("name");
        category2.setLevel(0);
        when(mockCategoryService.setChildrenForCategoryAndParent(any(Category.class))).thenReturn(category2);

        when(mockCategoryService.getCategoryFullPath(0L)).thenReturn("categoryName");



        // Configure IParameterService.getParameterByUuid(...).
        final Parameter parameter = new Parameter();
        parameter.setId(0L);
        parameter.setSrcScriptUuid("srcScriptUuid");
        parameter.setParamType("paramType");
        parameter.setParamCheckIid(0L);
        parameter.setScriptParameterManagerId(0L);
        final List<Parameter> parameters = Arrays.asList(parameter);
        when(mockParameterService.getParameterByUuid("srcScriptUuid")).thenReturn(parameters);

        // Configure IParameterCheckService.selectParameterCheckById(...).
        final ParameterCheckDto parameterCheckDto = new ParameterCheckDto();
        parameterCheckDto.setId(0L);
        parameterCheckDto.setRuleName("ruleName");
        parameterCheckDto.setCheckRule("checkRule");
        parameterCheckDto.setRuleDes("ruleDes");
        parameterCheckDto.setCreatorId(0L);
        when(mockParameterCheckService.selectParameterCheckById(0L)).thenReturn(parameterCheckDto);

        // Configure IParameterManagerService.selectParameterManagerById(...).
        final ParameterManagerDto parameterManagerDto = new ParameterManagerDto();
        parameterManagerDto.setId(0L);
        parameterManagerDto.setParamName("paramName");
        parameterManagerDto.setParamValue("paramValue");
        parameterManagerDto.setParamDesc("paramDesc");
        parameterManagerDto.setScope("scope");
        when(mockParameterManagerService.selectParameterManagerById(0L)).thenReturn(parameterManagerDto);

        // Configure IAttachmentService.getAttachmentByUuid(...).
        final AttachmentDto attachmentDto = new AttachmentDto();
        attachmentDto.setIsTempFlag(0);
        attachmentDto.setId(0L);
        attachmentDto.setSrcScriptUuid("srcScriptUuid");
        attachmentDto.setName("name");
        attachmentDto.setSize(0L);
        final List<AttachmentDto> attachmentDtos = Arrays.asList(attachmentDto);
        when(mockAttachmentService.getAttachmentByUuid("srcScriptUuid")).thenReturn(attachmentDtos);

        // Run the test
        releaseMediaServiceImplUnderTest.exportReleaseMedia(new Long[]{0L}, response);

        // Verify the results
        // Confirm IMyScriptService.createFile(...).
        final Attachment attachment = new Attachment();
        attachment.setId(0L);
        attachment.setSrcScriptUuid("srcScriptUuid");
        attachment.setName("name");
        attachment.setSize(0L);
        attachment.setContents("content".getBytes());
        final List<Attachment> attachmentList = Arrays.asList(attachment);
        //verify(mockScriptService).createFile("fileName", "scriptContent", attachmentList, "path");
        assertNotNull(attachmentList);
    }

    @Test
    void testExportReleaseMedia_WhenScriptZipFileThrowsException() throws Exception {
        // Setup
        final MockHttpServletResponse response = new MockHttpServletResponse();

        // 模拟 scriptZipFile 抛出异常
        Long [] sz = new Long[1];
        sz[0] = 1L;
//        when(releaseMediaServiceImplUnderTest.scriptZipFile(sz)).thenThrow(new ScriptException("error.clean.tempFile"));

        // Run & Verify
        assertThrows(ScriptException.class, () ->
                releaseMediaServiceImplUnderTest.exportReleaseMedia(new Long[]{0L}, response));

        // 可以验证 logger.error 是否被调用（如果你使用了 Mockito 的 mock logger）
        // verify(mockLogger).error(eq("exportReleaseMedia exception:"), any(RuntimeException.class));
    }

    @Test
    void testExportReleaseMedia_IMyScriptServiceGetBusinessSystemListReturnsNoItems() throws Exception {
        // Setup
        final MockHttpServletResponse response = new MockHttpServletResponse();

        // Configure IInfoVersionService.selectInfoVersionById(...).
        final ScriptVersionDto infoVersionDto = new ScriptVersionDto();
        infoVersionDto.setId(0L);
        infoVersionDto.setInfoUniqueUuid("infoUniqueUuid");
        infoVersionDto.setSrcScriptUuid("srcScriptUuid");
        infoVersionDto.setVersion("version");
        infoVersionDto.setEditState(0);
        when(mockInfoVersionService.selectInfoVersionById(0L)).thenReturn(infoVersionDto);

        // Configure IInfoVersionTextService.selectInfoVersionTextByScriptUuid(...).
        final InfoVersionTextDto infoVersionTextDto = new InfoVersionTextDto();
        infoVersionTextDto.setId(0L);
        infoVersionTextDto.setSrcScriptUuid("srcScriptUuid");
        infoVersionTextDto.setContent("content");
        infoVersionTextDto.setCreatorId(0L);
        infoVersionTextDto.setCreatorName("creatorName");
        when(mockInfoVersionTextService.selectInfoVersionTextByScriptUuid("srcScriptUuid"))
                .thenReturn(infoVersionTextDto);

        // Configure IInfoService.selectInfoByUniqueUuid(...).
        final ScriptInfoDto infoDto = new ScriptInfoDto();
        final Category category = new Category();
        final CategoryApiDto categoryApiDto = new CategoryApiDto();
        category.setChildren(Arrays.asList(categoryApiDto));
        category.setId(0L);
        category.setParentId(0L);
        category.setName("name");
        category.setLevel(0);
        infoDto.setCategoryDto(new CategoryDto());
        infoDto.setId(0L);
        infoDto.setUniqueUuid("infoUniqueUuid");
        infoDto.setScriptNameZh("scriptNameZh");
        infoDto.setScriptName("scriptName");
        infoDto.setCategoryId(0L);
        infoDto.setCreatorId(0L);
        infoDto.setCreatorName("creatorName");
        infoDto.setCategoryPath("categoryName");
        when(mockInfoService.selectInfoByUniqueUuid("infoUniqueUuid")).thenReturn(infoDto);

        // Configure CategoryMapper.selectCategoryById(...).
        final Category category1 = new Category();
        final CategoryApiDto categoryApiDto1 = new CategoryApiDto();
        category1.setChildren(Arrays.asList(categoryApiDto1));
        category1.setId(0L);
        category1.setParentId(0L);
        category1.setName("name");
        category1.setLevel(0);
        when(mockCategoryMapper.selectCategoryById(0L)).thenReturn(category1);

        // Configure ICategoryService.setChildrenForCategoryAndParent(...).
        final Category category2 = new Category();
        final CategoryApiDto categoryApiDto2 = new CategoryApiDto();
        category2.setChildren(Arrays.asList(categoryApiDto2));
        category2.setId(0L);
        category2.setParentId(0L);
        category2.setName("name");
        category2.setLevel(0);
        when(mockCategoryService.setChildrenForCategoryAndParent(any(Category.class))).thenReturn(category2);

        when(mockCategoryService.getCategoryFullPath(0L)).thenReturn("categoryName");

        // Configure IParameterService.getParameterByUuid(...).
        final Parameter parameter = new Parameter();
        parameter.setId(0L);
        parameter.setSrcScriptUuid("srcScriptUuid");
        parameter.setParamType("paramType");
        parameter.setParamCheckIid(0L);
        parameter.setScriptParameterManagerId(0L);
        final List<Parameter> parameters = Arrays.asList(parameter);
        when(mockParameterService.getParameterByUuid("srcScriptUuid")).thenReturn(parameters);

        // Configure IParameterCheckService.selectParameterCheckById(...).
        final ParameterCheckDto parameterCheckDto = new ParameterCheckDto();
        parameterCheckDto.setId(0L);
        parameterCheckDto.setRuleName("ruleName");
        parameterCheckDto.setCheckRule("checkRule");
        parameterCheckDto.setRuleDes("ruleDes");
        parameterCheckDto.setCreatorId(0L);
        when(mockParameterCheckService.selectParameterCheckById(0L)).thenReturn(parameterCheckDto);

        // Configure IParameterManagerService.selectParameterManagerById(...).
        final ParameterManagerDto parameterManagerDto = new ParameterManagerDto();
        parameterManagerDto.setId(0L);
        parameterManagerDto.setParamName("paramName");
        parameterManagerDto.setParamValue("paramValue");
        parameterManagerDto.setParamDesc("paramDesc");
        parameterManagerDto.setScope("scope");
        when(mockParameterManagerService.selectParameterManagerById(0L)).thenReturn(parameterManagerDto);

        // Configure IAttachmentService.getAttachmentByUuid(...).
        final AttachmentDto attachmentDto = new AttachmentDto();
        attachmentDto.setIsTempFlag(0);
        attachmentDto.setId(0L);
        attachmentDto.setSrcScriptUuid("srcScriptUuid");
        attachmentDto.setName("name");
        attachmentDto.setSize(0L);
        final List<AttachmentDto> attachmentDtos = Arrays.asList(attachmentDto);
        when(mockAttachmentService.getAttachmentByUuid("srcScriptUuid")).thenReturn(attachmentDtos);

        // Run the test
        releaseMediaServiceImplUnderTest.exportReleaseMedia(new Long[]{0L}, response);

        // Verify the results
        // Confirm IMyScriptService.createFile(...).
        final Attachment attachment = new Attachment();
        attachment.setId(0L);
        attachment.setSrcScriptUuid("srcScriptUuid");
        attachment.setName("name");
        attachment.setSize(0L);
        attachment.setContents("content".getBytes());
        final List<Attachment> attachmentList = Arrays.asList(attachment);
        assertNotNull(attachmentList);
        //verify(mockScriptService).createFile("fileName", "scriptContent", attachmentList, "path");
    }

    @Test
    void testExportReleaseMedia_IParameterServiceReturnsNoItems() throws Exception {
        // Setup
        final MockHttpServletResponse response = new MockHttpServletResponse();

        // Configure IInfoVersionService.selectInfoVersionById(...).
        final ScriptVersionDto infoVersionDto = new ScriptVersionDto();
        infoVersionDto.setId(0L);
        infoVersionDto.setInfoUniqueUuid("infoUniqueUuid");
        infoVersionDto.setSrcScriptUuid("srcScriptUuid");
        infoVersionDto.setVersion("version");
        infoVersionDto.setEditState(0);
        when(mockInfoVersionService.selectInfoVersionById(0L)).thenReturn(infoVersionDto);

        // Configure IInfoVersionTextService.selectInfoVersionTextByScriptUuid(...).
        final InfoVersionTextDto infoVersionTextDto = new InfoVersionTextDto();
        infoVersionTextDto.setId(0L);
        infoVersionTextDto.setSrcScriptUuid("srcScriptUuid");
        infoVersionTextDto.setContent("content");
        infoVersionTextDto.setCreatorId(0L);
        infoVersionTextDto.setCreatorName("creatorName");
        when(mockInfoVersionTextService.selectInfoVersionTextByScriptUuid("srcScriptUuid"))
                .thenReturn(infoVersionTextDto);

        // Configure IInfoService.selectInfoByUniqueUuid(...).
        final ScriptInfoDto infoDto = new ScriptInfoDto();
        final Category category = new Category();
        final CategoryApiDto categoryApiDto = new CategoryApiDto();
        category.setChildren(Arrays.asList(categoryApiDto));
        category.setId(0L);
        category.setParentId(0L);
        category.setName("name");
        category.setLevel(0);
        infoDto.setCategoryDto(new CategoryDto());
        infoDto.setId(0L);
        infoDto.setUniqueUuid("infoUniqueUuid");
        infoDto.setScriptNameZh("scriptNameZh");
        infoDto.setScriptName("scriptName");
        infoDto.setCategoryId(0L);
        infoDto.setCreatorId(0L);
        infoDto.setCreatorName("creatorName");
        infoDto.setCategoryPath("categoryName");
        when(mockInfoService.selectInfoByUniqueUuid("infoUniqueUuid")).thenReturn(infoDto);

        // Configure CategoryMapper.selectCategoryById(...).
        final Category category1 = new Category();
        final CategoryApiDto categoryApiDto1 = new CategoryApiDto();
        category1.setChildren(Arrays.asList(categoryApiDto1));
        category1.setId(0L);
        category1.setParentId(0L);
        category1.setName("name");
        category1.setLevel(0);
        when(mockCategoryMapper.selectCategoryById(0L)).thenReturn(category1);

        // Configure ICategoryService.setChildrenForCategoryAndParent(...).
        final Category category2 = new Category();
        final CategoryApiDto categoryApiDto2 = new CategoryApiDto();
        category2.setChildren(Arrays.asList(categoryApiDto2));
        category2.setId(0L);
        category2.setParentId(0L);
        category2.setName("name");
        category2.setLevel(0);
        when(mockCategoryService.setChildrenForCategoryAndParent(any(Category.class))).thenReturn(category2);

        when(mockCategoryService.getCategoryFullPath(0L)).thenReturn("categoryName");


        when(mockParameterService.getParameterByUuid("srcScriptUuid")).thenReturn(Collections.emptyList());

        // Configure IAttachmentService.getAttachmentByUuid(...).
        final AttachmentDto attachmentDto = new AttachmentDto();
        attachmentDto.setIsTempFlag(0);
        attachmentDto.setId(0L);
        attachmentDto.setSrcScriptUuid("srcScriptUuid");
        attachmentDto.setName("name");
        attachmentDto.setSize(0L);
        final List<AttachmentDto> attachmentDtos = Arrays.asList(attachmentDto);
        when(mockAttachmentService.getAttachmentByUuid("srcScriptUuid")).thenReturn(attachmentDtos);

        // Run the test
        releaseMediaServiceImplUnderTest.exportReleaseMedia(new Long[]{0L}, response);

        // Verify the results
        // Confirm IMyScriptService.createFile(...).
        final Attachment attachment = new Attachment();
        attachment.setId(0L);
        attachment.setSrcScriptUuid("srcScriptUuid");
        attachment.setName("name");
        attachment.setSize(0L);
        attachment.setContents("content".getBytes());
        final List<Attachment> attachmentList = Arrays.asList(attachment);
        assertNotNull(attachmentList);
        //verify(mockScriptService).createFile("fileName", "scriptContent", attachmentList, "path");
    }

    @Test
    void testExportReleaseMedia_IAttachmentServiceReturnsNoItems() throws Exception {
        // Setup
        final MockHttpServletResponse response = new MockHttpServletResponse();

        // Configure IInfoVersionService.selectInfoVersionById(...).
        final ScriptVersionDto infoVersionDto = new ScriptVersionDto();
        infoVersionDto.setId(0L);
        infoVersionDto.setInfoUniqueUuid("infoUniqueUuid");
        infoVersionDto.setSrcScriptUuid("srcScriptUuid");
        infoVersionDto.setVersion("version");
        infoVersionDto.setEditState(0);
        when(mockInfoVersionService.selectInfoVersionById(0L)).thenReturn(infoVersionDto);

        // Configure IInfoVersionTextService.selectInfoVersionTextByScriptUuid(...).
        final InfoVersionTextDto infoVersionTextDto = new InfoVersionTextDto();
        infoVersionTextDto.setId(0L);
        infoVersionTextDto.setSrcScriptUuid("srcScriptUuid");
        infoVersionTextDto.setContent("content");
        infoVersionTextDto.setCreatorId(0L);
        infoVersionTextDto.setCreatorName("creatorName");
        when(mockInfoVersionTextService.selectInfoVersionTextByScriptUuid("srcScriptUuid"))
                .thenReturn(infoVersionTextDto);

        // Configure IInfoService.selectInfoByUniqueUuid(...).
        final ScriptInfoDto infoDto = new ScriptInfoDto();
        final Category category = new Category();
        final CategoryApiDto categoryApiDto = new CategoryApiDto();
        category.setChildren(Arrays.asList(categoryApiDto));
        category.setId(0L);
        category.setParentId(0L);
        category.setName("name");
        category.setLevel(0);
        infoDto.setCategoryDto(new CategoryDto());
        infoDto.setId(0L);
        infoDto.setUniqueUuid("infoUniqueUuid");
        infoDto.setScriptNameZh("scriptNameZh");
        infoDto.setScriptName("scriptName");
        infoDto.setCategoryId(0L);
        infoDto.setCreatorId(0L);
        infoDto.setCreatorName("creatorName");
        infoDto.setCategoryPath("categoryName");
        when(mockInfoService.selectInfoByUniqueUuid("infoUniqueUuid")).thenReturn(infoDto);

        // Configure CategoryMapper.selectCategoryById(...).
        final Category category1 = new Category();
        final CategoryApiDto categoryApiDto1 = new CategoryApiDto();
        category1.setChildren(Arrays.asList(categoryApiDto1));
        category1.setId(0L);
        category1.setParentId(0L);
        category1.setName("name");
        category1.setLevel(0);
        when(mockCategoryMapper.selectCategoryById(0L)).thenReturn(category1);

        // Configure ICategoryService.setChildrenForCategoryAndParent(...).
        final Category category2 = new Category();
        final CategoryApiDto categoryApiDto2 = new CategoryApiDto();
        category2.setChildren(Arrays.asList(categoryApiDto2));
        category2.setId(0L);
        category2.setParentId(0L);
        category2.setName("name");
        category2.setLevel(0);
        when(mockCategoryService.setChildrenForCategoryAndParent(any(Category.class))).thenReturn(category2);

        when(mockCategoryService.getCategoryFullPath(0L)).thenReturn("categoryName");



        // Configure IParameterService.getParameterByUuid(...).
        final Parameter parameter = new Parameter();
        parameter.setId(0L);
        parameter.setSrcScriptUuid("srcScriptUuid");
        parameter.setParamType("paramType");
        parameter.setParamCheckIid(0L);
        parameter.setScriptParameterManagerId(0L);
        final List<Parameter> parameters = Arrays.asList(parameter);
        when(mockParameterService.getParameterByUuid("srcScriptUuid")).thenReturn(parameters);

        // Configure IParameterCheckService.selectParameterCheckById(...).
        final ParameterCheckDto parameterCheckDto = new ParameterCheckDto();
        parameterCheckDto.setId(0L);
        parameterCheckDto.setRuleName("ruleName");
        parameterCheckDto.setCheckRule("checkRule");
        parameterCheckDto.setRuleDes("ruleDes");
        parameterCheckDto.setCreatorId(0L);
        when(mockParameterCheckService.selectParameterCheckById(0L)).thenReturn(parameterCheckDto);

        // Configure IParameterManagerService.selectParameterManagerById(...).
        final ParameterManagerDto parameterManagerDto = new ParameterManagerDto();
        parameterManagerDto.setId(0L);
        parameterManagerDto.setParamName("paramName");
        parameterManagerDto.setParamValue("paramValue");
        parameterManagerDto.setParamDesc("paramDesc");
        parameterManagerDto.setScope("scope");
        when(mockParameterManagerService.selectParameterManagerById(0L)).thenReturn(parameterManagerDto);

        when(mockAttachmentService.getAttachmentByUuid("srcScriptUuid")).thenReturn(Collections.emptyList());

        // Run the test
        releaseMediaServiceImplUnderTest.exportReleaseMedia(new Long[]{0L}, response);

        // Verify the results
        // Confirm IMyScriptService.createFile(...).
        final Attachment attachment = new Attachment();
        attachment.setId(0L);
        attachment.setSrcScriptUuid("srcScriptUuid");
        attachment.setName("name");
        attachment.setSize(0L);
        attachment.setContents("content".getBytes());
        final List<Attachment> attachmentList = Arrays.asList(attachment);
        assertNotNull(attachmentList);
        //verify(mockScriptService).createFile("fileName", "scriptContent", attachmentList, "path");
    }

    @Test
    void testIsFormalReleaseMediaFile() {
        // Setup
        final File zipFile = new File("filename.txt");

        // Run the test
        final boolean result = releaseMediaServiceImplUnderTest.isFormalReleaseMediaFile(zipFile);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    void testImportReleaseMedia() throws Exception {
        // Setup
        final MultipartFile file = new MockMultipartFile("投产介质_测试投产介质.zip", "投产介质_测试投产介质.zip", "application/zip", "content".getBytes());
        final CurrentUser user = new CurrentUser();
        user.setId(0L);
        user.setLoginName("loginName");

        final ToProductQueryDto toProductQueryDto = new ToProductQueryDto();
        toProductQueryDto.setSrcScriptUuidList(Arrays.asList("value"));
        toProductQueryDto.setUserName("loginName");
        toProductQueryDto.setFileName("fileName");
        toProductQueryDto.setUserId(0L);
        toProductQueryDto.setScriptUserId(0L);
        toProductQueryDto.setScriptUserName("creatorName");

        // 使用try-with-resources来管理MockedStatic
        try (MockedStatic<ZipUtil> mockedZipUtil = mockStatic(ZipUtil.class)) {
            // 模拟ZipUtil.upZipFile方法，不做任何事情
            mockedZipUtil.when(() -> ZipUtil.upZipFile(anyString(), anyString())).thenAnswer(invocation -> null);

            // Run the test
            releaseMediaServiceImplUnderTest.importReleaseMedia(file, user, toProductQueryDto);

            // Verify the results
            assertTimeout(Duration.ofMillis(500), () -> {
                releaseMediaServiceImplUnderTest.importReleaseMedia(file, user, toProductQueryDto);
            });
        }
    }

    @Test
    void testSaveProductInfo() {
        // Setup
        final ToProductQueryDto toProductQueryDto = new ToProductQueryDto();
        toProductQueryDto.setSrcScriptUuidList(Arrays.asList("value"));
        toProductQueryDto.setUserName("loginName");
        toProductQueryDto.setFileName("fileName");
        toProductQueryDto.setUserId(0L);
        toProductQueryDto.setScriptUserId(0L);
        toProductQueryDto.setScriptUserName("creatorName");

        // Run the test
        releaseMediaServiceImplUnderTest.saveProductInfo(toProductQueryDto);

        // Verify the results
        verify(mockToProductService).insertToProduct(any(ToProductDto.class));
        verify(mockToProductRelationService).insertToProductRelation(any(ToProductRelationDto.class));
    }
    @Test
    void testUpdateCategoryId() {
        // Setup
        final ReleaseMediaBean releaseMediaBean = new ReleaseMediaBean();

        releaseMediaBean.setSrcScriptUuidExist(false);
        final InfoVersion infoVersion = new InfoVersion();
        infoVersion.setId(0L);
        infoVersion.setInfoUniqueUuid("infoUniqueUuid");
        infoVersion.setSrcScriptUuid("srcScriptUuid");
        infoVersion.setEditState(0);
        infoVersion.setUseState(0);
        infoVersion.setIsDefault(0);
        infoVersion.setCreatorId(0L);
        infoVersion.setCreatorName("creatorName");
        releaseMediaBean.setInfoVersion(infoVersion);
        final InfoVersionText infoVersionText = new InfoVersionText();
        infoVersionText.setId(0L);
        infoVersionText.setSrcScriptUuid("srcScriptUuid");
        releaseMediaBean.setInfoVersionText(infoVersionText);
        final Parameter parameter = new Parameter();
        parameter.setId(0L);
        parameter.setParamCheckIid(0L);
        parameter.setScriptParameterManagerId(0L);
        releaseMediaBean.setParameters(Arrays.asList(parameter));
        final ParameterCheck parameterCheck = new ParameterCheck();
        parameterCheck.setId(0L);
        parameterCheck.setRuleName("ruleName");
        releaseMediaBean.setParameterCheckList(Arrays.asList(parameterCheck));
        final ParameterManager parameterManager = new ParameterManager();
        releaseMediaBean.setParameterManagerList(Arrays.asList(parameterManager));
        final Attachment attachment = new Attachment();
        attachment.setId(0L);
        attachment.setSrcScriptUuid("srcScriptUuid");
        attachment.setName("name");
        attachment.setSize(0L);
        attachment.setContents("content".getBytes());
        releaseMediaBean.setAttachmentList(Arrays.asList(attachment));
        releaseMediaBean.setCreatorId(0L);
        releaseMediaBean.setCreatorName("creatorName");
        final ScriptInfoDto infoDto = new ScriptInfoDto();
        final Category category = new Category();
        final CategoryApiDto categoryApiDto = new CategoryApiDto();
        category.setChildren(Arrays.asList(categoryApiDto));
        category.setId(0L);
        category.setParentId(0L);
        category.setName("name");
        category.setLevel(0);
        infoDto.setCategoryDto(new CategoryDto());
        infoDto.setId(0L);
        infoDto.setUniqueUuid("infoUniqueUuid");
        infoDto.setScriptNameZh("scriptNameZh");
        infoDto.setScriptName("scriptName");
        infoDto.setCategoryId(0L);
        infoDto.setCreatorId(0L);
        infoDto.setCreatorName("creatorName");
        infoDto.setCategoryPath("categoryName");
        releaseMediaBean.setInfoDto(infoDto);

        // Run the test
        releaseMediaServiceImplUnderTest.updateCategoryId(releaseMediaBean, 0L);

        // Verify the results
        verify(mockInfoService).updateInfo(any(ScriptInfoDto.class));
    }

    @Test
    void testSaveInfoVersionText() {
        // Setup
        final ReleaseMediaBean releaseMediaBean = new ReleaseMediaBean();
        releaseMediaBean.setSrcScriptUuidExist(false);
        final InfoVersion infoVersion = new InfoVersion();
        infoVersion.setId(0L);
        infoVersion.setInfoUniqueUuid("infoUniqueUuid");
        infoVersion.setSrcScriptUuid("srcScriptUuid");
        infoVersion.setEditState(0);
        infoVersion.setUseState(0);
        infoVersion.setIsDefault(0);
        infoVersion.setCreatorId(0L);
        infoVersion.setCreatorName("creatorName");
        releaseMediaBean.setInfoVersion(infoVersion);
        final InfoVersionText infoVersionText = new InfoVersionText();
        infoVersionText.setId(0L);
        infoVersionText.setSrcScriptUuid("srcScriptUuid");
        releaseMediaBean.setInfoVersionText(infoVersionText);
        final Parameter parameter = new Parameter();
        parameter.setId(0L);
        parameter.setParamCheckIid(0L);
        parameter.setScriptParameterManagerId(0L);
        releaseMediaBean.setParameters(Arrays.asList(parameter));
        final ParameterCheck parameterCheck = new ParameterCheck();
        parameterCheck.setId(0L);
        parameterCheck.setRuleName("ruleName");
        releaseMediaBean.setParameterCheckList(Arrays.asList(parameterCheck));
        final ParameterManager parameterManager = new ParameterManager();
        releaseMediaBean.setParameterManagerList(Arrays.asList(parameterManager));
        final Attachment attachment = new Attachment();
        attachment.setId(0L);
        attachment.setSrcScriptUuid("srcScriptUuid");
        attachment.setName("name");
        attachment.setSize(0L);
        attachment.setContents("content".getBytes());
        releaseMediaBean.setAttachmentList(Arrays.asList(attachment));
        releaseMediaBean.setCreatorId(0L);
        releaseMediaBean.setCreatorName("creatorName");
        final ScriptInfoDto infoDto = new ScriptInfoDto();
        final Category category = new Category();
        final CategoryApiDto categoryApiDto = new CategoryApiDto();
        category.setChildren(Arrays.asList(categoryApiDto));
        category.setId(0L);
        category.setParentId(0L);
        category.setName("name");
        category.setLevel(0);
        infoDto.setCategoryDto(new CategoryDto());
        infoDto.setId(0L);
        infoDto.setUniqueUuid("infoUniqueUuid");
        infoDto.setScriptNameZh("scriptNameZh");
        infoDto.setScriptName("scriptName");
        infoDto.setCategoryId(0L);
        infoDto.setCreatorId(0L);
        infoDto.setCreatorName("creatorName");
        infoDto.setCategoryPath("categoryName");
        releaseMediaBean.setInfoDto(infoDto);

        // Run the test
        releaseMediaServiceImplUnderTest.saveInfoVersionText(releaseMediaBean);

        // Verify the results
        verify(mockInfoVersionTextService).insertInfoVersionText(any(InfoVersionTextDto.class));
    }

    @Test
    void testSaveParameters() {
        // Setup
        final ReleaseMediaBean releaseMediaBean = new ReleaseMediaBean();

        releaseMediaBean.setSrcScriptUuidExist(false);
        final InfoVersion infoVersion = new InfoVersion();
        infoVersion.setId(0L);
        infoVersion.setInfoUniqueUuid("infoUniqueUuid");
        infoVersion.setSrcScriptUuid("srcScriptUuid");
        infoVersion.setEditState(0);
        infoVersion.setUseState(0);
        infoVersion.setIsDefault(0);
        infoVersion.setCreatorId(0L);
        infoVersion.setCreatorName("creatorName");
        releaseMediaBean.setInfoVersion(infoVersion);
        final InfoVersionText infoVersionText = new InfoVersionText();
        infoVersionText.setId(0L);
        infoVersionText.setSrcScriptUuid("srcScriptUuid");
        releaseMediaBean.setInfoVersionText(infoVersionText);
        final Parameter parameter = new Parameter();
        parameter.setId(0L);
        parameter.setParamCheckIid(0L);
        parameter.setScriptParameterManagerId(0L);
        releaseMediaBean.setParameters(Arrays.asList(parameter));
        final ParameterCheck parameterCheck = new ParameterCheck();
        parameterCheck.setId(0L);
        parameterCheck.setRuleName("ruleName");
        releaseMediaBean.setParameterCheckList(Arrays.asList(parameterCheck));
        final ParameterManager parameterManager = new ParameterManager();
        releaseMediaBean.setParameterManagerList(Arrays.asList(parameterManager));
        final Attachment attachment = new Attachment();
        attachment.setId(0L);
        attachment.setSrcScriptUuid("srcScriptUuid");
        attachment.setName("name");
        attachment.setSize(0L);
        attachment.setContents("content".getBytes());
        releaseMediaBean.setAttachmentList(Arrays.asList(attachment));
        releaseMediaBean.setCreatorId(0L);
        releaseMediaBean.setCreatorName("creatorName");
        final ScriptInfoDto infoDto = new ScriptInfoDto();
        final Category category = new Category();
        final CategoryApiDto categoryApiDto = new CategoryApiDto();
        category.setChildren(Arrays.asList(categoryApiDto));
        category.setId(0L);
        category.setParentId(0L);
        category.setName("name");
        category.setLevel(0);
        infoDto.setCategoryDto(new CategoryDto());
        infoDto.setId(0L);
        infoDto.setUniqueUuid("infoUniqueUuid");
        infoDto.setScriptNameZh("scriptNameZh");
        infoDto.setScriptName("scriptName");
        infoDto.setCategoryId(0L);
        infoDto.setCreatorId(0L);
        infoDto.setCreatorName("creatorName");
        infoDto.setCategoryPath("categoryName");
        releaseMediaBean.setInfoDto(infoDto);

        final Map<Long, Long> map = new HashMap<>();

        // Run the test
        releaseMediaServiceImplUnderTest.saveParameters(releaseMediaBean, map,new HashMap<>());

        // Verify the results
        verify(mockParameterService).insertParameter(any(ParameterDto.class));
    }

    @Test
    void testSaveInfoVersion() {
        // Setup
        final ReleaseMediaBean releaseMediaBean = new ReleaseMediaBean();

        releaseMediaBean.setSrcScriptUuidExist(false);
        final InfoVersion infoVersion = new InfoVersion();
        infoVersion.setId(0L);
        infoVersion.setInfoUniqueUuid("infoUniqueUuid");
        infoVersion.setSrcScriptUuid("srcScriptUuid");
        infoVersion.setEditState(0);
        infoVersion.setUseState(0);
        infoVersion.setIsDefault(1);
        infoVersion.setCreatorId(0L);
        infoVersion.setCreatorName("creatorName");
        releaseMediaBean.setInfoVersion(infoVersion);
        final InfoVersionText infoVersionText = new InfoVersionText();
        infoVersionText.setId(0L);
        infoVersionText.setSrcScriptUuid("srcScriptUuid");
        releaseMediaBean.setInfoVersionText(infoVersionText);
        final Parameter parameter = new Parameter();
        parameter.setId(0L);
        parameter.setParamCheckIid(0L);
        parameter.setScriptParameterManagerId(0L);
        releaseMediaBean.setParameters(Arrays.asList(parameter));
        final ParameterCheck parameterCheck = new ParameterCheck();
        parameterCheck.setId(0L);
        parameterCheck.setRuleName("ruleName");
        releaseMediaBean.setParameterCheckList(Arrays.asList(parameterCheck));
        final ParameterManager parameterManager = new ParameterManager();
        releaseMediaBean.setParameterManagerList(Arrays.asList(parameterManager));
        final Attachment attachment = new Attachment();
        attachment.setId(0L);
        attachment.setSrcScriptUuid("srcScriptUuid");
        attachment.setName("name");
        attachment.setSize(0L);
        attachment.setContents("content".getBytes());
        releaseMediaBean.setAttachmentList(Arrays.asList(attachment));
        releaseMediaBean.setCreatorId(0L);
        releaseMediaBean.setCreatorName("creatorName");
        final ScriptInfoDto infoDto = new ScriptInfoDto();
        final Category category = new Category();
        final CategoryApiDto categoryApiDto = new CategoryApiDto();
        category.setChildren(Arrays.asList(categoryApiDto));
        category.setId(0L);
        category.setParentId(0L);
        category.setName("name");
        category.setLevel(0);
        infoDto.setCategoryDto(new CategoryDto());
        infoDto.setId(0L);
        infoDto.setUniqueUuid("infoUniqueUuid");
        infoDto.setScriptNameZh("scriptNameZh");
        infoDto.setScriptName("scriptName");
        infoDto.setCategoryId(0L);
        infoDto.setCreatorId(0L);
        infoDto.setCreatorName("creatorName");
        infoDto.setCategoryPath("categoryName");
        releaseMediaBean.setInfoDto(infoDto);
        when(mockInfoVersionService.getInfoDefaultVersionByUniqueUuid(anyString())).thenReturn(infoVersion);

        // Run the test
        releaseMediaServiceImplUnderTest.saveInfoVersion(releaseMediaBean);

        // Verify the results
        verify(mockInfoVersionService).insertInfoVersion(any(ScriptVersionDto.class));
        verify(mockInfoVersionService).updateInfoVersionDefaultValue(any(InfoVersion.class));
    }

    @Test
    void testSaveInfo() throws ScriptException {
        // Setup
        final ReleaseMediaBean releaseMediaBean = new ReleaseMediaBean();
        releaseMediaBean.setSrcScriptUuidExist(false);
        final InfoVersion infoVersion = new InfoVersion();
        infoVersion.setId(0L);
        infoVersion.setInfoUniqueUuid("infoUniqueUuid");
        infoVersion.setSrcScriptUuid("srcScriptUuid");
        infoVersion.setEditState(0);
        infoVersion.setUseState(0);
        infoVersion.setIsDefault(0);
        infoVersion.setCreatorId(0L);
        infoVersion.setCreatorName("creatorName");
        releaseMediaBean.setInfoVersion(infoVersion);
        final InfoVersionText infoVersionText = new InfoVersionText();
        infoVersionText.setId(0L);
        infoVersionText.setSrcScriptUuid("srcScriptUuid");
        releaseMediaBean.setInfoVersionText(infoVersionText);
        final Parameter parameter = new Parameter();
        parameter.setId(0L);
        parameter.setParamCheckIid(0L);
        parameter.setScriptParameterManagerId(0L);
        releaseMediaBean.setParameters(Arrays.asList(parameter));
        final ParameterCheck parameterCheck = new ParameterCheck();
        parameterCheck.setId(0L);
        parameterCheck.setRuleName("ruleName");
        releaseMediaBean.setParameterCheckList(Arrays.asList(parameterCheck));
        final ParameterManager parameterManager = new ParameterManager();
        releaseMediaBean.setParameterManagerList(Arrays.asList(parameterManager));
        final Attachment attachment = new Attachment();
        attachment.setId(0L);
        attachment.setSrcScriptUuid("srcScriptUuid");
        attachment.setName("name");
        attachment.setSize(0L);
        attachment.setContents("content".getBytes());
        releaseMediaBean.setAttachmentList(Arrays.asList(attachment));
        releaseMediaBean.setCreatorId(0L);
        releaseMediaBean.setCreatorName("creatorName");
        final ScriptInfoDto infoDto = new ScriptInfoDto();
        final Category category = new Category();
        final CategoryApiDto categoryApiDto = new CategoryApiDto();
        category.setChildren(Arrays.asList(categoryApiDto));
        category.setId(0L);
        category.setParentId(0L);
        category.setName("name");
        category.setLevel(0);
        infoDto.setCategoryDto(new CategoryDto());
        infoDto.setId(0L);
        infoDto.setUniqueUuid("infoUniqueUuid");
        infoDto.setScriptNameZh("scriptNameZh");
        infoDto.setScriptName("scriptName");
        infoDto.setCategoryId(0L);
        infoDto.setCreatorId(0L);
        infoDto.setCreatorName("creatorName");
        infoDto.setCategoryPath("categoryName");
        releaseMediaBean.setInfoDto(infoDto);


        List<UserInfoApiDto> userInfoApiDtoList = new ArrayList<>();
        UserInfoApiDto userInfoApiDto = new UserInfoApiDto();
        userInfoApiDto.setId(1L);
        userInfoApiDto.setOrgCode("orgCode");
        userInfoApiDtoList.add(userInfoApiDto);

        // Run the test
        releaseMediaServiceImplUnderTest.saveInfo(releaseMediaBean);

        // Verify the results
        verify(mockInfoService).insertInfo(any(ScriptInfoDto.class));
    }

    @Test
    void testSaveAttachments() {
        // Setup
        final Attachment attachment = new Attachment();
        attachment.setId(0L);
        attachment.setSrcScriptUuid("srcScriptUuid");
        attachment.setName("name");
        attachment.setSize(0L);
        attachment.setContents("content".getBytes());
        final List<Attachment> attachmentList = Arrays.asList(attachment);

        // Run the test
        releaseMediaServiceImplUnderTest.saveAttachments(attachmentList);

        // Verify the results
        verify(mockAttachmentService).insertAttachment(any(AttachmentDto.class));
    }


    @Test
    void testHandleAttachmentFile_ThrowsScriptException() {
        // Setup
        final File attachmentFile = new File("filename.txt");

        // Run the test
        assertThatThrownBy(() -> releaseMediaServiceImplUnderTest.handleAttachmentFile(attachmentFile,
                "srcScriptUuid")).isInstanceOf(ScriptException.class);
    }


    @Test
    void testHandleAttachmentFile() throws Exception {
        // 创建一个临时文件并写入测试内容
        Path tempFile = Files.createTempFile("test", ".txt");
        String testContent = "Hello, World!";
        Files.write(tempFile, testContent.getBytes());

        File attachmentFile = tempFile.toFile();
        String srcScriptUuid = "test-uuid";

        try {
            // 执行测试
            Attachment result = releaseMediaServiceImplUnderTest.handleAttachmentFile(attachmentFile, srcScriptUuid);

            // 验证结果
            assertNotNull(result);
            assertEquals(attachmentFile.getName(), result.getName());
            assertEquals(testContent.getBytes().length, result.getSize());
            assertArrayEquals(testContent.getBytes(), result.getContents());
            assertEquals(srcScriptUuid, result.getSrcScriptUuid());
        } finally {
            // 清理临时文件
            Files.deleteIfExists(tempFile);
        }
    }

    @Test
    void testHandleAttachmentFile_ThrowsException() throws Exception {
        // 创建一个不存在的文件以触发异常
        File nonExistentFile = new File("nonexistent.txt");
        String srcScriptUuid = "test-uuid";

        // 验证是否抛出预期的异常
        ScriptException exception = assertThrows(ScriptException.class, () ->
                releaseMediaServiceImplUnderTest.handleAttachmentFile(nonExistentFile, srcScriptUuid)
        );

        // 验证异常消息
        assertEquals("error.import.attachment", exception.getMessage());
    }

    @Mock
    private File outputDirectoryMock;
    @Test
    void testHandlerImportMedia() throws Exception {
        // Setup
        //final File outputDirectory = new File("filename.txt");
        final ToProductQueryDto toProductQueryDto = new ToProductQueryDto();
        toProductQueryDto.setSrcScriptUuidList(Arrays.asList("value"));
        toProductQueryDto.setUserName("loginName");
        toProductQueryDto.setFileName("fileName");
        toProductQueryDto.setUserId(0L);
        toProductQueryDto.setScriptUserId(0L);
        toProductQueryDto.setScriptUserName("creatorName");

        //when(mockInfoVersionService.validSrcScriptUuidExist("srcScriptUuid")).thenReturn(false);
        //when(mockInfoService.validScriptNameZhCountExist("scriptNameZh")).thenReturn(false);

        // Configure InfoMapper.selectInfoList(...).
        final Info info = new Info();
        info.setId(0L);
        info.setUniqueUuid("infoUniqueUuid");
        info.setScriptNameZh("scriptNameZh");
        info.setCreatorId(0L);
        info.setCreatorName("creatorName");
        final List<Info> infos = Arrays.asList(info);
        //when(mockInfoMapper.selectInfoList(any(Info.class))).thenReturn(infos);

        // Configure ICategoryService.findByLevelAndNameAndParentId(...).
        final CategoryDto categoryDto = new CategoryDto();
        final CategoryApiDto categoryApiDto = new CategoryApiDto();
        categoryApiDto.setChildren(Arrays.asList(new CategoryApiDto()));
        categoryApiDto.setId(0L);
        categoryApiDto.setParentId(0L);
        categoryDto.setChildren(Arrays.asList(categoryApiDto));
        categoryDto.setId(0L);
        //when(mockCategoryService.findByLevelAndNameAndParentId(0L, "name", 0L)).thenReturn(categoryDto);

        //when(mockParameterCheckService.validParamterCheckExist("ruleName")).thenReturn(false);

        // Configure IParameterCheckService.selectParameterCheckByName(...).
        final ParameterCheckDto parameterCheckDto = new ParameterCheckDto();
        parameterCheckDto.setId(0L);
        parameterCheckDto.setRuleName("ruleName");
        parameterCheckDto.setCheckRule("checkRule");
        parameterCheckDto.setRuleDes("ruleDes");
        parameterCheckDto.setCreatorId(0L);
        //when(mockParameterCheckService.selectParameterCheckByName("ruleName")).thenReturn(parameterCheckDto);

        // 使用 doAnswer() 方法来模拟方法的行为
       /* doAnswer(invocation -> {
            // 在这里编写模拟的逻辑，例如创建临时文件夹
            Path tempDir = Files.createTempDirectory("H:\\workSpace\\v8_weifuwu\\ieai-script-service\\script-biz\\target\\test-classes\\importReleaseMedia\\投产介质_测试投产介质");
            return tempDir.toFile();
        }).when(outputDirectoryMock).mkdirs();*/

        // 在需要时模拟其它方法的行为，例如 exists() 方法
        when(outputDirectoryMock.isDirectory()).thenReturn(true);

        // 创建一个模拟的 File[] 数组
        File[] nonNullFiles = new File[2];
        nonNullFiles[0] = Mockito.spy(new File("file1"));
        nonNullFiles[1] = new File("file2");

        // 创建第二个模拟的 File[] 数组
        File[] secondFiles = new File[1];
        secondFiles[0] = Mockito.spy(new File("seconedFile"));

        when(outputDirectoryMock.listFiles()).thenReturn(nonNullFiles);
        when(nonNullFiles[0].listFiles()).thenReturn(secondFiles);
        when(secondFiles[0].isDirectory()).thenReturn(true);



        // Run the test
        releaseMediaServiceImplUnderTest.handlerImportMedia(outputDirectoryMock, toProductQueryDto);

        // Verify the results
        assertTimeout(Duration.ofMillis(500), () -> {
            releaseMediaServiceImplUnderTest.handlerImportMedia(outputDirectoryMock,toProductQueryDto);  // 假设方法不会执行超过500ms
        });
    }


    @Test
    void testStoreReleaseMedia() throws Exception {
        // Setup
        final ReleaseMediaBean releaseMediaBean = new ReleaseMediaBean();

        releaseMediaBean.setSrcScriptUuidExist(false);
        final InfoVersion infoVersion = new InfoVersion();
        infoVersion.setId(0L);
        infoVersion.setInfoUniqueUuid("infoUniqueUuid");
        infoVersion.setSrcScriptUuid("srcScriptUuid");
        infoVersion.setEditState(0);
        infoVersion.setUseState(0);
        infoVersion.setIsDefault(1);
        infoVersion.setCreatorId(0L);
        infoVersion.setCreatorName("creatorName");
        releaseMediaBean.setInfoVersion(infoVersion);
        final InfoVersionText infoVersionText = new InfoVersionText();
        infoVersionText.setId(0L);
        infoVersionText.setSrcScriptUuid("srcScriptUuid");
        releaseMediaBean.setInfoVersionText(infoVersionText);
        final Parameter parameter = new Parameter();
        parameter.setId(0L);
        parameter.setParamCheckIid(0L);
        parameter.setScriptParameterManagerId(0L);
        releaseMediaBean.setParameters(Arrays.asList(parameter));
        final ParameterCheck parameterCheck = new ParameterCheck();
        parameterCheck.setId(0L);
        parameterCheck.setRuleName("ruleName");
        releaseMediaBean.setParameterCheckList(Arrays.asList(parameterCheck));
        final ParameterManager parameterManager = new ParameterManager();
        releaseMediaBean.setParameterManagerList(Arrays.asList(parameterManager));
        final Attachment attachment = new Attachment();
        attachment.setId(0L);
        attachment.setSrcScriptUuid("srcScriptUuid");
        attachment.setName("name");
        attachment.setSize(0L);
        attachment.setContents("content".getBytes());
        releaseMediaBean.setAttachmentList(Arrays.asList(attachment));
        releaseMediaBean.setCreatorId(0L);
        releaseMediaBean.setCreatorName("creatorName");
        final ScriptInfoDto infoDto = new ScriptInfoDto();
        final Category category = new Category();
        final CategoryApiDto categoryApiDto = new CategoryApiDto();
        category.setChildren(Arrays.asList(categoryApiDto));
        category.setId(1L);
        category.setParentId(0L);
        category.setName("name");
        category.setLevel(1);
        infoDto.setCategoryDto(new CategoryDto());
        infoDto.setId(0L);
        infoDto.setUniqueUuid("infoUniqueUuid");
        infoDto.setScriptNameZh("scriptNameZh");
        infoDto.setScriptName("scriptName");
        infoDto.setCategoryId(0L);
        infoDto.setCreatorId(0L);
        infoDto.setCreatorName("creatorName");
        infoDto.setCategoryPath("categoryName");
        releaseMediaBean.setInfoDto(infoDto);

        when(mockInfoService.validScriptNameZhCountExist("scriptNameZh")).thenReturn(true);

        // Configure InfoMapper.selectInfoList(...).
        final Info info = new Info();
        info.setId(0L);
        info.setUniqueUuid("infoUniqueUuid");
        info.setScriptNameZh("scriptNameZh");
        info.setCreatorId(0L);
        info.setCreatorName("creatorName");
        final List<Info> infos = Arrays.asList(info);
        when(mockInfoMapper.selectInfoList(any(Info.class))).thenReturn(infos);

        // Configure ICategoryService.findByLevelAndNameAndParentId(...).
        final CategoryDto categoryDto = new CategoryDto();
        final CategoryApiDto categoryApiDto1 = new CategoryApiDto();
        categoryApiDto1.setChildren(Arrays.asList(new CategoryApiDto()));
        categoryApiDto1.setId(1L);
        categoryApiDto1.setParentId(0L);
        categoryApiDto1.setLevel(1);
        categoryDto.setName("name");
        categoryDto.setChildren(Collections.singletonList(categoryApiDto1));
        categoryDto.setId(0L);
//        when(mockCategoryService.findByLevelAndNameAndParentId(1, "name", null)).thenReturn(new CategoryDto());
        when(mockCategoryService.findByLevelAndNameAndParentId(null,null , null)).thenReturn(new CategoryDto());

        when(mockParameterCheckService.validParamterCheckExist("ruleName")).thenReturn(false);

        // Configure IParameterCheckService.selectParameterCheckByName(...).
        final ParameterCheckDto parameterCheckDto = new ParameterCheckDto();
        parameterCheckDto.setId(0L);
        parameterCheckDto.setRuleName("ruleName");
        parameterCheckDto.setCheckRule("checkRule");
        parameterCheckDto.setRuleDes("ruleDes");
        parameterCheckDto.setCreatorId(0L);

        List<UserInfoApiDto> userInfoApiDtoList = new ArrayList<>();
        UserInfoApiDto userInfoApiDto = new UserInfoApiDto();
        userInfoApiDto.setId(1L);
        userInfoApiDto.setOrgCode("orgCode");
        userInfoApiDtoList.add(userInfoApiDto);

        when(mockUserInfoApi.queryUserInfoListByUserName(anyList())).thenReturn(userInfoApiDtoList);
        when(mockInfoVersionService.getInfoDefaultVersionByUniqueUuid(anyString())).thenReturn(infoVersion);
        // Run the test
        releaseMediaServiceImplUnderTest.storeReleaseMedia(releaseMediaBean,new ToProductQueryDto());

        // Verify the results
        verify(mockInfoMapper).updateInfo(any(Info.class));

        verify(mockInfoService).updateInfo(any(ScriptInfoDto.class));



        verify(mockInfoVersionService).insertInfoVersion(any(ScriptVersionDto.class));
        verify(mockInfoVersionService).updateInfoVersionDefaultValue(any(InfoVersion.class));
        verify(mockInfoVersionTextService).insertInfoVersionText(any(InfoVersionTextDto.class));
        verify(mockParameterCheckService).insertParameterCheck(any(ParameterCheckDto.class));
        verify(mockParameterService).insertParameter(any(ParameterDto.class));
    }

    @Test
    void testExportScriptProductionApi() throws Exception {
        // Setup
        List<String> srcScriptUuidList = new ArrayList<>();
        srcScriptUuidList.add("srcScriptUuid");

        List<Long> idList = new ArrayList<>();
        idList.add(1L);

        when(infoVersionMapper.getScriptIdsByUuids(srcScriptUuidList)).thenReturn(idList);

        File tempDir = Files.createTempDirectory("test").toFile();
        File tempFile = File.createTempFile("test", ".zip", tempDir);
        Files.write(tempFile.toPath(), "test content".getBytes());

        try (MockedStatic<FileUtils> mockedFileUtils = mockStatic(FileUtils.class);
             MockedStatic<ZipUtil> mockedZipUtil = mockStatic(ZipUtil.class)) {

            mockedFileUtils.when(() -> FileUtils.createFile(anyString())).thenReturn(tempDir);
            mockedFileUtils.when(() -> FileUtils.createZipFile(any(File.class))).thenReturn(tempFile);
            mockedZipUtil.when(() -> ZipUtil.readFileByte(any(File.class))).thenReturn("test content".getBytes());

            // Test normal case
            ScriptFileImportExportApiDto result = releaseMediaServiceImplUnderTest.exportScriptProductionApi(srcScriptUuidList);

            // Verify normal case
            assertNotNull(result);
            assertNull(result.getFileName());
            assertNull(result.getFileSuffix());
            assertNull(result.getFileContentByte());

            // Test exception case
            mockedZipUtil.when(() -> ZipUtil.readFileByte(any(File.class))).thenThrow(new IOException("Mock IO error"));
            ScriptFileImportExportApiDto exceptionResult = releaseMediaServiceImplUnderTest.exportScriptProductionApi(srcScriptUuidList);
            assertNotNull(exceptionResult);
        } finally {
            if (tempFile != null && tempFile.exists()) {
                tempFile.delete();
            }
            if (tempDir != null && tempDir.exists()) {
                tempDir.delete();
            }
        }
    }

    @Test
    void testExportScriptProductionApi_NullUuidList() {
        ScriptFileImportExportApiDto result = releaseMediaServiceImplUnderTest.exportScriptProductionApi(null);
        assertNotNull(result);
        verify(infoVersionMapper, never()).getScriptIdsByUuids(anyList());
    }

    @Test
    void importScriptProduction() {

        ScriptFileImportExportApiDto scriptFileImportExportApiDto = new ScriptFileImportExportApiDto();
        scriptFileImportExportApiDto.setFileContentByte(new byte[]{123});
        scriptFileImportExportApiDto.setFileName("服务投产_00");
        scriptFileImportExportApiDto.setFileSuffix("111");

        Map<String, String> stringStringMap = releaseMediaServiceImplUnderTest.importScriptProduction(scriptFileImportExportApiDto);

        assertNotNull(stringStringMap);
    }

    @Test
    void testCleanupFiles_InvalidZipFilePath() {
        // Setup
        File invalidZipFile = new File("/invalid/path/file.zip");
        File validSourceFolder = new File(System.getProperty("java.io.tmpdir"), "validFolder");

        // Mock FilePathValidator
        try (MockedStatic<FilePathValidator> mockedValidator = mockStatic(FilePathValidator.class)) {
            // Mock validator to return false for zip file path
            mockedValidator.when(() -> FilePathValidator.apply(invalidZipFile.getAbsolutePath())).thenReturn(false);
            mockedValidator.when(() -> FilePathValidator.apply(validSourceFolder.getAbsolutePath())).thenReturn(true);

            // Verify exception is thrown
            ScriptException exception = assertThrows(ScriptException.class, () ->
                    releaseMediaServiceImplUnderTest.cleanupFiles(invalidZipFile, validSourceFolder));

            assertFalse(exception.getMessage().contains("Invalid path for zip file"));
        }
    }

    @Test
    void testCleanupFiles_InvalidSourceFolderPath() {
        // Setup
        File validZipFile = new File(System.getProperty("java.io.tmpdir"), "valid.zip");
        File invalidSourceFolder = new File("/invalid/path/folder");

        // Mock FilePathValidator
        try (MockedStatic<FilePathValidator> mockedValidator = mockStatic(FilePathValidator.class)) {
            // Mock validator to return true for zip file but false for source folder
            mockedValidator.when(() -> FilePathValidator.apply(validZipFile.getAbsolutePath())).thenReturn(true);
            mockedValidator.when(() -> FilePathValidator.apply(invalidSourceFolder.getAbsolutePath())).thenReturn(false);

            // Verify exception is thrown
            ScriptException exception = assertThrows(ScriptException.class, () ->
                    releaseMediaServiceImplUnderTest.cleanupFiles(validZipFile, invalidSourceFolder));

            assertFalse(exception.getMessage().contains("Invalid path for source folder"));
        }
    }

    @Test
    void testCleanupFiles_DeleteError() {
        // Setup
        File zipFile = mock(File.class);
        File sourceFolder = mock(File.class);

        // Mock FilePathValidator
        try (MockedStatic<FilePathValidator> mockedValidator = mockStatic(FilePathValidator.class);
             MockedStatic<org.apache.commons.io.FileUtils> mockedFileUtils = mockStatic(org.apache.commons.io.FileUtils.class)) {

            // Mock validator to return true for both paths
            mockedValidator.when(() -> FilePathValidator.apply(any())).thenReturn(true);

            // Mock FileUtils to throw exception
            mockedFileUtils.when(() -> org.apache.commons.io.FileUtils.deleteQuietly(any(File.class)))
                    .thenThrow(new SecurityException("Mock delete error"));

            // Verify exception is thrown
            ScriptException exception = assertThrows(ScriptException.class, () ->
                    releaseMediaServiceImplUnderTest.cleanupFiles(zipFile, sourceFolder));

            assertEquals("error.clean.tempFile", exception.getMessage());
        }
    }

    @Test
    void testCleanupFiles_DeleteDirectoryError() {
        // Setup
        File zipFile = mock(File.class);
        File sourceFolder = mock(File.class);

        // Mock FilePathValidator
        try (MockedStatic<FilePathValidator> mockedValidator = mockStatic(FilePathValidator.class);
             MockedStatic<org.apache.commons.io.FileUtils> mockedFileUtils = mockStatic(org.apache.commons.io.FileUtils.class)) {

            // Mock validator to return true for both paths
            mockedValidator.when(() -> FilePathValidator.apply(any())).thenReturn(true);

            // Mock FileUtils.deleteQuietly to succeed but deleteDirectory to fail
            mockedFileUtils.when(() -> org.apache.commons.io.FileUtils.deleteQuietly(any(File.class))).thenReturn(true);
            mockedFileUtils.when(() -> org.apache.commons.io.FileUtils.deleteDirectory(any(File.class)))
                    .thenThrow(new IOException("Mock delete directory error"));

            // Verify exception is thrown
            ScriptException exception = assertThrows(ScriptException.class, () ->
                    releaseMediaServiceImplUnderTest.cleanupFiles(zipFile, sourceFolder));

            assertEquals("error.clean.tempFile", exception.getMessage());
        }
    }

    @Test
    void testHandleAttachmentsFolder() throws Exception {
        // 创建测试用的临时文件
        Path tempDir = Files.createTempDirectory("test_attachments");
        Path tempFile1 = Files.createTempFile(tempDir, "test1", ".txt");
        Path tempFile2 = Files.createTempFile(tempDir, "test2", ".txt");

        // 写入测试内容
        Files.write(tempFile1, "Test content 1".getBytes());
        Files.write(tempFile2, "Test content 2".getBytes());

        // 设置 ReleaseMediaBean
        ReleaseMediaBean releaseMediaBean = new ReleaseMediaBean();
        InfoVersion infoVersion = new InfoVersion();
        infoVersion.setSrcScriptUuid("test-uuid");
        releaseMediaBean.setInfoVersion(infoVersion);

        try {
            // 执行测试
            releaseMediaServiceImplUnderTest.handleAttachmentsFolder(tempDir.toFile(), releaseMediaBean);

            // 验证结果
            List<Attachment> attachments = releaseMediaBean.getAttachmentList();
            assertNotNull(attachments);
            assertEquals(2, attachments.size());

            // 验证第一个附件
            Attachment attachment1 = attachments.get(0);
            assertEquals(tempFile1.getFileName().toString(), attachment1.getName());
            assertEquals("test-uuid", attachment1.getSrcScriptUuid());
            assertArrayEquals("Test content 1".getBytes(), attachment1.getContents());

            // 验证第二个附件
            Attachment attachment2 = attachments.get(1);
            assertEquals(tempFile2.getFileName().toString(), attachment2.getName());
            assertEquals("test-uuid", attachment2.getSrcScriptUuid());
            assertArrayEquals("Test content 2".getBytes(), attachment2.getContents());

            // 验证 attachmentService 的 insertAttachment 方法被调用
            verify(mockAttachmentService, times(2)).insertAttachment(any(AttachmentDto.class));
        } finally {
            // 清理临时文件
            Files.deleteIfExists(tempFile1);
            Files.deleteIfExists(tempFile2);
            Files.deleteIfExists(tempDir);
        }
    }

    @Test
    void testHandleAttachmentsFolder_EmptyFolder() throws Exception {
        // 创建空文件夹
        Path tempDir = Files.createTempDirectory("test_empty_attachments");

        ReleaseMediaBean releaseMediaBean = new ReleaseMediaBean();
        InfoVersion infoVersion = new InfoVersion();
        infoVersion.setSrcScriptUuid("test-uuid");
        releaseMediaBean.setInfoVersion(infoVersion);

        try {
            // 执行测试
            releaseMediaServiceImplUnderTest.handleAttachmentsFolder(tempDir.toFile(), releaseMediaBean);

            // 验证结果
            assertNull(releaseMediaBean.getAttachmentList());

            // 验证 attachmentService 的 insertAttachment 方法没有被调用
            verify(mockAttachmentService, never()).insertAttachment(any(AttachmentDto.class));
        } finally {
            // 清理临时文件夹
            Files.deleteIfExists(tempDir);
        }
    }

    @Test
    void testHandleAttachmentsFolder_NullFiles() throws Exception {
        // 创建一个 mock File 对象，其 listFiles() 返回 null
        File mockFolder = mock(File.class);
        when(mockFolder.listFiles()).thenReturn(null);

        ReleaseMediaBean releaseMediaBean = new ReleaseMediaBean();
        InfoVersion infoVersion = new InfoVersion();
        infoVersion.setSrcScriptUuid("test-uuid");
        releaseMediaBean.setInfoVersion(infoVersion);

        // 执行测试
        releaseMediaServiceImplUnderTest.handleAttachmentsFolder(mockFolder, releaseMediaBean);

        // 验证结果
        assertNull(releaseMediaBean.getAttachmentList());

        // 验证 attachmentService 的 insertAttachment 方法没有被调用
        verify(mockAttachmentService, never()).insertAttachment(any(AttachmentDto.class));
    }

    @Test
    void testHandleAttachmentsFolder_FileProcessingError() throws Exception {
        // 创建一个会导致异常的 mock File 对象
        File mockFolder = mock(File.class);
        File mockFile = mock(File.class);
        when(mockFolder.listFiles()).thenReturn(new File[]{mockFile});
        when(mockFile.toPath()).thenThrow(new SecurityException("Access denied"));

        ReleaseMediaBean releaseMediaBean = new ReleaseMediaBean();
        InfoVersion infoVersion = new InfoVersion();
        infoVersion.setSrcScriptUuid("test-uuid");
        releaseMediaBean.setInfoVersion(infoVersion);

        // 验证是否抛出预期的异常
        assertThrows(ScriptException.class, () ->
                releaseMediaServiceImplUnderTest.handleAttachmentsFolder(mockFolder, releaseMediaBean)
        );
    }

    @Test
    void testHandleJsonFile() throws Exception {
        // 准备测试数据
        ToProductQueryDto toProductQueryDto = new ToProductQueryDto();
        toProductQueryDto.setUserName("testUser");
        toProductQueryDto.setSrcScriptUuidList(new ArrayList<>());

        // 创建测试用的JSON文件
        File jsonFile = File.createTempFile("test", ".json");
        String jsonContent = "{"
                + "\"infoDto\": {"
                + "  \"scriptNameZh\": \"测试脚本\","
                + "  \"scriptName\": \"test_script\","
                + "  \"creatorName\": \"testUser\","
                + "  \"creatorId\": 1,"
                + "  \"uniqueUuid\": \"test-unique-uuid\","
                + "  \"categoryDto\": {"
                + "    \"level\": 1,"
                + "    \"name\": \"测试分类\""
                + "  }"
                + "},"
                + "\"infoVersionText\": {"
                + "  \"content\": \"测试内容\""
                + "}"
                + "}";
        Files.write(jsonFile.toPath(), jsonContent.getBytes());

        // 创建将要返回的 ReleaseMediaBean
        ReleaseMediaBean mockReturnBean = new ReleaseMediaBean();
        ScriptInfoDto scriptInfoDto = new ScriptInfoDto();
        scriptInfoDto.setScriptNameZh("测试脚本");
        scriptInfoDto.setScriptName("test_script");
        scriptInfoDto.setCreatorName("testUser");
        scriptInfoDto.setCreatorId(1L);

        CategoryDto categoryDto = new CategoryDto();
        categoryDto.setLevel(1);
        categoryDto.setName("测试分类");
        scriptInfoDto.setCategoryDto(categoryDto);

        mockReturnBean.setInfoDto(scriptInfoDto);

        InfoVersion infoVersion = new InfoVersion();
        infoVersion.setSrcScriptUuid("test-uuid");
        infoVersion.setIsDefault(0);
        mockReturnBean.setInfoVersion(infoVersion);
        InfoVersionText infoVersionText = new InfoVersionText();

        mockReturnBean.setInfoVersionText(infoVersionText);
        mockReturnBean.setParameterCheckList(new ArrayList<ParameterCheck>());
        mockReturnBean.setParameters(new ArrayList<Parameter>());
        // Mock ObjectMapper
        when(objectMapper.readValue(any(Reader.class), eq(ReleaseMediaBean.class)))
                .thenReturn(mockReturnBean);

        // Mock 其他必要的服务调用
        when(mockInfoVersionService.validSrcScriptUuidExist("test-uuid")).thenReturn(false);
        when(mockInfoService.validScriptNameZhCountExist("测试脚本")).thenReturn(false);

        // Mock UserInfoApi 返回
        List<UserInfoApiDto> userInfoList = new ArrayList<>();
        UserInfoApiDto userInfoApiDto = new UserInfoApiDto();
        userInfoApiDto.setId(1L);
        userInfoApiDto.setFullName("testUser");
        userInfoList.add(userInfoApiDto);
        when(mockUserInfoApi.queryUserInfoListByUserName(anyList())).thenReturn(userInfoList);

        // Mock CategoryService 返回
        CategoryDto mockCategoryDto = new CategoryDto();
        mockCategoryDto.setId(1L);
        when(mockCategoryService.findByLevelAndNameAndParentId(1, "测试分类", null))
                .thenReturn(mockCategoryDto);

        try {
            // 执行测试
            ReleaseMediaBean result = releaseMediaServiceImplUnderTest.handleJsonFile(jsonFile, toProductQueryDto);

            // 验证结果
            assertNotNull(result);
            assertEquals("test-uuid", result.getInfoVersion().getSrcScriptUuid());

            // 验证方法调用
            verify(mockInfoVersionService).validSrcScriptUuidExist("test-uuid");
            verify(mockInfoService).validScriptNameZhCountExist("测试脚本");
            verify(mockUserInfoApi).queryUserInfoListByUserName(anyList());
            verify(mockCategoryService).findByLevelAndNameAndParentId(1, "测试分类", null);
        } finally {
            Files.deleteIfExists(jsonFile.toPath());
        }
    }

    @Test
    void testHandleJsonFile_InvalidJson() throws Exception {
        // 创建无效的JSON文件
        Path tempFile = Files.createTempFile("test", ".json");
        Files.write(tempFile, "invalid json content".getBytes());
        File jsonFile = tempFile.toFile();

        ToProductQueryDto toProductQueryDto = new ToProductQueryDto();
        toProductQueryDto.setUserName("testUser");
        toProductQueryDto.setScriptUserName("tsetUser");
        toProductQueryDto.setScriptUserId(1L);
        List<String> uuidList = new ArrayList<>();
        uuidList.add("test");
        toProductQueryDto.setSrcScriptUuidList(uuidList);

        try {
            // 验证是否抛出预期的异常
            assertThrows(ScriptException.class, () ->
                    releaseMediaServiceImplUnderTest.handleJsonFile(jsonFile, toProductQueryDto)
            );
        } finally {
            Files.deleteIfExists(tempFile);
        }
    }

    @Test
    @DisplayName("测试处理脚本文件夹 - 正常情况")
    void testHandleScriptFolder() throws Exception {
        // 创建测试目录和文件
        Path scriptDir = Files.createTempDirectory("script_folder");

        // 创建info.json文件
        Path jsonFile = scriptDir.resolve("info.json");
        Files.write(jsonFile, "{\"infoVersion\":{\"srcScriptUuid\":\"test-uuid\"}}".getBytes());

        // 准备测试数据
        ToProductQueryDto toProductQueryDto = new ToProductQueryDto();
        toProductQueryDto.setUserName("testUser");
        toProductQueryDto.setScriptUserName("tsetUser");
        toProductQueryDto.setScriptUserId(1L);
        List<String> uuidList = new ArrayList<>();
        uuidList.add("test");
        toProductQueryDto.setSrcScriptUuidList(uuidList);

        // Mock返回值
        ReleaseMediaBean mockBean = new ReleaseMediaBean();
        InfoVersion infoVersion = new InfoVersion();
        infoVersion.setSrcScriptUuid("test-uuid");
        mockBean.setInfoVersion(infoVersion);

        when(objectMapper.readValue(any(Reader.class), eq(ReleaseMediaBean.class)))
                .thenReturn(mockBean);

        try {
            // 使用反射调用私有方法
            Method handleScriptFolderMethod = ReleaseMediaServiceImpl.class.getDeclaredMethod(
                    "handleScriptFolder", File.class, ToProductQueryDto.class);
            handleScriptFolderMethod.setAccessible(true);
            handleScriptFolderMethod.invoke(releaseMediaServiceImplUnderTest, scriptDir.toFile(), toProductQueryDto);

            // 验证调用
            verify(objectMapper).readValue(any(Reader.class), eq(ReleaseMediaBean.class));
        } finally {
            // 清理
            Files.walk(scriptDir)
                    .sorted(Comparator.reverseOrder())
                    .map(Path::toFile)
                    .forEach(File::delete);
        }
    }

}

package com.ideal.script.service.impl.resulthandler;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ideal.script.common.constant.Constants;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.mapper.TaskInstanceMapper;
import com.ideal.script.mapper.TaskRuntimeStdoutMapper;
import com.ideal.script.model.dto.TaskRuntimeDto;
import com.ideal.script.service.ITaskInstanceService;
import com.ideal.script.service.ITaskRuntimeService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.redisson.api.RBucket;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;

import java.util.Collections;
import java.util.concurrent.TimeUnit;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ScriptResultHandlerServiceImplTest {

    @Mock
    private RedissonClient mockRedissonClient;
    @Mock
    private ITaskRuntimeService mockTaskRuntimeService;
    @Mock
    private ITaskInstanceService mockTaskInstanceService;
    @Mock
    private TaskRuntimeStdoutMapper taskRuntimeStdoutMapper;
    @Mock
    private ObjectMapper objectMapper;

    private ScriptResultHandlerServiceImpl scriptResultHandlerServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        scriptResultHandlerServiceImplUnderTest = new ScriptResultHandlerServiceImpl(mockRedissonClient,
                mockTaskRuntimeService, mockTaskInstanceService, taskRuntimeStdoutMapper,objectMapper);
    }

    @ParameterizedTest
    @ValueSource(strings = {"agent-script-retry-","agent-script-kill"})
    void testHandleScriptExecuteResult(String bizId) throws Exception {
        // Setup
        // Configure ITaskRuntimeService.selectTaskRuntimeById(...).
        final TaskRuntimeDto taskRuntimeDto = new TaskRuntimeDto();
        taskRuntimeDto.setAgentTaskId(0L);
        taskRuntimeDto.setId(0L);
        taskRuntimeDto.setTaskInstanceId(0L);
        taskRuntimeDto.setState(10);
        taskRuntimeDto.setBizId(bizId);



        when(mockTaskRuntimeService.selectTaskRuntimeById(any())).thenReturn(taskRuntimeDto);

        //when(mockTaskRuntimeService.getRunningAgentInstanceCount(0L)).thenReturn(0);
        String jsonMessage = "{\"scriptId\":\"123456\",\"stdout\":\"Script output goes here\",\"lastLine\":\"Last line of the output\",\"stderr\":\"Any error messages go here\",\"transcoding\":\"Completed\",\"status\":\"30\"}";

        JsonNode mockNode = mock(JsonNode.class);
        when(objectMapper.readTree(jsonMessage)).thenReturn(mockNode);
        when(mockNode.has(any())).thenReturn(true);

        JsonNode scriptIdNode = mock(JsonNode.class);
        when(mockNode.get("scriptId")).thenReturn(scriptIdNode);
        when(scriptIdNode.asText()).thenReturn("123456");

        JsonNode stdoutNode = mock(JsonNode.class);
        when(mockNode.get("stdout")).thenReturn(stdoutNode);
        when(stdoutNode.asText()).thenReturn("dGVzdCBvdXRwdXQ=");

        JsonNode stderrNode = mock(JsonNode.class);
        when(mockNode.get("stderr")).thenReturn(stderrNode);
        when(stderrNode.asText()).thenReturn("");

        JsonNode lastLineNode = mock(JsonNode.class);
        when(mockNode.get("lastLine")).thenReturn(lastLineNode);
        when(lastLineNode.asText()).thenReturn("bGFzdCBsaW5l");

        JsonNode statusNode = mock(JsonNode.class);
        when(mockNode.get("status")).thenReturn(statusNode);
        when(statusNode.asInt()).thenReturn(20);
        RBucket<Object> mockRBucket = mock(RBucket.class);
        when(mockRedissonClient.getBucket(Constants.CHECK_CONSUMPTION_SCRIPT_RUNTIME + 123456)).thenReturn(mockRBucket);
        when(mockRBucket.trySet("value",1,TimeUnit.HOURS)).thenReturn(true);

        TaskRuntimeDto taskRuntimeValDto = new TaskRuntimeDto();
        taskRuntimeValDto.setStartType(0);
        taskRuntimeValDto.setAgentPort(15000);
        taskRuntimeValDto.setAgentIp("127.0.0.1");
        taskRuntimeValDto.setScriptTaskId(1L);
        taskRuntimeValDto.setBizId("123456");
        taskRuntimeValDto.setState(1);
        when(mockTaskRuntimeService.selectTaskRuntimeById(any())).thenReturn(taskRuntimeValDto);
        // Run the test
        assertDoesNotThrow(() -> scriptResultHandlerServiceImplUnderTest.handleScriptExecuteResult(Collections.singletonList(jsonMessage)));
        // Verify the results
    }


    @Test
    void testHandleScriptExecuteResult_exception() throws Exception {
        // Setup
        // Configure ITaskRuntimeService.selectTaskRuntimeById(...).
        final TaskRuntimeDto taskRuntimeDto = new TaskRuntimeDto();
        taskRuntimeDto.setAgentTaskId(0L);
        taskRuntimeDto.setId(0L);
        taskRuntimeDto.setTaskInstanceId(0L);
        taskRuntimeDto.setState(10);
        taskRuntimeDto.setBizId("bizId");

        when(mockTaskRuntimeService.selectTaskRuntimeById(123456L)).thenReturn(taskRuntimeDto);

        doThrow(new ScriptException("update.task.state.error")).when(mockTaskInstanceService).allFinish(any());
        String jsonMessage = "{\"scriptId\":\"123456\",\"stdout\":\"Script output goes here\",\"lastLine\":\"Last line of the output\",\"stderr\":\"Any error messages go here\",\"transcoding\":\"Completed\",\"status\":\"20\"}";
        // 模拟 ObjectMapper 的行为
        JsonNode mockNode = mock(JsonNode.class);
        when(objectMapper.readTree(jsonMessage)).thenReturn(mockNode);

        // Mock node behavior
        when(mockNode.has(anyString())).thenReturn(true);

        JsonNode scriptIdNode = mock(JsonNode.class);
        when(mockNode.get("scriptId")).thenReturn(scriptIdNode);
        when(scriptIdNode.asText()).thenReturn("123456");

        JsonNode stdoutNode = mock(JsonNode.class);
        when(mockNode.get("stdout")).thenReturn(stdoutNode);
        when(stdoutNode.asText()).thenReturn("dGVzdCBvdXRwdXQ=");

        JsonNode stderrNode = mock(JsonNode.class);
        when(mockNode.get("stderr")).thenReturn(stderrNode);
        when(stderrNode.asText()).thenReturn("");

        JsonNode lastLineNode = mock(JsonNode.class);
        when(mockNode.get("lastLine")).thenReturn(lastLineNode);
        when(lastLineNode.asText()).thenReturn("bGFzdCBsaW5l");

        JsonNode statusNode = mock(JsonNode.class);
        when(mockNode.get("status")).thenReturn(statusNode);
        when(statusNode.asInt()).thenReturn(20);
        RBucket<Object> mockRBucket = mock(RBucket.class);
        when(mockRedissonClient.getBucket(Constants.CHECK_CONSUMPTION_SCRIPT_RUNTIME + 123456)).thenReturn(mockRBucket);
        when(mockRBucket.trySet("value",1,TimeUnit.HOURS)).thenReturn(true);

        // Run the test
        assertThatThrownBy(() -> scriptResultHandlerServiceImplUnderTest.handleScriptExecuteResult(Collections.singletonList(jsonMessage)))
                .isInstanceOf(ScriptException.class);

        // Verify the results
    }


    @Test
    void testValidateScriptResultJson_ThrowsScriptException() {
        // Setup
        // Run the test
        assertThatThrownBy(
                () -> scriptResultHandlerServiceImplUnderTest.validateScriptResultJson("jsonMessage"))
                .isInstanceOf(ScriptException.class);
    }

    @Test
    void testHandleScriptSendResult() throws Exception {
        // Setup
        String validJson = "{\"bizId\": \"123-456\", \"agentIp\": \"127.0.0.1\", \"agentPort\": 8080, \"taskId\": \"789012\", \"send\": true}";
        // Run the test
        RLock lockMock = Mockito.mock(RLock.class);
        Mockito.when(lockMock.tryLock(Mockito.anyLong(), Mockito.anyLong(), Mockito.any(TimeUnit.class))).thenReturn(true);
        when(mockRedissonClient.getLock(any(String.class))).thenReturn(lockMock);

        // 模拟 ObjectMapper 的行为
        JsonNode mockNode = mock(JsonNode.class);
        when(objectMapper.readTree(validJson)).thenReturn(mockNode);

        // Mock node behavior
        when(mockNode.has(anyString())).thenReturn(true);

        JsonNode bizIdNode = mock(JsonNode.class);
        when(mockNode.get("bizId")).thenReturn(bizIdNode);
        when(bizIdNode.asText()).thenReturn("123-456");
        //        when(mockNode.get("scriptId").asText()).thenReturn("123456");
        when(mockNode.get("agentIp")).thenReturn(mockNode);
        when(mockNode.get("agentIp").asText()).thenReturn("127.0.0.1");
        when(mockNode.get("agentPort")).thenReturn(mockNode);
        when(mockNode.get("agentPort").asText()).thenReturn("8080");
        when(mockNode.get("taskId")).thenReturn(mockNode);
        when(mockNode.get("taskId").asText()).thenReturn("789012");
        when(mockNode.get("send")).thenReturn(mockNode);
        when(mockNode.get("send").asText()).thenReturn("true");


        TaskRuntimeDto taskRuntimeDto = new TaskRuntimeDto();
        taskRuntimeDto.setStartType(0);
        taskRuntimeDto.setAgentPort(15000);
        taskRuntimeDto.setAgentIp("127.0.0.1");
        taskRuntimeDto.setScriptTaskId(1L);
        when(mockTaskRuntimeService.selectTaskRuntimeById(any())).thenReturn(taskRuntimeDto);

        scriptResultHandlerServiceImplUnderTest.handleScriptSendResult(validJson);

        // Verify the results
        verify(mockTaskRuntimeService).updateTaskRuntime(any(TaskRuntimeDto.class));
    }

    @Test
    void testValidateScriptSendResultJson() throws Exception {
        // Setup
        // Run the test
        String validJson = "{\"bizId\": \"123456\", \"agentIp\": \"127.0.0.1\", \"agentPort\": 8080, \"taskId\": \"789012\", \"send\": true}";

        // 模拟 ObjectMapper 的行为
        JsonNode mockNode = mock(JsonNode.class);
        when(objectMapper.readTree(validJson)).thenReturn(mockNode);
         when(mockNode.has(anyString())).thenReturn(true);
        scriptResultHandlerServiceImplUnderTest.validateScriptSendResultJson(validJson);

        // Verify the results
    }

    @Test
    void testValidateScriptSendResultJson_ThrowsScriptException() {
        // Setup
        String validJson = "{\"agentIp\": \"127.0.0.1\", \"agentPort\": 8080, \"taskId\": \"789012\", \"send\": true}";

        // Run the test
        assertThrows(ScriptException.class,() ->{
            scriptResultHandlerServiceImplUnderTest.validateScriptSendResultJson(validJson);
        });
    }

    @Test
    void testExtractNumber() throws Exception {
        // Setup
        // Run the test
        final long result = scriptResultHandlerServiceImplUnderTest.extractNumber("1-1");

        // Verify the results
        assertThat(result).isEqualTo(1L);
    }

    @Test
    void testExtractNumber_ThrowsScriptException() {
        // Setup
        // Run the test
        assertThatThrownBy(() -> scriptResultHandlerServiceImplUnderTest.extractNumber(""))
                .isInstanceOf(ScriptException.class);
    }

    @Test
    void testExtractNumber_partsIsZero() throws Exception {
        // Setup
        // Run the test
        assertThatThrownBy(() -> scriptResultHandlerServiceImplUnderTest.extractNumber("-"))
                .isInstanceOf(ScriptException.class);
    }

    @Test
    void testBizIdValid() throws Exception {
        // Setup
        // Run the test
        assertDoesNotThrow(() -> scriptResultHandlerServiceImplUnderTest.bizIdValid("bizId"));

        // Verify the results
    }

    @Test
    void testBizIdValid_ThrowsScriptException() {
        // Setup
        // Run the test
        assertThatThrownBy(() -> scriptResultHandlerServiceImplUnderTest.bizIdValid(""))
                .isInstanceOf(ScriptException.class);
    }

    @Test
    void testHandleScriptErrorResult() throws Exception {
        // Setup
        // Configure ITaskRuntimeService.selectTaskRuntimeById(...).
        final TaskRuntimeDto taskRuntimeDto = new TaskRuntimeDto();
        taskRuntimeDto.setAgentTaskId(0L);
        taskRuntimeDto.setId(0L);
        taskRuntimeDto.setTaskInstanceId(0L);
        taskRuntimeDto.setState(10);
        taskRuntimeDto.setBizId("1");
        taskRuntimeDto.setStartType(0);
        taskRuntimeDto.setAgentPort(15000);
        taskRuntimeDto.setAgentIp("127.0.0.1");
        taskRuntimeDto.setScriptTaskId(11L);
        when(mockTaskRuntimeService.selectTaskRuntimeById(any())).thenReturn(taskRuntimeDto);

        TaskRuntimeDto taskRuntimeValDto = new TaskRuntimeDto();
        taskRuntimeValDto.setStartType(0);
        taskRuntimeValDto.setAgentPort(15000);
        taskRuntimeValDto.setAgentIp("127.0.0.1");
        taskRuntimeValDto.setScriptTaskId(1L);
        when(mockTaskRuntimeService.selectTaskRuntimeById(any())).thenReturn(taskRuntimeValDto);

        RLock lockMock = Mockito.mock(RLock.class);
        Mockito.when(lockMock.tryLock(Mockito.anyLong(), Mockito.anyLong(), Mockito.any(TimeUnit.class))).thenReturn(true);
        when(mockRedissonClient.getLock(any(String.class))).thenReturn(lockMock);

        String expectedValue = "mockedValueFromRedis"; // 期望从Bucket中获取的值
        RBucket<String> bucketMock = Mockito.mock(RBucket.class);
        // Run the test
        assertDoesNotThrow(() -> scriptResultHandlerServiceImplUnderTest.handleScriptErrorResult("message", "1-1", 0L));

    }

   /* @Test
    void testHandleScriptErrorResult_bucketValueIsNull() throws Exception {
        // Setup
        // Configure ITaskRuntimeService.selectTaskRuntimeById(...).
        final TaskRuntimeDto taskRuntimeDto = new TaskRuntimeDto();
        taskRuntimeDto.setAgentTaskId(0L);
        taskRuntimeDto.setId(0L);
        taskRuntimeDto.setTaskInstanceId(0L);
        taskRuntimeDto.setState(0);
        taskRuntimeDto.setBizId("1");
        when(mockTaskRuntimeService.selectTaskRuntimeById(1L)).thenReturn(taskRuntimeDto);


        RLock lockMock = Mockito.mock(RLock.class);
        Mockito.when(lockMock.tryLock(Mockito.anyLong(), Mockito.anyLong(), Mockito.any(TimeUnit.class))).thenReturn(true);
        when(mockRedissonClient.getLock(any(String.class))).thenReturn(lockMock);

        RBucket<String> bucketMock = Mockito.mock(RBucket.class);
        when(bucketMock.get()).thenReturn(null); // 模拟RBucket.get()的结果
        when(mockRedissonClient.getBucket(any(String.class))).thenAnswer(invocation -> bucketMock);
        // Run the test
        scriptResultHandlerServiceImplUnderTest.handleScriptErrorResult("message", "1-1", 0L);
    }*/



    @Test
    void testHandleScriptErrorResult_message() throws Exception {
        // Setup
        // Configure ITaskRuntimeService.selectTaskRuntimeById(...).
        final TaskRuntimeDto taskRuntimeDto = new TaskRuntimeDto();
        taskRuntimeDto.setAgentTaskId(0L);
        taskRuntimeDto.setId(0L);
        taskRuntimeDto.setTaskInstanceId(0L);
        taskRuntimeDto.setState(10);
        taskRuntimeDto.setBizId("1");
        taskRuntimeDto.setStartType(0);
        taskRuntimeDto.setAgentPort(15000);
        taskRuntimeDto.setAgentIp("127.0.0.1");
        taskRuntimeDto.setScriptTaskId(1L);

        when(mockTaskRuntimeService.selectTaskRuntimeById(1L)).thenReturn(taskRuntimeDto);


        RLock lockMock = Mockito.mock(RLock.class);
        Mockito.when(lockMock.tryLock(Mockito.anyLong(), Mockito.anyLong(), Mockito.any(TimeUnit.class))).thenReturn(true);
        when(mockRedissonClient.getLock(any(String.class))).thenReturn(lockMock);

        RBucket<String> bucketMock = Mockito.mock(RBucket.class);
        // Run the test
        assertDoesNotThrow(() -> scriptResultHandlerServiceImplUnderTest.handleScriptErrorResult("agent发送异常", "1-1", 0L));
    }

    @Test
    void testUpdateData() throws Exception {
        // Setup
        // Configure ITaskRuntimeService.selectTaskRuntimeById(...).
        final TaskRuntimeDto taskRuntimeDto = new TaskRuntimeDto();
        taskRuntimeDto.setAgentTaskId(0L);
        taskRuntimeDto.setId(0L);
        taskRuntimeDto.setTaskInstanceId(0L);
        taskRuntimeDto.setState(0);
        taskRuntimeDto.setBizId("1");
        when(mockTaskRuntimeService.selectTaskRuntimeById(1L)).thenReturn(taskRuntimeDto);

        // Run the test
        scriptResultHandlerServiceImplUnderTest.updateData("1", 0);

        // Verify the results
        verify(mockTaskRuntimeService).updateExecTimeAndState(eq("1"), eq(0), any(TaskRuntimeDto.class));
    }

    @Test
    void testUpdateData_exception() throws Exception {
        // Setup
        // Configure ITaskRuntimeService.selectTaskRuntimeById(...).
        final TaskRuntimeDto taskRuntimeDto = new TaskRuntimeDto();
        taskRuntimeDto.setAgentTaskId(0L);

        taskRuntimeDto.setTaskInstanceId(0L);
        taskRuntimeDto.setState(0);
        taskRuntimeDto.setBizId("1");
        when(mockTaskRuntimeService.selectTaskRuntimeById(1L)).thenReturn(taskRuntimeDto);

        // Run the test
        assertThrows(ScriptException.class,() ->{
            scriptResultHandlerServiceImplUnderTest.updateData("1", 0);
        });

    }
}

package com.ideal.script.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.ideal.common.util.batch.BatchHandler;
import com.ideal.common.util.spring.MessageUtil;
import com.ideal.sc.util.CurrentUserUtil;
import com.ideal.script.dto.CategoryApiDto;
import com.ideal.script.dto.CategoryDto;
import com.ideal.script.dto.ScriptInfoQueryDto;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.mapper.CategoryMapper;
import com.ideal.script.mapper.InfoMapper;
import com.ideal.script.model.bean.*;
import com.ideal.script.model.dto.CategoryOrgDto;
import com.ideal.script.model.dto.CategoryRoleDto;
import com.ideal.script.model.dto.CategoryUserDto;
import com.ideal.script.model.entity.Category;
import com.ideal.script.model.entity.Info;
import com.ideal.script.service.IMyScriptService;
import com.ideal.script.service.IScriptVersionShareService;
import com.ideal.system.api.IOrgManagement;
import com.ideal.system.api.IRole;
import com.ideal.system.api.IUserInfo;
import com.ideal.system.common.component.model.CurrentUser;
import com.ideal.system.dto.OrgManagementApiDto;
import com.ideal.system.dto.PermissionUserInfoApiDto;
import com.ideal.system.dto.RoleApiDto;
import com.ideal.system.dto.ServicePermissionApiQueryDto;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.jupiter.params.provider.NullSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.function.Consumer;

import static java.util.Arrays.asList;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class CategoryServiceImplTest {

    @Mock
    private CategoryMapper mockCategoryMapper;
    @Mock
    private InfoMapper mockInfoMapper;
    @Mock
    private BatchHandler batchHandler;
    @Mock
    private IUserInfo mockUserInfoApi;
    @Mock
    private IOrgManagement mockOrgManagementApi;
    @Mock
    private IRole mockIRoleApi;
    @Mock
    private IScriptVersionShareService mockScriptVersionShareService;
    @Mock
    private IMyScriptService myScriptService;
    
    @Spy
    @InjectMocks
    private CategoryServiceImpl categoryServiceImplUnderTest;

    @Test
    void testSelectCategoryById() {
        final Category category = new Category();
        category.setChildren(Collections.singletonList(new CategoryApiDto()));
        category.setId(1L);
        category.setParentId(1L);
        category.setName("cellValue");
        category.setLevel(1);
        when(mockCategoryMapper.selectCategoryById(0L)).thenReturn(category);

        // Run the test
        final CategoryDto result = categoryServiceImplUnderTest.selectCategoryById(0L);
        //方法被调用一次
        Mockito.verify(mockCategoryMapper,Mockito.times(1)).selectCategoryById(0L);
        //断言不为空
        assertThat(result).isNotNull();
    }

    @Test
    void selectCategoryList() {
        CategoryDto categoryDto = new CategoryDto();
        Page<Category> page = new Page<>();
        when(mockCategoryMapper.selectCategoryList(any(Category.class))).thenReturn(page);

        PageInfo<CategoryDto> resultPage = categoryServiceImplUnderTest.selectCategoryList(categoryDto, 1, 10);
        assertNotNull(resultPage);
    }

    @Test
    void selectCategoryList_hasName() {
        CategoryDto categoryDto = new CategoryDto();
        categoryDto.setName("分类");


        Page<Category> page = new Page<>();
        Category category = new Category();
        category.setName("一级分类");
        category.setLevel(1);
        category.setId(1L);

        Category category1 = new Category();
        category1.setName("二级分类");
        category1.setParentId(1L);
        category1.setLevel(2);
        category1.setId(2L);

        Category category2 = new Category();
        category2.setName("二级分类1");
        category2.setParentId(1L);
        category2.setLevel(2);
        category2.setId(3L);

        Category category3 = new Category();
        category3.setName("三级分类");
        category3.setParentId(2L);
        category3.setLevel(3);
        category3.setId(4L);

        Category category5 = new Category();
        category5.setName("一级分类1");
        category5.setLevel(1);
        category5.setId(6L);


        Category category6 = new Category();
        category6.setName("三级分类");
        category6.setParentId(2L);
        category6.setLevel(3);
        category6.setId(4L);

        page.add(category1);
        page.add(category2);
        page.add(category3);
        page.add(category5);
        page.add(category);
        page.add(category6);

        Category category4 = new Category();
        category4.setName("二级分类3");
        category4.setParentId(1L);
        category4.setLevel(2);
        category4.setId(5L);

        when(mockCategoryMapper.selectCategoryById(2L)).thenReturn(category1);
        when(mockCategoryMapper.selectCategoryById(1L)).thenReturn(category);
        when(mockCategoryMapper.selectCategoryList(any(Category.class))).thenReturn(
                page,new ArrayList<>(),new ArrayList<>(),new ArrayList<>(),new ArrayList<>(),new ArrayList<>(),new ArrayList<>(),
                page,new ArrayList<>(),new ArrayList<>(),new ArrayList<>(),new ArrayList<>(),new ArrayList<>(),new ArrayList<>());

        PageInfo<CategoryDto> resultPage = categoryServiceImplUnderTest.selectCategoryList(categoryDto, 2, 10);
        assertNotNull(resultPage);
    }

    @Test
    void getCategoryWithChildren() {

        Category category = new Category();
        category.setId(1L);
        List<Category> childCategories = new ArrayList<>();
        childCategories.add(new Category());
        List<Category> childCategories1 = new ArrayList<>();
        childCategories1.add(new Category());
        when(mockCategoryMapper.selectCategoryList(any(Category.class))).thenReturn(childCategories,childCategories1,new ArrayList<>());
        List<Category> result = categoryServiceImplUnderTest.getCategoryWithChildren(category);
        assertNotNull(result);
    }

    @ParameterizedTest
    @NullSource
    @ValueSource(longs = {1L})
    void testInsertCategory(Long id) throws ScriptException {
        // Setup
        final CategoryDto categoryDto = new CategoryDto();
        categoryDto.setChildren(Collections.singletonList(new CategoryApiDto()));
        categoryDto.setId(id);
        categoryDto.setName("1-1");
        categoryDto.setCode("code");
        categoryDto.setLevel(1);

        Category category =  new Category();
        List<Category> categories = new ArrayList<>();
        category.setName("1-2");
        categories.add(category);


        when(mockCategoryMapper.insertCategory(any(Category.class))).thenReturn(1);
        when(mockCategoryMapper.selectCategoryList(any(Category.class))).thenReturn(categories);

        // Run the test
        final int result = categoryServiceImplUnderTest.insertCategory(categoryDto);

        // Verify the results
        Mockito.verify(mockCategoryMapper,times(1)).insertCategory(any(Category.class));
        assertThat(result).isEqualTo(1);
    }

    @Test
    void testInsertCategory_checkHasRootCategoryName_exception()  {
        // Setup
        final CategoryDto categoryDto = new CategoryDto();
        categoryDto.setChildren(Collections.singletonList(new CategoryApiDto()));
        categoryDto.setName("1-1");
        categoryDto.setCode("code");
        categoryDto.setLevel(1);

        Category category =  new Category();
        List<Category> categories = new ArrayList<>();
        category.setName("1-1");
        categories.add(category);


        when(mockCategoryMapper.selectCategoryList(any(Category.class))).thenReturn(categories);

        // Run the test
        assertThrows(ScriptException.class,()->{
            categoryServiceImplUnderTest.insertCategory(categoryDto);
        });

    }


    @Test
    void testInsertCategory_hasName() throws ScriptException {
        // Setup
        final CategoryDto categoryDto = new CategoryDto();

        categoryDto.setChildren(new ArrayList<>());
        categoryDto.setId(1L);
        categoryDto.setName("1-1");
        categoryDto.setCode("code");
        categoryDto.setLevel(1);

        Category category =  new Category();
        category.setName("1-1");
        category.setLevel(1);
        List<Category> categories = new ArrayList<>();
        categories.add(category);


        when(mockCategoryMapper.selectCategoryList(any(Category.class))).thenReturn(categories);

        assertThrows(ScriptException.class, ()->{
            categoryServiceImplUnderTest.insertCategory(categoryDto);
        });
        // Run the test
    }


    @ParameterizedTest
    @ValueSource(strings = {"一级/二级_","一级/二级_/三级"})
    void testUpdateCategory(String categoryPath) throws ScriptException {
        // Setup
        final CategoryDto categoryDto = new CategoryDto();
        categoryDto.setChildren(Collections.singletonList(new CategoryApiDto()));
        categoryDto.setId(2L);
        categoryDto.setName("假的二级分类");
        categoryDto.setCode("code");
        categoryDto.setLevel(2);


        List<Info> infoList = new ArrayList<>();
        Info info = new Info();
        info.setId(1L);
        info.setCategoryPath(categoryPath);
        infoList.add(info);

        //假设的第一次返回
        List<Category> categoryList = new ArrayList<>();
        Category category = new Category();
        category.setName("一级");
        category.setId(1L);
        categoryList.add(category);

        //假设的第二次返回
        List<Category> categoryList1 = new ArrayList<>();
        Category category1 = new Category();
        category1.setName("二级_");
        category1.setId(2L);
        category1.setParentId(1L);
        categoryList1.add(category1);

        when(mockCategoryMapper.selectCategoryList(any())).thenReturn(categoryList1).thenReturn(categoryList1).thenReturn(categoryList).thenReturn(new ArrayList<>());
        when(mockInfoMapper.selectInfoList(any())).thenReturn(infoList);
        when(mockCategoryMapper.updateCategory(any(Category.class))).thenReturn(1);

        // Run the test
        final int result = categoryServiceImplUnderTest.updateCategory(categoryDto);

        // Verify the results
        Mockito.verify(mockCategoryMapper,times(1)).updateCategory(any(Category.class));
        assertThat(result).isEqualTo(1);
    }


    @Test
    void testUpdateCategory_categoryPath_error() throws ScriptException {
        // Setup
        final CategoryDto categoryDto = new CategoryDto();
        categoryDto.setChildren(Collections.singletonList(new CategoryApiDto()));
        categoryDto.setId(2L);
        categoryDto.setName("假的二级分类");
        categoryDto.setCode("code");
        categoryDto.setLevel(2);


        List<Info> infoList = new ArrayList<>();
        Info info = new Info();
        info.setId(1L);
        info.setCategoryPath("一级/二级_/三级");
        infoList.add(info);

        //假设的第一次返回
        List<Category> categoryList = new ArrayList<>();
        Category category = new Category();
        category.setName("一级");
        category.setId(1L);
        categoryList.add(category);

        //假设的第二次返回
        List<Category> categoryList1 = new ArrayList<>();
        Category category1 = new Category();
        category1.setName("三级_");
        category1.setId(2L);
        category1.setParentId(1L);
        categoryList1.add(category1);

        when(mockCategoryMapper.selectCategoryList(any())).thenReturn(categoryList1).thenReturn(categoryList1).thenReturn(categoryList).thenReturn(new ArrayList<>());
        when(mockInfoMapper.selectInfoList(any())).thenReturn(infoList);
        when(mockCategoryMapper.updateCategory(any(Category.class))).thenReturn(1);

        // Run the test
        //这种情况应该不可能发生，因为数据都是从库里查出来的，所以该单元测试只做错误验证，满足覆盖率
        final int result = categoryServiceImplUnderTest.updateCategory(categoryDto);

        // Verify the results
        Mockito.verify(mockCategoryMapper,times(1)).updateCategory(any(Category.class));
        assertThat(result).isEqualTo(1);
    }







    @Test
    void testUpdateCategory_categoryPath_isNull() throws ScriptException {
        // Setup
        final CategoryDto categoryDto = new CategoryDto();
        categoryDto.setChildren(Collections.singletonList(new CategoryApiDto()));
        categoryDto.setId(1L);
        categoryDto.setName("一级");
        categoryDto.setCode("code");
        categoryDto.setLevel(1);
        when(mockCategoryMapper.selectCategoryList(any())).thenReturn(new ArrayList<>());
        when(mockCategoryMapper.updateCategory(any(Category.class))).thenReturn(1);
        // Run the test
        final int result = categoryServiceImplUnderTest.updateCategory(categoryDto);
        // Verify the results
        Mockito.verify(mockCategoryMapper,times(1)).updateCategory(any(Category.class));
        assertThat(result).isEqualTo(1);
    }



    @Test
    void testDeleteCategoryByIds() throws ScriptException {
        // Setup
        List<Long> childIdList = new ArrayList<>();
        childIdList.add(2L);
        childIdList.add(3L);
        when(mockCategoryMapper.selectChildCategoryIdList(1L)).thenReturn(childIdList);

        when(mockCategoryMapper.deleteCategoryByIds(any(Long[].class))).thenReturn(3);
        when(mockCategoryMapper.checkIdsExist(any(Long[].class))).thenReturn(Collections.singletonList(1L));
        when(mockCategoryMapper.getCategoryReferencedCount(any(List.class))).thenReturn(0);

        // Run the test
        final int result = categoryServiceImplUnderTest.deleteCategoryByIds(new Long[]{1L});
        // Verify the results
        Mockito.verify(mockCategoryMapper,times(1)).deleteCategoryByIds(any(Long[].class));
        assertThat(result).isEqualTo(3);
    }

    @Test
    void testDeleteCategoryByIds_CategoryReferenced() throws ScriptException {
        // Setup
        MockedStatic<MessageUtil> messageUtilMockedStatic = mockStatic(MessageUtil.class);

        when(mockCategoryMapper.checkIdsExist(any(Long[].class))).thenReturn(Collections.singletonList(1L));

        when(mockCategoryMapper.getCategoryReferencedCount(any(List.class))).thenReturn(2);
        messageUtilMockedStatic.when(() -> MessageUtil.message(anyString())).thenReturn("category.delete.referenced.by.scripts");
        // Run the test
       assertThrows(ScriptException.class,()->{
           categoryServiceImplUnderTest.deleteCategoryByIds(new Long[]{1L});
       });
        messageUtilMockedStatic.close();
    }
    
    @Test
    void testDeleteCategoryByIds_AllIdsNotExist() {
        // Setup - 模拟所有ID都不存在的情况
        when(mockCategoryMapper.checkIdsExist(any(Long[].class))).thenReturn(Collections.emptyList());
        
        // Run the test and assert exception
        ScriptException exception = assertThrows(ScriptException.class, () -> {
            categoryServiceImplUnderTest.deleteCategoryByIds(new Long[]{1L, 2L});
        });
        
        // 验证异常信息包含正确的关键字
        assertEquals("category.not.exist", exception.getMessage());
    }
    
    @Test
    void testDeleteCategoryByIds_PartialIdsNotExist() {
        // Setup - 模拟部分ID不存在的情况
        MockedStatic<MessageUtil> messageUtilMockedStatic = mockStatic(MessageUtil.class);
        
        // 只有ID 1存在，ID 2和3不存在
        when(mockCategoryMapper.checkIdsExist(any(Long[].class))).thenReturn(Collections.singletonList(1L));
        messageUtilMockedStatic.when(() -> MessageUtil.message("category.not.exist")).thenReturn("分类不存在");
        
        // 使用实际的IDs数组，以便测试Arrays.stream().filter().map().collect()逻辑
        Long[] ids = new Long[]{1L, 2L, 3L};
        
        // Run the test and assert exception
        ScriptException exception = assertThrows(ScriptException.class, () -> {
            categoryServiceImplUnderTest.deleteCategoryByIds(ids);
        });
        
        // 验证异常信息包含正确的关键字和不存在的ID
        assertTrue(exception.getMessage().contains("分类不存在"));
        assertTrue(exception.getMessage().contains("对应的id为:"));
        // 验证不存在的ID是否都包含在异常信息中
        assertTrue(exception.getMessage().contains("2"));
        assertTrue(exception.getMessage().contains("3"));
        
        messageUtilMockedStatic.close();
    }
    
    @Test
    void testDeleteCategoryByIds_DBErrorOnCategoryReferencedCount() {
        // Setup - 模拟数据库查询异常
        when(mockCategoryMapper.checkIdsExist(any(Long[].class))).thenReturn(Collections.singletonList(1L));
        when(mockCategoryMapper.getCategoryReferencedCount(any(List.class))).thenThrow(new RuntimeException("Database error"));
        
        // Run the test and assert exception
        ScriptException exception = assertThrows(ScriptException.class, () -> {
            categoryServiceImplUnderTest.deleteCategoryByIds(new Long[]{1L});
        });
        
        // 验证异常信息
        assertEquals("getCategoryReferencedCount fail！", exception.getMessage());
    }


    @Test
    void testAddCategoryAndChildrenToSet() {
        // Setup
        Set<Long> removeSet = new HashSet<>();
        //removeSet.add(1L); // Add some initial data to the set
        when(mockCategoryMapper.selectChildCategoryIdList(1L)).thenReturn(asList(2L, 3L),new ArrayList<>(),new ArrayList<>()); // Mock the behavior of categoryMapper

        // Run the method being tested
        categoryServiceImplUnderTest.addCategoryAndChildrenToSet(1L, removeSet);

        // Verify the results
        assertTrue(removeSet.contains(1L)); // The original ID should still be present in the set
        assertTrue(removeSet.contains(2L)); // The child ID 2 should have been added to the set
        assertTrue(removeSet.contains(3L)); // The child ID 3 should have been added to the set
    }
    @Test
    void testDeleteCategoryById() {
        // Setup
        when(mockCategoryMapper.deleteCategoryById(1L)).thenReturn(1);

        // Run the test
        final int result = categoryServiceImplUnderTest.deleteCategoryById(1L);

        // Verify the results
        Mockito.verify(mockCategoryMapper,times(1)).deleteCategoryById(1L);
        assertThat(result).isEqualTo(1);
    }

    @Test
    void testListFirstCategory() {
        // Setup
        // Configure CategoryMapper.selectCategoryList(...).
        final Category category = new Category();
        category.setChildren(Collections.singletonList(new CategoryApiDto()));
        category.setId(1L);
        category.setName("cellValue");
        category.setLevel(1);
        final List<Category> categories = Collections.singletonList(category);
        when(mockCategoryMapper.selectCategoryList(any(Category.class))).thenReturn(categories);

        // Run the test
        final List<CategoryDto> result = categoryServiceImplUnderTest.listFirstCategory();
        System.out.println(result);
        // Verify the results
        Mockito.verify(mockCategoryMapper,times(1)).selectCategoryList(any(Category.class));
    }


    @Test
    void testListNextCategory() {
        // Setup
        // Configure CategoryMapper.selectCategoryList(...).
        final Category category = new Category();
        category.setChildren(Collections.singletonList(new CategoryApiDto()));
        category.setId(2L);
        category.setParentId(1L);
        category.setName("cellValue");
        category.setLevel(2);
        final List<Category> categories = Collections.singletonList(category);
        when(mockCategoryMapper.selectCategoryList(any(Category.class))).thenReturn(categories);

        // Run the test
        final List<CategoryDto> result = categoryServiceImplUnderTest.listNextCategory(1L);

        System.out.println(result);
        Mockito.verify(mockCategoryMapper,times(1)).selectCategoryList(any(Category.class));
    }


    @Test
    void testSelectCategoryByIds() {
        // Setup
        // Configure CategoryMapper.selectCategoryByIds(...).
        final Category category = new Category();
        category.setChildren(Collections.singletonList(new CategoryApiDto()));
        category.setId(1L);
        category.setName("cellValue");
        category.setLevel(1);
        final List<Category> categories = Collections.singletonList(category);
        when(mockCategoryMapper.selectCategoryByIds(any(Long[].class))).thenReturn(categories);

        // Run the test
        final List<Category> result = categoryServiceImplUnderTest.selectCategoryByIds(new Long[]{1L});

        assertNotNull(result);
        Mockito.verify(mockCategoryMapper,times(1)).selectCategoryByIds(any(Long[].class));
        // Verify the results

    }

    @Test
    void testListMultiCategory() {
        // Setup
        // Configure CategoryMapper.selectCategoryList(...).
        Category category = new Category();
        category.setId(1L);
        category.setName("一级分类");
        category.setLevel(1);
        category.setParentId(null);

        final List<Category> categories = Collections.singletonList(category);
        when(mockCategoryMapper.selectCategoryList(any(Category.class))).thenReturn(categories);

        // Run the test
        final List<CategoryDto> result = categoryServiceImplUnderTest.listMultiCategory(1,null);
        assertNotNull(result);
        System.out.println(result);
        Mockito.verify(mockCategoryMapper,times(2)).selectCategoryList(any(Category.class));
    }


    @Test
    void testGetCategory() {
        // Setup
        // Configure CategoryMapper.selectCategoryById(...).
        final Category category = new Category();
        category.setChildren(Collections.singletonList(new CategoryApiDto()));
        category.setId(1L);
        category.setParentId(1L);
        category.setName("name");
        category.setLevel(1);
        when(mockCategoryMapper.selectCategoryById(1L)).thenReturn(category,new Category());

        // Run the test
        final Category result = categoryServiceImplUnderTest.getCategory(1L);
        assertNotNull(result);
        verify(mockCategoryMapper,times(2)).selectCategoryById(1L);
    }

    @Test
    void testBuildTree() {
        // Setup
        final Category category = new Category();
        category.setChildren(asList(new CategoryApiDto()));
        category.setId(1L);
        category.setParentId(1L);
        category.setName("name");
        category.setLevel(1);

        final Category category1 = new Category();
        category1.setChildren(asList(new CategoryApiDto()));
        category1.setId(1L);
        category1.setParentId(1L);
        category1.setName("name");
        category1.setLevel(1);
        final List<Category> categoryList = Collections.singletonList(category1);

        // Run the test
        final Category result = categoryServiceImplUnderTest.buildTree(category, categoryList, 0);
        assertNotNull(result);
    }

    @Test
    void testBuildCategoryHierarchy() {
        // Setup
        final MyScriptBean myScriptBean = new MyScriptBean();
        myScriptBean.setCategoryId(1L);
        myScriptBean.setCategory("category");
        myScriptBean.setScriptInfoId(1L);
        myScriptBean.setUniqueUuid("uniqueUuid");
        myScriptBean.setScriptNameZh("scriptNameZh");
        final List<MyScriptBean> myScriptBeans = Collections.singletonList(myScriptBean);

        // Configure CategoryMapper.selectCategoryList(...).
        final Category category = new Category();
        category.setChildren(Collections.singletonList(new CategoryApiDto()));
        category.setId(1L);
        category.setParentId(1L);
        category.setName("name");
        category.setLevel(1);

        final Category category1 = new Category();
        category1.setChildren(Collections.singletonList(new CategoryApiDto()));
        category1.setId(2L);
        category1.setParentId(1L);
        category1.setName("name");
        category1.setLevel(1);
        final List<Category> categoryList = Collections.singletonList(category);
        final List<Category> categoryList1 = Collections.singletonList(category1);
        when(mockCategoryMapper.selectCategoryList(any(Category.class))).thenReturn(categoryList,categoryList1,new ArrayList<>());

        // Run the test
        categoryServiceImplUnderTest.buildCategoryHierarchy(myScriptBeans);

        // Verify the results
        verify(mockCategoryMapper,times(1)).selectCategoryList(any(Category.class));
    }


    @Test
    void testGetCurrentAndSubclassIds_idsListExist() {
        // Setup
        ScriptInfoQueryDto scriptInfoQueryDto = new ScriptInfoQueryDto();
        scriptInfoQueryDto.setCategoryId(1L);

        when(mockCategoryMapper.getCategoryIds(Collections.singletonList(1L))).thenReturn(Collections.singletonList(2L),new ArrayList<>());

        // Run the test
        final List<Long> result = categoryServiceImplUnderTest.getCurrentAndSubclassIds(scriptInfoQueryDto);

        // Verify the results
        assertThat(result).isEqualTo(asList(1L,2L));
        verify(mockCategoryMapper,times(2)).getCategoryIds(Arrays.asList(1L, 2L));
    }

    @Test
    void testGetCurrentAndSubclassIds_idsListNull() {
        // Setup


        ScriptInfoQueryDto scriptInfoQueryDto = new ScriptInfoQueryDto();
        scriptInfoQueryDto.setCategoryId(null);
        // Run the test
        final List<Long> result = categoryServiceImplUnderTest.getCurrentAndSubclassIds(scriptInfoQueryDto);

        // Verify the results
        assertThat(result).isNull();
    }

    @Test
    void testGetCategoryFullPath(){

            Category category = new Category();
            category.setName("category");
            category.setParentId(1L);
            category.setId(1L);
            // Setup
            when(mockCategoryMapper.selectCategoryById(any(Long.class))).thenReturn(category,new Category());

            // Run the test
            final String result = categoryServiceImplUnderTest.getCategoryFullPath(any(Long.class));

            // Verify the results
            assertNotNull(result);
    }


    @Test
    void testGetCategoryFullPath_noExistCategory(){


        // Setup
        when(mockCategoryMapper.selectCategoryById(any(Long.class))).thenReturn(null);

        // Run the test
        final String result = categoryServiceImplUnderTest.getCategoryFullPath(any(Long.class));

        // Verify the results
        assertNotNull(result);
    }


    @Test
    void findByLevelAndNameAndParentId() throws ScriptException {

        final CategoryDto result = categoryServiceImplUnderTest.findByLevelAndNameAndParentId(1,"name",1L);
        verify(mockCategoryMapper).findByLevelAndNameAndParentId(any(Category.class));
    }
    @Test
    void findByLevelAndNameAndParentId_exceprion() throws ScriptException {

        Category category = new Category();
        List<Category> categoryList = new ArrayList<>();
        categoryList.add(category);
        categoryList.add(category);
        when(mockCategoryMapper.findByLevelAndNameAndParentId(any(Category.class))).thenReturn(categoryList);
        assertThrows(ScriptException.class,() ->{
            categoryServiceImplUnderTest.findByLevelAndNameAndParentId(1,"name",1L);
        });
    }

    @Test
    void setChildrenForCategoryAndParent_noCategoryDto() {
        final Category result = categoryServiceImplUnderTest.setChildrenForCategoryAndParent(null);
        assertNull(result);
    }

    @Test
    void setChildrenForCategoryAndParent() {
        Category conCategory = new Category();
        conCategory.setParentId(1L);
        conCategory.setId(2L);

        Category category = new Category();
        category.setParentId(2L);
        category.setId(3L);

        when(mockCategoryMapper.selectCategoryById(anyLong())).thenReturn(category,new Category());

        final Category result = categoryServiceImplUnderTest.setChildrenForCategoryAndParent(conCategory);
        assertNotNull(result);
    }
    @Test
    void setChildrenForCategoryAndParent_noParentCategory() {
        Category category = new Category();


        final Category result = categoryServiceImplUnderTest.setChildrenForCategoryAndParent(category);
        assertNotNull(result);
    }

    @Test
    void getAllCategoryIds() {
        // 使用doAnswer模拟retrieveAllDescendantCategoryIds的行为，避免递归调用
        doAnswer(invocation -> {
            Long categoryId = invocation.getArgument(0);
            List<Long> allCategoryIds = invocation.getArgument(1);
            
            // 手动模拟方法实现，但避免递归
            allCategoryIds.add(categoryId);
            
            // 模拟添加子分类ID，不递归调用自身
            if (categoryId == 1L) {
                allCategoryIds.add(2L);
                allCategoryIds.add(3L);
            }
            
            return null;
        }).when(categoryServiceImplUnderTest).retrieveAllDescendantCategoryIds(anyLong(), any(List.class));
        
        // 执行测试方法
        final List<Long> result = categoryServiceImplUnderTest.getAllCategoryIds(1L);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(3, result.size());
        assertTrue(result.contains(1L));
        assertTrue(result.contains(2L));
        assertTrue(result.contains(3L));
        
        // 验证retrieveAllDescendantCategoryIds被调用
        verify(categoryServiceImplUnderTest).retrieveAllDescendantCategoryIds(eq(1L), any(List.class));
    }

    @Test
    void testGetCategoryWithChildren() {
        // 创建测试数据
        Category parentCategory = new Category();
        parentCategory.setId(1L);
        parentCategory.setName("Parent Category");

        Category childCategory = new Category();
        childCategory.setId(2L);
        childCategory.setName("Child Category");
        childCategory.setParentId(1L);

        Category childCategory1 = new Category();
        childCategory1.setId(3L);
        childCategory1.setName("Child Category1111");
        childCategory1.setParentId(2L);

        List<Category> childCategories = new ArrayList<>();
        childCategories.add(childCategory);

        List<Category> childCategories1 = new ArrayList<>();
        childCategories1.add(childCategory1);

        // 设置模拟行为
        when(mockCategoryMapper.selectCategoryList(any(Category.class))).thenReturn(childCategories,childCategories1,new ArrayList<>());

        // 调用被测试的方法
        List<Category> result = categoryServiceImplUnderTest.getCategoryWithChildren(parentCategory, true);

        // 验证结果
        assertEquals(1, result.size());
        /*Category actualParentCategory = result.get(0);
        assertEquals(parentCategory.getId(), actualParentCategory.getId());
        assertEquals(parentCategory.getName(), actualParentCategory.getName());
        assertNotNull(actualParentCategory.getChildren());
        assertEquals(1, actualParentCategory.getChildren().size());
        Category actualChildCategory = actualParentCategory.getChildren().get(0);
        assertEquals(childCategory.getId(), actualChildCategory.getId());
        assertEquals(childCategory.getName(), actualChildCategory.getName());
        assertEquals(childCategory.getParentId(), actualChildCategory.getParentId());

        // 验证是否调用了正确的方法
        verify(categoryMapper, times(1)).selectCategoryList(parentCategory);*/
    }

    @Test
    void testHandleCategoryPath() {
        // 测试空路径情况
        String result1 = categoryServiceImplUnderTest.handleCategoryPath(null);
        assertNull(result1);
        
        // 测试空字符串情况
        String result2 = categoryServiceImplUnderTest.handleCategoryPath("");
        assertEquals("", result2);
        
        // 测试不包含特殊字符的情况
        String path1 = "一级/二级/三级";
        String result3 = categoryServiceImplUnderTest.handleCategoryPath(path1);
        assertEquals(path1, result3);
        
        // 测试包含%字符的情况
        String path2 = "一级/二级%/三级";
        String result4 = categoryServiceImplUnderTest.handleCategoryPath(path2);
        assertEquals("一级/二级\\\\%/三级", result4);
        
        // 测试包含_字符的情况
        String path3 = "一级/二级_/三级";
        String result5 = categoryServiceImplUnderTest.handleCategoryPath(path3);
        assertEquals("一级/二级\\\\_/三级", result5);
        
        // 测试包含多个特殊字符的情况
        String path4 = "一级/二级%_/三级%";
        String result6 = categoryServiceImplUnderTest.handleCategoryPath(path4);
        assertEquals("一级/二级\\\\%\\\\_/三级\\\\%", result6);
    }

    @Test
    void testBuildCategoryPath() {
        // 创建测试数据
        Category category = new Category();
        category.setId(1L);
        category.setName("一级分类");
        category.setLevel(1);
        
        Category childCategory = new Category();
        childCategory.setId(2L);
        childCategory.setParentId(1L);
        childCategory.setName("二级分类");
        childCategory.setLevel(2);
        
        // 模拟孩子节点的查询结果
        List<Category> childCategoryList = new ArrayList<>();
        childCategoryList.add(childCategory);
        
        // 模拟父节点的查询结果
        List<Category> parentCategoryList = new ArrayList<>();
        parentCategoryList.add(category);
        
        // 这里不应该设置parentId为null，因为这样会导致在buildCategoryFullPath中循环终止
        // 保持childCategory.parentId = 1L
        
        // 设置正确的模拟行为 - 我们需要模拟两次调用selectCategoryList的结果
        // 1. 第一次调用时返回孩子节点（二级分类）
        // 2. 第二次调用时返回父节点（一级分类）
        // 3. 第三次调用时返回空列表，表示已经达到最顶层
        when(mockCategoryMapper.selectCategoryList(any(Category.class)))
            .thenReturn(childCategoryList)  // 第一次调用返回二级分类
            .thenReturn(parentCategoryList) // 第二次调用返回一级分类
            .thenReturn(new ArrayList<>());  // 第三次调用返回空列表
        
        // 调用被测试的方法
        String result = categoryServiceImplUnderTest.buildCategoryPath(childCategory);
        
        // 验证结果
        assertEquals("一级分类/二级分类", result);
        
        // 验证方法调用
        verify(mockCategoryMapper, times(2)).selectCategoryList(any(Category.class));
    }

    @Test
    @DisplayName("测试不分页获取分类列表")
    void testSelectCategoryListNoPage() {
        // 创建测试数据
        CategoryDto categoryDto = new CategoryDto();
        categoryDto.setName("测试分类");
        
        // 有name参数的情况
        Category category = new Category();
        category.setId(1L);
        category.setName("测试分类");
        List<Category> categoryList = Collections.singletonList(category);
        
        // 使用spy彻底拦截getCategoryWithChildren方法，避免递归调用导致栈溢出
        CategoryServiceImpl spyCategoryService = Mockito.spy(categoryServiceImplUnderTest);
        doReturn(categoryList).when(spyCategoryService).getCategoryWithChildren(any(Category.class));
        
        // 调用被测试的方法
        List<CategoryDto> result1 = spyCategoryService.selectCategoryListNoPage(categoryDto);
        
        // 验证结果
        assertNotNull(result1);
        assertEquals(1, result1.size());
        assertEquals("测试分类", result1.get(0).getName());
        
        // 测试name为空的情况
        CategoryDto categoryDto2 = new CategoryDto();
        
        Category categoryLevel1 = new Category();
        categoryLevel1.setId(2L);
        categoryLevel1.setLevel(1);
        categoryLevel1.setName("一级分类");
        
        List<Category> categoryListLevel1 = Collections.singletonList(categoryLevel1);
        
        // 为第二个测试场景重新配置spy
        doReturn(categoryListLevel1).when(spyCategoryService).getCategoryWithChildren(any(Category.class));
        
        // 调用被测试的方法
        List<CategoryDto> result2 = spyCategoryService.selectCategoryListNoPage(categoryDto2);
        
        // 验证结果
        assertNotNull(result2);
        assertEquals(1, result2.size());
        assertEquals("一级分类", result2.get(0).getName());
        
        // 测试传入null的情况 - 不需要mock，因为这种情况方法内部不会调用getCategoryWithChildren
        List<CategoryDto> result3 = spyCategoryService.selectCategoryListNoPage(null);
        
        assertNotNull(result3);
        assertEquals(0, result3.size());
    }

    @Test
    void testAssignCategoryToOrg() {
        // 创建测试数据
        CategoryOrgDto categoryOrgDto = new CategoryOrgDto();
        categoryOrgDto.setCategoryId(1L);
        categoryOrgDto.setLevel(1);
        
        OrgBean orgBean1 = new OrgBean();
        orgBean1.setOrgId(1L);
        orgBean1.setCode("org1");
        
        OrgBean orgBean2 = new OrgBean();
        orgBean2.setOrgId(2L);
        orgBean2.setCode("org2");
        
        OrgBean existingOrgBean = new OrgBean();
        existingOrgBean.setOrgId(3L);
        existingOrgBean.setCode("org3");
        existingOrgBean.setId(10L);  // 数据库记录ID
        
        List<OrgBean> orgList = Arrays.asList(orgBean1, orgBean2);
        categoryOrgDto.setOrgList(orgList);
        
        // 模拟现有关系
        List<OrgBean> existingRelations = Collections.singletonList(existingOrgBean);
        when(mockCategoryMapper.getCategoryOrgRelations(1L)).thenReturn(existingRelations);
        
        // 执行测试方法
        categoryServiceImplUnderTest.assignCategoryToOrg(categoryOrgDto);
        
        // 验证调用
        verify(mockCategoryMapper).getCategoryOrgRelations(1L);
        verify(mockCategoryMapper).deleteCategoryDepartmentByIds(Collections.singletonList(10L));
        verify(batchHandler).batchData(any(List.class), any(Consumer.class));
    }

    @Test
    void testAssignCategoryToOrg_NoExistingRelations() {
        // 创建测试数据
        CategoryOrgDto categoryOrgDto = new CategoryOrgDto();
        categoryOrgDto.setCategoryId(1L);
        categoryOrgDto.setLevel(1);
        
        OrgBean orgBean = new OrgBean();
        orgBean.setOrgId(1L);
        orgBean.setCode("org1");
        
        List<OrgBean> orgList = Collections.singletonList(orgBean);
        categoryOrgDto.setOrgList(orgList);
        
        // 模拟没有现有关系
        when(mockCategoryMapper.getCategoryOrgRelations(1L)).thenReturn(new ArrayList<>());
        
        // 执行测试方法
        categoryServiceImplUnderTest.assignCategoryToOrg(categoryOrgDto);
        
        // 验证调用
        verify(mockCategoryMapper).getCategoryOrgRelations(1L);
        verify(mockCategoryMapper, times(0)).deleteCategoryDepartmentByIds(any(List.class));
        verify(batchHandler).batchData(any(List.class), any(Consumer.class));
    }

    @Test
    void testAssignCategoryToOrg_NoNewRelations() {
        // 创建测试数据
        CategoryOrgDto categoryOrgDto = new CategoryOrgDto();
        categoryOrgDto.setCategoryId(1L);
        categoryOrgDto.setLevel(1);
        
        // 不设置新关系 - 使用空列表而不是null
        categoryOrgDto.setOrgList(new ArrayList<>());
        
        // 模拟现有关系
        OrgBean existingOrgBean = new OrgBean();
        existingOrgBean.setOrgId(1L);
        existingOrgBean.setCode("org1");
        existingOrgBean.setId(10L);
        List<OrgBean> existingRelations = Collections.singletonList(existingOrgBean);
        
        when(mockCategoryMapper.getCategoryOrgRelations(1L)).thenReturn(existingRelations);
        
        // 执行测试方法
        categoryServiceImplUnderTest.assignCategoryToOrg(categoryOrgDto);
        
        // 验证调用
        verify(mockCategoryMapper).getCategoryOrgRelations(1L);
        verify(mockCategoryMapper).deleteCategoryDepartmentByIds(Collections.singletonList(10L));
        verify(batchHandler, times(0)).batchData(any(List.class), any(Consumer.class));
    }

    @Test
    void testGetCategoryOrgRelations() {
        // 创建测试数据
        OrgBean orgBean1 = new OrgBean();
        orgBean1.setOrgId(1L);
        orgBean1.setCode("org1");
        orgBean1.setId(10L);
        
        OrgBean orgBean2 = new OrgBean();
        orgBean2.setOrgId(2L);
        orgBean2.setCode("org2");
        orgBean2.setId(20L);
        
        List<OrgBean> orgList = Arrays.asList(orgBean1, orgBean2);
        
        when(mockCategoryMapper.getCategoryOrgRelations(1L)).thenReturn(orgList);
        
        // 执行测试方法
        CategoryOrgBean result = categoryServiceImplUnderTest.getCategoryOrgRelations(1L);
        
        // 验证调用
        verify(mockCategoryMapper).getCategoryOrgRelations(1L);
        
        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getOrgList());
        assertEquals(2, result.getOrgList().size());
        assertEquals("org1", result.getOrgList().get(0).getCode());
        assertEquals("org2", result.getOrgList().get(1).getCode());
    }

    @Test
    void testGetCategoryOrgRelations_EmptyList() {
        // 模拟空列表
        when(mockCategoryMapper.getCategoryOrgRelations(1L)).thenReturn(new ArrayList<>());
        
        // 执行测试方法
        CategoryOrgBean result = categoryServiceImplUnderTest.getCategoryOrgRelations(1L);
        
        // 验证调用
        verify(mockCategoryMapper).getCategoryOrgRelations(1L);
        
        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getOrgList());
        assertEquals(0, result.getOrgList().size());
    }

    @Test
    void testAssignCategoryToUser() throws ScriptException {
        // 创建测试数据
        CategoryUserDto categoryUserDto = new CategoryUserDto();
        categoryUserDto.setCategoryId(1L);
        
        UserBean userBean1 = new UserBean();
        userBean1.setUserId(1L);
        
        UserBean userBean2 = new UserBean();
        userBean2.setUserId(2L);
        
        UserBean existingUserBean = new UserBean();
        existingUserBean.setUserId(3L);
        existingUserBean.setId(10L);  // 数据库记录ID
        
        List<UserBean> userList = Arrays.asList(userBean1, userBean2);
        categoryUserDto.setUserList(userList);
        
        // 模拟存在部门关系
        OrgBean orgBean = new OrgBean();
        List<OrgBean> orgList = Collections.singletonList(orgBean);
        when(mockCategoryMapper.getCategoryOrgRelations(1L)).thenReturn(orgList);
        
        // 模拟现有用户关系
        List<UserBean> existingUsers = Collections.singletonList(existingUserBean);
        when(mockCategoryMapper.getCategoryUsertRelations(1L)).thenReturn(existingUsers);
        
        // 执行测试方法
        categoryServiceImplUnderTest.assignCategoryToUser(categoryUserDto);
        
        // 验证调用
        verify(mockCategoryMapper).getCategoryOrgRelations(1L);
        verify(mockCategoryMapper).getCategoryUsertRelations(1L);
        verify(mockCategoryMapper).deleteCategoryUserByIds(Collections.singletonList(10L));
        verify(batchHandler).batchData(any(List.class), any(Consumer.class));
    }

    @Test
    void testAssignCategoryToUser_NoDepartmentRelation() {
        // 创建测试数据
        CategoryUserDto categoryUserDto = new CategoryUserDto();
        categoryUserDto.setCategoryId(1L);
        
        // 模拟没有部门关系
        when(mockCategoryMapper.getCategoryOrgRelations(1L)).thenReturn(new ArrayList<>());
        
        // 执行测试方法并验证异常
        assertThrows(ScriptException.class, () -> {
            categoryServiceImplUnderTest.assignCategoryToUser(categoryUserDto);
        });
        
        // 验证调用
        verify(mockCategoryMapper).getCategoryOrgRelations(1L);
        verify(mockCategoryMapper, times(0)).getCategoryUsertRelations(anyLong());
    }

    @Test
    void testAssignCategoryToUser_SameUser() throws ScriptException {
        // 创建测试数据
        CategoryUserDto categoryUserDto = new CategoryUserDto();
        categoryUserDto.setCategoryId(1L);
        
        UserBean userBean = new UserBean();
        userBean.setUserId(1L);
        
        UserBean existingUserBean = new UserBean();
        existingUserBean.setUserId(1L);  // 相同的用户ID
        existingUserBean.setId(10L);
        
        List<UserBean> userList = Collections.singletonList(userBean);
        categoryUserDto.setUserList(userList);
        
        // 模拟存在部门关系
        OrgBean orgBean = new OrgBean();
        List<OrgBean> orgList = Collections.singletonList(orgBean);
        when(mockCategoryMapper.getCategoryOrgRelations(1L)).thenReturn(orgList);
        
        // 模拟现有用户关系
        List<UserBean> existingUsers = new ArrayList<>(Collections.singletonList(existingUserBean));
        when(mockCategoryMapper.getCategoryUsertRelations(1L)).thenReturn(existingUsers);
        
        // 执行测试方法
        categoryServiceImplUnderTest.assignCategoryToUser(categoryUserDto);
        
        // 验证调用
        verify(mockCategoryMapper).getCategoryOrgRelations(1L);
        verify(mockCategoryMapper).getCategoryUsertRelations(1L);
        // 由于用户相同，不会删除现有用户
        verify(mockCategoryMapper, times(0)).deleteCategoryUserByIds(any(List.class));
        // 由于用户相同，不会添加新用户
        verify(batchHandler, times(0)).batchData(any(List.class), any(Consumer.class));
    }

    @Test
    void testGetCategoryUserRelations() {
        // 创建测试数据
        UserBean userBean1 = new UserBean();
        userBean1.setUserId(1L);
        userBean1.setId(10L);
        
        UserBean userBean2 = new UserBean();
        userBean2.setUserId(2L);
        userBean2.setId(20L);
        
        List<UserBean> userList = Arrays.asList(userBean1, userBean2);
        
        when(mockCategoryMapper.getCategoryUsertRelations(1L)).thenReturn(userList);
        
        // 执行测试方法
        CategoryUserBean result = categoryServiceImplUnderTest.getCategoryUserRelations(1L);
        
        // 验证调用
        verify(mockCategoryMapper).getCategoryUsertRelations(1L);
        
        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getUserList());
        assertEquals(2, result.getUserList().size());
        assertEquals(1L, result.getUserList().get(0).getUserId());
        assertEquals(2L, result.getUserList().get(1).getUserId());
    }

    @Test
    void testGetCategoryUserRelations_EmptyList() {
        // 模拟空列表
        when(mockCategoryMapper.getCategoryUsertRelations(1L)).thenReturn(new ArrayList<>());
        
        // 执行测试方法
        CategoryUserBean result = categoryServiceImplUnderTest.getCategoryUserRelations(1L);
        
        // 验证调用
        verify(mockCategoryMapper).getCategoryUsertRelations(1L);
        
        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getUserList());
        assertEquals(0, result.getUserList().size());
    }

    @Test
    void testQueryPermissionUserInfoList() {
        // 创建测试数据
        CategoryUserBean categoryUserBean = new CategoryUserBean();
        categoryUserBean.setCategoryId(1L);
        
        ServicePermissionApiQueryDto servicePermissionApiQueryDto = new ServicePermissionApiQueryDto();
        categoryUserBean.setServicePermissionApiQueryDto(servicePermissionApiQueryDto);
        
        // 模拟分类部门关系
        OrgBean orgBean = new OrgBean();
        orgBean.setOrgId(1L);
        List<OrgBean> orgList = Collections.singletonList(orgBean);
        when(mockCategoryMapper.getCategoryOrgRelations(1L)).thenReturn(orgList);
        
        // 模拟当前用户
        try (MockedStatic<CurrentUserUtil> currentUserUtilMockedStatic = mockStatic(CurrentUserUtil.class)) {
            CurrentUser currentUser = new CurrentUser();
            currentUser.setOrgId(2L);
            currentUserUtilMockedStatic.when(CurrentUserUtil::getCurrentUser).thenReturn(currentUser);
            
            // 模拟API返回结果
            PermissionUserInfoApiDto userInfoApiDto = new PermissionUserInfoApiDto();
            List<PermissionUserInfoApiDto> userInfoList = Collections.singletonList(userInfoApiDto);
            when(mockUserInfoApi.queryPermissionUserInfoList(any(ServicePermissionApiQueryDto.class))).thenReturn(userInfoList);
            
            // 执行测试方法
            List<PermissionUserInfoApiDto> result = categoryServiceImplUnderTest.queryPermissionUserInfoList(categoryUserBean);
            
            // 验证调用
            verify(mockCategoryMapper).getCategoryOrgRelations(1L);
            verify(mockUserInfoApi).queryPermissionUserInfoList(any(ServicePermissionApiQueryDto.class));
            
            // 验证结果
            assertNotNull(result);
            assertEquals(1, result.size());
        }
    }

    @Test
    void testQueryPermissionUserInfoPage() {
        // 创建测试数据
        CategoryUserBean categoryUserBean = new CategoryUserBean();
    }

    @Test
    void testSelectOrgManagementTree() {
        // 创建测试数据
        OrgManagementApiDto orgManagementApiDto = new OrgManagementApiDto();
        
        // 模拟API返回结果
        List<OrgManagementApiDto> orgList = new ArrayList<>();
        when(mockOrgManagementApi.selectOrgManagementTree(any(OrgManagementApiDto.class))).thenReturn(orgList);
        
        // 执行测试方法
        List<OrgManagementApiDto> result = categoryServiceImplUnderTest.selectOrgManagementTree(orgManagementApiDto);
        
        // 验证调用
        verify(mockOrgManagementApi).selectOrgManagementTree(orgManagementApiDto);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(orgList, result);
    }

    @Test
    void testSelectNotShareOrgManagementTree_NoSharedOrgs() {
        // 创建测试数据
        Long scriptVersionId = 1L;
        
        // 模拟组织管理API返回结果
        OrgManagementApiDto org1 = new OrgManagementApiDto();
        org1.setCode("org1");
        
        OrgManagementApiDto org2 = new OrgManagementApiDto();
        org2.setCode("org2");
        
        List<OrgManagementApiDto> orgList = Arrays.asList(org1, org2);
        when(mockOrgManagementApi.selectOrgManagementTree(any(OrgManagementApiDto.class))).thenReturn(orgList);
        
        // 模拟版本共享服务返回空列表（没有共享部门）
        when(mockScriptVersionShareService.getObjectIdList(scriptVersionId, (short) 1)).thenReturn(new ArrayList<>());
        
        // 执行测试方法
        List<OrgManagementApiDto> result = categoryServiceImplUnderTest.selectNotShareOrgManagementTree(scriptVersionId);
        
        // 验证调用
        verify(mockOrgManagementApi).selectOrgManagementTree(any(OrgManagementApiDto.class));
        verify(mockScriptVersionShareService).getObjectIdList(scriptVersionId, (short) 1);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("org1", result.get(0).getCode());
        assertEquals("org2", result.get(1).getCode());
    }

    @Test
    void testSelectNotShareOrgManagementTree_WithSharedOrgs() {
        // 创建测试数据
        Long scriptVersionId = 1L;
        
        // 模拟组织管理API返回结果
        OrgManagementApiDto org1 = new OrgManagementApiDto();
        org1.setCode("org1");
        
        OrgManagementApiDto org2 = new OrgManagementApiDto();
        org2.setCode("org2");
        
        List<OrgManagementApiDto> orgList = Arrays.asList(org1, org2);
        when(mockOrgManagementApi.selectOrgManagementTree(any(OrgManagementApiDto.class))).thenReturn(orgList);
        
        // 模拟版本共享服务返回已共享部门列表（org1已共享）
        List<String> sharedOrgCodes = Collections.singletonList("org1");
        when(mockScriptVersionShareService.getObjectIdList(scriptVersionId, (short) 1)).thenReturn(sharedOrgCodes);
        
        // 执行测试方法
        List<OrgManagementApiDto> result = categoryServiceImplUnderTest.selectNotShareOrgManagementTree(scriptVersionId);
        
        // 验证调用
        verify(mockOrgManagementApi).selectOrgManagementTree(any(OrgManagementApiDto.class));
        verify(mockScriptVersionShareService).getObjectIdList(scriptVersionId, (short) 1);
        
        // 验证结果 - 只应返回org2，因为org1已共享
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("org2", result.get(0).getCode());
    }

    @Test
    void testSelectNotShareOrgManagementTree_WithNestedOrgs() {
        // 创建测试数据
        Long scriptVersionId = 1L;
        
        // 模拟组织管理API返回结果 - 创建嵌套部门结构
        OrgManagementApiDto childOrg = new OrgManagementApiDto();
        childOrg.setCode("child");
        
        OrgManagementApiDto parentOrg = new OrgManagementApiDto();
        parentOrg.setCode("parent");
        parentOrg.setChildren(Collections.singletonList(childOrg));
        
        List<OrgManagementApiDto> orgList = Collections.singletonList(parentOrg);
        when(mockOrgManagementApi.selectOrgManagementTree(any(OrgManagementApiDto.class))).thenReturn(orgList);
        
        // 模拟版本共享服务返回已共享部门列表（child已共享）
        List<String> sharedOrgCodes = Collections.singletonList("child");
        when(mockScriptVersionShareService.getObjectIdList(scriptVersionId, (short) 1)).thenReturn(sharedOrgCodes);
        
        // 执行测试方法
        List<OrgManagementApiDto> result = categoryServiceImplUnderTest.selectNotShareOrgManagementTree(scriptVersionId);
        
        // 验证调用
        verify(mockOrgManagementApi).selectOrgManagementTree(any(OrgManagementApiDto.class));
        verify(mockScriptVersionShareService).getObjectIdList(scriptVersionId, (short) 1);
        
        // 验证结果 - 父部门应保留，但不包含已共享的子部门
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("parent", result.get(0).getCode());
        assertEquals(0, result.get(0).getChildren().size());
    }

    @Test
    void testSelectCategoryListDFS() {
        // 创建测试数据
        CategoryDto categoryDto = new CategoryDto();
        
        // 模拟所有分类数据
        Category category1 = new Category();
        category1.setId(1L);
        category1.setLevel(1);
        category1.setName("一级分类1");
        
        Category category2 = new Category();
        category2.setId(2L);
        category2.setLevel(2);
        category2.setParentId(1L);
        category2.setName("二级分类1");
        
        Category category3 = new Category();
        category3.setId(3L);
        category3.setLevel(1);
        category3.setName("一级分类2");
        
        // 使用ArrayList代替Arrays.asList，因为Arrays.asList返回的列表不支持结构修改操作
        List<Category> allCategories = new ArrayList<>();
        allCategories.add(category1);
        allCategories.add(category2);
        allCategories.add(category3);
        
        when(mockCategoryMapper.selectAllCategories()).thenReturn(allCategories);
        
        // 模拟分类部门关系 - 同样使用ArrayList
        OrgBean orgBean1 = new OrgBean();
        orgBean1.setCategoryId(1L);
        orgBean1.setCode("ORG001");
        
        OrgBean orgBean2 = new OrgBean();
        orgBean2.setCategoryId(2L);
        orgBean2.setCode("ORG002");
        
        List<OrgBean> allRelations = new ArrayList<>();
        allRelations.add(orgBean1);
        allRelations.add(orgBean2);
        
        when(mockCategoryMapper.selectAllOrgRelations()).thenReturn(allRelations);
        
        // 模拟当前用户
        try (MockedStatic<CurrentUserUtil> currentUserUtilMockedStatic = mockStatic(CurrentUserUtil.class)) {
            CurrentUser currentUser = new CurrentUser();
            currentUser.setOrgCode("ORG001");
            currentUserUtilMockedStatic.when(CurrentUserUtil::getCurrentUser).thenReturn(currentUser);
            
            // 执行测试方法
            List<CategoryDto> result = categoryServiceImplUnderTest.selectCategoryListDFS(categoryDto);
            
            // 验证调用
            verify(mockCategoryMapper).selectAllCategories();
            verify(mockCategoryMapper).selectAllOrgRelations();
            
            // 验证结果
            assertNotNull(result);
        }
    }

    @Test
    void testGetCategoryByOrgCode() {
        // 创建测试数据
        String orgCode = "ORG001";
        
        // 模拟返回结果
        List<Long> categoryIds = Arrays.asList(1L, 2L);
        when(mockCategoryMapper.getCategoryByOrgCode(orgCode)).thenReturn(categoryIds);
        
        // 执行测试方法
        List<Long> result = categoryServiceImplUnderTest.getCategoryByOrgCode(orgCode);
        
        // 验证调用
        verify(mockCategoryMapper).getCategoryByOrgCode(orgCode);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(1L, result.get(0));
        assertEquals(2L, result.get(1));
    }

    @Test
    void testGetUserByCategoryIds() {
        // 创建测试数据
        List<Long> categoryIds = Arrays.asList(1L, 2L);
        
        // 模拟返回结果
        UserBean userBean1 = new UserBean();
        userBean1.setId(1L);
        userBean1.setUserId(101L);
        
        UserBean userBean2 = new UserBean();
        userBean2.setId(2L);
        userBean2.setUserId(102L);
        
        List<UserBean> userBeans = Arrays.asList(userBean1, userBean2);
        when(mockCategoryMapper.getUserByCategoryIds(categoryIds)).thenReturn(userBeans);
        
        // 执行测试方法
        List<UserBean> result = categoryServiceImplUnderTest.getUserByCategoryIds(categoryIds);
        
        // 验证调用
        verify(mockCategoryMapper).getUserByCategoryIds(categoryIds);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(101L, result.get(0).getUserId());
        assertEquals(102L, result.get(1).getUserId());
    }

    @ParameterizedTest
    @DisplayName("测试setCategoryPermission方法 - 不同参数组合")
    @MethodSource("createParamsForSetCategoryPermission")
    void testSetCategoryPermission(Long categoryId, Boolean isSuperUser, List<Long> categoryIdList, 
                                  String sysOrgCode, List<String> orgCategoryPath) {
        // 创建测试用的bean对象
        CategoryPermissionAwareMock mockBean = new CategoryPermissionAwareMock();
        mockBean.setCategoryId(categoryId);
        
        // 准备CurrentUser对象
        CurrentUser currentUser = new CurrentUser();
        currentUser.setSupervisor(isSuperUser);
        currentUser.setOrgCode(sysOrgCode);
        
        // 准备CategoryPermissionInfo对象
        CategoryPermissionInfo permissionInfo = new CategoryPermissionInfo();
        permissionInfo.setCategoryIdList(categoryIdList);
        permissionInfo.setSysOrgCode(sysOrgCode);
        permissionInfo.setSuperUser(isSuperUser);
        permissionInfo.setOrgCategoryPath(orgCategoryPath);
        
        // 创建spy对象用于测试
        CategoryServiceImpl spyCategoryService = Mockito.spy(categoryServiceImplUnderTest);
        
        // 完全模拟setCategoryPermissionInfo方法，直接返回我们准备好的对象
        doReturn(permissionInfo).when(spyCategoryService).setCategoryPermissionInfo(Mockito.eq(categoryId), Mockito.eq(currentUser));
        
        // 执行测试方法
        spyCategoryService.setCategoryPermission(mockBean, currentUser);
        
        // 断言结果
        if (categoryIdList != null && !categoryIdList.isEmpty()) {
            assertEquals(categoryIdList, mockBean.getCategoryIdList());
        } else {
            assertNull(mockBean.getCategoryIdList());
        }
        
        assertEquals(sysOrgCode, mockBean.getSysOrgCode());
        
        if (isSuperUser != null) {
            assertEquals(isSuperUser, mockBean.getSuperUser());
        } else {
            assertNull(mockBean.getSuperUser());
        }
        
        if (orgCategoryPath != null && !orgCategoryPath.isEmpty()) {
            assertEquals(orgCategoryPath, mockBean.getOrgCategoryPath());
        } else {
            assertNull(mockBean.getOrgCategoryPath());
        }
    }
    
    @Test
    @DisplayName("测试setCategoryPermission方法 - permissionInfo为null")
    void testSetCategoryPermission_NullPermissionInfo() {
        // 创建测试用的bean对象
        CategoryPermissionAwareMock mockBean = new CategoryPermissionAwareMock();
        mockBean.setCategoryId(1L);
        
        // 准备CurrentUser对象
        CurrentUser currentUser = new CurrentUser();
        currentUser.setSupervisor(false);
        currentUser.setOrgCode("testOrgCode");
        
        // 创建spy对象用于测试
        CategoryServiceImpl spyCategoryService = Mockito.spy(categoryServiceImplUnderTest);
        
        // 模拟setCategoryPermissionInfo返回null
        doReturn(null).when(spyCategoryService).setCategoryPermissionInfo(Mockito.eq(1L), Mockito.eq(currentUser));
        
        // 执行测试方法
        spyCategoryService.setCategoryPermission(mockBean, currentUser);
        
        // 断言结果 - 应保持bean原有状态不变
        assertNull(mockBean.getCategoryIdList());
        assertNull(mockBean.getSysOrgCode());
        assertNull(mockBean.getSuperUser());
        assertNull(mockBean.getOrgCategoryPath());
    }
    
    @Test
    @DisplayName("测试setCategoryPermission方法 - 简单场景")
    void testSetCategoryPermissionSimple() {
        // 创建测试用的bean对象
        CategoryPermissionAwareMock mockBean = new CategoryPermissionAwareMock();
        mockBean.setCategoryId(1L);
        
        // 准备CurrentUser对象
        CurrentUser currentUser = new CurrentUser();
        currentUser.setSupervisor(false);
        currentUser.setOrgCode("testOrgCode");
        
        // 准备CategoryPermissionInfo对象
        CategoryPermissionInfo permissionInfo = new CategoryPermissionInfo();
        permissionInfo.setCategoryIdList(Arrays.asList(1L, 2L));
        permissionInfo.setSysOrgCode("testOrgCode");
        permissionInfo.setSuperUser(false);
        permissionInfo.setOrgCategoryPath(Arrays.asList("path1", "path2"));
        
        // 创建spy对象用于测试
        CategoryServiceImpl spyCategoryService = Mockito.spy(categoryServiceImplUnderTest);
        
        // 模拟setCategoryPermissionInfo方法
        doReturn(permissionInfo).when(spyCategoryService).setCategoryPermissionInfo(Mockito.eq(1L), Mockito.eq(currentUser));
        
        // 执行测试方法
        spyCategoryService.setCategoryPermission(mockBean, currentUser);
        
        // 断言结果
        assertEquals(Arrays.asList(1L, 2L), mockBean.getCategoryIdList());
        assertEquals("testOrgCode", mockBean.getSysOrgCode());
        assertEquals(false, mockBean.getSuperUser());
        assertEquals(Arrays.asList("path1", "path2"), mockBean.getOrgCategoryPath());
    }

    // 测试用例参数源
    static Object[][] createParamsForSetCategoryPermission() {
        return new Object[][] {
            // 场景1: 非管理员用户，所有参数正常
            {1L, false, Arrays.asList(1L, 2L, 3L), "testOrgCode", Arrays.asList("path1", "path2")},
            
            // 场景2: 管理员用户
            {1L, true, Arrays.asList(1L, 2L, 3L), "####", null},
            
            // 场景3: categoryIdList为空
            {1L, false, null, "testOrgCode", Arrays.asList("path1", "path2")},
            
            // 场景4: sysOrgCode为null
            {1L, false, Arrays.asList(1L, 2L, 3L), null, Arrays.asList("path1", "path2")},
            
            // 场景5: orgCategoryPath为空
            {1L, false, Arrays.asList(1L, 2L, 3L), "testOrgCode", null},
            
            // 场景6: categoryId为null，极端情况
            {null, false, Arrays.asList(1L, 2L, 3L), "testOrgCode", Arrays.asList("path1", "path2")}
        };
    }
    
    // 模拟实现CategoryPermissionAware接口的测试类
    private static class CategoryPermissionAwareMock implements CategoryPermissionAware {
        private List<Long> categoryIdList;
        private String sysOrgCode;
        private Boolean superUser;
        private List<String> orgCategoryPath;
        private Long categoryId;
        
        @Override
        public void setCategoryIdList(List<Long> categoryIdList) {
            this.categoryIdList = categoryIdList;
        }
        
        @Override
        public void setSysOrgCode(String sysOrgCode) {
            this.sysOrgCode = sysOrgCode;
        }
        
        @Override
        public void setSuperUser(boolean superUser) {
            this.superUser = superUser;
        }
        
        @Override
        public void setOrgCategoryPath(List<String> orgCategoryPath) {
            this.orgCategoryPath = orgCategoryPath;
        }
        
        @Override
        public Long getCategoryId() {
            return categoryId;
        }
        
        public List<Long> getCategoryIdList() {
            return categoryIdList;
        }
        
        public String getSysOrgCode() {
            return sysOrgCode;
        }
        
        public Boolean getSuperUser() {
            return superUser;
        }
        
        public List<String> getOrgCategoryPath() {
            return orgCategoryPath;
        }
        
        public void setCategoryId(Long categoryId) {
            this.categoryId = categoryId;
        }
    }

    @ParameterizedTest
    @DisplayName("测试setCategoryPermissionInfo方法 - 不同分类ID场景")
    @MethodSource("createParamsForSetCategoryPermissionInfo_CategoryId")
    void testSetCategoryPermissionInfo_CategoryId(Long categoryId, boolean shouldQueryChildCategories) {
        // 准备测试数据
        CurrentUser currentUser = Mockito.mock(CurrentUser.class);
        Mockito.when(currentUser.getSupervisor()).thenReturn(false);
        Mockito.when(currentUser.getOrgCode()).thenReturn("testOrgCode");
        
        // 创建CategoryServiceImpl的部分mock
        CategoryServiceImpl mockService = Mockito.spy(categoryServiceImplUnderTest);
        
        // 模拟获取分类ID列表
        List<Long> mockCategoryIds = new ArrayList<>();
        if (shouldQueryChildCategories && categoryId != null && categoryId > 0) {
            mockCategoryIds.add(categoryId);
            mockCategoryIds.add(categoryId + 1);
            Mockito.doReturn(mockCategoryIds).when(mockService).getAllCategoryIds(categoryId);
        }
        
        // 模拟getCategoryByOrgCode方法
        List<Long> orgCategoryIds = Arrays.asList(10L, 20L);
        Mockito.when(mockCategoryMapper.getCategoryByOrgCode("testOrgCode")).thenReturn(orgCategoryIds);
        
        // 模拟buildCategoryPath方法
        for (Long orgCatId : orgCategoryIds) {
            Category cat = new Category();
            cat.setId(orgCatId);
            Mockito.doReturn("mock/path/" + orgCatId).when(mockService).buildCategoryPath(
                Mockito.argThat(c -> c != null && orgCatId.equals(c.getId()))
            );
        }
        
        // 模拟handleCategoryPath方法 - 注意这里不添加%，因为%应该在setCategoryPermissionInfo方法内部添加
        Mockito.doReturn("escaped_mock/path/10").when(mockService).handleCategoryPath("mock/path/10");
        Mockito.doReturn("escaped_mock/path/20").when(mockService).handleCategoryPath("mock/path/20");
        
        // 执行测试方法
        CategoryPermissionInfo result = mockService.setCategoryPermissionInfo(categoryId, currentUser);
        
        // 断言结果
        assertNotNull(result);
        
        // 分类ID列表验证
        if (shouldQueryChildCategories && categoryId != null && categoryId > 0) {
            assertEquals(mockCategoryIds, result.getCategoryIdList());
        } else {
            assertNull(result.getCategoryIdList());
        }
        
        // 验证其他属性
        assertEquals("testOrgCode", result.getSysOrgCode());
        assertEquals(Boolean.FALSE, result.getSuperUser());
        
        // 验证分类路径处理 - 在CategoryServiceImpl.setCategoryPermissionInfo方法中会给路径添加%后缀
        assertNotNull(result.getOrgCategoryPath());
        assertEquals(2, result.getOrgCategoryPath().size()); // 确保有2个路径
        
        // 验证路径包含%，这是setCategoryPermissionInfo方法内部加上的
        List<String> expectedPaths = Arrays.asList(
            "escaped_mock/path/10%",  // 注意这里期望的是带%的路径
            "escaped_mock/path/20%"   // 注意这里期望的是带%的路径
        );
        
        for (String path : result.getOrgCategoryPath()) {
            assertTrue(expectedPaths.contains(path), "路径 " + path + " 不在预期列表中");
        }
    }
    
    @Test
    @DisplayName("测试setCategoryPermissionInfo方法 - 管理员用户")
    void testSetCategoryPermissionInfo_SupervisorUser() {
        // 准备测试数据 - 管理员用户
        CurrentUser currentUser = Mockito.mock(CurrentUser.class);
        Mockito.when(currentUser.getSupervisor()).thenReturn(true);
        Mockito.when(currentUser.getOrgCode()).thenReturn("testOrgCode");
        
        Long categoryId = 1L;
        List<Long> categoryIdList = Arrays.asList(1L, 2L, 3L);
        
        // 创建CategoryServiceImpl的部分mock
        CategoryServiceImpl mockService = Mockito.mock(CategoryServiceImpl.class);
        
        // 允许调用真实的setCategoryPermissionInfo方法
        Mockito.when(mockService.setCategoryPermissionInfo(Mockito.eq(categoryId), Mockito.eq(currentUser)))
               .thenCallRealMethod();
        
        // 模拟getAllCategoryIds方法
        Mockito.when(mockService.getAllCategoryIds(categoryId)).thenReturn(categoryIdList);
        
        // 执行测试方法
        CategoryPermissionInfo result = mockService.setCategoryPermissionInfo(categoryId, currentUser);
        
        // 断言结果
        assertNotNull(result);
        assertEquals(categoryIdList, result.getCategoryIdList());
        assertEquals("####", result.getSysOrgCode()); // 管理员用户的特殊处理
        assertTrue(result.getSuperUser());
        assertNull(result.getOrgCategoryPath()); // 管理员用户不需要设置分类路径
        
        // 验证调用
        Mockito.verify(mockService).getAllCategoryIds(categoryId);
        Mockito.verify(mockCategoryMapper, never()).getCategoryByOrgCode(Mockito.anyString()); // 管理员不应该调用此方法
    }
    
    @Test
    @DisplayName("测试setCategoryPermissionInfo方法 - 普通用户")
    void testSetCategoryPermissionInfo_RegularUser() {
        // 准备测试数据 - 普通用户
        CurrentUser currentUser = Mockito.mock(CurrentUser.class);
        Mockito.when(currentUser.getSupervisor()).thenReturn(false);
        Mockito.when(currentUser.getOrgCode()).thenReturn("testOrgCode");
        
        Long categoryId = 1L;
        List<Long> categoryIdList = Arrays.asList(1L, 2L);
        List<Long> categoryByOrgCodeList = Arrays.asList(10L, 20L);
        
        // 创建CategoryServiceImpl的spy对象
        CategoryServiceImpl spyService = Mockito.spy(categoryServiceImplUnderTest);
        
        // 模拟getAllCategoryIds方法
        Mockito.doReturn(categoryIdList).when(spyService).getAllCategoryIds(categoryId);
        
        // 模拟getCategoryByOrgCode方法
        Mockito.when(mockCategoryMapper.getCategoryByOrgCode("testOrgCode")).thenReturn(categoryByOrgCodeList);
        
        // 模拟buildCategoryPath方法
        Category cat10 = new Category();
        cat10.setId(10L);
        Category cat20 = new Category();
        cat20.setId(20L);
        
        Mockito.doReturn("path/10").when(spyService).buildCategoryPath(
            Mockito.argThat(c -> c != null && c.getId() == 10L)
        );
        
        Mockito.doReturn("path/20").when(spyService).buildCategoryPath(
            Mockito.argThat(c -> c != null && c.getId() == 20L)
        );
        
        // 模拟handleCategoryPath方法 - 注意这里不添加%，因为%应该在setCategoryPermissionInfo方法内部添加
        Mockito.doReturn("escaped_path/10").when(spyService).handleCategoryPath("path/10");
        Mockito.doReturn("escaped_path/20").when(spyService).handleCategoryPath("path/20");
        
        // 执行测试方法
        CategoryPermissionInfo result = spyService.setCategoryPermissionInfo(categoryId, currentUser);
        
        // 断言结果
        assertNotNull(result);
        assertEquals(categoryIdList, result.getCategoryIdList());
        assertEquals("testOrgCode", result.getSysOrgCode());
        assertEquals(Boolean.FALSE, result.getSuperUser());
        
        // 验证分类路径处理 - 在CategoryServiceImpl.setCategoryPermissionInfo方法中会给路径添加%后缀
        assertNotNull(result.getOrgCategoryPath());
        assertEquals(2, result.getOrgCategoryPath().size()); // 确保有2个路径
        
        // 验证路径包含%，这是setCategoryPermissionInfo方法内部加上的
        List<String> expectedPaths = Arrays.asList(
            "escaped_path/10%",  // 注意这里期望的是带%的路径
            "escaped_path/20%"   // 注意这里期望的是带%的路径
        );
        
        for (String path : result.getOrgCategoryPath()) {
            assertTrue(expectedPaths.contains(path), "路径 " + path + " 不在预期列表中");
        }
    }

    // 参数化测试数据源 - categoryId测试场景
    static Object[][] createParamsForSetCategoryPermissionInfo_CategoryId() {
        return new Object[][] {
            // categoryId, shouldQueryChildCategories
            {1L, true},          // 正常的categoryId，应查询子分类
            {0L, false},         // categoryId为0，不应查询子分类
            {null, false},       // categoryId为null，不应查询子分类
        };
    }
    
    // 通过修改原测试方法，重写getAllCategoryIds测试，避免递归调用
    @Test
    void testGetAllCategoryIds() {
        // 创建一个新的spy对象，避免与之前的测试交叉影响
        CategoryServiceImpl spyCategoryService = Mockito.spy(new CategoryServiceImpl(
            mockScriptVersionShareService, mockCategoryMapper, mockInfoMapper, 
            batchHandler, mockUserInfoApi, mockOrgManagementApi, mockIRoleApi,myScriptService
        ));
        
        // 直接模拟retrieveAllDescendantCategoryIds方法，防止递归
        doAnswer(invocation -> {
            Long categoryId = invocation.getArgument(0);
            List<Long> allCategoryIds = invocation.getArgument(1);
            
            // 添加当前分类ID
            allCategoryIds.add(categoryId);
            
            // 模拟子分类ID
            if (categoryId.equals(1L)) {
                allCategoryIds.add(2L);
                allCategoryIds.add(3L);
            } else if (categoryId.equals(2L)) {
                allCategoryIds.add(4L);
            }
            
            return null;
        }).when(spyCategoryService).retrieveAllDescendantCategoryIds(Mockito.anyLong(), Mockito.any(List.class));
        
        // 执行测试方法
        final List<Long> result = spyCategoryService.getAllCategoryIds(1L);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(3, result.size());
        assertTrue(result.contains(1L));
        assertTrue(result.contains(2L));
        assertTrue(result.contains(3L));
        
        // 验证retrieveAllDescendantCategoryIds被调用
        Mockito.verify(spyCategoryService).retrieveAllDescendantCategoryIds(Mockito.eq(1L), Mockito.any(List.class));
    }

    @Test
    void testSelectRoleManagementList() {
        // 准备测试数据
        RoleApiDto queryParam = new RoleApiDto();
        Integer pageNum = 1;
        Integer pageSize = 10;

        List<RoleApiDto> roleApiDtoList = Arrays.asList(new RoleApiDto(), new RoleApiDto());
        PageInfo<RoleApiDto> expectedPageInfo = new PageInfo<>();
        expectedPageInfo.setList(roleApiDtoList);

        // 模拟 iRoleApi.selectRolePage 的行为
        when(mockIRoleApi.selectRolePage(pageNum, pageSize, queryParam)).thenReturn(expectedPageInfo);

        // 执行测试
        PageInfo<RoleApiDto> result = categoryServiceImplUnderTest.selectRoleManagementList(queryParam, pageNum, pageSize);

        // 验证结果
        assertEquals(expectedPageInfo, result);

        // 验证 queryParam.setDataShare(1) 是否被调用
        assertEquals(Integer.valueOf(1), queryParam.getDataShare());

        // 验证 iRoleApi.selectRolePage 是否被调用
        verify(mockIRoleApi, times(1)).selectRolePage(pageNum, pageSize, queryParam);
    }

    @Test
    public void testAssignCategoryToRole() {
        // 准备测试数据
        CategoryRoleDto categoryRoleDto = new CategoryRoleDto();
        categoryRoleDto.setCategoryId(1L);

        RoleApiDto roleApiDto1 = new RoleApiDto();
        roleApiDto1.setId(101L);

        RoleApiDto roleApiDto2 = new RoleApiDto();
        roleApiDto2.setId(102L);

        categoryRoleDto.setRoleList(Arrays.asList(roleApiDto1, roleApiDto2));

        CategoryRoleBean existingRelation1 = new CategoryRoleBean();
        existingRelation1.setId(1L);
        existingRelation1.setRoleId("101");

        CategoryRoleBean existingRelation2 = new CategoryRoleBean();
        existingRelation2.setId(2L);
        existingRelation2.setRoleId("103");

        List<CategoryRoleBean> existingRelations = Arrays.asList(existingRelation1, existingRelation2);

        // 设置 Mock 对象的行为
        when(mockCategoryMapper.getCategoryRoleRelations(1L)).thenReturn(existingRelations);

        // 调用被测试的方法
        categoryServiceImplUnderTest.assignCategoryToRole(categoryRoleDto);

        // 验证 Mock 对象的行为
        verify(mockCategoryMapper).getCategoryRoleRelations(1L);

        // 验证插入新关系的逻辑
        ArgumentCaptor<List<CategoryRoleBean>> addListCaptor = ArgumentCaptor.forClass(List.class);
        verify(batchHandler).batchData(addListCaptor.capture(), any(Consumer.class));
        List<CategoryRoleBean> capturedAddList = addListCaptor.getValue();
        assert capturedAddList.size() == 1;
        assert capturedAddList.get(0).getRoleId().equals("102");

        // 验证删除旧关系的逻辑
        ArgumentCaptor<List<Long>> delIdsCaptor = ArgumentCaptor.forClass(List.class);
        verify(mockCategoryMapper).deleteCategoryRoleByIds(delIdsCaptor.capture());
        List<Long> capturedDelIds = delIdsCaptor.getValue();
        assert capturedDelIds.size() == 1;
        assert capturedDelIds.contains(2L);
    }

    @Test
    public void testGetCategoryRoleRelations() {
        // 准备测试数据
        long categoryId = 1L;

        CategoryRoleBean categoryRoleBean1 = new CategoryRoleBean();
        categoryRoleBean1.setRoleId("101");

        CategoryRoleBean categoryRoleBean2 = new CategoryRoleBean();
        categoryRoleBean2.setRoleId("102");

        List<CategoryRoleBean> existingRelations = Arrays.asList(categoryRoleBean1, categoryRoleBean2);

        // 设置 Mock 对象的行为
        when(mockCategoryMapper.getCategoryRoleRelations(categoryId)).thenReturn(existingRelations);

        // 调用被测试的方法
        CategoryRoleDto result = categoryServiceImplUnderTest.getCategoryRoleRelations(categoryId);

        // 验证 Mock 对象的行为
        verify(mockCategoryMapper).getCategoryRoleRelations(categoryId);

        // 验证结果
        assertNotNull(result);
        assertEquals(categoryId, result.getCategoryId());
        assertEquals(2, result.getRoleList().size());
        assertEquals(101L, result.getRoleList().get(0).getId());
        assertEquals(102L, result.getRoleList().get(1).getId());
    }

    @Test
    public void testQueryPermissionUserInfoPageByRole_WithExistingRelations() {
        // 准备测试数据
        CategoryRoleDto queryParam = new CategoryRoleDto();
        queryParam.setCategoryId(1L);

        ServicePermissionApiQueryDto servicePermissionApiQueryDto = new ServicePermissionApiQueryDto();
        servicePermissionApiQueryDto.setPermissionCodes(Arrays.asList("permission1", "permission2"));
        queryParam.setServicePermissionApiQueryDto(servicePermissionApiQueryDto);

        Integer pageNum = 1;
        Integer pageSize = 10;

        CategoryRoleBean categoryRoleBean1 = new CategoryRoleBean();
        categoryRoleBean1.setRoleId("101");

        CategoryRoleBean categoryRoleBean2 = new CategoryRoleBean();
        categoryRoleBean2.setRoleId("102");

        List<CategoryRoleBean> existingRelations = Arrays.asList(categoryRoleBean1, categoryRoleBean2);

        List<Long> roleIds = Arrays.asList(101L, 102L);

        PageInfo<PermissionUserInfoApiDto> expectedPageInfo = new PageInfo<>();
        expectedPageInfo.setList(Arrays.asList(new PermissionUserInfoApiDto(), new PermissionUserInfoApiDto()));

        // 设置 Mock 对象的行为
        when(mockCategoryMapper.getCategoryRoleRelations(queryParam.getCategoryId())).thenReturn(existingRelations);
        when(mockUserInfoApi.queryPermissionUserInfoPage(any(ServicePermissionApiQueryDto.class), eq(pageNum), eq(pageSize))).thenReturn(expectedPageInfo);

        // 调用被测试的方法
        PageInfo<PermissionUserInfoApiDto> result = categoryServiceImplUnderTest.queryPermissionUserInfoPageByRole(queryParam, pageNum, pageSize);

        // 验证 Mock 对象的行为
        verify(mockCategoryMapper).getCategoryRoleRelations(queryParam.getCategoryId());
        verify(mockUserInfoApi).queryPermissionUserInfoPage(any(ServicePermissionApiQueryDto.class), eq(pageNum), eq(pageSize));

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.getList().size());
    }

    @Test
    public void testQueryPermissionUserInfoPageByRole_WithNoExistingRelations() {
        // 准备测试数据
        CategoryRoleDto queryParam = new CategoryRoleDto();
        queryParam.setCategoryId(1L);

        ServicePermissionApiQueryDto servicePermissionApiQueryDto = new ServicePermissionApiQueryDto();
        servicePermissionApiQueryDto.setPermissionCodes(new ArrayList<>()); // 初始化为空列表
        queryParam.setServicePermissionApiQueryDto(servicePermissionApiQueryDto);

        Integer pageNum = 1;
        Integer pageSize = 10;

        List<CategoryRoleBean> existingRelations = new ArrayList<>();

        // 设置 Mock 对象的行为
        when(mockCategoryMapper.getCategoryRoleRelations(queryParam.getCategoryId())).thenReturn(existingRelations);

        // 调用被测试的方法
        PageInfo<PermissionUserInfoApiDto> result = categoryServiceImplUnderTest.queryPermissionUserInfoPageByRole(queryParam, pageNum, pageSize);

        // 验证 Mock 对象的行为
        verify(mockCategoryMapper).getCategoryRoleRelations(queryParam.getCategoryId());
        verify(mockUserInfoApi, never()).queryPermissionUserInfoPage(any(ServicePermissionApiQueryDto.class), eq(pageNum), eq(pageSize));

        // 验证结果
        assertNotNull(result);
        assertTrue(result.getList()==null);
    }
}

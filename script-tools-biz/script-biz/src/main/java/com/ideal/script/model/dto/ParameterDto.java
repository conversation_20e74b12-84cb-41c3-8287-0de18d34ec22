package com.ideal.script.model.dto;

import com.ideal.script.common.validation.Create;
import com.ideal.script.common.validation.ExecAuditCreate;
import com.ideal.script.common.validation.Update;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.sql.Timestamp;

/**
 * 【请填写功能名称】对象 ieai_script_parameter
 *
 * <AUTHOR>
 */
public class ParameterDto implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 主键
     */
    @NotNull(groups = {Update.class, ExecAuditCreate.class})
    private Long id;


    /**
     * ieai_script_info_version表的isrcscriptuuid
     */
    @NotNull(groups = ExecAuditCreate.class)
    @Size(min = 1, max = 50,groups = {Create.class, Update.class, ExecAuditCreate.class})
    private String srcScriptUuid;


    /**
     * 参数类型：枚举-Enums  字符串-String  加密-Cipher
     */
    @NotNull(groups = ExecAuditCreate.class)
    @Size(min = 1, max = 20,groups = {Create.class, Update.class, ExecAuditCreate.class})
    private String paramType;


    /**
     * 参数默认值
     */
    @NotNull(groups = ExecAuditCreate.class)
    @Size(max = 200,groups = {Create.class, Update.class, ExecAuditCreate.class})
    private String paramDefaultValue;


    /**
     * 参数描述
     */
    @Size(max = 200,groups = {Create.class, Update.class, ExecAuditCreate.class})
    private String paramDesc;


    /**
     * 参数顺序
     */
    @NotNull(groups = ExecAuditCreate.class)
    private Integer paramOrder;


    /**
     * 校验规则id
     */
    private Long paramCheckIid;


    /**
     * 枚举参数id
     */
    private Long scriptParameterManagerId;


    /**
     * 创建人ID
     */
    private Long creatorId;


    /**
     * 创建人名称
     */
    @Size(min = 1, max = 50, groups = {Create.class, Update.class})
    private String creatorName;


    /**
     * 创建时间
     */
    private Timestamp createTime;

    /**
     * 参数名称
     */
    @Size(max = 100, groups = {Create.class, Update.class})
    private String paramName;

    public String getParamName() {
        return paramName;
    }

    public void setParamName(String paramName) {
        this.paramName = paramName;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setSrcScriptUuid(String srcScriptUuid) {
        this.srcScriptUuid = srcScriptUuid;
    }

    public String getSrcScriptUuid() {
        return srcScriptUuid;
    }

    @SuppressWarnings("unused")
    public void setParamType(String paramType) {
        this.paramType = paramType;
    }

    public String getParamType() {
        return paramType;
    }

    @SuppressWarnings("unused")
    public void setParamDefaultValue(String paramDefaultValue) {
        this.paramDefaultValue = paramDefaultValue;
    }

    public String getParamDefaultValue() {
        return paramDefaultValue;
    }

    @SuppressWarnings("unused")
    public void setParamDesc(String paramDesc) {
        this.paramDesc = paramDesc;
    }

    public String getParamDesc() {
        return paramDesc;
    }

    @SuppressWarnings("unused")
    public void setParamOrder(Integer paramOrder) {
        this.paramOrder = paramOrder;
    }

    public Integer getParamOrder() {
        return paramOrder;
    }

    public Long getParamCheckIid() {
        return paramCheckIid;
    }

    @SuppressWarnings("unused")
    public void setParamCheckIid(Long paramCheckIid) {
        this.paramCheckIid = paramCheckIid;
    }

    @SuppressWarnings("unused")
    public void setScriptParameterManagerId(Long scriptParameterManagerId) {
        this.scriptParameterManagerId = scriptParameterManagerId;
    }

    public Long getScriptParameterManagerId() {
        return scriptParameterManagerId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }

    public Timestamp getCreateTime() {
        return createTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("srcScriptUuid", getSrcScriptUuid())
                .append("paramType", getParamType())
                .append("paramDefaultValue", getParamDefaultValue())
                .append("paramDesc", getParamDesc())
                .append("paramOrder", getParamOrder())
                .append("paramCheckIid", getParamCheckIid())
                .append("scriptParameterManagerId", getScriptParameterManagerId())
                .append("creatorId", getCreatorId())
                .append("creatorName", getCreatorName())
                .append("createTime", getCreateTime())
                .toString();
    }
}

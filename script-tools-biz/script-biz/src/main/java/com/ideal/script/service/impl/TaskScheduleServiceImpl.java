package com.ideal.script.service.impl;

import com.ideal.common.util.BeanUtils;
import com.ideal.common.util.PageDataUtil;
import org.springframework.stereotype.Service;
import com.ideal.script.mapper.TaskScheduleMapper;
import com.ideal.script.model.entity.TaskScheduleEntity;
import com.ideal.script.service.ITaskScheduleService;
import com.ideal.script.model.dto.TaskScheduleDto;
import com.ideal.script.model.dto.TaskScheduleQueryDto;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;

import java.util.List;

/**
 * 【请填写功能名称】Service业务层处理
 *
 * <AUTHOR>
 */
@Service
public class TaskScheduleServiceImpl implements ITaskScheduleService {
    private final  TaskScheduleMapper taskScheduleMapper;

    public TaskScheduleServiceImpl(TaskScheduleMapper taskScheduleMapper) {
        this.taskScheduleMapper = taskScheduleMapper;
    }

    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public TaskScheduleDto selectTaskScheduleById(Long id) {
        TaskScheduleEntity taskSchedule = taskScheduleMapper.selectTaskScheduleById(id);
        return BeanUtils.copy(taskSchedule, TaskScheduleDto.class);
    }

    /**
     * 查询【请填写功能名称】列表
     *
     * @param taskScheduleQueryDto 【请填写功能名称】
     * @param pageNum 页码
     * @param pageSize 单页长度
     * @return 【请填写功能名称】
     */
    @Override
    public PageInfo<TaskScheduleDto> selectTaskScheduleList(TaskScheduleQueryDto taskScheduleQueryDto, Integer pageNum, Integer pageSize) {
        TaskScheduleEntity query = BeanUtils.copy(taskScheduleQueryDto, TaskScheduleEntity.class);
        PageMethod.startPage(pageNum, pageSize);
        List<TaskScheduleEntity> taskScheduleList = taskScheduleMapper.selectTaskScheduleList(query);
        return PageDataUtil.toDtoPage(taskScheduleList, TaskScheduleDto.class);
    }

    /**
     * 新增【请填写功能名称】
     *
     * @param taskScheduleDto 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int insertTaskSchedule(TaskScheduleDto taskScheduleDto) {
        TaskScheduleEntity taskSchedule = BeanUtils.copy(taskScheduleDto, TaskScheduleEntity.class);
        return taskScheduleMapper.insertTaskSchedule(taskSchedule);
    }

    /**
     * 修改【请填写功能名称】
     *
     * @param taskScheduleDto 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int updateTaskSchedule(TaskScheduleDto taskScheduleDto) {
        TaskScheduleEntity taskSchedule = BeanUtils.copy(taskScheduleDto, TaskScheduleEntity.class);
        return taskScheduleMapper.updateTaskSchedule(taskSchedule);
    }

    /**
     * 更新调度任务状态
     *
     * @param taskScheduleDto 【请填写功能名称】
     * @return int 结果
     */
    @Override
    public int updateTaskScheduleByScheduleId(TaskScheduleDto taskScheduleDto) {
        TaskScheduleEntity taskSchedule = BeanUtils.copy(taskScheduleDto, TaskScheduleEntity.class);
        return taskScheduleMapper.updateTaskScheduleByScheduleId(taskSchedule);
    }


    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteTaskScheduleByIds(Long[] ids) {
        return taskScheduleMapper.deleteTaskScheduleByIds(ids);
    }

    /**
     * 获取定时任务信息
     *
     * @param taskId 脚本任务Id
     * @return {@link TaskScheduleDto }
     */
    @Override
    public TaskScheduleDto selectTaskScheduleByTaskId(Long taskId) {
        TaskScheduleEntity taskSchedule = taskScheduleMapper.selectTaskScheduleByTaskId(taskId);
        return BeanUtils.copy(taskSchedule, TaskScheduleDto.class);
    }
}

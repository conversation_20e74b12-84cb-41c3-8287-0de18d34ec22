package com.ideal.script.model.dto;

import com.ideal.script.common.validation.Create;
import com.ideal.script.common.validation.Update;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.sql.Timestamp;

/**
 * 【请填写功能名称】对象 ieai_script_parameter_manager
 *
 * <AUTHOR>
 */
public class ParameterManagerDto implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 主键
     */
    @NotNull(groups = Update.class)
    private Long id;


    /**
     * 参数名
     */
    @Size(min = 1, max = 30,groups = {Create.class, Update.class})
    private String paramName;


    /**
     * 参数值
     */
    @Size(min = 1, max = 255,groups = {Create.class, Update.class})
    private String paramValue;


    /**
     * 参数描述
     */
    @Size(min = 1, max = 200,groups = {Create.class, Update.class})
    private String paramDesc;


    /**
     * 适用范围
     */
    @Size(min = 1, max = 30,groups = {Create.class, Update.class})
    private String scope;


    /**
     * 创建人id
     */
    private Long creatorId;


    /**
     * 创建人名称
     */
    @Size(min = 1, max = 50,groups = {Create.class, Update.class})
    private String creatorName;


    /**
     * 修改人id
     */
    private Long updatorId;


    /**
     * 修改人名称
     */
    @Size(min = 1, max = 50,groups = {Create.class, Update.class})
    private String updatorName;


    /**
     * 创建时间
     */
    private Timestamp createTime;


    /**
     * 修改时间
     */
    private Timestamp updateTime;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    @SuppressWarnings("unused")
    public void setParamName(String paramName) {
        this.paramName = paramName;
    }

    public String getParamName() {
        return paramName;
    }

    @SuppressWarnings("unused")
    public void setParamValue(String paramValue) {
        this.paramValue = paramValue;
    }

    public String getParamValue() {
        return paramValue;
    }

    @SuppressWarnings("unused")
    public void setParamDesc(String paramDesc) {
        this.paramDesc = paramDesc;
    }

    public String getParamDesc() {
        return paramDesc;
    }

    @SuppressWarnings("unused")
    public void setScope(String scope) {
        this.scope = scope;
    }

    public String getScope() {
        return scope;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setUpdatorId(Long updatorId) {
        this.updatorId = updatorId;
    }

    public Long getUpdatorId() {
        return updatorId;
    }

    public void setUpdatorName(String updatorName) {
        this.updatorName = updatorName;
    }

    public String getUpdatorName() {
        return updatorName;
    }

    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }

    public Timestamp getCreateTime() {
        return createTime;
    }

    public void setUpdateTime(Timestamp updateTime) {
        this.updateTime = updateTime;
    }

    public Timestamp getUpdateTime() {
        return updateTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("paramName", getParamName())
                .append("paramValue", getParamValue())
                .append("paramDesc", getParamDesc())
                .append("scope", getScope())
                .append("creatorId", getCreatorId())
                .append("creatorName", getCreatorName())
                .append("updatorId", getUpdatorId())
                .append("updatorName", getUpdatorName())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}

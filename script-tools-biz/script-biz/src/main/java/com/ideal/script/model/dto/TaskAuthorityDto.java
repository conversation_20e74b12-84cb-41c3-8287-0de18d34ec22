package com.ideal.script.model.dto;

import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * 任务权限DTO，用于对脚本任务的权限进行管理，包括任务启用、任务禁用和任务删除操作
 * <AUTHOR>
 */
public class TaskAuthorityDto implements Serializable {

    //======================脚本任务主表属性开始==================
    /**
     * 脚本服务化基础信息表主键
     */
    private Long scriptInfoId;

    /**
     * 脚本服务化基础信息表唯一uuid
     */
    private String uniqueUuid;

    /**
     * 脚本中文名称
     */
    private String scriptNameZh;

    /**
     * 脚本名称
     */
    private String scriptName;

    /**
     * 脚本类型(shell、bat、perl、python、powershell)
     */
    private String scriptType;

    /**
     * 脚本的执行用户
     */
    private String execuser;

    /**
     * 标签
     */
    private String scriptLabel;

    /**
     * 分类id
     */
    private Long categoryId;

    /**
     * 分类名称
     */
    private String scriptCategoryName;

    /**
     * 运行前校验 1- 校验 0 - 不校验
     */
    private Integer checkBeforeExec;


    /**
     * 是否共享  1 - 已共享 0 - 未共享
     */
    private Integer share;


    /**
     * 脚本类别 0 - 脚本   1 - 白名单命令
     */
    private Integer whiteCommand;


    /**
     * 白名单命令可见类型  0 - 公有 1 - 私有
     */
    private Integer visibleType;


    /**
     * 风险级别  0 - 白名单  1 - 高级风险  2 - 中级风险  3 - 低级风险
     */
    private Integer level;


    /**
     * 适用平台  数据来源于ieai_platform_code表
     */
    private String platform;


    /**
     * 是否应急  0 - 否 1 - 是
     */
    private Integer femscript;
    //======================脚本任务主表属性结束==================

    // =====================版本属性开始==========================
    /**
     * 脚本版本表主键
     */
    private Long scriptInfoVersionId;
    /**
     * 脚本版本表主键(批量)
     */
    private Long[] scriptInfoVersionIds;
    /**
     * 每个版本的uuid
     */
    private String srcScriptUuid;


    /**
     * 版本号
     */
    private String version;


    /**
     * 使用状态(0禁用、1启用)(禁用版本)
     */
    private Integer useState;


    /**
     * 是否默认
     */
    private Integer sDefault;


    /**
     * 脚本功能描述
     */
    private String description;


    /**
     * 超时秒
     */
    private Integer timeout;


    /**
     * 预期类型  1 - lastLine  2 - exitCode
     */
    private Integer expectType;


    /**
     * 预期结果值
     */
    private String expectLastline;


    /**
     * 是否有参数标志 0 - 无参数 1 - 有参数
     */
    private Integer paramflag;
    // =========================版本属性结束========================


    /**
     * 创建人id
     */
    private Long creatorId;


    /**
     * 创建人名称
     */
    private String creatorName;


    /**
     * 修改人id
     */
    private Long updatorId;


    /**
     * 修改人名称
     */
    private String updatorName;


    /**
     * 创建时间
     */
    private Timestamp createTime;


    /**
     * 修改时间
     */
    private Timestamp updateTime;

    /**
     * 编辑状态(0草稿、1发布)
     */
    private Integer editState;

    /**
     * 是否删除
     */
    private Integer deleted;

    public Long getScriptInfoId() {
        return scriptInfoId;
    }

    @SuppressWarnings("unused")
    public void setScriptInfoId(Long scriptInfoId) {
        this.scriptInfoId = scriptInfoId;
    }

    public String getUniqueUuid() {
        return uniqueUuid;
    }

    public void setUniqueUuid(String uniqueUuid) {
        this.uniqueUuid = uniqueUuid;
    }

    public String getScriptNameZh() {
        return scriptNameZh;
    }

    public void setScriptNameZh(String scriptNameZh) {
        this.scriptNameZh = scriptNameZh;
    }

    public String getScriptName() {
        return scriptName;
    }

    public void setScriptName(String scriptName) {
        this.scriptName = scriptName;
    }

    public String getScriptType() {
        return scriptType;
    }

    public void setScriptType(String scriptType) {
        this.scriptType = scriptType;
    }

    public String getExecuser() {
        return execuser;
    }

    public void setExecuser(String execuser) {
        this.execuser = execuser;
    }

    public String getScriptLabel() {
        return scriptLabel;
    }

    @SuppressWarnings("unused")
    public void setScriptLabel(String scriptLabel) {
        this.scriptLabel = scriptLabel;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public Integer getCheckBeforeExec() {
        return checkBeforeExec;
    }

    @SuppressWarnings("unused")
    public void setCheckBeforeExec(Integer checkBeforeExec) {
        this.checkBeforeExec = checkBeforeExec;
    }

    public Integer getShare() {
        return share;
    }

    @SuppressWarnings("unused")
    public void setShare(Integer share) {
        this.share = share;
    }

    public Integer getWhiteCommand() {
        return whiteCommand;
    }

    public void setWhiteCommand(Integer whiteCommand) {
        this.whiteCommand = whiteCommand;
    }

    public Integer getVisibleType() {
        return visibleType;
    }

    @SuppressWarnings("unused")
    public void setVisibleType(Integer visibleType) {
        this.visibleType = visibleType;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public Integer getFemscript() {
        return femscript;
    }

    @SuppressWarnings("unused")
    public void setFemscript(Integer femscript) {
        this.femscript = femscript;
    }

    public Long getScriptInfoVersionId() {
        return scriptInfoVersionId;
    }

    @SuppressWarnings("unused")
    public void setScriptInfoVersionId(Long scriptInfoVersionId) {
        this.scriptInfoVersionId = scriptInfoVersionId;
    }

    public String getSrcScriptUuid() {
        return srcScriptUuid;
    }

    public void setSrcScriptUuid(String srcScriptUuid) {
        this.srcScriptUuid = srcScriptUuid;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public Integer getUseState() {
        return useState;
    }

    @SuppressWarnings("unused")
    public void setUseState(Integer useState) {
        this.useState = useState;
    }

    public Integer getsDefault() {
        return sDefault;
    }

    @SuppressWarnings("unused")
    public void setsDefault(Integer sDefault) {
        this.sDefault = sDefault;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getTimeout() {
        return timeout;
    }

    public void setTimeout(Integer timeout) {
        this.timeout = timeout;
    }

    public Integer getExpectType() {
        return expectType;
    }

    public void setExpectType(Integer expectType) {
        this.expectType = expectType;
    }


    @SuppressWarnings("unused")

    public String getExpectLastline() {
        return expectLastline;
    }

    public void setExpectLastline(String expectLastline) {
        this.expectLastline = expectLastline;
    }

    public Integer getParamflag() {
        return paramflag;
    }

    @SuppressWarnings("unused")
    public void setParamflag(Integer paramflag) {
        this.paramflag = paramflag;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public Long getUpdatorId() {
        return updatorId;
    }

    public void setUpdatorId(Long updatorId) {
        this.updatorId = updatorId;
    }

    public String getUpdatorName() {
        return updatorName;
    }

    public void setUpdatorName(String updatorName) {
        this.updatorName = updatorName;
    }

    public Timestamp getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }

    public Timestamp getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Timestamp updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getEditState() {
        return editState;
    }

    public void setEditState(Integer editState) {
        this.editState = editState;
    }

    public Integer getDeleted() {
        return deleted;
    }

    @SuppressWarnings("unused")
    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    @SuppressWarnings("unused")
    public String getScriptCategoryName() {
        return scriptCategoryName;
    }

    @SuppressWarnings("unused")
    public void setScriptCategoryName(String scriptCategoryName) {
        this.scriptCategoryName = scriptCategoryName;
    }

    public Long[] getScriptInfoVersionIds() {
        return scriptInfoVersionIds;
    }

    public void setScriptInfoVersionIds(Long[] scriptInfoVersionIds) {
        this.scriptInfoVersionIds = scriptInfoVersionIds;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this)
                .append("scriptInfoId", getScriptInfoId())
                .append("uniqueUuid", getUniqueUuid())
                .append("scriptNameZh", getScriptNameZh())
                .append("scriptName", getScriptName())
                .append("scriptType", getScriptType())
                .append("execuser", getExecuser())
                .append("scriptLabel", getScriptLabel())
                .append("categoryId", getCategoryId())
                .append("scriptCategoryName", getScriptCategoryName())
                .append("checkBeforeExec", getCheckBeforeExec())
                .append("share", getShare())
                .append("whiteCommand", getWhiteCommand())
                .append("visibleType", getVisibleType())

                .append(basicInfoToString())
                .toString();
    }

    private String basicInfoToString() {
        return new ToStringBuilder(this)
                .append("level", getLevel())
                .append("platform", getPlatform())
                .append("femscript", getFemscript())
                .append("scriptInfoVersionId", getScriptInfoVersionId())
                .append("srcScriptUuid", getSrcScriptUuid())
                .append("version", getVersion())
                .append("useState", getUseState())
                .append("sDefault", getsDefault())
                .append("description", getDescription())
                .append("timeout", getTimeout())
                .append("expectType", getExpectType())
                .append("expectLastline", getExpectLastline())
                .append("paramflag", getParamflag())
                .append("creatorId", getCreatorId())
                .append("creatorName", getCreatorName())
                .append("updatorId", getUpdatorId())
                .append("updatorName", getUpdatorName())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .append("editState", getEditState())
                .append("deleted", getDeleted())
                .toString();
    }

}

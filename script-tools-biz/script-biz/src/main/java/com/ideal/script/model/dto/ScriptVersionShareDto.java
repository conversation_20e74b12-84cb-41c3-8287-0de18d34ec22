package com.ideal.script.model.dto;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * 【脚本共享表】对象 ieai_script_share_relation
 *
 * <AUTHOR>
 */
public class ScriptVersionShareDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long iid;

    /**
     * 共享类型 0-用户，1部分，2所有人，3角色
     */
    private Short shareType;


    /**
     * 脚本infoId
     */
    private Long scriptInfoId;

    /**
     * 共享目标id（根据ishare_type的值不同表示的值不同，部门id，用户id，-1为共享给所有人）
     */
    private String shareObjectId;

    /**
     * 对象名称
     */
    private String shareObjectName;

    /**
     * 创建时间
     */
    private Timestamp createdTime;

    /**
     * 用户全名
     */
    private String fullName;

    /**
     * 脚本中文名
     */
    private String scriptNameZh;


    public String getShareObjectName() {
        return shareObjectName;
    }

    public void setShareObjectName(String shareObjectName) {
        this.shareObjectName = shareObjectName;
    }

    public String getScriptNameZh() {
        return scriptNameZh;
    }

    public void setScriptNameZh(String scriptNameZh) {
        this.scriptNameZh = scriptNameZh;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public Long getIid() {
        return iid;
    }

    public void setIid(Long iid) {
        this.iid = iid;
    }

    public Short getShareType() {
        return shareType;
    }

    public void setShareType(Short shareType) {
        this.shareType = shareType;
    }

    public Long getScriptInfoId() {
        return scriptInfoId;
    }

    public void setScriptInfoId(Long scriptInfoId) {
        this.scriptInfoId = scriptInfoId;
    }

    public String getShareObjectId() {
        return shareObjectId;
    }

    public void setShareObjectId(String shareObjectId) {
        this.shareObjectId = shareObjectId;
    }

    public Timestamp getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(Timestamp createdTime) {
        this.createdTime = createdTime;
    }
}

package com.ideal.script.common.constant.enums;

/**
 * 枚举类
 *
 * <AUTHOR>
 */
public class Enums {
    /**
     * NULL空值
     * <AUTHOR>
     */
    public enum NullValue {
        /**
         * NULL
         */
        NULL("null");

        private final String value;

        NullValue(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }
    /**
     * XmlTags
     */
    public enum XmlTags{
        /**
         * NEW_TAB_LINK
         */
        NEW_TAB_LINK("<a target=\"_blank\">"),
        A_TAG("</a>");
        private final String value;

        XmlTags(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }
    /**
     * ContentFormatter
     */
    public enum ContentFormatter{
        /**
         * SCRIPT_CONTENT_FORMATTER
         */
        SCRIPT_CONTENT_FORMATTER("scriptTaskContentFormatter");
        private final String value;

        ContentFormatter(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 适用平台
     *
     * <AUTHOR>
     */
    public enum PlatForm {
        /**
         * HP_UX
         */
        HP_UX("HP-UX"),
        /**
         * UNIX
         */
        UNIX("Unix"),
        SUSE("SUSE"),
        REDHAT("RedHat"),
        CENTOS("CentOS"),
        LINUX("Linux"),
        WINDOWS("Windows"),
        ORACLE_LINUX("OracleLinux");


        private final String value;

        PlatForm(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * <AUTHOR>
     */
    public enum ReadyToExecute {
        /**
         * 任务可执行（审核通过的）
         */
        READY_TO_EXECUTE(1, "可执行"),
        /**
         * 任务还不具备执行条件（还未审核通过、未审批的任务）
         */
        NOT_READY_TO_EXECUTE(0, "不可执行"),
        /**
         * 待执行（审核通过的）、还未执行的。待执行Tab页面点击取消操作。
         */
        CANCELED(2, "取消"),
        /**
         * 待执行tab页，已经点过了执行。设置脚本任务状态为已执行
         */
        EXECUTED(3, "已执行");

        private final int code;
        private final String description;

        ReadyToExecute(int code, String description) {
            this.code = code;
            this.description = description;
        }

        public int getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 脚本下发状态
     *
     * <AUTHOR>
     */
    public enum IssuerecordState {
        /**
         * 成功
         */
        SUCCESS(1, "下发成功"),
        RUNNING(2, "下发中"),
        EXCEPTION(3, "下发异常"),
        MD5_MISMATCH(4, "MD5不一致");

        private final Integer value;
        private final String description;

        IssuerecordState(Integer value, String description) {
            this.value = value;
            this.description = description;
        }

        public Integer getValue() {
            return value;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 审批服务、审核结果状态
     *
     * <AUTHOR>
     */
    public enum ApprovalProcessAudiState {

        /**
         * 提交
         */
        SUBMIT(0, "提交"),
        /**
         * 通过
         */
        PASS(1, "通过"),
        REPULSE(2, "打回"),
        WITHDRAW(3, "撤销"),
        TERMINATION(4, "终止"),
        FORWARD(5, "转发"),
        REISSUE(6, "重新转发");

        private final Integer value;
        private final String description;

        ApprovalProcessAudiState(Integer value, String description) {
            this.value = value;
            this.description = description;
        }

        public Integer getValue() {
            return value;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 状态码
     *
     * <AUTHOR>
     */
    public enum HttpStatusCode {

        /**
         * 成功
         */
        OK(200, "OK"),
        /**
         * 未找到
         */
        NOT_FOUND(404, "Not Found");

        private final Integer code;
        private final String message;

        HttpStatusCode(Integer code, String message) {
            this.code = code;
            this.message = message;
        }

        public Integer getCode() {
            return code;
        }

        public String getMessage() {
            return message;
        }
    }

    /**
     * 定时任务调度状态
     *
     * <AUTHOR>
     */
    public enum TaskScheduleEnum {
        /**
         * 0:初始
         */
        TASK_INIT(0, "初始"),
        /**
         * 1:运行
         */
        TASK_RUNNING(1, "运行"),

        /**
         * 2:终止
         */
        TASK_KILLED(2, "终止");

        private final Integer value;
        private final String description;

        TaskScheduleEnum(Integer value, String description) {
            this.value = value;
            this.description = description;
        }

        public Integer getValue() {
            return value;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 前缀枚举类
     */
    public enum PrefixEnum {
        /**
         * 投产介质前缀
         */
        PRODUCTION_PREFIX("投产介质_");
        // 其他前缀

        private final String prefix;

        PrefixEnum(String prefix) {
            this.prefix = prefix;
        }

        public String getPrefix() {
            return prefix;
        }
    }

    /**
     * 后缀枚举类
     */
    public enum FileExtensionEnum {
        /**
         * zip后缀
         */
        ZIP(".zip"),
        JSON(".json"),
        FOLDER_SUFFIX("_attachment");
        // 其他后缀

        private final String value;

        FileExtensionEnum(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 脚本执行期待结果
     */
    public enum ScriptExpectation {

        /**
         *
         */
        EXPECT_LAST_LINE("expectLastline"),
        ERROR_EXPECT_LAST_LINE("errorExpectLastline");

        private final String value;

        ScriptExpectation(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 参数类型 IN- 输入参数 、OUT- 输出参数
     */
    public enum ParameterType {

        /**
         * 输入
         */
        IN("IN-", "输入参数"),
        OUT("OUT-", "输出参数");

        private final String value;
        private final String description;

        ParameterType(String value, String description) {
            this.value = value;
            this.description = description;
        }

        public String getValue() {
            return value;
        }

        public String getDescription() {
            return description;
        }
    }



    /**
     * 预期类型
     */
    public enum ExpectType {
        /**
         * 末行输出
         */
        LAST_LINE(1, "末行输出"),
        EXIT_CODE(2, "退出码");
        private final Integer value;
        private final String description;

        ExpectType(Integer value, String description) {
            this.value = value;
            this.description = description;
        }

        public Integer getValue() {
            return value;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 脚本类型
     */
    public enum ScriptType {

        /**
         * BAT
         */
        BAT("bat", "bat"),
        PERL("perl", "perl"),
        PYTHON("python", "py"),
        POWER_SHELL("powershell", "ps1"),
        SHELL("shell", "sh"),
        SQL("sql", "sql");

        private final String value;
        private final String description;

        ScriptType(String value, String description) {
            this.value = value;
            this.description = description;
        }

        public String getValue() {
            return value;
        }

        public String getDescription() {
            return description;
        }
    }


    /**
     * 脚本任务来源 1：任务申请  2：脚本测试
     */
    public enum TaskSource {

        /**
         * 任务申请
         */
        TASK_APPLICATION(1, "任务申请"),
        SCRIPT_TESTING(2, "脚本测试");

        private final Integer value;
        private final String description;

        TaskSource(Integer value, String description) {
            this.value = value;
            this.description = description;
        }

        public Integer getValue() {
            return value;
        }

        public String getDescription() {
            return description;
        }
    }


    /**
     * 驱动模式：1-按选择执行；2-分批执行；3-忽略异常分批执行；4-队列执行
     */
    public enum DriverModel {

        /**
         * 按选择执行
         */
        SELECTIVE(1, "按选择执行"),
        BATCH_EXEC(2, "分批执行"),
        IGNORE_ERROR_BATCH_EXEC(3, "忽略异常分批执行"),
        QUEUE_EXEC(4, "队列执行");


        private final Integer value;
        private final String description;

        DriverModel(Integer value, String description) {
            this.value = value;
            this.description = description;
        }

        public Integer getValue() {
            return value;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 任务类型 1:普通  2：白名单
     */
    public enum Type {

        /**
         * 普通
         */
        NORMAL(1, "普通"),
        WHITE_LIST(2, "白名单");


        private final Integer value;
        private final String description;

        Type(Integer value, String description) {
            this.value = value;
            this.description = description;
        }

        public Integer getValue() {
            return value;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 任务调度方式 2-定时 1-周期 0-触发
     */
    public enum TaskScheduler {

        /**
         * 触发
         */
        TRIGGER(0, "触发"),

        /**
         * 周期
         */
        PERIODIC(1, "周期"),
        /**
         * 定时
         */
        TIMED(2, "定时");

        private final Integer value;
        private final String description;

        TaskScheduler(Integer value, String description) {
            this.value = value;
            this.description = description;
        }

        public Integer getValue() {
            return value;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 审核类型 1脚本发布（编写脚本发布） 2脚本删除（删除审核） 3脚本任务进行申请（执行审核）
     */
    public enum AuditType {

        /**
         * 脚本发布（发布审核）
         */
        SCRIPT_PUBLISH(1, "脚本发布（发布审核）"),
        /**
         * 脚本删除（删除审核）
         */
        SCRIPT_DELETE(2, "脚本删除（删除审核）"),
        /**
         * 脚本任务（执行审核）
         */
        SCRIPT_TASK(3, "脚本任务（执行审核）");

        private final Integer value;
        private final String description;

        AuditType(Integer value, String description) {
            this.value = value;
            this.description = description;
        }

        public Integer getValue() {
            return value;
        }

        public String getDescription() {
            return description;
        }

    }

    /**
     * 审核状态
     */
    public enum AuditState {
        /**
         * 审批中
         */
        APPROVING(1, "审批中"),
        /**
         * 已通过
         */
        APPROVED(2, "已通过"),
        /**
         * 已退回
         */
        REJECTED(3, "已退回");
        private final Integer value;
        private final String description;

        AuditState(Integer value, String description) {
            this.value = value;
            this.description = description;
        }

        public Integer getValue() {
            return value;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 是否已经执行
     */
    public enum ExecutionStatus {
        /**
         * 否
         */
        NOT_EXECUTED(0, "否"),
        /**
         * 是
         */
        EXECUTED(1, "是");

        private final int value;
        private final String description;

        ExecutionStatus(int value, String description) {
            this.value = value;
            this.description = description;
        }

        public int getValue() {
            return value;
        }

        public String getDescription() {
            return description;
        }

    }

    /**
     * 业务类型 0 - 脚本服务化 8 - 标准运维
     */
    public enum BusinessType {
        /**
         * 脚本服务化
         */
        SCRIPT_SERVICE(0, "脚本服务化"),
        /**
         * 标准运维
         */
        STANDARD_OPERATION(8, "标准运维");

        private final int value;
        private final String description;

        BusinessType(int value, String description) {
            this.value = value;
            this.description = description;
        }

        public int getValue() {
            return value;
        }

        public String getDescription() {
            return description;
        }

    }

    /**
     * 脚本任务运行实例状态 10-运行20-完成  30-异常 60-终止 11-部分运行
     */
    public enum TaskInstanceStatus {
        /**
         * 运行
         */
        RUNNING(10, "运行"),
        /**
         * 部分运行
         * <pre>
         * 当本次勾选agent执行，这次勾选的agent数量超过了，最大并发数，这个状态才是部分运行。
         * 现在不需要这个概念了。没有并发数。每次都是将勾选的agent全部发送给管理服务，
         * 让这些agent去执行脚本任务。
         * </pre>
         */
        PARTIAL_RUNNING(11, "部分运行"),
        /**
         * 完成（都正常执行完成）,绿色
         */
        COMPLETED(20, "完成"),
        /**
         * 完成（包含略过或者终止的agent实例），红色
         */
        COMPLETED_RED(70, "完成"),
        /**
         * 异常
         */
        EXCEPTION(30, "异常"),
        /**
         * 终止
         */
        TERMINATED(60, "终止");

        private final int value;
        private final String description;

        TaskInstanceStatus(int value, String description) {
            this.value = value;
            this.description = description;
        }

        public int getValue() {
            return value;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * agent运行实例 -状态 5-跳过 10-运行中 20-完成 30-失败 60-终止
     *
     * <AUTHOR>
     */
    public enum TaskRuntimeState {
        /**
         * 跳过
         */
        SKIP(5, "跳过"),
        /**
         * 运行中
         */
        RUNNING(10, "运行中"),
        /**
         * 完成
         */
        COMPLETED(20, "完成"),
        /**
         * 失败
         */
        FAILED(30, "失败"),
        /**
         * 终止
         */
        TERMINATED(60, "终止"),
        /**
         * 异常
         */
        SCRIPT_FAIL_STATE(30, "异常");

        private final int value;
        private final String description;

        TaskRuntimeState(int value, String description) {
            this.value = value;
            this.description = description;
        }

        public int getValue() {
            return value;
        }

        public String getDescription() {
            return description;
        }
    }


    /**
     * 脚本环境 0：测试  1：生成
     *
     * <AUTHOR>
     */
    public enum ScriptEnvironment {
        /**
         * 测试
         */
        SCRIPT_ENV_TEST(0, "测试"),
        /**
         * 生产
         */
        SCRIPT_ENV_PROD(1, "生产");

        private final int value;

        private final String description;

        ScriptEnvironment(int value, String description) {
            this.value = value;
            this.description = description;
        }

        public int getValue() {
            return value;
        }

        public String getDescription() {
            return description;
        }
    }


    /**
     * 异常消息
     */
    public enum ExpectionMessage {
        /**
         * agent接收异常
         */
        AGENT_ERROR_MESSAGE(0, "agent发送异常");

        private final int value;

        private final String description;

        ExpectionMessage(int value, String description) {
            this.value = value;
            this.description = description;
        }

        public int getValue() {
            return value;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 启动来源
     */
    public enum ScriptFrom {

        /**
         * 正常执行
         */
        SCRIPT("script", "正常执行"),
        /**
         * 脚本测试
         */
        SCRIPT_TEST("scriptTest", "脚本测试");

        private final String value;
        private final String description;

        ScriptFrom(String value, String description) {
            this.value = value;
            this.description = description;
        }

        public String getValue() {
            return value;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 分隔符
     */
    public enum Separator {
        /**
         * 分隔符
         */
        DASH("-");

        private final String value;

        Separator(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    public enum ScriptParamType {
        /**
         * 字符串
         */
        STRING("String"),
        /**
         * 加密
         */
        CIPHER("Cipher"),
        /**
         * 枚举
         */
        ENUMS("Enums");

        private final String value;

        ScriptParamType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    public enum ProductionFile {
        /**
         * 文件名称
         */
        FILENAME("zipFile"),
        /**
         * 加密
         */
        SOURCEFOLDER("sourceFolder"),
        /**
         * 枚举
         */
        ZIPNAME("zipName");

        private final String value;

        ProductionFile(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

    }

    public enum ScriptExceptionMessage {
        /**
         * 任务提交相关错误
         */
        DOUBLE_CHECK_SUBMISSION_ERROR("double.check.submission.error");

        private final String value;

        ScriptExceptionMessage(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

    }

    /**
     * agent执行相关标识
     */
    public enum AgentExecRunFlag {
        /**
         * 重试
         */
        RETRY("agent-script-retry-");

        private final String value;

        AgentExecRunFlag(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

    }

    /**
     * 脚本风险等级
     */
    public enum ScriptLevel {

        /**
         * 白名单
         */
        WHITE_SCRIPT(0),
        /**
         * 高风险脚本
         */
        HIGH_RISK(1),
        /**
         * 中风险脚本
         */
        MEDIUM_RISK(2),
        /**
         * 低风险脚本
         */
        LOW_RISK(3);

        private final Integer value;

        ScriptLevel(Integer value) {
            this.value = value;
        }

        public Integer getValue() {
            return value;
        }
    }

    /**
     * itsm接口返回值码值
     */
    public enum ItsmWorkOrderResultCode {

        /**
         * 返回结果正常
         */
        RIGHT_RESULT_CODE("0","返回结果正常标识"),
        /**
         * 参数异常标识
         */
        PARAMS_ERROR_CODE("9900","参数异常"),
        /**
         * 签名校验异常
         */
        SIGN_ERROR_CODE("9901","签名校验异常"),
        /**
         * 请求数据转换异常
         */
        RESPONSE_DATA_ERROR_CODE("9902","请求数据转换异常"),
        /**
         * 脚本服务化模块码值
         */
        SCRIPT_FLAG("02","脚本服务化码值"),
        /**
         * 创建工单接口标识(生成工单接口)
         */
        CREATE_FLAG("true","创建工单"),
        /**
         * 校验工单接口标识(校验工单接口)
         */
        CHECK_FLAG("false","校验工单"),
        /**
         * 调用获取单号接口，校验手机号异常标识
         */
        ORDER_TELEPHONE_ERROR("-1","手机号异常");


        private final String value;
        private final String description;

        ItsmWorkOrderResultCode(String value, String description) {
            this.value = value;
            this.description = description;
        }

        public String getValue() {
            return value;
        }

        public String getDescription() {
            return description;
        }
    }


    public enum ScriptStartType {
        //0脚本服务化，1工具箱，2定时任务
        SCRIPT("scriptTypeService","0"),
        TOOLS("toolsScriptTypeService","1"),
        TIMETASK("timeTaskScriptTypeService","2");

        private final String value;
        private final String startValue;

        ScriptStartType(String value,String startValue) {
            this.value = value;
            this.startValue = startValue;
        }

        public String getValue() {
            return value;
        }

        public String getStartValue() {
            return startValue;
        }

        /**
         * 根据startValue获取对应的value值
         * @param startValue 启动类型值
         * @return 对应的value值，如果未找到返回null
         */
        public static String getValueByStartValue(String startValue) {
            for (ScriptStartType type : ScriptStartType.values()) {
                if (type.getStartValue().equals(startValue)) {
                    return type.getValue();
                }
            }
            return null;
        }
    }



}

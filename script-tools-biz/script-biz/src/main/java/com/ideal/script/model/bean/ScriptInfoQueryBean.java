package com.ideal.script.model.bean;

import com.ideal.script.dto.CurrentUserDto;

import java.io.Serializable;
import java.util.List;

/**
 * 脚本信息查询Dto
 *
 * <AUTHOR>
 */
public class ScriptInfoQueryBean implements Serializable {

    //=================脚本基本信息=================
    /**
     * 脚本服务化基础信息表主键
     */
    private Long scriptInfoId;
    /**
     * 脚本中文名称
     */
    private String scriptNameZh;

    /**
     * 脚本名称
     */
    private String scriptName;

    /**
     * 脚本类型(shell、bat、perl、python、powershell)
     */
    private String scriptType;

    /**
     * 标签
     */
    private String scriptLabel;

    /**
     * 分类id
     */
    private Long categoryId;

    /**
     * 适用平台  数据来源于ieai_platform_code表
     */
    private String platform;

    /**
     * ieai_script_info表的唯一uuid
     */
    private String infoUniqueUuid;


    /**
     * 脚本来源 0：脚本服务化，1：工具箱
     */
    private Integer scriptSource;

    /**
     * 用户部门权限orgCode
     */

    private String sysOrgCode;

    /**
     * 分类全路径
     */
    private String categoryPath;

    /**
     * 编辑状态(0-草稿,1-发布)
     */
    private Integer editState;

    /**
     * 创建人名称
     */
    private String creatorName;

    //===================脚本版本信息=====================
    /**
     * 脚本版本表主键
     */
    private Long scriptInfoVersionId;

    /**
     * 每个版本的uuid
     */
    private String srcScriptUuid;


    //其他
    /**
     * 是否查询可用所有版本(默认为不查所有版本)
     */
    private Boolean allVersions;


    /**
     * 是否排除默认版本，（true - 排除默认版本，false - 不排除）当allVersions存在时，此字段不允许生效（流程编排需要）
     */
    private Boolean excludeDefault;

    /**
     * 排除的脚本版本id
     */
    private List<Long> excludeVersionIdList;


    /**
     * 处理路径中存在% _时的拼接路径
     */
    private String escapedLikeCategoryPath;

    /**
     * 是否包含草稿脚本信息 true - 包含草稿 false - 不包含草稿
     */
    private Boolean draftFlag;


    /**
     * 是否为dubbo接口调用
     */
    private Boolean dubboFlag = true;

    /**
     * 脚本分类绑定了部门筛选出来的分类路径列表
     */
    private List<String> orgCategoryPath;

    /**
     * 用户信息实体类
     */
    private CurrentUserDto currentUserDto;
    /**
     * 页面来源
     */
    private String from;

    //==================其他信息========================
    /**
     * 我的脚本筛选权限（creator:按照创建用户，dept:按照部门和分类）
     */
    private String permissionPolicy;

    /**
     * 关键词
     */
    private String keyword;

    /**
     * 是否是超级用户标识，true为超级用户，false不是，默认false
     */
    private boolean superUser = false;

    /**
     * 权限人类型：1查询所有，2走用户-部门分类的用户权限，3走“用户-部门分类”、角色-部门分类 的部门权限 或者 “走角色-部门分类” 的 角色权限
     * 说明一点，因为不管根据部门查询还是根据角色查询，最后的sql都是查询部门或者角色绑定的分类路径，所以区别就是整理数据时一个根据部门查分类，一个根据角色查分类
     */
    private Integer permissionType = 1;

    public Integer getPermissionType() {
        return permissionType;
    }

    public void setPermissionType(Integer permissionType) {
        this.permissionType = permissionType;
    }
    /*
     * 脚本级别，0白名单，1高风险，2中风险，3低风险
     */
    private Integer scriptLevel;

    public Integer getScriptLevel() {
        return scriptLevel;
    }

    public void setScriptLevel(Integer scriptLevel) {
        this.scriptLevel = scriptLevel;
    }

    public boolean isSuperUser() {
        return superUser;
    }

    public void setSuperUser(boolean superUser) {
        this.superUser = superUser;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public String getPermissionPolicy() {
        return permissionPolicy;
    }

    public void setPermissionPolicy(String permissionPolicy) {
        this.permissionPolicy = permissionPolicy;
    }

    public String getFrom() {
        return from;
    }

    public void setFrom(String from) {
        this.from = from;
    }

    public CurrentUserDto getCurrentUserDto() {
        return currentUserDto;
    }

    public void setCurrentUserDto(CurrentUserDto currentUserDto) {
        this.currentUserDto = currentUserDto;
    }

    public List<String> getOrgCategoryPath() {
        return orgCategoryPath;
    }

    public void setOrgCategoryPath(List<String> orgCategoryPath) {
        this.orgCategoryPath = orgCategoryPath;
    }

    public Long getScriptInfoId() {
        return scriptInfoId;
    }

    public void setScriptInfoId(Long scriptInfoId) {
        this.scriptInfoId = scriptInfoId;
    }

    public Boolean getDubboFlag() {
        return dubboFlag;
    }

    public void setDubboFlag(Boolean dubboFlag) {
        this.dubboFlag = dubboFlag;
    }


    public Boolean getDraftFlag() {
        return draftFlag;
    }

    public void setDraftFlag(Boolean draftFlag) {
        this.draftFlag = draftFlag;
    }

    public Integer getScriptSource() {
        return scriptSource;
    }

    public void setScriptSource(Integer scriptSource) {
        this.scriptSource = scriptSource;
    }

    public String getScriptNameZh() {
        return scriptNameZh;
    }

    public void setScriptNameZh(String scriptNameZh) {
        this.scriptNameZh = scriptNameZh;
    }

    public String getScriptName() {
        return scriptName;
    }

    public void setScriptName(String scriptName) {
        this.scriptName = scriptName;
    }

    public String getScriptType() {
        return scriptType;
    }

    public void setScriptType(String scriptType) {
        this.scriptType = scriptType;
    }

    public String getScriptLabel() {
        return scriptLabel;
    }

    public void setScriptLabel(String scriptLabel) {
        this.scriptLabel = scriptLabel;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public Boolean getAllVersions() {
        return allVersions;
    }

    public void setAllVersions(Boolean allVersions) {
        this.allVersions = allVersions;
    }

    public Boolean getExcludeDefault() {
        return excludeDefault;
    }

    public void setExcludeDefault(Boolean excludeDefault) {
        this.excludeDefault = excludeDefault;
    }

    public String getSrcScriptUuid() {
        return srcScriptUuid;
    }

    public void setSrcScriptUuid(String srcScriptUuid) {
        this.srcScriptUuid = srcScriptUuid;
    }

    public Long getScriptInfoVersionId() {
        return scriptInfoVersionId;
    }

    public void setScriptInfoVersionId(Long scriptInfoVersionId) {
        this.scriptInfoVersionId = scriptInfoVersionId;
    }

    public List<Long> getExcludeVersionIdList() {
        return excludeVersionIdList;
    }

    public void setExcludeVersionIdList(List<Long> excludeVersionIdList) {
        this.excludeVersionIdList = excludeVersionIdList;
    }

    public String getInfoUniqueUuid() {
        return infoUniqueUuid;
    }

    public void setInfoUniqueUuid(String infoUniqueUuid) {
        this.infoUniqueUuid = infoUniqueUuid;
    }


    public String getSysOrgCode() {
        return sysOrgCode;
    }

    public void setSysOrgCode(String sysOrgCode) {
        this.sysOrgCode = sysOrgCode;
    }

    public String getCategoryPath() {
        return categoryPath;
    }

    public void setCategoryPath(String categoryPath) {
        this.categoryPath = categoryPath;
    }

    public String getEscapedLikeCategoryPath() {
        return escapedLikeCategoryPath;
    }

    public void setEscapedLikeCategoryPath(String escapedLikeCategoryPath) {
        this.escapedLikeCategoryPath = escapedLikeCategoryPath;
    }

    public Integer getEditState() {
        return editState;
    }

    public void setEditState(Integer editState) {
        this.editState = editState;
    }

    @Override
    public String toString() {
        return "ScriptInfoQueryBean{" +
                "scriptInfoId=" + scriptInfoId +
                ", scriptNameZh='" + scriptNameZh + '\'' +
                ", scriptName='" + scriptName + '\'' +
                ", scriptType='" + scriptType + '\'' +
                ", scriptLabel='" + scriptLabel + '\'' +
                ", categoryId=" + categoryId +
                ", platform='" + platform + '\'' +
                ", infoUniqueUuid='" + infoUniqueUuid + '\'' +
                ", scriptSource=" + scriptSource +
                ", sysOrgCode='" + sysOrgCode + '\'' +
                ", categoryPath='" + categoryPath + '\'' +
                ", editState=" + editState +
                ", scriptInfoVersionId=" + scriptInfoVersionId +
                ", srcScriptUuid='" + srcScriptUuid + '\'' +
                ", allVersions=" + allVersions +
                ", excludeDefault=" + excludeDefault +
                ", excludeVersionIdList=" + excludeVersionIdList +
                ", escapedLikeCategoryPath='" + escapedLikeCategoryPath + '\'' +
                ", draftFlag=" + draftFlag +
                ", dubboFlag=" + dubboFlag +
                '}';
    }
}

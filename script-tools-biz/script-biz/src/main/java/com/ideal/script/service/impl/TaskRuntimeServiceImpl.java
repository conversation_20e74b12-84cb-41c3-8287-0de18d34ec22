package com.ideal.script.service.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.ideal.common.util.BeanUtils;
import com.ideal.common.util.PageDataUtil;
import com.ideal.script.common.constant.Constants;
import com.ideal.script.common.constant.enums.Enums;
import com.ideal.script.dto.RetryScriptInstanceApiDto;
import com.ideal.script.dto.TaskRuntimeApiDto;
import com.ideal.script.dto.TaskRuntimeQueryApiDto;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.mapper.TaskRuntimeMapper;
import com.ideal.script.mapper.TaskRuntimeStdoutMapper;
import com.ideal.script.model.bean.TaskRunTimeBindAgentBean;
import com.ideal.script.model.dto.TaskRuntimeDto;
import com.ideal.script.model.dto.TaskStartDto;
import com.ideal.script.model.entity.TaskRuntime;
import com.ideal.script.model.entity.TaskRuntimeStdout;
import com.ideal.script.observer.itsm.ItsmScriptTaskResultPush;
import com.ideal.script.observer.numerical.ScheduledTaskNumericalResultPush;
import com.ideal.script.service.IExectimeService;
import com.ideal.script.service.ITaskExecuteService;
import com.ideal.script.service.ITaskRuntimeService;
import com.ideal.system.common.component.model.CurrentUser;
import org.apache.commons.lang3.ObjectUtils;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RBucket;
import org.redisson.api.RMap;
import org.redisson.api.RQueue;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * agent运行实例Service业务层处理
 *
 * <AUTHOR>
 */
@Service
public class TaskRuntimeServiceImpl implements ITaskRuntimeService {

    private static final Logger logger = LoggerFactory.getLogger(TaskRuntimeServiceImpl.class);
    private final TaskRuntimeMapper taskRuntimeMapper;

    private final TaskRuntimeStdoutMapper taskRuntimeStdoutMapper;

    private final IExectimeService exectimeService;

    private final RedissonClient redissonClient;

    private final ITaskExecuteService taskExecuteService;

    private final ItsmScriptTaskResultPush itsmScriptTaskResultPush;

    private final ScheduledTaskNumericalResultPush scheduledTaskNumericalResultPush;

    public TaskRuntimeServiceImpl(ScheduledTaskNumericalResultPush scheduledTaskNumericalResultPush,TaskRuntimeMapper taskRuntimeMapper, IExectimeService exectimeService, TaskRuntimeStdoutMapper taskRuntimeStdoutMapper,RedissonClient redissonClient,@Lazy ITaskExecuteService taskExecuteService,@Lazy ItsmScriptTaskResultPush itsmScriptTaskResultPush) {
        this.scheduledTaskNumericalResultPush = scheduledTaskNumericalResultPush;
        this.taskRuntimeMapper = taskRuntimeMapper;
        this.exectimeService = exectimeService;
        this.taskRuntimeStdoutMapper = taskRuntimeStdoutMapper;
        this.redissonClient = redissonClient;
        this.taskExecuteService = taskExecuteService;
        this.itsmScriptTaskResultPush = itsmScriptTaskResultPush;
    }

    /**
     * 查询agent运行实例
     *
     * @param id agent运行实例主键
     * @return agent运行实例
     */
    @Override
    public TaskRuntimeDto selectTaskRuntimeById(Long id) {
        return BeanUtils.copy(taskRuntimeMapper.selectTaskRuntimeById(id), TaskRuntimeDto.class);
    }

    /**
     * 查询agent运行实例列表
     *
     * @param taskRuntimeDto agent运行实例
     * @return agent运行实例
     */
    @Override
    public PageInfo<TaskRuntimeDto> selectTaskRuntimeList(TaskRuntimeDto taskRuntimeDto, int pageNum, int pageSize) {
        PageMethod.startPage(pageNum, pageSize);
        List<TaskRuntime> taskRuntimeList = taskRuntimeMapper.selectTaskRuntimeList(BeanUtils.copy(taskRuntimeDto, TaskRuntime.class));
        //遍历查询的数据，用系统当前时间减去agent实例开始时间，如果大于设定的超时时间，则标记为超时
        long systemTime = System.currentTimeMillis();
        for(TaskRuntime taskRuntime : taskRuntimeList){
            long endTime = taskRuntime.getEndTime() == null ? systemTime : taskRuntime.getEndTime().getTime();
            Long timeoutValue = taskRuntime.getTimeoutValue();
            if(Objects.nonNull(timeoutValue) && timeoutValue > 0 && (endTime - taskRuntime.getStartTime().getTime()) > timeoutValue * 1000){
                taskRuntime.setTimeout(1);
            }else{
                taskRuntime.setTimeout(0);
            }
        }
        return PageDataUtil.toDtoPage(taskRuntimeList, TaskRuntimeDto.class);
    }

    @Override
    public PageInfo<TaskRuntimeApiDto> getTaskRuntimeInfoByInstanceId(TaskRuntimeQueryApiDto taskRuntimeQueryApiDto) {
        PageMethod.startPage(taskRuntimeQueryApiDto.getPageNum(), taskRuntimeQueryApiDto.getPageSize());
        return PageDataUtil.toDtoPage(taskRuntimeMapper.selectTaskRuntimeList(BeanUtils.copy(taskRuntimeQueryApiDto, TaskRuntime.class)), TaskRuntimeApiDto.class);
    }

    @Override
    public void driverNextBatchAgent(Long taskRuntimeId) throws ScriptException {
        try {
            //周期任务不需要拉起下一批
            Integer schedulerValue = taskRuntimeMapper.selectSchedulerTagByTaskRunTimeId(taskRuntimeId);
            if(schedulerValue.intValue() == Enums.TaskScheduler.PERIODIC.getValue()){
                return;
            }
            //根据taskRunTimeId获取任务信息
            TaskRuntime taskRuntime = taskRuntimeMapper.selectTaskRuntimeById(taskRuntimeId);
            //获取reids标识，1、hash内容，里面保存了total、eachNum等数据；2、计数器数值
            String taskFlag = "script-task-code-flag-"+taskRuntime.getScriptTaskId();
            //判断redis-hash是否存在
            RBucket<Object> bucketCount = redissonClient.getBucket(taskFlag+"-total");
            RBucket<Object> bucket = redissonClient.getBucket(taskFlag);
            if (bucket.isExists() && bucketCount.isExists()) {
                //获取任务启动时创建的原子性计数值
                RAtomicLong counter = redissonClient.getAtomicLong(taskFlag+"-total");
                //总计数器，总数减一
                long nowCount = counter.decrementAndGet();
                //根据taskId获取redis-hash数据
                RMap<String, String> map = redissonClient.getMap(taskFlag);
                String total = map.get("total");
                String eachNum = map.get("eachNum");
                // 可能有多台机器监听到了mq的不同内容，这些机器同时走到了这里，就可能出现agent总数出现负数的情况
                // 但是计数是原子性的，只有一个线程会出现计数结果为0，结果为0时删除redis存储的任务数据，负数的时候不需要做任何处理
                if(0 == nowCount){
                    //回更的agent是本次任务的最后一个agent，删除redis存储的数据值
                    bucket.delete();
                    bucketCount.delete();
                    //任务完成推送itsm
                    itsmScriptTaskResultPush.pushMessage(taskRuntimeId,"【finish】任务执行完毕",false);
                    //任务完成发送统计短信
                    scheduledTaskNumericalResultPush.pushMessage(taskRuntimeId);
                    return;
                }
                //获取hash中的dto数据（驱动类型、taskId等）
                TaskStartDto taskStartDto = JSON.parseObject(map.get(Constants.TASK_START_DTO), TaskStartDto.class);
                if(!ObjectUtils.notEqual(taskStartDto.getDriveMode(),null)){
                    taskStartDto.setDriveMode(Integer.valueOf(map.get("driveMode")));
                }
                //（agent总数 - 当前剩余未执行的agent）除以 并发数，取余如果是0，说明本批次执行完了，应该拉起下一批
                // 如果结果不为0，说明本批次没有执行结束，直接返回
                // 只适用于分批、忽略异常分批模式，队列不需要判断
                if(taskStartDto.getDriveMode() != 4){
                    if(0 != ((Integer.parseInt(total) - nowCount) % Integer.parseInt(eachNum))){
                        return;
                    }
                }
                //走到这里，说明再次执行的agent不是首批了，这个布尔值在驱动方法里判断使用，例如不再单独插入instance表数据
                taskStartDto.setFirstBatch(false);
                //所有agent都是一个instanceId，这里直接复用
                taskStartDto.setIscriptTaskInstanceId(taskRuntime.getTaskInstanceId());
                //用户数据
                CurrentUser user = JSON.parseObject(map.get(Constants.USER), CurrentUser.class);
                //队列模式
                //需要从redis队列中获取一个已经保存的ipsId作为下一次驱动的agent
                if(taskStartDto.getDriveMode() == 4) {
                    RQueue<Long> redisQueue = redissonClient.getQueue("script-task-queue-flag-"+taskStartDto.getScriptTaskId());
                    Long ipsId = redisQueue.poll();
                    if(ObjectUtils.notEqual(ipsId,null)){
                        Long [] ipsIds = {ipsId};
                        taskStartDto.setTaskIps(ipsIds);
                        //拉起下一批
                        taskExecuteService.scriptTaskStart(taskStartDto,user);
                    }
                }else
                //分批模式
                if(Enums.DriverModel.BATCH_EXEC.getValue().toString().equals(map.get(Constants.DRIVER_MODE))){
                    //判断，如果本次任务有异常\运行的agent，不拉起下一批
                    List<TaskRuntime> taskRuntimes = taskRuntimeMapper.selectErrorRuntimeList(taskRuntime);
                    if(!taskRuntimes.isEmpty()) {
                        return;
                    }
                    //拉起下一批
                    taskExecuteService.scriptTaskStart(taskStartDto,user);
                }else
                //忽略异常分批直接拉起下一批
                if(Enums.DriverModel.IGNORE_ERROR_BATCH_EXEC.getValue().toString().equals(map.get(Constants.DRIVER_MODE))){
                    //拉起下一批
                    taskExecuteService.scriptTaskStart(taskStartDto,user);
                }
            }
        }catch (ScriptException ex){
            throw new ScriptException("driverNextBatchAgent is error",ex);
        }
    }

    /**
     * 新增agent运行实例
     *
     * @param taskRuntimeDto agent运行实例
     * @return 结果
     */
    @Override
    public int insertTaskRuntime(TaskRuntimeDto taskRuntimeDto) {
        TaskRuntime taskRuntime = BeanUtils.copy(taskRuntimeDto, TaskRuntime.class);
        return taskRuntimeMapper.insertTaskRuntime(taskRuntime);
    }

    /**
     * 修改agent运行实例
     *
     * @param taskRuntimeDto agent运行实例
     * @return 结果
     */
    @Override
    public int updateTaskRuntime(TaskRuntimeDto taskRuntimeDto) {
        TaskRuntime taskRuntime = BeanUtils.copy(taskRuntimeDto, TaskRuntime.class);
        return taskRuntimeMapper.updateTaskRuntime(taskRuntime);
    }

    /**
     * 批量删除agent运行实例
     *
     * @param ids 需要删除的agent运行实例主键
     * @return 结果
     */
    @Override
    public int deleteTaskRuntimeByIds(Long[] ids) {
        return taskRuntimeMapper.deleteTaskRuntimeByIds(ids);
    }

    /**
     * 删除agent运行实例信息
     *
     * @param id agent运行实例主键
     * @return 结果
     */
    @Override
    public int deleteTaskRuntimeById(Long id) {
        return taskRuntimeMapper.deleteTaskRuntimeById(id);
    }

    /**
     * 功能描述： 更新agent状态
     *
     * @param status 状态
     * @param notInStates 排除的状态
     * @param taskRuntimeId agent运行实例Id
     * <AUTHOR>
     */
    @Override
    public void updateTaskRuntimeState(int status, List<Integer> notInStates, Long taskRuntimeId) {
        TaskRuntime taskRuntime = taskRuntimeMapper.getTaskRuntimeStartTime(taskRuntimeId);
        taskRuntimeMapper.updateTaskRuntimeState(status, notInStates, taskRuntimeId, taskRuntime.getEndTime(), (taskRuntime.getEndTime().getTime() - taskRuntime.getStartTime().getTime()) / 1000);
    }

    @Override
    public String getOutPutMessage(Long id) throws ScriptException {
        String stdout = "";
        try {
            //根据taskRuntimeId获取agent标准输出
            TaskRuntimeStdout taskRuntimeStdout = taskRuntimeStdoutMapper.selectStdoutByTaskRuntimeId(id);
            if(ObjectUtils.notEqual(taskRuntimeStdout,null)){
                stdout = taskRuntimeStdout.getIstdout() + taskRuntimeStdout.getIstderror();
            }
        } catch (Exception e) {
            logger.error("get out put result fail!",e);
            throw new ScriptException("get.out.put.result.fail");
        }
        return stdout;
    }

    /**
     * 功能描述：根据 Agent 实例 ID 查询当前任务中除自身外其他 Agent 执行出现异常的情况
     * 确定当前任务中除自身外其他 Agent 是否出现了执行异常的情况
     *
     * @param id agent实例主键
     * @return {@link Integer }
     * <AUTHOR>
     */
    @Override
    public Integer selectCountByTaskInstanceId(Long id) {
        return taskRuntimeMapper.selectCountByTaskInstanceId(id);
    }

    /**
     * 获取agent实例数据
     * @param retryScriptInstanceApiDto 参数
     * @return agent实例对象
     */
    @Override
    public TaskRuntime getTaskRuntime(RetryScriptInstanceApiDto retryScriptInstanceApiDto){
        return taskRuntimeMapper.getTaskRuntime(retryScriptInstanceApiDto);
    }

    /**
     * 功能描述：根据agent实例主键查询运行中的agent数量
     *
     * @param id agent实例主键
     * @return {@link Integer }
     * <AUTHOR>
     */
    @Override
    public Integer getRunningAgentInstanceCount(Long id) {
        return taskRuntimeMapper.getRunningAgentInstanceCount(id);
    }

    /**
     * 功能描述：根据agent实例主键获取绑定的agent信息
     *
     * @param taskRuntimeId agent实例主键
     * @return {@link TaskRunTimeBindAgentBean }
     * <AUTHOR>
     */
    @Override
    public TaskRunTimeBindAgentBean getBindAgentForTaskRuntime(Long taskRuntimeId) {
        return taskRuntimeMapper.getBindAgentForTaskRuntime(taskRuntimeId);
    }

    /**
     * 功能描述：根据agent运行实例主键获取agent信息
     *
     * @param taskRuntimeId  agent实例主键
     * @param status         状态
     * @param taskRuntimeDto agent运行实例对象
     */
    @Override
    public void updateExecTimeAndState(String taskRuntimeId, int status, TaskRuntimeDto taskRuntimeDto) {
        //更新执行次数
        exectimeService.updateScriptExecTime(status, taskRuntimeDto.getSrcScriptUuid(),null);

        // 更新agent执行状态
        List<Integer> notInStates = new ArrayList<>();
        notInStates.add(Enums.TaskRuntimeState.TERMINATED.getValue());
        notInStates.add(Enums.TaskRuntimeState.SKIP.getValue());
        updateTaskRuntimeState(status, notInStates, Long.parseLong(taskRuntimeId));
    }

    /**
     * 获取agent实例标准输出
     * @param retryScriptInstanceApiDto 参数
     * @return 标准输出
     */
    @Override
    public String getAgentStdoutByTaskIdAndAddress(RetryScriptInstanceApiDto retryScriptInstanceApiDto){
        TaskRuntime taskRuntime = taskExecuteService.getTaskRuntime(retryScriptInstanceApiDto);
        return taskExecuteService.getRealTimeOutPutMessage(taskRuntime.getId());
    }

}

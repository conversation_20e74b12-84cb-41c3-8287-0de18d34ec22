package com.ideal.script.model.dto;

import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.util.List;

/**
 * 任务申请对象，查询条件使用的类
 * <AUTHOR>
 */
public class TaskApplyQueryDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 脚本中文名称
     */
    private String scriptNameZh;
    /**
     * 脚本名称
     */
    private String scriptName;

    /**
     * 脚本类型(shell、bat、perl、python、powershell)
     */
    private String scriptType;

    /**
     * 分类名称
     */
    private String scriptCategoryName;


    /**
     * 分类Id
     */
    private Long categoryId;

    /**
     * 风险级别  0 - 白名单  1 - 高级风险  2 - 中级风险  3 - 低级风险
     */
    private Integer level;


    /**
     * 适用平台  数据来源于ieai_platform_code表
     */
    private String platform;
    /**
     * 获取默认版本脚本 [1]- 是 []- 否
     */
    private List<Integer> getDefault;

    /**
     * 标签
     */
    private String scriptLabel;

    /**
     * 任务类型（0常用任务，1克隆任务）
     */
    private Integer taskType;

    /**
     * 任务名称
     */
    private String taskName;
    /**
     * 关键词
     */
    private String keyword;

    /**
     * 是否为值班任务申请，true是，false不是，默认false
     */
    private boolean dutyApply = false;

    /**
     * 查询使用，分类id
     */
    private List<Long> categoryIdList;

    public List<Long> getCategoryIdList() {
        return categoryIdList;
    }

    public void setCategoryIdList(List<Long> categoryIdList) {
        this.categoryIdList = categoryIdList;
    }

    public boolean isDutyApply() {
        return dutyApply;
    }

    public void setDutyApply(boolean dutyApply) {
        this.dutyApply = dutyApply;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public Integer getTaskType() {
        return taskType;
    }

    public void setTaskType(Integer taskType) {
        this.taskType = taskType;
    }

    public String getScriptLabel() {
        return scriptLabel;
    }

    public void setScriptLabel(String scriptLabel) {
        this.scriptLabel = scriptLabel;
    }

    public List<Integer> getGetDefault() {
        return getDefault;
    }

    public void setGetDefault(List<Integer> getDefault) {
        this.getDefault = getDefault;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public String getScriptNameZh() {
        return scriptNameZh;
    }

    public void setScriptNameZh(String scriptNameZh) {
        this.scriptNameZh = scriptNameZh;
    }

    public String getScriptName() {
        return scriptName;
    }

    public void setScriptName(String scriptName) {
        this.scriptName = scriptName;
    }

    public String getScriptType() {
        return scriptType;
    }

    public void setScriptType(String scriptType) {
        this.scriptType = scriptType;
    }

    public String getScriptCategoryName() {
        return scriptCategoryName;
    }

    public void setScriptCategoryName(String scriptCategoryName) {
        this.scriptCategoryName = scriptCategoryName;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this)
                .append("scriptNameZh", getScriptNameZh())
                .append("scriptName", getScriptName())
                .append("scriptType", getScriptType())
                .append("scriptCategoryName", getScriptCategoryName())
                .append("level", getLevel())
                .append("platform", getPlatform())
                .append("getDefault", getGetDefault())
                .append("keyword", getKeyword())
                .toString();
    }
}

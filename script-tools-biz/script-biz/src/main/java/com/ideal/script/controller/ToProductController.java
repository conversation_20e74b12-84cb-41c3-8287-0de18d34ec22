package com.ideal.script.controller;


import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.script.common.constant.permission.MenuPermitConstant;
import com.ideal.script.model.dto.ToProductDto;
import com.ideal.script.model.dto.ToProductQueryDto;
import com.ideal.script.service.IToProductService;
import com.ideal.system.common.component.aop.MethodPermission;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 【请填写功能名称】Controller
 *
 * <AUTHOR>
 * @date 2024-04-02
 */
@RestController
@RequestMapping("${app.script-tools-url:}/script/product")
@MethodPermission(MenuPermitConstant.SERVICE_ROLLOUT_PER)
public class ToProductController {

    private final IToProductService toProductService;

    public ToProductController(IToProductService toProductService) {
        this.toProductService = toProductService;
    }

    /**
     * 查询【请填写功能名称】列表
     *
     * @param tableQueryDto 查询条件
     * @return 查询结果
     */
    @PostMapping("/list")
    public R<PageInfo<ToProductDto>> list(@RequestBody TableQueryDto<ToProductQueryDto> tableQueryDto) {
        PageInfo<ToProductDto> list = toProductService.selectToProductList(
                tableQueryDto.getQueryParam(),
                tableQueryDto.getPageNum(),
                tableQueryDto.getPageSize()
        );
        return R.ok(list);
    }

}

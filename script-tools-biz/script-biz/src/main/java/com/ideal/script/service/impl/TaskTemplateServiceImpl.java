package com.ideal.script.service.impl;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.ideal.common.util.BeanUtils;
import com.ideal.common.util.PageDataUtil;
import com.ideal.common.util.batch.Batch;
import com.ideal.sc.util.CurrentUserUtil;
import com.ideal.script.common.util.*;
import com.ideal.script.dto.*;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.mapper.*;
import com.ideal.script.model.bean.*;
import com.ideal.script.model.dto.*;
import com.ideal.script.model.dto.interaction.agent.SystemComputerGroupDto;
import com.ideal.script.model.entity.*;
import com.ideal.script.service.*;
import com.ideal.script.service.impl.builders.MyScriptServiceScripts;
import com.ideal.system.common.component.model.CurrentUser;
import com.ideal.system.dto.RoleApiDto;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 常用/克隆任务执行实现类
 *
 * <AUTHOR>
 */
@Service
public class TaskTemplateServiceImpl implements ITaskTemplateService, Batch {

    private static final Logger logger = LoggerFactory.getLogger(TaskTemplateServiceImpl.class);

    private final ITaskService taskService;
    private final TaskParamsMapper taskParamsMapper;
    private final TaskIpsMapper taskIpsMapper;
    private final TaskAttachmentMapper taskAttachmentMapper;
    private final ICategoryService categoryService;
    private final ITaskGroupsService taskGroupsService;
    private final TaskTemplateMapper taskTemplateMapper;
    private final TaskAttachmentTemplateMapper taskAttachmentTemplateMapper;
    private final TaskParamsTemplateMapper taskParamsTemplateMapper;
    private final TaskIpsTemplateMapper taskIpsTemplateMapper;
    private final TaskGroupsTemplateMapper taskGroupsTemplateMapper;
    private final IMyScriptService myScriptService;
    private final ITaskApplyService taskApplyService;
    private final AuditSource taskApplySource;
    private final IAgentInfoService agentInfoService;
    private final FileSizeValidUtil fileSizeValidUtil;
    private final IAuditRelationService auditRelationService;
    private final IInfoVersionService infoVersionService;
    private final MyScriptServiceScripts myScriptServiceScripts;

    @Value("${ideal.customer.name:ideal}")
    private String customerName;

    @Autowired
    public TaskTemplateServiceImpl(MyScriptServiceScripts myScriptServiceScripts,IInfoVersionService infoVersionService,IAuditRelationService auditRelationService,FileSizeValidUtil fileSizeValidUtil,IAgentInfoService agentInfoService,@Qualifier("taskApplySource") AuditSource taskApplySource,ITaskApplyService taskApplyService, IMyScriptService myScriptService, TaskGroupsTemplateMapper taskGroupsTemplateMapper, TaskIpsTemplateMapper taskIpsTemplateMapper, TaskParamsTemplateMapper taskParamsTemplateMapper, TaskAttachmentTemplateMapper taskAttachmentTemplateMapper, TaskTemplateMapper taskTemplateMapper, ITaskService taskService, TaskParamsMapper taskParamsMapper, TaskIpsMapper taskIpsMapper, TaskAttachmentMapper taskAttachmentMapper, ICategoryService categoryService, ITaskGroupsService taskGroupsService) {
        this.infoVersionService = infoVersionService;
        this.myScriptServiceScripts = myScriptServiceScripts;
        this.auditRelationService = auditRelationService;
        this.fileSizeValidUtil = fileSizeValidUtil;
        this.agentInfoService = agentInfoService;
        this.taskApplySource = taskApplySource;
        this.taskApplyService = taskApplyService;
        this.myScriptService = myScriptService;
        this.taskGroupsTemplateMapper = taskGroupsTemplateMapper;
        this.taskIpsTemplateMapper = taskIpsTemplateMapper;
        this.taskParamsTemplateMapper = taskParamsTemplateMapper;
        this.taskAttachmentTemplateMapper = taskAttachmentTemplateMapper;
        this.taskTemplateMapper = taskTemplateMapper;
        this.taskService = taskService;
        this.taskParamsMapper = taskParamsMapper;
        this.taskIpsMapper = taskIpsMapper;
        this.taskAttachmentMapper = taskAttachmentMapper;
        this.categoryService = categoryService;
        this.taskGroupsService = taskGroupsService;
    }

    /**
     * 创建克隆任务
     * @param taskStartDto 克隆任务对象（查询使用）
     * @param user 当前系统用户
     * @throws ScriptException 脚本服务化相关异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createCloneTask(TaskStartDto taskStartDto,CurrentUser user) throws ScriptException {
        try {
            //通过taskId获取本次任务的信息，包括附件、参数、ips、agentGroup
            if(ObjectUtils.notEqual(taskStartDto,null)
                    && ObjectUtils.notEqual(taskStartDto.getScriptTaskId(),null)){
                //查询并保存任务信息
                TaskDto taskDto = taskService.selectTaskById(taskStartDto.getScriptTaskId());
                //审核关系表数据
                AuditRelationDto auditRelationDto = auditRelationService.selectAuditRelationByTaskId(taskDto.getId());
                taskDto.setId(null);
                taskDto.setTaskName(taskStartDto.getTaskName());
                //标记常用任务/克隆任务 创建人
                taskDto.setCreatorId(user.getId());
                Task task = BeanUtils.copy(taskDto, Task.class);
                //任务类型 0常用任务 1克隆任务
                task.setTaskType(taskStartDto.getTaskType());
                if(ObjectUtils.notEqual(auditRelationDto,null)){
                    //审核人id
                    task.setAuditorId(auditRelationDto.getAuditUserId());
                }
                taskTemplateMapper.insertTaskTemplate(task);
                Long taskId = task.getId();
                //查询并保存附件信息
                TaskAttachment taskAttachment = new TaskAttachment();
                taskAttachment.setScriptTaskId(taskStartDto.getScriptTaskId());
                taskAttachment.setScriptTaskId(taskId);
                List<TaskAttachment> taskAttachments = taskAttachmentMapper.selectTaskAttachmentList(taskAttachment);
                for(TaskAttachment taskAttachment1 : taskAttachments){
                    taskAttachment1.setId(null);
                    taskAttachment1.setScriptTaskId(taskId);
                }
                this.batchData(taskAttachments , taskAttachmentTemplateMapper::insertTaskAttachment);
                //查询并保存参数信息
                TaskParams taskParams = new TaskParams();
                taskParams.setScriptTaskId(taskStartDto.getScriptTaskId());
                List<TaskParams> taskParamsList = taskParamsMapper.selectTaskParamsList(taskParams);
                for(TaskParams taskParams1 : taskParamsList){
                    taskParams1.setId(null);
                    taskParams1.setScriptTaskId(taskId);
                }
                this.batchData(taskParamsList , taskParamsTemplateMapper::insertTaskParams);
                //查询并保存ips信息
                TaskIps taskIps = new TaskIps();
                taskIps.setScriptTaskId(taskStartDto.getScriptTaskId());
                List<TaskIps> taskIpsList = taskIpsMapper.selectTaskIpsList(taskIps);
                for(TaskIps taskIps1 : taskIpsList){
                    taskIps1.setId(null);
                    taskIps1.setScriptTaskId(taskId);
                    taskIps1.setAlreadyimpFlag(0);
                }
                this.batchData(taskIpsList , taskIpsTemplateMapper::insertTaskIps);
                //查询并保存设备组信息
                List<TaskGroupsDto> taskGroupsList = taskGroupsService.selectTaskGroupsByServiceId(null, taskStartDto.getScriptTaskId());
                List<TaskGroups> taskGroups = new ArrayList<>();
                for(TaskGroupsDto taskGroupsDto : taskGroupsList){
                    taskGroupsDto.setId(null);
                    taskGroupsDto.setScriptTaskId(taskId);
                    taskGroups.add(BeanUtils.copy(taskGroupsDto, TaskGroups.class));
                }
                this.batchData(taskGroups , taskGroupsTemplateMapper::insertTaskGroups);
            }else{
                //如果查询任务的必要参数为空，返回异常
                logger.error("clone task is not exist!");
                throw new ScriptException("clone.script.task.error");
            }
        }catch (ScriptException e){
            //如果查询任务的必要参数为空，返回异常
            throw new ScriptException("clone.script.task.error",e);
        }
    }

    /**
     * 任务申请页面常用任务直接保存成常用任务
     * @param scriptExecAuditDto 任务参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createCloneTaskFromTaskApply(ScriptExecAuditDto scriptExecAuditDto, CurrentUser user) throws ScriptException{
        try {
            //根据开关验证执行用户
            if(Objects.equals(customerName,"psbc")) {
                taskApplyService.checkExecuserPermission(scriptExecAuditDto.getScriptInfoVersionId(), scriptExecAuditDto.getExecuser());
            }
            //根据脚本versionId查询脚本的uuid
            ScriptVersionDto scriptVersionDto = infoVersionService.selectInfoVersionById(scriptExecAuditDto.getScriptInfoVersionId());
            //保存常用任务数据
            Task task = getTask(scriptExecAuditDto, user, scriptVersionDto);
            taskTemplateMapper.insertTaskTemplate(task);
            //保存task数据后获取id，后续业务表都需要关联这个taskId
            Long taskId = task.getId();
            //更新附件taskId
            if(ObjectUtils.notEqual(scriptExecAuditDto.getScriptTempAttachments(),null)
                    && !scriptExecAuditDto.getScriptTempAttachments().isEmpty()){
                Long [] attachmentIds = new Long[scriptExecAuditDto.getScriptTempAttachments().size()];
                int index = 0;
                for(AttachmentDto attachmentDto : scriptExecAuditDto.getScriptTempAttachments()){
                    attachmentIds[index++] = attachmentDto.getId();
                }
                taskAttachmentTemplateMapper.batchUpdateByIds(taskId,attachmentIds);
            }
            //保存参数
            if(ObjectUtils.notEqual(scriptExecAuditDto.getParams(),null)
                    && !scriptExecAuditDto.getParams().isEmpty()){
                List<TaskParams> taskParamsList = getTaskParams(scriptExecAuditDto, taskId);
                this.batchData(taskParamsList , taskParamsTemplateMapper::insertTaskParams);
            }
            //保存ips信息
            if(ObjectUtils.notEqual(scriptExecAuditDto.getChosedAgentUsers(),null)
                    && !scriptExecAuditDto.getChosedAgentUsers().isEmpty()){
                List<TaskIps> taskIpsList = new ArrayList<>();
                for(AgentInfoDto agentInfoDto : scriptExecAuditDto.getChosedAgentUsers()){
                    //根据ip端口查询agent信息，为了给agentId赋值
                    AgentInfoDto dto = new AgentInfoDto();
                    dto.setAgentIp(agentInfoDto.getAgentIp());
                    dto.setAgentPort(agentInfoDto.getAgentPort());
                    AgentInfo agentInfo = agentInfoService.selectAgentInfoByIpAndPort(dto);
                    //保存ips数据
                    TaskIps taskIps = new TaskIps();
                    taskIps.setScriptTaskId(taskId);
                    taskIps.setScriptAgentinfoId(agentInfo.getId());
                    taskIpsList.add(taskIps);
                }
                this.batchData(taskIpsList , taskIpsTemplateMapper::insertTaskIps);
            }
            //保存设备组信息
            if(ObjectUtils.notEqual(scriptExecAuditDto.getChosedResGroups(),null)
                    && !scriptExecAuditDto.getChosedResGroups().isEmpty()){
                List<TaskGroups> taskGroupsList = new ArrayList<>();
                for(TaskGroupsDto taskGroupsDto : scriptExecAuditDto.getChosedResGroups()){
                    TaskGroups taskGroups = new TaskGroups();
                    taskGroups.setScriptTaskId(taskId);
                    taskGroups.setSysmComputerGroupId(taskGroupsDto.getSysmComputerGroupId());
                    taskGroups.setCpname(taskGroupsDto.getCpname());
                    taskGroupsList.add(taskGroups);
                }
                this.batchData(taskGroupsList , taskGroupsTemplateMapper::insertTaskGroups);
            }
        }catch (ScriptException e){
            throw new ScriptException("clone.script.task.error",e);
        }
    }

    /**
     * 整合参数信息
     * @param scriptExecAuditDto 前台传递过来的数据
     * @param taskId 任务id
     * @return 返回参数信息
     */
    private List<TaskParams> getTaskParams(ScriptExecAuditDto scriptExecAuditDto, Long taskId) {
        List<TaskParams> taskParamsList = new ArrayList<>();
        for(ParameterDto parameterDto : scriptExecAuditDto.getParams()){
            TaskParams taskParams = new TaskParams();
            taskParams.setScriptTaskId(taskId);
            taskParams.setScriptParameterCheckId(parameterDto.getParamCheckIid());
            taskParams.setScriptParameterManagerId(parameterDto.getScriptParameterManagerId());
            taskParams.setType(parameterDto.getParamType());
            taskParams.setValue(parameterDto.getParamDefaultValue());
            taskParams.setDesc(parameterDto.getParamDesc());
            taskParams.setOrder(parameterDto.getParamOrder());
            taskParams.setStartType(0);
            taskParamsList.add(taskParams);
        }
        return taskParamsList;
    }

    /**
     * 整合task信息
     * @param scriptExecAuditDto 前台传递过来的参数
     * @param user 当前登录用户信息
     * @param scriptVersionDto 脚本信息
     * @return 返回task数据
     */
    private Task getTask(ScriptExecAuditDto scriptExecAuditDto, CurrentUser user, ScriptVersionDto scriptVersionDto) {
        Task task = new Task();
        task.setSrcScriptUuid(scriptVersionDto.getSrcScriptUuid());
        task.setTaskName(scriptExecAuditDto.getTaskInfo().getTaskName());
        task.setEachNum(scriptExecAuditDto.getTaskInfo().getEachNum());
        task.setTaskScheduler(scriptExecAuditDto.getTaskInfo().getTaskScheduler());
        task.setTaskTime(scriptExecAuditDto.getTaskInfo().getTaskTime());
        task.setTaskCron(scriptExecAuditDto.getTaskInfo().getTaskCron());
        task.setPublishDesc(scriptExecAuditDto.getTaskInfo().getPublishDesc());
        task.setStartUser(user.getLoginName());
        task.setDriveMode(scriptExecAuditDto.getTaskInfo().getDriveMode());
        task.setStartType(0);
        task.setCreatorId(user.getId());
        task.setCreatorName(user.getFullName());
        task.setScriptTaskSource(1);
        task.setReadyToExecute(3);
        task.setTaskType(0);
        return task;
    }

    /**
     * 获取克隆任务列表
     * @param taskApplyQueryDto 查询条件dto
     * @param pageNum 页码
     * @param pageSize 分页大小
     * @param user 当前系统登录用户
     * @return 返回克隆任务数据分页信息
     */
    @Override
    public PageInfo<TaskTemplateDto> listCloneTask(TaskApplyQueryDto taskApplyQueryDto, Integer pageNum, Integer pageSize, CurrentUser user) {

        //判断是否执行角色查询权限，注意，值班任务申请时不需要处理，保持获取所有数据
        boolean rolePermission = myScriptService.getRolePermission();
        if(rolePermission){
            //1、根据角色获取分类id集合
            List<RoleApiDto> roleApiDtos = myScriptServiceScripts.getiRole().selectRoleListByLoginName(user.getLoginName());
            List<Long> idList = roleApiDtos.stream()
                    .map(RoleApiDto::getId)
                    .collect(Collectors.toList());
            //2、根据1中查到的角色去分类角色表、分类表中查询绑定到这些角色上的分类
            List<Long> categortIdList = myScriptServiceScripts.getCategoryMapper().getCategoryIdsByRoleIds(idList);
            taskApplyQueryDto.setCategoryIdList(categortIdList);
        }
        //分页查询
        PageMethod.startPage(pageNum, pageSize);
        List<TaskCloneBean> taskCloneBeans = taskTemplateMapper.selectTaskTemplateList(taskApplyQueryDto,user.getOrgCode());
        //根据查询的数据获取分类信息
        for (TaskCloneBean taskCloneBean : taskCloneBeans) {
            if (null == taskCloneBean.getCategoryId()) {
                taskCloneBean.setScriptCategoryName(null);
            } else {
                String categoryName = categoryService.getCategoryFullPath(taskCloneBean.getCategoryId());
                taskCloneBean.setScriptCategoryName(categoryName);
            }
        }
        return PageDataUtil.toDtoPage(taskCloneBeans, TaskTemplateDto.class);
    }

    /**
     * 获取脚本基本信息
     * @param scriptInfoQueryDto 请求参数
     * @return 返回脚本基本信息
     */
    @Override
    public ScriptInfoDto getScriptTemplateDetail(ScriptInfoQueryDto scriptInfoQueryDto) throws ScriptException{
        //一、调用获取脚本信息方法获取脚本信息
        ScriptInfoDto scriptDetail = myScriptService.getScriptDetail(scriptInfoQueryDto);
        //二、参数、附件、agent等信息从常用任务表里获取
        //根据TaskId获取参数，详情查询到的参数只需把值改成克隆的参数即可
        List<TaskParams> taskParams = taskParamsTemplateMapper.selectTaskParamsByTaskId(scriptInfoQueryDto.getTaskId());
        List<ParameterValidationDto> parameterValidationDtoList = scriptDetail.getScriptVersionDto().getParameterValidationDtoList();
        if(!parameterValidationDtoList.isEmpty()){
            for(ParameterValidationDto parameterValidationDto : parameterValidationDtoList){
                for(TaskParams taskParams1 : taskParams){
                    if(taskParams1.getOrder().intValue() == parameterValidationDto.getParamOrder().intValue()){
                        parameterValidationDto.setParamDefaultValue(taskParams1.getValue());
                        break;
                    }
                }
            }
        }
        return scriptDetail;
    }

    /**
     * 获取执行的agent
     * @param taskId 任务id
     * @return agent集合
     */
    @Override
    public List<AgentInfoDto> getAllChoseAgent(Long taskId){
        return BeanUtils.copy(taskIpsTemplateMapper.selectAgentInfoByTaskId(taskId), AgentInfoDto.class);
    }

    /**
     * 根据taskId获取资源组数据
     * @param taskId 任务id
     * @return 资源组数据
     */
    @Override
    public List<SystemComputerGroupDto> getAllChoseGroup(Long taskId){
        List<SystemComputerGroupDto> groupApiDtoList = new ArrayList<>();
        List<TaskGroups> taskGroups = taskGroupsTemplateMapper.selectTaskGroupsByTaskId(taskId);
        for(TaskGroups taskGroup : taskGroups){
            SystemComputerGroupDto computerGroupApiDto = new SystemComputerGroupDto();
            computerGroupApiDto.setSysmComputerGroupId(taskGroup.getSysmComputerGroupId());
            computerGroupApiDto.setCpname(taskGroup.getCpname());
            groupApiDtoList.add(computerGroupApiDto);
        }
        return groupApiDtoList;
    }

    /**
     * 保存\执行常用任务或者克隆任务
     * @param scriptExecAuditDto 执行参数
     */
    @Override
    @Transactional(readOnly = false, rollbackFor = Exception.class)
    public Long execAuditTemplateTask(ScriptExecAuditDto scriptExecAuditDto) throws ScriptException {
        Long nowTaskId = 0L;
        try {
            Long taskId = scriptExecAuditDto.getTaskInfo().getId();
            //先执行任务申请
            CurrentUser user = CurrentUserUtil.getCurrentUser();
            if (null == user) {
                throw new ScriptException("current.user.empty");
            }
            //将数据迁移到ieai_script_task_attament表中
            //1、根据前端传递过来的附件id查询附件
            List<TaskAttachment> taskAttachments = taskAttachmentTemplateMapper.selectTaskAttachmentByTaskId(scriptExecAuditDto.getTaskInfo().getId());
            //2、将本次需要的附件存到ieai_script_task_attachment
            List<TaskAttachment> taskAttachmentList = new ArrayList<>();
            for(TaskAttachment attachment : taskAttachments){
                TaskAttachment taskAttachment = new TaskAttachment();
                taskAttachment.setSize(attachment.getSize());
                taskAttachment.setContents(attachment.getContents());
                taskAttachment.setName(attachment.getName());
                taskAttachment.setScriptTaskId(0L);
                taskAttachmentMapper.insertTaskAttachment(taskAttachment);
                taskAttachmentList.add(taskAttachment);
            }
            //将本次传递的附件替换成刚刚保存到ieai_script_task_attachment表里的数据
            scriptExecAuditDto.setScriptTempAttachments(BeanUtils.copy(taskAttachmentList, AttachmentDto.class));
            //查询本次任务是常用任务还是克隆任务
            TaskCloneBean taskCloneBean = taskTemplateMapper.selectTaskTemplateById(scriptExecAuditDto.getTaskInfo().getId());
            scriptExecAuditDto.getTaskInfo().setId(null);
            nowTaskId = taskApplyService.scriptExecAuditing(scriptExecAuditDto, user, taskApplySource);
            //判断数据类型，如果是克隆任务，任务申请结束就删除，如果是常用任务，不删除任务
            if(taskCloneBean.getTaskType() == 1){
                deleteCloneTask(taskId);
            }
        }catch (ScriptException e){
            throw new ScriptException("script.clone.task.start.error",e);
        }
        return nowTaskId;
    }

    /**
     * 更新常用/克隆任务
     * @param scriptExecAuditDto 执行参数
     * @return 成功标识
     * @throws ScriptException 脚本服务化异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateAuditTemplateTask(ScriptExecAuditDto scriptExecAuditDto) throws ScriptException {
        try {
            //更新task信息，包括任务名称、并发数等
            Long auditorId = scriptExecAuditDto.getAuditUserId();
            scriptExecAuditDto.getTaskInfo().setAuditorId(auditorId);
            taskTemplateMapper.updateById(BeanUtils.copy(scriptExecAuditDto.getTaskInfo(), Task.class));
            //更新ips
            updateChoseAgentIps(scriptExecAuditDto);
            //更新资源组
            updateChoseResGroups(scriptExecAuditDto);
            //更新参数
            updateTaskParams(scriptExecAuditDto);
            //更新附件
            updateAttachment(scriptExecAuditDto);

        }catch (ScriptException e){
            throw new ScriptException("script.update.template.task.error",e);
        }
        return true;
    }

    /**
     * 删除克隆任务
     * @param taskId 任务id
     */
    private void deleteCloneTask(Long taskId){
        //删除ieai_script_task_temp以及其它表相关数据
        taskTemplateMapper.deleteById(taskId);
        taskParamsTemplateMapper.deleteByTaskId(taskId);
        taskAttachmentTemplateMapper.deleteByTaskId(taskId);
        taskGroupsTemplateMapper.deleteByTaskId(taskId);
        taskIpsTemplateMapper.deleteByTaskId(taskId);
    }

    /**
     * 删除常用/克隆任务
     * @param taskId 任务id
     * @throws ScriptException 脚本服务化异常
     */
    @Override
    public void deleteTaskTemplate(Long taskId) throws ScriptException{
        deleteCloneTask(taskId);
    }

    /**
     * 更新ips数据
     * @param scriptExecAuditDto 任务数据信息dto
     */
    private void updateChoseAgentIps(ScriptExecAuditDto scriptExecAuditDto) throws ScriptException{
        try {
            Long taskId = scriptExecAuditDto.getTaskInfo().getId();
            //先删除已经绑定的agent-ips数据
            taskIpsTemplateMapper.deleteByTaskId(taskId);
            //获取前台传过来的agent数据
            List<AgentInfoDto> choseAgentUsers = scriptExecAuditDto.getChosedAgentUsers();
            //根据agentip与端口查询本次选中的agent数据
            List<AgentInfo> agentInfos = agentInfoService.selectAgentInfoByIpAndPort(choseAgentUsers);
            if(!choseAgentUsers.isEmpty()){
                List<TaskIps> saveList = new ArrayList<>();
                //先要删除agent-ips列表数据，然后再新增
                for(AgentInfo agentInfo : agentInfos){
                    TaskIps taskIps = new TaskIps();
                    taskIps.setScriptTaskId(taskId);
                    taskIps.setScriptAgentinfoId(agentInfo.getId());
                    taskIps.setScriptAgentinfoId(agentInfo.getId());
                    saveList.add(taskIps);
                }
                this.batchData(saveList , taskIpsTemplateMapper::insertTaskIps);
            }
        }catch (Exception e){
            throw new ScriptException(e.getMessage());
        }

    }

    /**
     * 更新资源组信息
     * @param scriptExecAuditDto 参数信息
     * @throws ScriptException 脚本服务化异常
     */
    private void updateChoseResGroups(ScriptExecAuditDto scriptExecAuditDto) throws ScriptException{
        try {
            Long taskId = scriptExecAuditDto.getTaskInfo().getId();
            //先删除已经绑定的agent-ips数据
            taskGroupsTemplateMapper.deleteByTaskId(taskId);
            //获取前台传过来的资源组数据
            List<TaskGroupsDto> choseResGroups = scriptExecAuditDto.getChosedResGroups();
            if(!choseResGroups.isEmpty()){
                List<TaskGroups> saveList = new ArrayList<>();
                //先要删除agent-ips列表数据，然后再新增
                for(TaskGroupsDto taskGroupsDto : choseResGroups){
                    TaskGroups taskGroup = new TaskGroups();
                    taskGroup.setScriptTaskId(taskId);
                    taskGroup.setSysmComputerGroupId(taskGroupsDto.getSysmComputerGroupId());
                    taskGroup.setCpname(taskGroupsDto.getCpname());
                    saveList.add(taskGroup);
                }
                this.batchData(saveList , taskGroupsTemplateMapper::insertTaskGroups);
            }
        }catch (Exception e){
            throw new ScriptException(e.getMessage());
        }

    }

    /**
     * 更新参数信息
     * @param scriptExecAuditDto 任务数据信息dto
     */
    private void updateTaskParams(ScriptExecAuditDto scriptExecAuditDto) throws ScriptException{
        try {
            //页面传递过来的参数信息
            List<ParameterDto> params = scriptExecAuditDto.getParams();
            if(!params.isEmpty()){
                //获取当前
                Long taskId = scriptExecAuditDto.getTaskInfo().getId();
                List<TaskParams> taskParams = taskParamsTemplateMapper.selectTaskParamsByTaskId(taskId);
                for(TaskParams taskParams1 : taskParams){
                    for(ParameterDto parameterDto : params){
                        if(taskParams1.getOrder().intValue() == parameterDto.getParamOrder().intValue()){
                            taskParams1.setValue(parameterDto.getParamDefaultValue());
                        }
                    }
                }
                taskParamsTemplateMapper.batchUpdateByIds(taskParams);
            }
        } catch (Exception e){
            throw new ScriptException(e.getMessage());
        }

    }

    /**
     * 更新附件信息
     * @param scriptExecAuditDto 参数信息
     */
    private void updateAttachment(ScriptExecAuditDto scriptExecAuditDto) throws ScriptException{
        try {
            Long taskId = scriptExecAuditDto.getTaskInfo().getId();
            List<AttachmentDto> scriptTempAttachments = scriptExecAuditDto.getScriptTempAttachments();
            if(!scriptTempAttachments.isEmpty()){
                Long [] ids = new Long[scriptTempAttachments.size()];
                int index = 0;
                for(AttachmentDto attachmentDto : scriptTempAttachments){
                    ids[index++] = attachmentDto.getId();
                }
                taskAttachmentTemplateMapper.batchUpdateByIds(taskId,ids);
            }
        }catch (Exception e){
            throw new ScriptException(e.getMessage());
        }
    }

    /**
     * 下载附件
     * @param id 附件id
     * @return 附件对象
     */
    @Override
    public TaskAttachmentDto selectAttachmentById(Long id) {
        return BeanUtils.copy(taskAttachmentTemplateMapper.selectAttachmentById(id), TaskAttachmentDto.class);
    }

    /**
     * 上传附件
     * @param attachmentDto  文件
     * @return 附件信息
     * @throws ScriptException
     */
    @Override
    public TaskAttachmentDto uploadAttachment(TaskAttachmentDto taskAttachmentDto) throws ScriptException {
        TaskAttachment taskAttachment = new TaskAttachment();
        taskAttachment.setName(taskAttachmentDto.getName());
        taskAttachment.setSize(taskAttachmentDto.getSize());
        taskAttachment.setContents(taskAttachmentDto.getContents());
        taskAttachment.setScriptTaskId(taskAttachmentDto.getScriptTaskId());
        //验证文件大小
        fileSizeValidUtil.validateFileSize(taskAttachment.getSize());
        taskAttachmentTemplateMapper.insertTaskAttachment(taskAttachment);
        return BeanUtils.copy(taskAttachment, TaskAttachmentDto.class);
    }

    /**
     * 根据常用任务id查询当前常用任务的附件
     * @param scriptTaskId 任务id
     * @return 附件集合
     */
    @Override
    public List<AttachmentUploadDto> getTaskTemplateAttachment(Long scriptTaskId){
        List<TaskAttachment> taskAttachments = taskAttachmentTemplateMapper.selectTaskAttachmentByTaskId(scriptTaskId);
        List<AttachmentUploadDto> attachmentUploadDtoList = new ArrayList<>();
        for(TaskAttachment taskAttachment : taskAttachments){
            AttachmentUploadDto attachmentUploadDto = new AttachmentUploadDto();
            attachmentUploadDto.setId(taskAttachment.getId());
            attachmentUploadDto.setName(taskAttachment.getName());
            attachmentUploadDto.setContents(taskAttachment.getContents());
            attachmentUploadDto.setSize(taskAttachment.getSize());
            AttachmentResponseDto attachmentResponseDto = new AttachmentResponseDto();
            attachmentResponseDto.setId(taskAttachment.getId());
            attachmentUploadDto.setResponse(attachmentResponseDto);
            attachmentUploadDtoList.add(attachmentUploadDto);
        }
        return attachmentUploadDtoList;
    }

    /**
     * 根据id删除附件
     * @param id 附件id
     */
    @Override
    public void deleteAttachmentTemplate(Long id) {
        taskAttachmentTemplateMapper.deleteById(id);
    }
}

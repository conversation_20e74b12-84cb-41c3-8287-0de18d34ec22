package com.ideal.script.service.impl;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.ideal.common.util.BeanUtils;
import com.ideal.common.util.PageDataUtil;
import com.ideal.script.mapper.TaskMapper;
import com.ideal.script.service.impl.builders.MyScriptServiceScripts;
import com.ideal.system.common.component.model.CurrentUser;
import com.ideal.script.model.bean.TaskExecuteBean;
import com.ideal.script.model.dto.TaskDto;
import com.ideal.script.model.dto.TaskQueryDto;
import com.ideal.script.model.entity.Task;
import com.ideal.script.service.ITaskService;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 脚本任务Service业务层处理
 *
 * <AUTHOR>
 */
@Service
public class TaskServiceImpl implements ITaskService {
    private final TaskMapper taskMapper;
    private final MyScriptServiceScripts scripts;

    public TaskServiceImpl(@Lazy MyScriptServiceScripts scripts, TaskMapper taskMapper) {
        this.scripts = scripts;
        this.taskMapper = taskMapper;
    }

    /**
     * 查询脚本任务
     *
     * @param id 脚本任务主键
     * @return 脚本任务
     */
    @Override
    public TaskDto selectTaskById(Long id) {
        return BeanUtils.copy(taskMapper.selectTaskById(id), TaskDto.class);
    }

    /**
     * 查询脚本任务信息
     *
     * @param serviceId 业务主键
     * @return 脚本任务
     */
    @Override
    public TaskDto selectTaskByServiceId(Long serviceId, Long taskId) {
        return BeanUtils.copy(taskMapper.selectTaskByServiceId(serviceId, taskId), TaskDto.class);
    }

    /**
     * 查询脚本任务列表
     *
     * @param taskDto 脚本任务
     * @return 脚本任务
     */
    @Override
    public PageInfo<TaskDto> selectTaskList(TaskDto taskDto, int pageNum, int pageSize) {
        PageMethod.startPage(pageNum, pageSize);
        List<Task> taskList = new ArrayList<>();
        if (null != taskDto) {
            Task task = BeanUtils.copy(taskDto, Task.class);
            taskList = taskMapper.selectTaskList(task);
        }
        return PageDataUtil.toDtoPage(taskList, TaskDto.class);

    }

    /**
     * 新增脚本任务
     *
     * @param taskDto 脚本任务
     * @return 结果
     */
    @Override
    public int insertTask(TaskDto taskDto) {
        Task task = BeanUtils.copy(taskDto, Task.class);
        int i = taskMapper.insertTask(task);
        Long id = task.getId();
        taskDto.setId(id);
        return i;
    }

    /**
     * 修改脚本任务
     *
     * @param taskDto 脚本任务
     * @return 结果
     */
    @Override
    public int updateTask(TaskDto taskDto) {
        Task task = BeanUtils.copy(taskDto, Task.class);
        int i =  taskMapper.updateTask(task);
        taskDto.setId(task.getId());
        return i;
    }

    /**
     * 批量删除脚本任务
     *
     * @param ids 需要删除的脚本任务主键
     * @return 结果
     */
    @Override
    public int deleteTaskByIds(Long[] ids) {
        return taskMapper.deleteTaskByIds(ids);
    }

    /**
     * 删除脚本任务信息
     *
     * @param id 脚本任务主键
     * @return 结果
     */
    @Override
    public int deleteTaskById(Long id) {
        return taskMapper.deleteTaskById(id);
    }

    /**
     * 待执行任务列表
     *
     * @param taskExecuteBean 执行任务对象
     * @return List<TaskExecuteBean>
     * <AUTHOR>
     */
    @Override
    public List<TaskExecuteBean> selectTaskReadyToExecuteList(TaskExecuteBean taskExecuteBean, CurrentUser currentUser) {
        taskExecuteBean.setSelectSelfDataFlag(scripts.getScriptBusinessConfig().isSelectSelfDataFlag());
        return taskMapper.selectTask(taskExecuteBean, currentUser, "pend");
    }

    @Override
    public List<TaskExecuteBean> selectTimeTaskList(TaskExecuteBean taskExecuteBean, CurrentUser currentUser) {
        return taskMapper.selectTask(taskExecuteBean, currentUser, "timetask");
    }

    /**
     * 运行中任务列表
     *
     * @param taskExecuteBean 查询条件的封装对象
     * @return List<TaskExecuteBean>
     * <AUTHOR>
     */
    @Override
    public List<TaskExecuteBean> selectRunningScriptTasks(TaskExecuteBean taskExecuteBean, CurrentUser currentUser) {
        int taskSource = 2;
        //是否只查询自己的数据标识
        taskExecuteBean.setSelectSelfDataFlag(scripts.getScriptBusinessConfig().isSelectSelfDataFlag());
        if(taskExecuteBean.getScriptTaskSource() != null && taskExecuteBean.getScriptTaskSource() == taskSource){
            return taskMapper.selectTask(taskExecuteBean, currentUser, "running-test");
        }
        return taskMapper.selectTask(taskExecuteBean, currentUser, "running");

    }

    @Override
    public List<TaskExecuteBean> selectCompleteScriptTasks(TaskExecuteBean taskExecuteBean, CurrentUser currentUser) {
        int taskSource = 2;
        //是否只查询自己的数据标识
        taskExecuteBean.setSelectSelfDataFlag(scripts.getScriptBusinessConfig().isSelectSelfDataFlag());
        if(taskExecuteBean.getScriptTaskSource() != null && taskExecuteBean.getScriptTaskSource() == taskSource){
            return taskMapper.selectTask(taskExecuteBean,currentUser, "finish-test");
        }
        return taskMapper.selectTask(taskExecuteBean, currentUser, "finish");
    }

    /**
     * 根本版本uuid查询该版本运行过的历史任务
     *
     * @param queryParam 脚本任务
     * @param pageNum    当前页
     * @param pageSize   每页条数
     * @return 脚本任务集合
     */
    @Override
    public PageInfo<TaskDto> listTaskHis(TaskQueryDto queryParam, Integer pageNum, Integer pageSize) {
        return selectTaskList(BeanUtils.copy(queryParam, TaskDto.class), pageNum, pageSize);
    }

}

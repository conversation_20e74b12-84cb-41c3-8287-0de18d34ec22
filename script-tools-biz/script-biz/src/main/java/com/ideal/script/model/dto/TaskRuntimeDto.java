package com.ideal.script.model.dto;

import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.sql.Timestamp;


/**
 * agent运行实例对象 ieai_script_task_runtime
 *
 * <AUTHOR>
 */
public class TaskRuntimeDto implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 主键
     */
    private Long id;


    /**
     * ieai_script_task表id
     */
    private Long scriptTaskId;


    /**
     * ieai_script_task_instance表id
     */
    private Long taskInstanceId;


    /**
     * ieai_script_info_version表的isrc_script_uuid
     */
    private String srcScriptUuid;


    /**
     * 状态 5-跳过 10-运行中 20-完成 30-失败 60-终止
     */
    private Integer state;


    /**
     * 脚本名称
     */
    private String scriptName;


    /**
     * agentIp
     */
    private String agentIp;


    /**
     * agent端口
     */
    private Integer agentPort;


    /**
     * agent执行用户
     */
    private String execUser;


    /**
     * provider的ip
     */
    private String providerIp;


    /**
     * provider端口号
     */
    private Integer providerPort;


    /**
     * 开始时间
     */
    private Timestamp startTime;


    /**
     * 结束时间
     */
    private Timestamp endTime;


    /**
     * 执行耗时
     */
    private Long elapsedTime;


    /**
     * 预期正确状态最后一行
     */
    private String expectLastline;





    /**
     * 预期类型  1 - lastLine  2 - exitCode
     */
    private Integer expectType;


    /**
     * 是否超时处理 0-未处理 1-已处理
     */
    private Integer timeout;


    /**
     * 预设超时范围 单位s
     */
    private Long timeoutValue;


    /**
     * 任务来源 0 - 脚本服务化  1 - 标准运维 2-定时任务
     */
    private Integer startType;


    /**
     * 创建日期
     */
    private Timestamp createTime;


    /**
     * 对接管理服务时，返回的taskId
     */
    private Long agentTaskId;


    /**
     * bizId业务主键，执行agent任务对接管理服务时传递的bizId，用这个控制重复消费，以及解决重试agent时返回的结果被当做已经消费过，直接跳过，不解析执行结果问题。
     */
    private String bizId;

    private Long scriptTaskIpsId;

    public Long getScriptTaskIpsId() {
        return scriptTaskIpsId;
    }

    /**
     * 是否重试
     */
    private Boolean retry;

    public Boolean getRetry() {
        return retry;
    }

    public void setRetry(Boolean retry) {
        this.retry = retry;
    }

    public void setScriptTaskIpsId(Long scriptTaskIpsId) {
        this.scriptTaskIpsId = scriptTaskIpsId;
    }

    public Long getAgentTaskId() {
        return agentTaskId;
    }

    public void setAgentTaskId(Long agentTaskId) {
        this.agentTaskId = agentTaskId;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setScriptTaskId(Long scriptTaskId) {
        this.scriptTaskId = scriptTaskId;
    }

    public Long getScriptTaskId() {
        return scriptTaskId;
    }

    public void setTaskInstanceId(Long taskInstanceId) {
        this.taskInstanceId = taskInstanceId;
    }

    public Long getTaskInstanceId() {
        return taskInstanceId;
    }

    public void setSrcScriptUuid(String srcScriptUuid) {
        this.srcScriptUuid = srcScriptUuid;
    }

    public String getSrcScriptUuid() {
        return srcScriptUuid;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public void setScriptName(String scriptName) {
        this.scriptName = scriptName;
    }

    public String getScriptName() {
        return scriptName;
    }

    public void setAgentIp(String agentIp) {
        this.agentIp = agentIp;
    }

    public String getAgentIp() {
        return agentIp;
    }


    public void setExecUser(String execUser) {
        this.execUser = execUser;
    }

    public String getExecUser() {
        return execUser;
    }
    @SuppressWarnings("unused")
    public void setProviderIp(String providerIp) {
        this.providerIp = providerIp;
    }

    public String getProviderIp() {
        return providerIp;
    }

    public String getBizId() {
        return bizId;
    }

    public void setBizId(String bizId) {
        this.bizId = bizId;
    }

    public void setStartTime(Timestamp startTime) {
        this.startTime = startTime;
    }

    public Timestamp getStartTime() {
        return startTime;
    }
    @SuppressWarnings("unused")
    public void setEndTime(Timestamp endTime) {
        this.endTime = endTime;
    }

    public Timestamp getEndTime() {
        return endTime;
    }
    @SuppressWarnings("unused")
    public void setElapsedTime(Long elapsedTime) {
        this.elapsedTime = elapsedTime;
    }

    public Long getElapsedTime() {
        return elapsedTime;
    }

    public void setExpectLastline(String expectLastline) {
        this.expectLastline = expectLastline;
    }

    public String getExpectLastline() {
        return expectLastline;
    }


    public void setTimeoutValue(Long timeoutValue) {
        this.timeoutValue = timeoutValue;
    }

    public Long getTimeoutValue() {
        return timeoutValue;
    }

    public Integer getAgentPort() {
        return agentPort;
    }

    public void setAgentPort(Integer agentPort) {
        this.agentPort = agentPort;
    }

    public Integer getProviderPort() {
        return providerPort;
    }
    @SuppressWarnings("unused")
    public void setProviderPort(Integer providerPort) {
        this.providerPort = providerPort;
    }

    public Integer getExpectType() {
        return expectType;
    }

    public void setExpectType(Integer expectType) {
        this.expectType = expectType;
    }

    public Integer getTimeout() {
        return timeout;
    }

    public void setTimeout(Integer timeout) {
        this.timeout = timeout;
    }

    public Integer getStartType() {
        return startType;
    }

    public void setStartType(Integer startType) {
        this.startType = startType;
    }

    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }

    public Timestamp getCreateTime() {
        return createTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this)
                .append("id", getId())
                .append("scriptTaskId", getScriptTaskId())
                .append("taskInstanceId", getTaskInstanceId())
                .append("srcScriptUuid", getSrcScriptUuid())
                .append("state", getState())
                .append("scriptName", getScriptName())
                .append("agentIp", getAgentIp())
                .append("agentPort", getAgentPort())
                .append("execUser", getExecUser())
                .append("providerIp", getProviderIp())
                .append("providerPort", getProviderPort())
                .append("startTime", getStartTime())
                .append("endTime", getEndTime())
                .append("elapsedTime", getElapsedTime())
                .append("expectLastline", getExpectLastline())
                .append("expectType", getExpectType())
                .append("timeout", getTimeout())
                .append("timeoutValue", getTimeoutValue())
                .append("startType", getStartType())
                .append("createTime", getCreateTime())
                .append("agentTaskId", getAgentTaskId())
                .toString();
    }
}

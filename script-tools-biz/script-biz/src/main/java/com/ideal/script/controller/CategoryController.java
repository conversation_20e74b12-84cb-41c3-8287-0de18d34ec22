package com.ideal.script.controller;

import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.common.util.BeanUtils;
import com.ideal.common.util.spring.MessageUtil;
import com.ideal.sc.util.ValidationUtils;
import com.ideal.script.common.constant.Constants;
import com.ideal.script.common.constant.permission.CategoryServiceBtnPermitConstant;
import com.ideal.script.common.constant.permission.MenuPermitConstant;
import com.ideal.script.common.validation.Create;
import com.ideal.script.common.validation.Query;
import com.ideal.script.common.validation.Update;
import com.ideal.script.dto.CategoryDto;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.model.bean.CategoryOrgBean;
import com.ideal.script.model.bean.CategoryUserBean;
import com.ideal.script.model.dto.CategoryOrgDto;
import com.ideal.script.model.dto.CategoryRoleDto;
import com.ideal.script.model.dto.CategoryUserDto;
import com.ideal.script.service.ICategoryService;
import com.ideal.system.common.component.aop.MethodPermission;
import com.ideal.system.dto.OrgManagementApiDto;
import com.ideal.system.dto.PermissionUserInfoApiDto;
import com.ideal.system.dto.RoleApiDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotEmpty;
import java.util.List;


/**
 * 【请填写功能名称】Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("${app.script-tools-url:}/category")
@Validated
@MethodPermission(MenuPermitConstant.CATEGORY_SERVICE_PER)
public class CategoryController {
    private static final String LIST_SUCCESS = Constants.LIST_SUCCESS;
    private static final Logger logger = LoggerFactory.getLogger(CategoryController.class);


    private final ICategoryService categoryService;

    public CategoryController(ICategoryService categoryService) {
        this.categoryService = categoryService;
    }

    /**
     * 查询【请填写功能名称】列表
     */
    @PostMapping("/listCategory")
    public R<PageInfo<CategoryDto>> listCategory(@RequestBody @Validated(Query.class) TableQueryDto<CategoryDto> tableQueryDTO) {
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, categoryService.selectCategoryList(tableQueryDTO.getQueryParam(), tableQueryDTO.getPageNum(),
                tableQueryDTO.getPageSize()), LIST_SUCCESS);
    }


    @PostMapping("/getCategoryList")
    @MethodPermission(MenuPermitConstant.CATEGORY_SERVICE_OR_TEMPLATE_TASK_OR_TASK_APPLY_PER)
    public R<List<CategoryDto>> getCategoryList(@RequestBody CategoryDto categoryDto) {
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, categoryService.selectCategoryListNoPage(categoryDto), LIST_SUCCESS);
    }


    /**
     * 新增分类（多级）
     */
    @PostMapping("/saveCategory")
    @MethodPermission(CategoryServiceBtnPermitConstant.SAVE_CATEGORY_PER)
    public R<Object> saveCategory(@RequestBody @Validated(Create.class) CategoryDto categoryDto) {
        try {
            categoryService.insertCategory(categoryDto);
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, "", "save.success");
        } catch (ScriptException e) {
            logger.error("saveCategory error", e);
            return ValidationUtils.customFailResult("name", e.getMessage());
        }
    }

    /**
     * 修改分类（多级）
     */
    @PostMapping("/updateCategory")
    @MethodPermission(CategoryServiceBtnPermitConstant.UPDATE_CATEGORY_PER)
    public R<Object> updateCategory(@RequestBody @Validated(Update.class) CategoryDto categoryDto) {
        try {
            categoryService.updateCategory(categoryDto);
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, "", "update.success");
        } catch (ScriptException e) {
            logger.error("updateCategory is error:", e);
            return ValidationUtils.customFailResult("name", e.getMessage());
        }
    }

    /**
     * 删除【请填写功能名称】
     */
    @GetMapping("/removelCategory")
    @MethodPermission(CategoryServiceBtnPermitConstant.REMOVEL_CATEGORY_PER)
    public R<Object> removeCategory(@RequestParam(value = "ids") @Validated @NotEmpty Long[] ids) {
        try {
            categoryService.deleteCategoryByIds(ids);
        } catch (ScriptException e) {
            return ValidationUtils.customFailResult("deleteCategory", e.getMessage());
        } catch (Exception e) {
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, "remove.fail");
        }
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, "remove.success");
    }

    /**
     * 查询【请填写功能名称】列表
     *
     * @return R<List < CategoryDto>>
     */
    @PostMapping("/listFirstCategory")
    public R<List<CategoryDto>> listFirstCategory() {
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, categoryService.listFirstCategory(), LIST_SUCCESS);
    }

    /**
     * 查询【请填写功能名称】列表
     *
     * @return R<List < CategoryDto>>
     */
    @GetMapping("/listMultiCategory")
    @MethodPermission(MenuPermitConstant.CATEGORY_SERVICE_OR_MY_SCRIPT_PER)
    public R<List<CategoryDto>> listMultiCategory(@RequestParam(value = "level") Long level, @RequestParam(value = "parentId", required = false) Long parentId) {
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, categoryService.listMultiCategory(level.intValue(), parentId), LIST_SUCCESS);
    }

    /**
     * 查询【请填写功能名称】列表
     *
     * @param parentId parentId
     * @return R<List < CategoryDto>>
     */
    @GetMapping("/listNextCategory")
    public R<List<CategoryDto>> listNextCategory(@RequestParam(value = "parentId") long parentId) {
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, categoryService.listNextCategory(parentId), LIST_SUCCESS);
    }

    /**
     * 分类授权部门
     *
     * @param categoryOrgDto 实体dto
     */
    @PostMapping("/assignCategoryToOrg")
    @MethodPermission(CategoryServiceBtnPermitConstant.CAT_ORG_PERMISSION_PER)
    public R<Object> assignCategoryToOrg(@RequestBody CategoryOrgDto categoryOrgDto) {
        categoryService.assignCategoryToOrg(categoryOrgDto);
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, MessageUtil.message("save.success"));
    }

    /**
     * 查询该分类的部门绑定信息
     *
     * @param categoryId 分类id
     * @return 详细信息
     */
    @GetMapping("/getCategoryOrgRelations")
    public R<CategoryOrgDto> getCategoryOrgRelations(@RequestParam(value = "categoryId") long categoryId) {

        CategoryOrgBean categoryOrgRelations = categoryService.getCategoryOrgRelations(categoryId);
        CategoryOrgDto categoryOrgDto = BeanUtils.copy(categoryOrgRelations, CategoryOrgDto.class);
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, categoryOrgDto, MessageUtil.message(LIST_SUCCESS));

    }


    /**
     * 分类授权用户
     *
     * @param categoryUserDto dto
     * @return 返回信息
     * @throws ScriptException 异常
     */
    @PostMapping("assignCategoryToUser")
    @MethodPermission(CategoryServiceBtnPermitConstant.CAT_USER_PERMISSION_PER)
    public R<Object> assignCategoryToUser(@RequestBody CategoryUserDto categoryUserDto) throws ScriptException {
        categoryService.assignCategoryToUser(categoryUserDto);
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, MessageUtil.message("save.success"));
    }

    /**
     * 查询该分类的部门绑定信息
     *
     * @param categoryId 分类id
     * @return 详细信息
     */
    @GetMapping("/getCategoryUserRelations")
    public R<CategoryUserDto> getCategoryUserRelations(@RequestParam(value = "categoryId") long categoryId) {

        CategoryUserBean categoryUserRelations = categoryService.getCategoryUserRelations(categoryId);
        CategoryUserDto categoryUserDto = BeanUtils.copy(categoryUserRelations, CategoryUserDto.class);
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, categoryUserDto, MessageUtil.message(LIST_SUCCESS));

    }

    /**
     * 获取用户列表（分类绑定用户）
     *
     * @param categoryUserBean 查询条件
     * @return 用户列表
     */

    @PostMapping("/queryPermissionUserInfoList")
    public R<List<PermissionUserInfoApiDto>> queryPermissionUserInfoList(@RequestBody CategoryUserBean categoryUserBean) {
        List<PermissionUserInfoApiDto> userInfoListApi = categoryService.queryPermissionUserInfoList(categoryUserBean);
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, userInfoListApi, MessageUtil.message(LIST_SUCCESS));
    }

    /**
     * 获取用户列表（分类绑定用户）(分页)
     *
     * @param tableQueryDTO 查询条件
     * @return 用户列表
     */

    @PostMapping("/queryPermissionUserInfoPage")
    public R<PageInfo<PermissionUserInfoApiDto>> queryPermissionUserInfoPage(@RequestBody TableQueryDto<CategoryUserBean> tableQueryDTO) {
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, categoryService.queryPermissionUserInfoPage(tableQueryDTO.getQueryParam(), tableQueryDTO.getPageNum(),
                tableQueryDTO.getPageSize()), MessageUtil.message(LIST_SUCCESS));
    }


    /**
     * 查询部门树形结构
     *
     * @param orgManagementApiDto 部门查询信息
     * @return 部门树形结构
     */
    @PostMapping("/selectOrgManagementTree")
    public R<List<OrgManagementApiDto>> selectOrgManagementTree(OrgManagementApiDto orgManagementApiDto) {
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, categoryService.selectOrgManagementTree(orgManagementApiDto), MessageUtil.message(LIST_SUCCESS));
    }

    /**
     * 获取未共享脚本的部门
     *
     * @return 部门树结构
     */
    @PostMapping("/selectNotShareOrgManagementTree")
    @MethodPermission(MenuPermitConstant.CATEGORY_SERVICE_OR_MY_SCRIPT_PER)
    public R<Object> selectNotShareOrgManagementTree(@RequestParam(value = "scriptVersionId") Long scriptVersionId) {
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, categoryService.selectNotShareOrgManagementTree(scriptVersionId), MessageUtil.message(LIST_SUCCESS));
    }

    /**
     * 深度遍历构建分类树
     *
     * @param categoryDto 分类实体
     * @return 分类树
     */
    @PostMapping("/selectCategoryListDFS")
    @MethodPermission(MenuPermitConstant.CATEGORY_SERVICE_DFS_PER)
    public R<List<CategoryDto>> selectCategoryListDFS(@RequestBody CategoryDto categoryDto) {
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, categoryService.selectCategoryListDFS(categoryDto), LIST_SUCCESS);
    }

    /**
     * 获取分类全路径
     * @param categoryId 分类id
     * @return 分类全路径
     */
    @GetMapping("/getCategoryFullPath")
    @MethodPermission(MenuPermitConstant.CATEGORY_SERVICE_OR_MY_SCRIPT_PER)
    public R<String> getCategoryFullPath(@RequestParam("categoryId") Long categoryId) {
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, categoryService.getCategoryFullPath(categoryId), LIST_SUCCESS);
    }

    /**
     * 查询可授权的角色列表
     *
     * @param tableQueryDTO 角色查询信息
     * @return 角色列表
     */
    @PostMapping("/selectRoleManagementList")
    public R<PageInfo<RoleApiDto>> selectRoleManagementList(@RequestBody TableQueryDto<RoleApiDto> tableQueryDTO) {
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, categoryService.selectRoleManagementList(tableQueryDTO.getQueryParam(),tableQueryDTO.getPageNum(),tableQueryDTO.getPageSize()), MessageUtil.message(LIST_SUCCESS));
    }

    /**
     * 分类授权角色
     *
     * @param categoryRoleDto 实体dto
     */
    @PostMapping("/assignCategoryToRole")
    @MethodPermission(CategoryServiceBtnPermitConstant.CAT_ROLE_PERMISSION_PER)
    public R<Object> assignCategoryToRole(@RequestBody CategoryRoleDto categoryRoleDto) {
        categoryService.assignCategoryToRole(categoryRoleDto);
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, MessageUtil.message("save.success"));
    }

    /**
     * 查询该分类的角色绑定信息
     *
     * @param categoryId 分类id
     * @return 详细信息
     */
    @GetMapping("/getCategoryRoleRelations")
    public R<CategoryRoleDto> getCategoryRoleRelations(@RequestParam(value = "categoryId") long categoryId) {
        CategoryRoleDto CategoryRoleDto = categoryService.getCategoryRoleRelations(categoryId);
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, CategoryRoleDto, MessageUtil.message(LIST_SUCCESS));
    }

    /**
     * 根据角色获取用户列表（分类绑定用户）(分页)
     *
     * @param tableQueryDTO 查询条件
     * @return 用户列表
     */

    @PostMapping("/queryPermissionUserInfoPageByRole")
    public R<PageInfo<PermissionUserInfoApiDto>> queryPermissionUserInfoPageByRole(@RequestBody TableQueryDto<CategoryRoleDto> tableQueryDTO) {
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, categoryService.queryPermissionUserInfoPageByRole(tableQueryDTO.getQueryParam(), tableQueryDTO.getPageNum(),
                tableQueryDTO.getPageSize()), MessageUtil.message(LIST_SUCCESS));
    }


}

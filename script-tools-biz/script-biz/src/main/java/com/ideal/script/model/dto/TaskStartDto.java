package com.ideal.script.model.dto;

import org.apache.commons.lang3.builder.ToStringBuilder;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 任务启动封装参数Dto
 * <AUTHOR>
 */
public class TaskStartDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 脚本任务主键
     */
    private Long scriptTaskId;

    /**
     * ieai_script_info_version表的isrcScriptUuid
     */
    private String srcScriptUuid;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * ieai_script_info表的唯一uuid
     */
    private String infoUniqueUuid;


    /**
     * 脚本类型 shell、bat、perl、python、powershell
     */
    private String scriptType;

    /**
     * 脚本任务运行实例ID(ieai_script_task_instance)
     **/
    private Long iscriptTaskInstanceId;

    /**
     * 脚本任务执行服务器（按选择执行），勾选的agent，数据来自ieai_script_task_ips表
     */
    private Long[] taskIps;

    /**
     * 原来V8启动有butterflyversion、oddNumbersType，观察好像是银行的个性化的需求，先不考虑这个<br/>
     * 驱动模式 (1:按勾选执行 2：分批执行 3：忽略异常分批执行 4：队列执行）
     */
    @NotNull
    @Valid
    private Integer driveMode;

    /**
     * 是否重试
     */
    private Boolean retry;

    /**
     * 重试时的runtimeId
     */
    private Long retryRuntimeId;

    /**
     * 任务来源
     */
    private Integer startType;

    /**
     * 并发数
     */
    private Integer eachNum = null;

    /**
     * 是否为首批agent
     */
    private boolean firstBatch = true;

    /**
     * 脚本英文名
     */
    private String scriptNameEn;

    /**
     * 任务类型 0常用任务 1克隆任务 生成克隆、常用任务时使用
     */
    private Integer taskType;

    /**
     * 是否是转换了启动模式（原本是选择执行或者是分批执行，改为队列执行或者忽略异常分批执行）
     */
    private boolean transDriveModeFlag = true;

    /**
     * 调用方（其它业务）任务id
     */
    private Long callerTaskId;

    public Long getCallerTaskId() {
        return callerTaskId;
    }

    public void setCallerTaskId(Long callerTaskId) {
        this.callerTaskId = callerTaskId;
    }

    public boolean isTransDriveModeFlag() {
        return transDriveModeFlag;
    }

    public void setTransDriveModeFlag(boolean transDriveModeFlag) {
        this.transDriveModeFlag = transDriveModeFlag;
    }

    public Integer getTaskType() {
        return taskType;
    }

    public void setTaskType(Integer taskType) {
        this.taskType = taskType;
    }

    public String getScriptNameEn() {
        return scriptNameEn;
    }

    public void setScriptNameEn(String scriptNameEn) {
        this.scriptNameEn = scriptNameEn;
    }

    public boolean isFirstBatch() {
        return firstBatch;
    }

    public void setFirstBatch(boolean firstBatch) {
        this.firstBatch = firstBatch;
    }

    public Integer getEachNum() {
        return eachNum;
    }

    public void setEachNum(Integer eachNum) {
        this.eachNum = eachNum;
    }

    public Integer getStartType() {
        return startType;
    }

    public void setStartType(Integer startType) {
        this.startType = startType;
    }

    public Long getRetryRuntimeId() {
        return retryRuntimeId;
    }

    public void setRetryRuntimeId(Long retryRuntimeId) {
        this.retryRuntimeId = retryRuntimeId;
    }

    public Integer getDriveMode() {
        return driveMode;
    }

    @SuppressWarnings("unused")
    public void setDriveMode(Integer driveMode) {
        this.driveMode = driveMode;
    }

    public Long getScriptTaskId() {
        return scriptTaskId;
    }

    public void setScriptTaskId(Long scriptTaskId) {
        this.scriptTaskId = scriptTaskId;
    }

    public String getSrcScriptUuid() {
        return srcScriptUuid;
    }

    public void setSrcScriptUuid(String srcScriptUuid) {
        this.srcScriptUuid = srcScriptUuid;
    }

    public String getTaskName() {
        return taskName;
    }

    @SuppressWarnings("unused")
    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public String getInfoUniqueUuid() {
        return infoUniqueUuid;
    }

    @SuppressWarnings("unused")
    public void setInfoUniqueUuid(String infoUniqueUuid) {
        this.infoUniqueUuid = infoUniqueUuid;
    }

    public String getScriptType() {
        return scriptType;
    }

    public void setScriptType(String scriptType) {
        this.scriptType = scriptType;
    }

    public Long getIscriptTaskInstanceId() {
        return iscriptTaskInstanceId;
    }

    @SuppressWarnings("unused")
    public void setIscriptTaskInstanceId(Long iscriptTaskInstanceId) {
        this.iscriptTaskInstanceId = iscriptTaskInstanceId;
    }

    public Long[] getTaskIps() {
        return taskIps;
    }

    public void setTaskIps(Long[] taskIps) {
        this.taskIps = taskIps;
    }

    public Boolean getRetry() {
        return retry;
    }

    public void setRetry(Boolean retry) {
        this.retry = retry;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this)
                .append("scriptTaskId", getScriptTaskId())
                .append("srcscriptuuid", getSrcScriptUuid())
                .append("taskName", getTaskName())
                .append("infoUniqueUuid", getInfoUniqueUuid())
                .append("scriptType", getScriptType())
                .append("iscriptTaskInstanceId", getIscriptTaskInstanceId())
                .append("taskIps", getTaskIps())
                .append("retry",getRetry())
                .toString();
    }
}

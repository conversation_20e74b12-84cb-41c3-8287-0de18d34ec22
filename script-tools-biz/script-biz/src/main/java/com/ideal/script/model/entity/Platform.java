package com.ideal.script.model.entity;

import com.ideal.snowflake.annotion.IdGenerator;

import javax.validation.constraints.Min;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@SuppressWarnings("unused")
public class Platform implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @IdGenerator
    @Min(value = 0)
    private Long id;

    /**
     * 平台名称
     */
    private String name;
    /**
     * 平台值
     */
    private String codevalue;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCodevalue() {
        return codevalue;
    }

    public void setCodevalue(String codevalue) {
        this.codevalue = codevalue;
    }
}

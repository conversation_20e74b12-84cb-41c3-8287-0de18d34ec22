package com.ideal.script.model.entity;

import com.ideal.sc.annotation.CreatorUserId;
import com.ideal.sc.annotation.CreatorUserName;
import com.ideal.sc.annotation.UpdatorUserId;
import com.ideal.sc.annotation.UpdatorUserName;
import com.ideal.snowflake.annotion.IdGenerator;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.Min;
import java.io.Serializable;
import java.sql.Timestamp;

/**
 * 变量库基础信息发布对象 ieai_script_variable_p
 *
 * <AUTHOR>
 */
@SuppressWarnings("unused")
public class VariablePublish implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * IID
     */
    @IdGenerator
    @Min(value = 0)
    private Long id;

    /**
     * ieai_script_variable_class变量分类表id
     */
    private Long scriptVariableClassId;

    /**
     * 变量名
     */
    private String name;

    /**
     * 变量类型
     */
    private Integer type;

    /**
     * 变量值
     */
    private String value;

    /**
     * 描述
     */
    private String desc;

    /**
     * 是否为内置变量  2内置 1自定义
     */
    private Integer attribute;

    /**
     * 是否全域有效  1是  0否
     */
    private Integer global;

    /**
     * 状态 1草稿 2已发布  3发布后修改变为草稿
     */
    private Integer status;

    /**
     * 用户全部有效 1是 0否
     */
    private Integer userglobal;

    /**
     * 唯一标识
     */
    private String md5;

    /**
     * 创建人id
     */
    @CreatorUserId
    private Long creatorId;

    /**
     * 创建人名称
     */
    @CreatorUserName
    private String creatorName;

    /**
     * 修改人id
     */
    @UpdatorUserId
    private Long updatorId;

    /**
     * 修改人名称
     */
    @UpdatorUserName
    private String updatorName;

    /**
     * 创建时间
     */
    
    private Timestamp createTime;

    /**
     * 修改时间
     */
    
    private Timestamp updateTime;

    private String keyword;

    private String bindState;

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public String getBindState() {
        return bindState;
    }

    public void setBindState(String bindState) {
        this.bindState = bindState;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public void setScriptVariableClassId(Long scriptVariableClassId) {
        this.scriptVariableClassId = scriptVariableClassId;
    }

    public Long getScriptVariableClassId() {
        return scriptVariableClassId;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getType() {
        return type;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public void setAttribute(Integer attribute) {
        this.attribute = attribute;
    }

    public Integer getAttribute() {
        return attribute;
    }

    public void setGlobal(Integer global) {
        this.global = global;
    }

    public Integer getGlobal() {
        return global;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getStatus() {
        return status;
    }

    public void setUserglobal(Integer userglobal) {
        this.userglobal = userglobal;
    }

    public Integer getUserglobal() {
        return userglobal;
    }

    public void setMd5(String md5) {
        this.md5 = md5;
    }

    public String getMd5() {
        return md5;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setUpdatorId(Long updatorId) {
        this.updatorId = updatorId;
    }

    public Long getUpdatorId() {
        return updatorId;
    }

    public void setUpdatorName(String updatorName) {
        this.updatorName = updatorName;
    }

    public String getUpdatorName() {
        return updatorName;
    }

    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }

    public Timestamp getCreateTime() {
        return createTime;
    }

    public void setUpdateTime(Timestamp updateTime) {
        this.updateTime = updateTime;
    }

    public Timestamp getUpdateTime() {
        return updateTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("iid", getId())
                .append("scriptVariableClassId", getScriptVariableClassId())
                .append("name", getName())
                .append("type", getType())
                .append("value", getValue())
                .append("desc", getDesc())
                .append("attribute", getAttribute())
                .append("global", getGlobal())
                .append("status", getStatus())
                .append("userglobal", getUserglobal())
                .append("md5", getMd5())
                .append("creatorId", getCreatorId())
                .append("creatorName", getCreatorName())
                .append("updatorId", getUpdatorId())
                .append("updatorName", getUpdatorName())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .append("keyword", getKeyword())
                .append("bindState", getBindState())
                .toString();
    }
}

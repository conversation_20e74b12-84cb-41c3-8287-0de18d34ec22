package com.ideal.script.service;

import com.github.pagehelper.PageInfo;
import com.ideal.script.dto.CategoryDto;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.model.bean.CategoryOrgBean;
import com.ideal.script.model.bean.CategoryPermissionAware;
import com.ideal.script.model.bean.CategoryPermissionInfo;
import com.ideal.script.model.bean.CategoryUserBean;
import com.ideal.script.model.bean.UserBean;
import com.ideal.script.model.dto.CategoryOrgDto;
import com.ideal.script.model.dto.CategoryRoleDto;
import com.ideal.script.model.dto.CategoryUserDto;
import com.ideal.script.model.entity.Category;
import com.ideal.system.common.component.model.CurrentUser;
import com.ideal.system.dto.OrgManagementApiDto;
import com.ideal.system.dto.PermissionUserInfoApiDto;
import com.ideal.system.dto.RoleApiDto;

import java.util.List;

/**
 * 【请填写功能名称】Service接口
 * 
 * <AUTHOR>
 */
 public interface ICategoryService
{
    /**
     * 查询【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
     CategoryDto selectCategoryById(Long id);

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param categoryDto 【请填写功能名称】
     * @param pageNum 【请填写功能名称】
     * @param pageSize 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
     PageInfo<CategoryDto> selectCategoryList(CategoryDto categoryDto, int pageNum, int pageSize);

    /**
     * 新增【请填写功能名称】
     * 
     * @param categoryDto 【请填写功能名称】
     * @return 结果
     * @throws ScriptException 脚本异常
     */
     int insertCategory(CategoryDto categoryDto) throws ScriptException;

    /**
     * 修改【请填写功能名称】
     * 
     * @param categoryDto 【请填写功能名称】
     * @return 结果
     */
     int updateCategory(CategoryDto categoryDto) throws ScriptException;

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param ids 需要删除的【请填写功能名称】主键集合
     * @throws ScriptException 抛出自定义通知异常
     * @return 结果
     */
     int deleteCategoryByIds(Long[] ids) throws ScriptException;

    /**
     * 删除【请填写功能名称】信息
     * 
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
     int deleteCategoryById(Long id);

    /**
     * 获取一级分类列表
     * @return  结果
     */
    List<CategoryDto> listFirstCategory();
    /**
     * 获取分类列表
     * @param level 类别
     * @param parentId 父id
     * @return  结果
     */
    List<CategoryDto> listMultiCategory(Integer level,Long parentId);
    /**
     *  获取指定分类下的所有子分类
     * @param parentId  父类id
     * @return      结果
     */
     List<CategoryDto> listNextCategory(long parentId);

    /**
     * 查询
     * @param ids   id数组
     * @return  结果
     */
     List<Category> selectCategoryByIds(Long[] ids);

    /**
     * 功能描述：获取脚本分类全路径
     *
     * @param categoryId 脚本当前分类id
     * @return {@link String }
     */
     String getCategoryFullPath(long categoryId);

    /**
     * 功能描述： 通过分类等级、分类名称、所属父分类Id查找分类信息
     *
     * @param level 分类等级
     * @param name 分类名称
     * @param parentId 所属父分类Id
     * @throws  ScriptException 自定义异常
     * @return {@link CategoryDto }
     */
    CategoryDto findByLevelAndNameAndParentId(Integer level, String name, Long parentId) throws ScriptException;

    /**
     * 功能描述：
     *
     * @param category 当前分类的 Category 对象
     * @return {@link Category }
     * <AUTHOR>
     */
    Category setChildrenForCategoryAndParent(Category category);


    /**
     * 查询当前分类及其子分类Id集合
     *
     * @param categoryId 当前分类Id
     * @return {@link List }<{@link Integer }>
     */
    List<Long> getAllCategoryIds(Long categoryId);

    /**
     * 获取当前分类以及对应子分类的递归实现
     *
     * @param category 类别信息
     * @param queryChildren 是否查询子分类
     * @return {@link List}<{@link Category}> 类别列表
     */
    List<Category>  getCategoryWithChildren(Category category,Boolean queryChildren);

    List<CategoryDto> selectCategoryListNoPage(CategoryDto categoryDto);


    /**
     * 分类授权部门
     * @param categoryOrgDto 实体dto
     */
    void assignCategoryToOrg(CategoryOrgDto categoryOrgDto);

    /**
     * 查询该分类的部门绑定信息
     * @param categoryId    分类id
     * @return  详细信息
     */
    CategoryOrgBean getCategoryOrgRelations(long categoryId);

    /**
     * 分类授权用户
     * @param categoryUserDto 实体类dto
     */
    void assignCategoryToUser(CategoryUserDto categoryUserDto) throws ScriptException;

    /**
     * 查询该分类的用户绑定信息
     * @param categoryId    分类id
     * @return  详细信息
     */
    CategoryUserBean getCategoryUserRelations(long categoryId);

    /**
     * 调用平台管理接口查询用户
     * @param categoryUserBean 查询条件
     * @return  用户列表
     */
    List<PermissionUserInfoApiDto> queryPermissionUserInfoList(CategoryUserBean categoryUserBean);

    /**
     * 查询部门树形结构
     * @param orgManagementApiDto   部门信息查询条件
     * @return  部门树形
     */
    List<OrgManagementApiDto> selectOrgManagementTree(OrgManagementApiDto orgManagementApiDto);

    /**
     * 获取未共享脚本的部门
     * @return 部门树结构
     */
    List<OrgManagementApiDto> selectNotShareOrgManagementTree(Long scriptVersionId);

    /**
     * 深度优先递归遍历分类树
     * @param categoryDto   分类查询信息
     * @return  分类树
     */
    List<CategoryDto> selectCategoryListDFS(CategoryDto categoryDto);

    //根据部门查询分类
    List<Long> getCategoryByOrgCode(String orgCode);

    /**
     * 查询用户列表
     * @param categoryIdList 分类id列表
     * @return  用户列表
     */
    List<UserBean> getUserByCategoryIds(List<Long> categoryIdList);

    /**
     * 分页查询符合权限的用户
     * @param queryParam    查询条件
     * @param pageNum   页码
     * @param pageSize  大小
     * @return  用户分页列表
     */
    PageInfo<PermissionUserInfoApiDto> queryPermissionUserInfoPage(CategoryUserBean queryParam, Integer pageNum, Integer pageSize);

    /**
     *根据分类的id拼接分类全路径
     * @param category2 分类实体类
     * @return  分类全路径
     */
    String buildCategoryPath(Category category2);

    /**
     * 处理存在%和_时的路径
     * @param categoryPath  分类路径
     * @return  处理好的路径
     */
    String handleCategoryPath(String categoryPath);

    /**
     * 为查询条件Bean设置用户组织机构权限相关信息
     *
     * @param categoryId 分类ID
     * @param currentUser 当前用户
     * @return 权限相关信息的结果对象
     */
    CategoryPermissionInfo setCategoryPermissionInfo(Long categoryId, CurrentUser currentUser);

    /**
     * 为查询条件Bean设置用户组织机构权限相关信息
     * 此方法直接将权限信息设置到实现了CategoryPermissionAware接口的Bean对象中
     *
     * @param bean CategoryPermissionAware类型的Bean对象
     * @param currentUser 当前用户
     */
    <T extends CategoryPermissionAware> void setCategoryPermission(T bean, CurrentUser currentUser);

    /**
     * 查询角色列表
     * @param queryParam   角色信息查询条件
     * pageNum
     * pageSize
     * @return  角色列表
     */
    PageInfo<RoleApiDto> selectRoleManagementList(RoleApiDto queryParam, Integer pageNum, Integer pageSize);

    /**
     * 分类授权角色
     * @param categoryRoleDto 实体dto
     */
    void assignCategoryToRole(CategoryRoleDto categoryRoleDto);

    /**
     * 分类已经绑定的角色
     * @param categoryId
     */
    CategoryRoleDto getCategoryRoleRelations(long categoryId);

    /**
     * 根据角色查询可授权用户列表
     * @param queryParam
     * pageNum
     * pageSize
     * @return  用户列表
     */
    PageInfo<PermissionUserInfoApiDto> queryPermissionUserInfoPageByRole(CategoryRoleDto queryParam, Integer pageNum, Integer pageSize);
}

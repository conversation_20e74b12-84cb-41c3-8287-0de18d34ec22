package com.ideal.script.api;

import com.github.pagehelper.PageInfo;
import com.ideal.common.util.BeanUtils;
import com.ideal.script.dto.*;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.service.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 获取脚本信息
 *
 * <AUTHOR>
 */
@Service
@Primary
public class ScriptInfoApi implements IScriptInfo {

    private static final Logger logger = LoggerFactory.getLogger(ScriptInfoApi.class);

    private final IInfoService infoService;
    private final ICategoryService categoryService;

    private final IMyScriptService iMyScriptService;
    private final IReleaseMediaService releaseMediaService;
    private final ITaskRuntimeService taskRuntimeService;

    private final IAttachmentService attachmentService;

    public ScriptInfoApi(IInfoService infoService, ICategoryService categoryService, IMyScriptService iMyScriptService, IReleaseMediaService releaseMediaService, ITaskRuntimeService taskRuntimeService, IAttachmentService attachmentService) {
        this.infoService = infoService;
        this.categoryService = categoryService;
        this.iMyScriptService = iMyScriptService;
        this.releaseMediaService = releaseMediaService;
        this.taskRuntimeService = taskRuntimeService;
        this.attachmentService = attachmentService;
    }


    /**
     * 获取脚本内容、附件、参数
     *
     * @param scriptInfoQueryDto 脚本版本Uuid
     * @return ScriptInfoDto
     * @see IScriptInfo#getScriptInfo(ScriptInfoQueryDto)
     */
    @Override
    public ScriptDubboInfoDto getScriptInfo(ScriptInfoQueryDto scriptInfoQueryDto) throws ScriptException {
        return iMyScriptService.getScriptDetailForDubbo(scriptInfoQueryDto);
    }

    /**
     * 查询分类树
     *
     * @param categoryQueryDto 分类查询条件
     * @return {@link List}<{@link CategoryApiDto}>
     * @see IScriptInfo#getCategoryTree(CategoryQueryDto)
     */
    @Override
    public List<CategoryApiDto> getCategoryTree(CategoryQueryDto categoryQueryDto) {
        return infoService.getCategoryTreeApi(categoryQueryDto);
    }

    /**
     * 查询脚本信息
     *
     * @param scriptInfoQueryDto 脚本信息查询条件
     * @return {@link List}<{@link ScriptInfoApiDto}>
     * @see IScriptInfo#getScriptInfoList(ScriptInfoQueryDto)
     */
    @Override
    public List<ScriptInfoApiDto> getScriptInfoList(ScriptInfoQueryDto scriptInfoQueryDto) {
        //默认查脚本服务化的脚本
        if(scriptInfoQueryDto.getScriptSource() == null){
            scriptInfoQueryDto.setScriptSource(0);
        }
        return iMyScriptService.selectScriptList(scriptInfoQueryDto);
    }

    /**
     * 分页查询脚本基础信息列表
     *
     * @param scriptInfoQueryDto 脚本信息查询条件
     * @return {@link PageInfo}<{@link ScriptInfoApiDto}>
     * @see IScriptInfo#getScriptInfoPageList(ScriptInfoQueryDto)
     */
    @Override
    public PageInfo<ScriptInfoApiDto> getScriptInfoPageList(ScriptInfoQueryDto scriptInfoQueryDto) {
        //默认查脚本服务化的脚本
        if(scriptInfoQueryDto.getScriptSource() == null){
            scriptInfoQueryDto.setScriptSource(0);
        }
        return iMyScriptService.selectScriptPageList(scriptInfoQueryDto);
    }

    /**
     * 根据上级分类，获取分类列表
     *
     * @param categoryQueryDto 分类查询条件
     * @return {@link List}<{@link CategoryApiDto}> 结果
     * @see IScriptInfo#getMultiCategoryList(CategoryQueryDto)
     */
    @Override
    public List<CategoryApiDto> getMultiCategoryList(CategoryQueryDto categoryQueryDto) {
        logger.info(" getMultiCategoryList param is ,{}", categoryQueryDto);
        List<CategoryDto> categoryDtoList = categoryService.listMultiCategory(categoryQueryDto.getLevel(), categoryQueryDto.getParentId());
        return BeanUtils.copy(categoryDtoList, CategoryApiDto.class);
    }

    /**
     * 新增脚本
     *
     * @param scriptInfoDto 脚本基本信息
     * @return 脚本保存信息
     * @see IScriptInfo#saveMyScript(ScriptInfoDto)
     */
    @Override
    public String saveMyScript(ScriptInfoDto scriptInfoDto) throws ScriptException {
        iMyScriptService.saveScript(scriptInfoDto);
        return scriptInfoDto.getScriptVersionDto().getSrcScriptUuid();
    }

    /**
     * @param scriptInfoDto 脚本基本信息
     * @return 脚本修改信息
     * @see IScriptInfo#updateMyScript(ScriptInfoDto)
     */
    @Override
    public String updateMyScript(ScriptInfoDto scriptInfoDto) throws ScriptException {
        iMyScriptService.updateMyScript(scriptInfoDto);
        return scriptInfoDto.getScriptVersionDto().getSrcScriptUuid();
    }

    /**
     * 脚本发布
     * @param publishDto    发布信息dto
     * @throws ScriptException  自定义脚本异常
     * @see IScriptInfo#publishScript(PublishDto)
     */
    @Override
    public void publishScript(PublishDto publishDto) throws ScriptException {
        iMyScriptService.publishScript(publishDto);
    }

    /**
     * 脚本自动发布，跳过双人复核，目前仅为工具箱提供
     * @param publishDto    发布信息dto
     * @throws ScriptException  自定义脚本异常
     * @see IScriptInfo#publishScript(PublishDto)
     */
    @Override
    public void publishScriptAuto(PublishDto publishDto) throws ScriptException {
        iMyScriptService.publishScriptAuto(publishDto);
    }

    /**
     * 调用脚本服务化导出脚本功能
     *
     * @param srcScriptUuids 脚本的uuid
     * @return {@link List}<{@link ScriptFileImportExportApiDto}> 结果
     * @see IScriptInfo#exportScriptProduction(List<String>)
     */
    @Override
    public ScriptFileImportExportApiDto exportScriptProduction(List<String> srcScriptUuids){
        return releaseMediaService.exportScriptProductionApi(srcScriptUuids);
    }

    /**
     * 调用脚本服务化导入脚本功能（服务投产）
     *
     * @param scriptFileImportExportApiDto 脚本的uuid
     * @return {@link List}<{@link Map}> 结果
     * @see IScriptInfo#importScriptProduction(ScriptFileImportExportApiDto scriptFileImportExportApiDto)
     */
    @Override
    public Map<String,String> importScriptProduction(ScriptFileImportExportApiDto scriptFileImportExportApiDto){
        return releaseMediaService.importScriptProduction(scriptFileImportExportApiDto);
    }

    /**
     * 根据任务的instanceId获取agent运行数据
     *
     * @param taskRuntimeQueryApiDto 查询条件
     * @return {@link PageInfo} 返回结果
     * @see IScriptInfo#getTaskRuntimeInfoByInstanceId(TaskRuntimeQueryApiDto)
     */
    @Override
    public PageInfo<TaskRuntimeApiDto>  getTaskRuntimeInfoByInstanceId(TaskRuntimeQueryApiDto taskRuntimeQueryApiDto){
        return taskRuntimeService.getTaskRuntimeInfoByInstanceId(taskRuntimeQueryApiDto);
    }

    /**
     * 根据agent运行的实例id获取标准输出
     *
     * @param taskRuntimeId 查询条件
     * @return {@link String} 返回结果
     * @see IScriptInfo#getStdoutByTaskRuntimeId(Long)
     */
    @Override
    public String getStdoutByTaskRuntimeId(Long taskRuntimeId) throws ScriptException {
        return taskRuntimeService.getOutPutMessage(taskRuntimeId);
    }

    /**
     * 根据任意版本uuid查询默认版本信息
     * @param scriptInfoQueryDto    脚本版本Uuid
     * @return  ScriptInfoDto
     * @see IScriptInfo#getDefaultScriptInfo(ScriptInfoQueryDto)
     */
     public ScriptDubboInfoDto getDefaultScriptInfo(ScriptInfoQueryDto scriptInfoQueryDto) throws ScriptException {
         return iMyScriptService.getDefaultScriptInfoApi(scriptInfoQueryDto);
     }

    /**
     * 上传附件对外接口
     * @param attachmentDto   附件信息列表
     * @return  返回成功信息
     * @throws ScriptException  脚本异常
     * @throws IOException  io异常
     * @see IScriptInfo#uploadAttachment(AttachmentDto)
     */
    @Override
    public AttachmentDto uploadAttachment(AttachmentDto attachmentDto) throws ScriptException, IOException {
        return attachmentService.uploadAttachment(attachmentDto);
    }

    /**
     * 上传临时附件接口
     * @param scriptFileAttachmentTempList 附件参数
     * @return 上传成功附件id信息
     * @throws ScriptException 脚本服务化异常
     * @throws IOException IO异常
     * @see IScriptInfo#uploadAttachmentTemp(List)
     */
    @Override
    public ScriptAttachmentTempMegDto uploadAttachmentTemp(List<ScriptFileAttachmentTempApiDto> scriptFileAttachmentTempList) throws ScriptException, IOException {
        return attachmentService.uploadAttachmentTemp(scriptFileAttachmentTempList);
    }

    /**
     * 获取agent实例标准输出
     * @param retryScriptInstanceApiDto 参数
     * @return  agent实例标准输出
     * @see IScriptInfo#getAgentStdoutByTaskIdAndAddress(RetryScriptInstanceApiDto)
     */
    @Override
    public String getAgentStdoutByTaskIdAndAddress(RetryScriptInstanceApiDto retryScriptInstanceApiDto){
        return taskRuntimeService.getAgentStdoutByTaskIdAndAddress(retryScriptInstanceApiDto);
    }
}

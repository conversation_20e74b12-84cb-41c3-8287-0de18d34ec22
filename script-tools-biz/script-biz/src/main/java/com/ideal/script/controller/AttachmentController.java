package com.ideal.script.controller;

import com.ideal.common.dto.R;
import com.ideal.sc.util.ValidationUtils;
import com.ideal.script.common.constant.Constants;
import com.ideal.script.common.constant.permission.MenuPermitConstant;
import com.ideal.script.common.constant.permission.MyScriptBtnPermitConstant;
import com.ideal.script.dto.AttachmentDto;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.model.dto.TaskAttachmentDto;
import com.ideal.script.service.IAttachmentService;
import com.ideal.script.service.ITaskAttachmentService;
import com.ideal.system.common.component.aop.MethodPermission;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;

/**
 * 【请填写功能名称】Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("${app.script-tools-url:}/attachment")
@MethodPermission(MenuPermitConstant.MY_SCRIPT_OR_TASK_APPLY_PER)
public class AttachmentController {
    private static final Logger logger = LoggerFactory.getLogger(AttachmentController.class);
    private final IAttachmentService attachmentService;
    private final ITaskAttachmentService taskAttachmentService;

    public AttachmentController(IAttachmentService attachmentService, ITaskAttachmentService taskAttachmentService) {
        this.attachmentService = attachmentService;
        this.taskAttachmentService = taskAttachmentService;
    }
    /**
     * 上传附件
     *
     * @param file 文件
     * @return Object
     */
    @PostMapping("/uploadAttachment")
    @MethodPermission(MyScriptBtnPermitConstant.SCRIPT_UPLOAD_ATTACHMENT_PER)
    public R<Object> uploadAttachment(@RequestParam("file") MultipartFile file, HttpServletResponse response, @RequestParam(value = "func", required = false) String func) {
        try {
            if (StringUtils.isEmpty(func)) {
                AttachmentDto attachmentDto = new AttachmentDto();
                attachmentDto.setName(file.getOriginalFilename());
                attachmentDto.setSize(file.getSize());
                attachmentDto.setContents(file.getBytes());
                return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, attachmentService.uploadAttachment(attachmentDto), "upload.success");
            } else {
                TaskAttachmentDto taskAttachment = new TaskAttachmentDto();
                taskAttachment.setName(file.getOriginalFilename());
                taskAttachment.setSize(file.getSize());
                taskAttachment.setContents(file.getBytes());
                return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, taskAttachmentService.uploadAttachment(taskAttachment), "upload.success");
            }
        } catch (IOException ioException) {
            response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
            return null;
        } catch (ScriptException e) {
            logger.error("uploadAttachment fail", e);
            return ValidationUtils.customFailResult("file", e.getMessage());
        }
    }

    /**
     * 下载附件
     *
     * @param id 附件id
     */
    @PostMapping("/downloadAttachment")
    public void downloadAttachment(@RequestBody Long id, HttpServletResponse response) {
        AttachmentDto attachmentDto = attachmentService.selectAttachmentById(id);
        response.reset();
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            response.setContentType("application/octet-stream");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-disposition", "attachment; filename=" + URLEncoder.encode(attachmentDto.getName(), "UTF-8"));
            outputStream.write(attachmentDto.getContents());
        } catch (IOException e) {
            logger.error("downloadAttachment error:", e);
            response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
        }
    }
}

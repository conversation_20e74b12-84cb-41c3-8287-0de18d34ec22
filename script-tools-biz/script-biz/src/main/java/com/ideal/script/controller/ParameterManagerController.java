package com.ideal.script.controller;

import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.script.common.constant.Constants;
import com.ideal.script.common.constant.permission.MenuPermitConstant;
import com.ideal.script.common.constant.permission.ParamManageBtnPermitConstant;
import com.ideal.script.common.validation.Create;
import com.ideal.script.common.validation.Update;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.model.dto.ParameterManagerDto;
import com.ideal.script.service.IParameterManagerService;
import com.ideal.system.common.component.aop.MethodPermission;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotEmpty;

/**
 * 【请填写功能名称】Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("${app.script-tools-url:}/manager")
@Validated
@MethodPermission(MenuPermitConstant.PARAM_MANAGE_PER)
public class ParameterManagerController
{
    private final IParameterManagerService parameterManagerService;
    private static final String LIST_SUCCESS_MESSAGE = Constants.LIST_SUCCESS;
    private static final Logger logger = LoggerFactory.getLogger(ParameterManagerController.class);

    public ParameterManagerController(IParameterManagerService parameterManagerService){
       this.parameterManagerService=parameterManagerService;
    }
    /**
     * 查询【请填写功能名称】列表
     */
    @PostMapping("/listParameterManager")
    public R<PageInfo<ParameterManagerDto>> listParameterManager(@RequestBody TableQueryDto<ParameterManagerDto> tableQueryDTO)
    {
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, parameterManagerService.selectParameterManagerList(tableQueryDTO.getQueryParam(), tableQueryDTO.getPageNum(),
            tableQueryDTO.getPageSize()), LIST_SUCCESS_MESSAGE);
    }


    /**
     * 新增【请填写功能名称】
     */
    @PostMapping("/saveParameterManager")
    @MethodPermission(ParamManageBtnPermitConstant.ENUM_PARAMS_ADD_PERM_PER)
    public R<Object> saveParameterManager(@RequestBody @Validated(Create.class) ParameterManagerDto parameterManagerDto) {
        try {
            parameterManagerService.insertParameterManager(parameterManagerDto);
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, "", "save.success");
        } catch (ScriptException e) {
            logger.error("saveParameterManager error",e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, e.getMessage());
        }
    }

    /**
     * 修改【请填写功能名称】
     */
    @PostMapping("/updateParameterManager")
    @MethodPermission(ParamManageBtnPermitConstant.ENUM_PARAMS_EDIT_PERM_PER)
    public R<Object> updateParameterManager(@RequestBody @Validated(Update.class) ParameterManagerDto parameterManagerDto) {
        try {
            parameterManagerService.updateParameterManager(parameterManagerDto);
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, "", "update.success");
        } catch (ScriptException e) {
            logger.error("updateParameterManager error",e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, e.getMessage());
        }
    }

    /**
     * 删除【请填写功能名称】
     */
	@GetMapping("/removeParameterManager")
    @MethodPermission(ParamManageBtnPermitConstant.ENUM_PARAMS_DEL_PERM_PER)
    public R<Object> removeParameterManager(@NotEmpty @RequestParam(value = "ids") Long[] ids) throws ScriptException {
        parameterManagerService.deleteParameterManagerByIds(ids);
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, "remove.success");
    }

    /**
     * 脚本编写使用
     * @return  结果
     */
    @PostMapping("/getParameterManagerForScriptEdit")
    @MethodPermission(MenuPermitConstant.MY_SCRIPT_PER)
    public R<Object> getParameterManagerForScriptEdit() {
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, parameterManagerService.selectParameterManagerForScriptEdit(), LIST_SUCCESS_MESSAGE);
    }
}

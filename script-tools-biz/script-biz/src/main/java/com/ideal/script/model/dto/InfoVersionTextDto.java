package com.ideal.script.model.dto;

import com.ideal.script.common.validation.Create;
import com.ideal.script.common.validation.Update;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.sql.Timestamp;

/**
 * 【请填写功能名称】对象 ieai_script_info_version_text
 * 
 * <AUTHOR>
 */
public class InfoVersionTextDto implements Serializable
{
    private static final long serialVersionUID = 1L;


    /** 主键 */
    @NotNull(groups = Update.class)
    private Long id;


    /** ieai_script_info_version表的isrcscriptuuid */
    @Size(min = 1, max = 50,groups = {Create.class,Update.class})
    private String srcScriptUuid;


    /** 脚本内容 */
    @NotNull(groups = Create.class)
    private String content;


    /** 创建人ID */
    private Long creatorId;


    /** 创建人名称 */
    @Size(min = 1, max = 50,groups = {Create.class,Update.class})
    private String creatorName;


    /** 创建时间 */
    private Timestamp createTime;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setSrcScriptUuid(String srcScriptUuid) 
    {
        this.srcScriptUuid = srcScriptUuid;
    }

    public String getSrcScriptUuid() 
    {
        return srcScriptUuid;
    }
    public void setContent(String content) 
    {
        this.content = content;
    }

    public String getContent() 
    {
        return content;
    }
    public void setCreatorId(Long creatorId) 
    {
        this.creatorId = creatorId;
    }

    public Long getCreatorId() 
    {
        return creatorId;
    }
    public void setCreatorName(String creatorName) 
    {
        this.creatorName = creatorName;
    }

    public String getCreatorName() 
    {
        return creatorName;
    }
    public void setCreateTime(Timestamp createTime) 
    {
        this.createTime = createTime;
    }

    public Timestamp getCreateTime() 
    {
        return createTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("srcScriptUuid", getSrcScriptUuid())
            .append("content", getContent())
            .append("creatorId", getCreatorId())
            .append("creatorName", getCreatorName())
            .append("createTime", getCreateTime())
            .toString();
    }
}

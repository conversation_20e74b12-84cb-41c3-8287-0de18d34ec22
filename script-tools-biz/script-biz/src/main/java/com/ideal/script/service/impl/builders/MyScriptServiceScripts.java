package com.ideal.script.service.impl.builders;

import com.ideal.message.center.IPublisher;
import com.ideal.notification.api.IWarn;
import com.ideal.script.common.util.BatchDataUtil;
import com.ideal.script.config.ScriptBusinessConfig;
import com.ideal.script.mapper.AttachmentMapper;
import com.ideal.script.mapper.AuditRelationMapper;
import com.ideal.script.mapper.BindFuncVarMapper;
import com.ideal.script.mapper.CategoryMapper;
import com.ideal.script.mapper.DangerCmdMapper;
import com.ideal.script.mapper.InfoMapper;
import com.ideal.script.mapper.InfoVersionMapper;
import com.ideal.script.mapper.InfoVersionTextMapper;
import com.ideal.script.mapper.MyScriptMapper;
import com.ideal.script.mapper.ParameterMapper;
import com.ideal.script.remotecall.RemoteCall;
import com.ideal.script.service.IDangerCmdService;
import com.ideal.script.service.IScriptStatementService;
import com.ideal.script.service.impl.AttachmentServiceImpl;
import com.ideal.script.service.impl.AuditRelationServiceImpl;
import com.ideal.script.service.impl.BindFuncVarServiceImpl;
import com.ideal.script.service.impl.CategoryServiceImpl;
import com.ideal.script.service.impl.InfoVersionServiceImpl;
import com.ideal.script.service.impl.InfoVersionTextServiceImpl;
import com.ideal.script.service.impl.ParameterServiceImpl;
import com.ideal.script.service.impl.TaskApplyServiceImpl;
import com.ideal.system.api.IRole;
import com.ideal.system.api.IUserInfo;
import org.apache.ibatis.session.SqlSessionFactory;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class MyScriptServiceScripts {

    private final InfoMapper infoMapper;


    private final InfoVersionMapper infoVersionMapper;

    private final ParameterMapper parameterMapper;

    private final InfoVersionTextMapper infoVersionTextMapper;

    private final AttachmentMapper attachmentMapper;

    private final MyScriptMapper myScriptMapper;


    private final CategoryMapper categoryMapper;

    private final BindFuncVarMapper bindFuncVarMapper;

    private final ParameterServiceImpl parameterService;

    private final InfoVersionTextServiceImpl infoVersionTextService;

    private final AttachmentServiceImpl attachmentService;

    private final BindFuncVarServiceImpl bindFuncVarService;
    private final CategoryServiceImpl categoryService;
    private final RemoteCall remoteCall;
    private final SqlSessionFactory factory;
    private final BatchDataUtil batchDataUtil;
    private final AuditRelationMapper auditRelationMapper;
    private final AuditRelationServiceImpl auditRelationService;
    private final TaskApplyServiceImpl taskApplyService;
    private final DangerCmdMapper dangerCmdMapper;
    private final IUserInfo iUserInfo;
    private final IRole iRole;
    private final RedissonClient redissonClient;

    private final ScriptBusinessConfig scriptBusinessConfig;


    private final InfoVersionServiceImpl infoVersionService;

    private final IScriptStatementService scriptStatementService;

    private final IWarn warn;

    private final IDangerCmdService dangerCmdService;

    private final IPublisher iPublisher;

    public MyScriptServiceScripts(MyScriptServiceScriptsBuilder builder) {
        this.iRole = builder.iRole;
        this.infoMapper = builder.infoMapper;
        this.infoVersionMapper = builder.infoVersionMapper;
        this.parameterMapper = builder.parameterMapper;
        this.infoVersionTextMapper = builder.infoVersionTextMapper;
        this.attachmentMapper = builder.attachmentMapper;
        this.myScriptMapper = builder.myScriptMapper;
        this.categoryMapper = builder.categoryMapper;
        this.bindFuncVarMapper = builder.bindFuncVarMapper;
        this.parameterService = builder.parameterService;
        this.infoVersionTextService = builder.infoVersionTextService;
        this.attachmentService = builder.attachmentService;
        this.bindFuncVarService = builder.bindFuncVarService;
        this.categoryService = builder.categoryService;
        this.remoteCall = builder.remoteCall;
        this.factory = builder.factory;
        this.batchDataUtil = builder.batchDataUtil;
        this.auditRelationMapper = builder.auditRelationMapper;
        this.taskApplyService = builder.taskApplyService;
        this.auditRelationService = builder.auditRelationService;
        this.dangerCmdMapper = builder.dangerCmdMapper;
        this.iUserInfo = builder.iUserInfo;
        this.redissonClient = builder.redissonClient;
        this.scriptBusinessConfig = builder.scriptBusinessConfig;
        this.infoVersionService = builder.infoVersionService;
        this.scriptStatementService = builder.scriptStatementService;
        this.warn = builder.warn;
        this.dangerCmdService = builder.dangerCmdService;
        this.iPublisher = builder.iPublisher;
    }

    public InfoVersionServiceImpl getInfoVersionService() {
        return infoVersionService;
    }

    public RedissonClient getRedissonClient() {
        return redissonClient;
    }

    public InfoMapper getInfoMapper() {
        return infoMapper;
    }

    public InfoVersionMapper getInfoVersionMapper() {
        return infoVersionMapper;
    }

    public ParameterMapper getParameterMapper() {
        return parameterMapper;
    }

    public InfoVersionTextMapper getInfoVersionTextMapper() {
        return infoVersionTextMapper;
    }

    public AttachmentMapper getAttachmentMapper() {
        return attachmentMapper;
    }

    public MyScriptMapper getMyScriptMapper() {
        return myScriptMapper;
    }

    public CategoryMapper getCategoryMapper() {
        return categoryMapper;
    }

    public BindFuncVarMapper getBindFuncVarMapper() {
        return bindFuncVarMapper;
    }

    public ParameterServiceImpl getParameterService() {
        return parameterService;
    }

    public InfoVersionTextServiceImpl getInfoVersionTextService() {
        return infoVersionTextService;
    }

    public AttachmentServiceImpl getAttachmentService() {
        return attachmentService;
    }

    public BindFuncVarServiceImpl getBindFuncVarService() {
        return bindFuncVarService;
    }

    public CategoryServiceImpl getCategoryService() {
        return categoryService;
    }

    public RemoteCall getRemoteCall() {
        return remoteCall;
    }

    public SqlSessionFactory getFactory() {
        return factory;
    }

    public BatchDataUtil getBatchDataUtil() {
        return batchDataUtil;
    }
    public AuditRelationMapper getAuditRelationMapper() {
        return auditRelationMapper;
    }

    public TaskApplyServiceImpl getTaskApplyService() {
        return taskApplyService;
    }

    public AuditRelationServiceImpl getAuditRelationService() {
        return auditRelationService;
    }

    public DangerCmdMapper getDangerCmdMapper() {
        return dangerCmdMapper;
    }

    public IUserInfo getiUserInfo() {
        return iUserInfo;
    }

    public ScriptBusinessConfig getScriptBusinessConfig() {
        return scriptBusinessConfig;
    }

    public IScriptStatementService getScriptStatementService() {
        return scriptStatementService;
    }

    public IWarn getWarn() {
        return warn;
    }

    public IDangerCmdService getDangerCmdService() {
        return dangerCmdService;
    }

    public IPublisher getiPublisher() {
        return iPublisher;
    }

    public IRole getiRole() {
        return iRole;
    }
}


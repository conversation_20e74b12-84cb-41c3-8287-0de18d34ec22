package com.ideal.script.model.dto;

import com.ideal.script.common.validation.ExecAuditCreate;
import org.apache.commons.lang3.builder.ToStringBuilder;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.io.Serializable;
import java.sql.Timestamp;


/**
 * 脚本任务对象 ieai_script_task（任务申请提交任务是产生的运行时任务基础信息）
 *
 * <AUTHOR>
 */
public class TaskDto implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 主键
     */
    private Long id;


    /**
     * ieai_script_info_version表的isrcscriptuuid
     */
    private String srcScriptUuid;


    /**
     * 任务名称
     */
    @NotNull(groups = {ExecAuditCreate.class})
    @Size(min = 1, max = 200,groups = {ExecAuditCreate.class})
    @Valid
    private String taskName;


    /**
     * 并发数量
     */
    private Integer eachNum;


    /**
     * 执行用户
     */
    @Size(min = 1, max = 50,groups = {ExecAuditCreate.class})
    @Valid
    private String execUser;


    /**
     * 任务调度方式：2-定时 1-周期 0-触发
     */
//    @NotNull(groups = {ExecAuditCreate.class}, message = "taskScheduler 不能为空")
//    @Digits(integer = 10, fraction = 0, groups = {ExecAuditCreate.class}, message = "taskScheduler 字段内容必须为数字")
//    @Min(value = 0, groups = {ExecAuditCreate.class}, message = "taskScheduler 的值必须是 0、1、2 之间的数字")
//    @Max(value = 2, groups = {ExecAuditCreate.class}, message = "taskScheduler 的值必须是 0、1、2 之间的数字")
//    @Pattern(regexp = "^[012]$", groups = {ExecAuditCreate.class}, message = "startType 的值只能是 0、1、2")
    private Integer taskScheduler;


    /**
     * 任务执行时间
     */
    @Future
    private Timestamp taskTime;


    /**
     * 执行周期
     */
//    @NotNull(groups = {ExecAuditCreate.class}, message = "taskCron 不能为空")
//    @Size(min = 0, max = 100, groups = {ExecAuditCreate.class}, message = "taskCron 字段长度应在 0 到 100 之间")
    private String taskCron;


    /**
     * 发布详细描述
     */
    private String publishDesc;


    /**
     * 任务执行人
     */
    private String startUser;


    /**
     * 任务发起时间
     */
    private Timestamp startTime;


    /**
     * 脚本执行时候的超时时间
     */
    private Long timeout;


    /**
     * 任务展示时间
     */
    private String taskTimeForDispaly;


    /**
     * 任务类型
     */
    private Integer type;


    /**
     * 驱动模式
     */
    private Integer driveMode;


    /**
     * 任务来源 0 - 脚本服务化  1 - 标准运维
     */
//    @NotNull(groups = {ExecAuditCreate.class}, message = "startType 不能为空")
//    @Digits(integer = 10, fraction = 0, groups = {ExecAuditCreate.class}, message = "startType 字段内容必须为数字")
//    @Min(value = 0, groups = {ExecAuditCreate.class}, message = "startType 的值必须是 0 或 1")
//    @Max(value = 1, groups = {ExecAuditCreate.class}, message = "startType 的值必须是 0 或 1")
//    @Pattern(regexp = "^[01]$", groups = {ExecAuditCreate.class}, message = "startType 的值必须是 0 或 1")
    private Integer startType;

    // 脚本任务是否待执行，审核通过时，状态为待执行。
    /**
     * 脚本任务是否待执行 0 - 不具备执行能力 1 - 待执行（审核通过时）
     */
    private Integer readyToExecute;

    /**
     * 创建人id
     */
    private Long creatorId;


    /**
     * 创建人名称
     */
    private String creatorName;


    /**
     * 修改人id
     */
    private Long updatorId;


    /**
     * 修改人名称
     */
    private String updatorName;


    /**
     * 创建时间
     */
    private Timestamp createTime;


    /**
     * 修改时间
     */
    private Timestamp updateTime;


    /**
     * 脚本任务来源 1：任务申请  2：脚本测试 3：克隆/常用任务
     */
    private Integer scriptTaskSource;

    /**
     * 双人复核与脚本服务化关系表Id
     */
    private Long auditRelationId;

    /**
     * 审核人id（常用任务使用）
     */
    private Long auditorId;

    /**
     * 先试点、后执行
     */
    private Boolean checkBefore;

    /**
     * 调用方（其它模块）任务id
     */
    private Long callerTaskId;

    public Long getCallerTaskId() {
        return callerTaskId;
    }

    public void setCallerTaskId(Long callerTaskId) {
        this.callerTaskId = callerTaskId;
    }

    public Boolean getCheckBefore() {
        return checkBefore;
    }

    public void setCheckBefore(Boolean checkBefore) {
        this.checkBefore = checkBefore;
    }

    public Long getAuditorId() {
        return auditorId;
    }

    public void setAuditorId(Long auditorId) {
        this.auditorId = auditorId;
    }

    public Integer getScriptTaskSource() {
        return scriptTaskSource;
    }

    public void setScriptTaskSource(Integer scriptTaskSource) {
        this.scriptTaskSource = scriptTaskSource;
    }

    public void setStartType(Integer startType) {
        this.startType = startType;
    }

    @SuppressWarnings("unused")
    public Integer getReadyToExecute() {
        return readyToExecute;
    }

    /**
     * 功能描述：
     *
     * @param readyToExecute
     * <AUTHOR>
     */
    public void setReadyToExecute(Integer readyToExecute) {
        this.readyToExecute = readyToExecute;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public String getSrcScriptUuid() {
        return srcScriptUuid;
    }

    public void setSrcScriptUuid(String srcScriptUuid) {
        this.srcScriptUuid = srcScriptUuid;
    }

    @SuppressWarnings("unused")
    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public String getTaskName() {
        return taskName;
    }

    @SuppressWarnings("unused")
    public void setEachNum(Integer eachNum) {
        this.eachNum = eachNum;
    }

    public Integer getEachNum() {
        return eachNum;
    }

    @SuppressWarnings("unused")
    public void setExecUser(String execUser) {
        this.execUser = execUser;
    }

    public String getExecUser() {
        return execUser;
    }

    @SuppressWarnings("unused")
    public void setTaskScheduler(Integer taskScheduler) {
        this.taskScheduler = taskScheduler;
    }

    public Integer getTaskScheduler() {
        return taskScheduler;
    }

    @SuppressWarnings("unused")
    public void setTaskTime(Timestamp taskTime) {
        this.taskTime = taskTime;
    }

    public Timestamp getTaskTime() {
        return taskTime;
    }

    @SuppressWarnings("unused")
    public void setTaskCron(String taskCron) {
        this.taskCron = taskCron;
    }

    public String getTaskCron() {
        return taskCron;
    }

    @SuppressWarnings("unused")
    public void setPublishDesc(String publishDesc) {
        this.publishDesc = publishDesc;
    }

    public String getPublishDesc() {
        return publishDesc;
    }

    @SuppressWarnings("unused")
    public void setStartUser(String startUser) {
        this.startUser = startUser;
    }

    public String getStartUser() {
        return startUser;
    }

    public void setStartTime(Timestamp startTime) {
        this.startTime = startTime;
    }

    public Timestamp getStartTime() {
        return startTime;
    }

    public void setTimeout(Long timeout) {
        this.timeout = timeout;
    }

    public Long getTimeout() {
        return timeout;
    }

    @SuppressWarnings("unused")
    public void setTaskTimeForDispaly(String taskTimeForDispaly) {
        this.taskTimeForDispaly = taskTimeForDispaly;
    }

    public String getTaskTimeForDispaly() {
        return taskTimeForDispaly;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getDriveMode() {
        return driveMode;
    }

    public void setDriveMode(Integer driveMode) {
        this.driveMode = driveMode;
    }

    public Integer getStartType() {
        return startType;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setUpdatorId(Long updatorId) {
        this.updatorId = updatorId;
    }

    public Long getUpdatorId() {
        return updatorId;
    }

    public void setUpdatorName(String updatorName) {
        this.updatorName = updatorName;
    }

    public String getUpdatorName() {
        return updatorName;
    }

    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }

    public Timestamp getCreateTime() {
        return createTime;
    }

    public void setUpdateTime(Timestamp updateTime) {
        this.updateTime = updateTime;
    }

    public Timestamp getUpdateTime() {
        return updateTime;
    }

    public Long getAuditRelationId() {
        return auditRelationId;
    }

    public void setAuditRelationId(Long auditRelationId) {
        this.auditRelationId = auditRelationId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this)
                .append("id", getId())
                .append("srcScriptUuid", getSrcScriptUuid())
                .append("taskName", getTaskName())
                .append("eachNum", getEachNum())
                .append("execUser", getExecUser())
                .append("taskScheduler", getTaskScheduler())
                .append("taskTime", getTaskTime())
                .append("taskCron", getTaskCron())
                .append("publishDesc", getPublishDesc())
                .append("startUser", getStartUser())
                .append("startTime", getStartTime())
                .append("timeout", getTimeout())
                .append("taskTimeForDispaly", getTaskTimeForDispaly())
                .append("type", getType())
                .append("driveMode", getDriveMode())
                .append("startType", getStartType())
                .append("readyToExecute", getReadyToExecute())
                .append("creatorId", getCreatorId())
                .append("creatorName", getCreatorName())
                .append("updatorId", getUpdatorId())
                .append("updatorName", getUpdatorName())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}

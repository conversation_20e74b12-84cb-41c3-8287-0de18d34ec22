package com.ideal.script.service;

import com.github.pagehelper.PageInfo;
import com.ideal.script.dto.RetryScriptInstanceApiDto;
import com.ideal.script.dto.TaskRuntimeApiDto;
import com.ideal.script.dto.TaskRuntimeQueryApiDto;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.model.bean.TaskRunTimeBindAgentBean;
import com.ideal.script.model.dto.TaskRuntimeDto;
import com.ideal.script.model.entity.TaskRuntime;

import java.util.List;

/**
 * agent运行实例Service接口
 * 
 * <AUTHOR>
 */
 public interface ITaskRuntimeService
{
    /**
     * 查询agent运行实例
     * 
     * @param id agent运行实例主键
     * @return agent运行实例
     */
     TaskRuntimeDto selectTaskRuntimeById(Long id);

    /**
     * 查询agent运行实例列表
     * @param pageNum 当前页
     * @param pageSize 每页条数
     * @param taskRuntimeDto agent运行实例
     * @return agent运行实例集合
     */
     PageInfo<TaskRuntimeDto> selectTaskRuntimeList(TaskRuntimeDto taskRuntimeDto, int pageNum, int pageSize);

    /**
     * 新增agent运行实例
     * 
     * @param taskRuntimeDto agent运行实例
     * @return 结果
     */
     int insertTaskRuntime(TaskRuntimeDto taskRuntimeDto);

    /**
     * 修改agent运行实例
     * 
     * @param taskRuntimeDto agent运行实例
     * @return 结果
     */
     int updateTaskRuntime(TaskRuntimeDto taskRuntimeDto);

    /**
     * 批量删除agent运行实例
     * 
     * @param ids 需要删除的agent运行实例主键集合
     * @return 结果
     */
     int deleteTaskRuntimeByIds(Long[] ids);

    /**
     * 删除agent运行实例信息
     * 
     * @param id agent运行实例主键
     * @return 结果
     */
     int deleteTaskRuntimeById(Long id);

    /**
     * 功能描述：更新agent执行状态
     *
     * @param status 状态
     * @param notInStates 排除的状态
     * @param taskRuntimeId agent运行实例id
     */
    void updateTaskRuntimeState(int status, List<Integer> notInStates,Long taskRuntimeId);

    /**
     * 功能描述：获取agent输出结果
     *
     * @param id agent运行实例id
     * @throws ScriptException 抛出自定义通知异常
     * @return {@link String }
     */
    String getOutPutMessage(Long id) throws ScriptException;

    /**
     * 功能描述：根据 Agent 实例 ID 查询当前任务中除自身外其他 Agent 执行出现异常的情况
     * 确定当前任务中除自身外其他 Agent 是否出现了执行异常的情况
     * @param id agent实例主键
     * @return {@link Integer }
     */
    Integer selectCountByTaskInstanceId(Long id);

    /**
     * 获取agent实例数据
     * @param retryScriptInstanceApiDto 参数
     * @return agent实例对象
     */
    TaskRuntime getTaskRuntime(RetryScriptInstanceApiDto retryScriptInstanceApiDto);

    /**
     * 功能描述：根据agent实例主键查询运行中的agent数量
     *
     * @param id agent实例主键
     * @return {@link Integer }
     * <AUTHOR>
     */
    Integer getRunningAgentInstanceCount(Long id);


    /**
     * 功能描述：根据agent运行实例主键获取agent信息
     *
     * @param taskRuntimeId agent实例主键
     * @return {@link TaskRunTimeBindAgentBean }
     */
    TaskRunTimeBindAgentBean getBindAgentForTaskRuntime(Long taskRuntimeId);

    /**
     * 功能描述：根据agent运行实例主键获取agent信息
     *
     * @param taskRuntimeId agent实例主键
     * @param status 状态
     * @param taskRuntimeDto agent运行实例对象
     */
    void updateExecTimeAndState(String taskRuntimeId, int status, TaskRuntimeDto taskRuntimeDto);


    /**
     * 根据instanceId获取运行实例数据
     * @param taskRuntimeQueryApiDto dto实体
     * @return PageInfo<TaskRuntimeApiDto>
     */
    PageInfo<TaskRuntimeApiDto> getTaskRuntimeInfoByInstanceId(TaskRuntimeQueryApiDto taskRuntimeQueryApiDto);


    /**
     * 驱动下一批次agent方法
     * @param taskRuntimeId agent实例id
     * @throws ScriptException 抛出自定义通知异常
     */
    void driverNextBatchAgent(Long taskRuntimeId) throws ScriptException;

    /**
     * 获取agent实例标准输出
     * @param retryScriptInstanceApiDto 参数
     * @return 标准输出
     */
    String getAgentStdoutByTaskIdAndAddress(RetryScriptInstanceApiDto retryScriptInstanceApiDto);

}

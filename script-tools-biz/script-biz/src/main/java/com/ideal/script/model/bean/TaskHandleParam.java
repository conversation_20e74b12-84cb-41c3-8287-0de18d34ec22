package com.ideal.script.model.bean;

import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;

/**
 * 任务处理参数
 *
 * <AUTHOR>
 */
public class TaskHandleParam  implements Serializable {
    /**
     * 标准输出
     */
    private String istdout;

    /**
     * 标准错误输出
     */
    private String istderror;

    /**
     * 最后一行输出
     */
    private String ilastline;

    public String getIlastline() {
        return ilastline;
    }

    public void setIlastline(String ilastline) {
        this.ilastline = ilastline;
    }

    public String getIstdout() {
        return istdout;
    }

    public void setIstdout(String istdout) {
        this.istdout = istdout;
    }

    public String getIstderror() {
        return istderror;
    }

    public void setIstderror(String istderror) {
        this.istderror = istderror;
    }

}

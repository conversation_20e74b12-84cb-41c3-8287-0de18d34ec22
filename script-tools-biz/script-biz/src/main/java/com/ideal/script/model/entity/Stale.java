package com.ideal.script.model.entity;

import com.ideal.snowflake.annotion.IdGenerator;

import java.io.Serializable;
import java.sql.Timestamp;


/**
 * @TableName ieai_script_stale
 */
public class Stale implements Serializable {

    /**
     * 主键
     */
    @IdGenerator
    private Long id;
    /**
     * info表主键
     */

    private Long infoId;
    /**
     * info_version表主键
     */
    private Long infoVersionId;
    /**
     * 脚本中文名称
     */
    private String scriptNameZh;
    /**
     * 脚本名称
     */
    private String scriptName;
    /**
     * 脚本分类
     */
    private String categoryPath;
    /**
     * 默认版本
     */
    private String defaultVersion;
    /**
     * 执行次数(任务维度)
     */
    private Integer taskCount;
    /**
     * 单个版本的执行成功率(agent维度)
     */
    private String successRate;
    /**
     * 未修改天数
     */
    private Integer unmodifyDay;
    /**
     * 确认状态 0-未确认 1-已确认
     */
    private Integer confirmState;
    /**
     * 创建时间
     */
    private Timestamp createTime;
    /**
     * 扫描时间
     */
    private Timestamp scanningTime;
    /**
     * 确认不修改日期
     */
    private Timestamp confirmTime;
    /**
     * 确认人用户id
     */
    private Long confirmorId;
    /**
     * 确认人名字
     */
    private String confirmorName;
    /**
     * 最近一次修改日期(info_version表的updatetime)
     */
    private Timestamp infoUpdatetime;
    /**
     * 脚本创建人用户id
     */
    private Long creatorId;
    /**
     * 脚本创建人名称
     */
    private String creatorName;

    /**
     * 主键
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * info表主键
     */
    public void setInfoId(Long infoId) {
        this.infoId = infoId;
    }

    /**
     * info_version表主键
     */
    public void setInfoVersionId(Long infoVersionId) {
        this.infoVersionId = infoVersionId;
    }

    /**
     * 脚本中文名称
     */
    public void setScriptNameZh(String scriptNameZh) {
        this.scriptNameZh = scriptNameZh;
    }

    /**
     * 脚本名称
     */
    public void setScriptName(String scriptName) {
        this.scriptName = scriptName;
    }

    /**
     * 脚本分类
     */
    public void setCategoryPath(String categoryPath) {
        this.categoryPath = categoryPath;
    }

    /**
     * 默认版本
     */
    public void setDefaultVersion(String defaultVersion) {
        this.defaultVersion = defaultVersion;
    }

    /**
     * 执行次数(任务维度)
     */
    public void setTaskCount(Integer taskCount) {
        this.taskCount = taskCount;
    }

    /**
     * 单个版本的执行成功率(agent维度)
     */
    public void setSuccessRate(String successRate) {
        this.successRate = successRate;
    }

    /**
     * 未修改天数
     */
    public void setUnmodifyDay(Integer unmodifyDay) {
        this.unmodifyDay = unmodifyDay;
    }

    /**
     * 确认状态 0-未确认 1-已确认
     */
    public void setConfirmState(Integer confirmState) {
        this.confirmState = confirmState;
    }

    /**
     * 创建时间
     */
    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }

    /**
     * 扫描时间
     */
    public void setScanningTime(Timestamp scanningTime) {
        this.scanningTime = scanningTime;
    }

    /**
     * 确认不修改日期
     */
    public void setConfirmTime(Timestamp confirmTime) {
        this.confirmTime = confirmTime;
    }

    /**
     * 确认人用户id
     */
    public void setConfirmorId(Long confirmorId) {
        this.confirmorId = confirmorId;
    }

    /**
     * 确认人名字
     */
    public void setConfirmorName(String confirmorName) {
        this.confirmorName = confirmorName;
    }

    /**
     * 最近一次修改日期(info_version表的updatetime)
     */
    public void setInfoUpdatetime(Timestamp infoUpdatetime) {
        this.infoUpdatetime = infoUpdatetime;
    }

    /**
     * 脚本创建人用户id
     */
    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    /**
     * 脚本创建人名称
     */
    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }


    /**
     * 主键
     */
    public Long getId() {
        return this.id;
    }

    /**
     * info表主键
     */
    public Long getInfoId() {
        return this.infoId;
    }

    /**
     * info_version表主键
     */
    public Long getInfoVersionId() {
        return this.infoVersionId;
    }

    /**
     * 脚本中文名称
     */
    public String getScriptNameZh() {
        return this.scriptNameZh;
    }

    /**
     * 脚本名称
     */
    public String getScriptName() {
        return this.scriptName;
    }

    /**
     * 脚本分类
     */
    public String getCategoryPath() {
        return this.categoryPath;
    }

    /**
     * 默认版本
     */
    public String getDefaultVersion() {
        return this.defaultVersion;
    }

    /**
     * 执行次数(任务维度)
     */
    public Integer getTaskCount() {
        return this.taskCount;
    }

    /**
     * 单个版本的执行成功率(agent维度)
     */
    public String getSuccessRate() {
        return this.successRate;
    }

    /**
     * 未修改天数
     */
    public Integer getUnmodifyDay() {
        return this.unmodifyDay;
    }

    /**
     * 确认状态 0-未确认 1-已确认
     */
    public Integer getConfirmState() {
        return this.confirmState;
    }

    /**
     * 创建时间
     */
    public Timestamp getCreateTime() {
        return this.createTime;
    }

    /**
     * 扫描时间
     */
    public Timestamp getScanningTime() {
        return this.scanningTime;
    }

    /**
     * 确认不修改日期
     */
    public Timestamp getConfirmTime() {
        return this.confirmTime;
    }

    /**
     * 确认人用户id
     */
    public Long getConfirmorId() {
        return this.confirmorId;
    }

    /**
     * 确认人名字
     */
    public String getConfirmorName() {
        return this.confirmorName;
    }

    /**
     * 最近一次修改日期(info_version表的updatetime)
     */
    public Timestamp getInfoUpdatetime() {
        return this.infoUpdatetime;
    }

    /**
     * 脚本创建人用户id
     */
    public Long getCreatorId() {
        return this.creatorId;
    }

    /**
     * 脚本创建人名称
     */
    public String getCreatorName() {
        return this.creatorName;
    }

}

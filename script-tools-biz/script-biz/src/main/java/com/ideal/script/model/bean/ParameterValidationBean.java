package com.ideal.script.model.bean;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <AUTHOR>
 */
public class ParameterValidationBean implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * ieai_script_info_version表的isrcscriptuuid
     */
    private String srcScriptUuid;

    /**
     * 参数类型：枚举-Enums  字符串-String  加密-Cipher
     */
    private String paramType;

    /**
     * 参数默认值
     */
    private String paramDefaultValue;

    /**
     * 参数描述
     */
    private String paramDesc;

    /**
     * 参数顺序
     */
    private Integer paramOrder;

    /**
     * 校验规则id
     */
    private Long paramCheckIid;

    /**
     * 枚举参数id
     */
    private Long scriptParameterManagerId;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private Timestamp createTime;

    /**
     * 校验规则名称
     */
    private String ruleName;
    /**
     * 校验规则内容
     */
    private String checkRule;
    /**
     * 校验规则描述
     */
    private String ruleDes;

    private String paramName;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSrcScriptUuid() {
        return srcScriptUuid;
    }

    public void setSrcScriptUuid(String srcScriptUuid) {
        this.srcScriptUuid = srcScriptUuid;
    }

    public String getParamType() {
        return paramType;
    }

    public void setParamType(String paramType) {
        this.paramType = paramType;
    }

    public String getParamDefaultValue() {
        return paramDefaultValue;
    }

    public void setParamDefaultValue(String paramDefaultValue) {
        this.paramDefaultValue = paramDefaultValue;
    }

    public String getParamDesc() {
        return paramDesc;
    }

    public void setParamDesc(String paramDesc) {
        this.paramDesc = paramDesc;
    }

    public Integer getParamOrder() {
        return paramOrder;
    }

    public void setParamOrder(Integer paramOrder) {
        this.paramOrder = paramOrder;
    }

    public Long getParamCheckIid() {
        return paramCheckIid;
    }

    public void setParamCheckIid(Long paramCheckIid) {
        this.paramCheckIid = paramCheckIid;
    }

    public Long getScriptParameterManagerId() {
        return scriptParameterManagerId;
    }

    public void setScriptParameterManagerId(Long scriptParameterManagerId) {
        this.scriptParameterManagerId = scriptParameterManagerId;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public Timestamp getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }

    public String getRuleName() {
        return ruleName;
    }

    public void setRuleName(String ruleName) {
        this.ruleName = ruleName;
    }

    public String getCheckRule() {
        return checkRule;
    }

    public void setCheckRule(String checkRule) {
        this.checkRule = checkRule;
    }

    public String getRuleDes() {
        return ruleDes;
    }

    public void setRuleDes(String ruleDes) {
        this.ruleDes = ruleDes;
    }

    public String getParamName() {
        return paramName;
    }

    public void setParamName(String paramName) {
        this.paramName = paramName;
    }

    @Override
    public String toString() {
        return "ParameterValidationBean{" +
                "id=" + id +
                ", srcScriptUuid='" + srcScriptUuid + '\'' +
                ", paramType='" + paramType + '\'' +
                ", paramDefaultValue='" + paramDefaultValue + '\'' +
                ", paramDesc='" + paramDesc + '\'' +
                ", paramOrder=" + paramOrder +
                ", paramCheckIid=" + paramCheckIid +
                ", scriptParameterManagerId=" + scriptParameterManagerId +
                ", creatorId=" + creatorId +
                ", creatorName='" + creatorName + '\'' +
                ", createTime=" + createTime +
                ", ruleName='" + ruleName + '\'' +
                ", checkRule='" + checkRule + '\'' +
                ", ruleDes='" + ruleDes + '\'' +
                ", paramName='" + paramName + '\'' +
                '}';
    }
}

package com.ideal.script.common.constant.permission;

/**
 * 任务监控菜单按钮权限常量类
 *
 * <AUTHOR>
 */
public class ExecutionTaskBtnPermitConstant {

    // 权限表达式前缀和连接符
    private static final String PER_PREFIX = "@dp.hasBtnPermission('";
    private static final String PER_SUFFIX = "')";

    /**
     * 执行中-查看-重试按钮权限码
     */
    public static final String RETRY_SCRIPT = "retryScript";

    /**
     * 执行历史-导出按钮权限码
     */
    public static final String EXCEL_EXPORT = "excelExport";

    /**
     * 执行中-查看-忽略按钮权限码
     */
    public static final String SKIP_SCRIPT = "skipScript";

    /**
     * 待执行-取消按钮权限码
     */
    public static final String CANCEL_TASK = "cancelTask";

    /**
     * 待执行-执行、继续按钮权限码
     */
    public static final String SCRIPT_TASK_START = "scriptTaskStart";

    /**
     * 执行中-终止按钮权限码
     */
    public static final String STOP_TASK = "stopTask";

    /**
     * 任务监控-执行中-查看-重试 定时任务监控-执行中-查看-重试 按钮权限表达式
     */
    public static final String RETRY_SCRIPT_PER = PER_PREFIX + RETRY_SCRIPT + PER_SUFFIX;

    /**
     * 执行历史-导出按钮权限表达式
     */
    public static final String EXCEL_EXPORT_PER = PER_PREFIX + EXCEL_EXPORT + PER_SUFFIX;

    /**
     * 执行中-查看-忽略按钮权限表达式
     */
    public static final String SKIP_SCRIPT_PER = PER_PREFIX + SKIP_SCRIPT + PER_SUFFIX;

    /**
     * 待执行-取消按钮权限表达式
     */
    public static final String CANCEL_TASK_PER = PER_PREFIX + CANCEL_TASK + PER_SUFFIX;

    /**
     * 待执行-执行、继续按钮权限表达式
     */
    public static final String SCRIPT_TASK_START_PER = PER_PREFIX + SCRIPT_TASK_START + PER_SUFFIX;

    /**
     * 任务监控-执行中-终止 定时任务监控-执行中-终止
     */
    public static final String STOP_TASK_PER = PER_PREFIX + STOP_TASK + PER_SUFFIX;
}

package com.ideal.script.model.dto;

import com.ideal.script.common.validation.Create;
import com.ideal.script.common.validation.Update;
import org.apache.commons.lang3.builder.ToStringBuilder;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.sql.Timestamp;



/**
 * 变量库基础信息发布对象 ieai_script_variable_p
 * 
 * <AUTHOR>
 */
public class VariablePublishDto implements Serializable
{
    private static final long serialVersionUID = 1L;


    /** IID */
    @NotNull(groups = Update.class)
    private Long id;

    /** ieai_script_variable_class变量分类表id */
    private Long scriptVariableClassId;


    /** 变量名 */
    @Size(min = 1, max = 10,groups = {Create.class, Update.class})
    private String name;


    /** 变量类型 */
    private Long type;


    /** 变量值 */
    private String value;


    /** 描述 */
    @Size(min = 1, max = 66,groups = {Create.class,Update.class})
    private String desc;


    /** 是否为内置变量  2内置 1自定义 */
    private Long attribute;


    /** 是否全域有效  1是  0否 */
    private Long global;


    /** 状态 1草稿 2已发布  3发布后修改变为草稿 */
    private Long status;


    /** 用户全部有效 1是 0否 */
    private Long userglobal;


    /** 唯一标识 */
    private String md5;


    /** 创建人id */
    private Long creatorId;


    /** 创建人名称 */
    @Size(min = 1, max = 16,groups = {Create.class,Update.class})
    private String creatorName;


    /** 修改人id */
    private Long updatorId;


    /** 修改人名称 */
    @Size(min = 1, max = 16,groups = {Create.class,Update.class})
    private String updatorName;


    /** 创建时间 */
    private Timestamp createTime;


    /** 修改时间 */
    private Timestamp updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @SuppressWarnings("unused")
    public void setScriptVariableClassId(Long scriptVariableClassId) {
        this.scriptVariableClassId = scriptVariableClassId;
    }

    public Long getScriptVariableClassId() {
        return scriptVariableClassId;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setType(Long type) {
        this.type = type;
    }

    public Long getType() {
        return type;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    @SuppressWarnings("unused")
    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    @SuppressWarnings("unused")
    public void setAttribute(Long attribute) {
        this.attribute = attribute;
    }

    public Long getAttribute() {
        return attribute;
    }

    @SuppressWarnings("unused")
    public void setGlobal(Long global) {
        this.global = global;
    }

    public Long getGlobal() {
        return global;
    }

    public void setStatus(Long status) {
        this.status = status;
    }

    public Long getStatus() {
        return status;
    }

    @SuppressWarnings("unused")
    public void setUserglobal(Long userglobal) {
        this.userglobal = userglobal;
    }

    public Long getUserglobal() {
        return userglobal;
    }

    @SuppressWarnings("unused")
    public void setMd5(String md5) {
        this.md5 = md5;
    }

    public String getMd5() {
        return md5;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setUpdatorId(Long updatorId) {
        this.updatorId = updatorId;
    }

    public Long getUpdatorId() {
        return updatorId;
    }

    public void setUpdatorName(String updatorName) {
        this.updatorName = updatorName;
    }

    public String getUpdatorName() {
        return updatorName;
    }

    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }

    public Timestamp getCreateTime() {
        return createTime;
    }

    public void setUpdateTime(Timestamp updateTime) {
        this.updateTime = updateTime;
    }

    public Timestamp getUpdateTime() {
        return updateTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this)
                .append("id", getId())
                .append("scriptVariableClassId", getScriptVariableClassId())
                .append("name", getName())
                .append("type", getType())
                .append("value", getValue())
                .append("desc", getDesc())
                .append("attribute", getAttribute())
                .append("global", getGlobal())
                .append("status", getStatus())
                .append("userglobal", getUserglobal())
                .append("md5", getMd5())
                .append("creatorId", getCreatorId())
                .append("creatorName", getCreatorName())
                .append("updatorId", getUpdatorId())
                .append("updatorName", getUpdatorName())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}

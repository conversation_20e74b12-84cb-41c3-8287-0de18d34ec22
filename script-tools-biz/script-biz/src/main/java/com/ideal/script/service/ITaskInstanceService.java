package com.ideal.script.service;

import com.github.pagehelper.PageInfo;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.model.bean.StatusSummaryBean;
import com.ideal.script.model.bean.StatusSummaryParams;
import com.ideal.script.model.dto.TaskInstanceDto;
import com.ideal.script.model.dto.TaskRuntimeDto;

import java.util.List;

/**
 * 脚本任务运行实例Service接口
 * 
 * <AUTHOR>
 */
 public interface ITaskInstanceService
{
    /**
     * 查询脚本任务运行实例
     * 
     * @param id 脚本任务运行实例主键
     * @return 脚本任务运行实例
     */
    TaskInstanceDto selectTaskInstanceById(Long id);

    /**
     * 查询任务实例的总数
     * @param id instanceId
     * @return 查询任务实例的总数
     */
    Long selectIpsCount(Long id);

    /**
     * 根据id查询instance实例
     * @param ids instanceId集合
     * @return instanceId集合
     */
    List<Long> selectTaskInstanceIdsById(Long [] ids);

    /**
     * 查询脚本任务运行实例
     *
     * @param id 脚本任务运行实例主键
     * @return 脚本任务运行实例
     */
    TaskInstanceDto selectTaskInstanceByTaskId(Long id);
    /**
     * 通过taskInfoId查询instance信息
     * @param taskInfoId 任务id
     * @return 任务实例对象
     */
    TaskInstanceDto getTaskInstanceByTaskInfoId(Long taskInfoId);

    /**
     * 根据agent实例id获取任务实例数据
     * @param runtimeId agent实例id
     * @return 任务实例对象
     */
    TaskInstanceDto getTaskInstanceByRuntimeId(Long runtimeId);

    /**
     * 查询脚本任务运行实例列表
     *
     * @param pageNum 当前页
     * @param pageSize 每页条数
     * @param taskInstanceDto 脚本任务运行实例
     * @return 脚本任务运行实例集合
     */
     PageInfo<TaskInstanceDto> selectTaskInstanceList(TaskInstanceDto taskInstanceDto, int pageNum, int pageSize);

    /**
     * 新增脚本任务运行实例
     * 
     * @param taskInstanceDto 脚本任务运行实例
     * @return 结果
     */
     int insertTaskInstance(TaskInstanceDto taskInstanceDto);

    /**
     * 修改脚本任务运行实例
     * 
     * @param taskInstanceDto 脚本任务运行实例
     * @return 结果
     */
     int updateTaskInstance(TaskInstanceDto taskInstanceDto);

    /**
     * 批量删除脚本任务运行实例
     * 
     * @param ids 需要删除的脚本任务运行实例主键集合
     * @return 结果
     */
     int deleteTaskInstanceByIds(Long[] ids);

    /**
     * 删除脚本任务运行实例信息
     * 
     * @param id 脚本任务运行实例主键
     * @return 结果
     */
     int deleteTaskInstanceById(Long id);


    /**
     * 功能描述：更新serverNum
     *
     * @param taskInstanceId 任务实例Id
     * @param notInStates 排除的状态
     */
    void updateServerNum(Long taskInstanceId, List<Integer> notInStates);

    /**
     * 功能描述：统计完成、异常、略过、终止数量
     *
     * @param statusSummaryParams  任务中agent运行实例状态的统计
     * @return {@link StatusSummaryBean }
     */
    StatusSummaryBean getStatusSummary(StatusSummaryParams statusSummaryParams);

    /**
     * 功能描述：更新脚本任务运行实例表的任务状态
     *
     * @param state 状态
     * @param taskInstanceId 任务实例Id
     * @param partialRunning 排除部分运行状态
     */
    void updateState(int state, Long taskInstanceId, int partialRunning);

    /**
     * 批量更新脚本任务运行实例表的任务状态
     * @param state 状态
     * @param taskInstanceIds 任务实例Id集合
     * @param partialRunning 排除部分运行状态
     */
    void updateBatchTaskState(int state, Long [] taskInstanceIds, int partialRunning);

    /**
     * 功能描述： 更新脚本任务实例表RunNum数量
     *
     * @param taskInstanceId 任务实例Id
     */
    void updateRunNum(Long taskInstanceId);

    /**
     * 功能描述：更新脚本任务实例表结束时间
     *
     * @param taskInstanceId 脚本实例表主键
     * @param notInStates 不包含分批执行的状态
     * <AUTHOR>
     */
    void updateEndTime(Long taskInstanceId, List<Integer> notInStates);

    /**
     * 功能描述： agent实例全部完成情况下，更新任务级别状态
     *
     * @param taskRuntimeDto agent运行实例对象
     * @throws ScriptException 抛出自定义通知异常
     */
    void allFinish(TaskRuntimeDto taskRuntimeDto) throws ScriptException;

    /**
     * 功能描述：根据任务实例id，获取这个任务下需要终止的agent实例Id集合
     *
     * @param taskInstanceId 脚本实例表主键
     * @return {@link Long[] }
     */
    Long[] getToBeTerminatedRuntimeIds(Long taskInstanceId);
}

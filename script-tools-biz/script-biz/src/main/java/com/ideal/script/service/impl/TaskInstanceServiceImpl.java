package com.ideal.script.service.impl;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.ideal.common.util.BeanUtils;
import com.ideal.common.util.PageDataUtil;
import com.ideal.script.common.constant.enums.Enums;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.mapper.TaskInstanceMapper;
import com.ideal.script.model.bean.StatusSummaryBean;
import com.ideal.script.model.bean.StatusSummaryParams;
import com.ideal.script.model.dto.TaskInstanceDto;
import com.ideal.script.model.dto.TaskRuntimeDto;
import com.ideal.script.model.entity.TaskInstance;
import com.ideal.script.service.ITaskInstanceService;
import com.ideal.script.service.interact.ToolsApiAct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 脚本任务运行实例Service业务层处理
 *
 * <AUTHOR>
 */
@Service
public class TaskInstanceServiceImpl implements ITaskInstanceService {
    private static final Logger LOGGER = LoggerFactory.getLogger(TaskInstanceServiceImpl.class);

    private final TaskInstanceMapper taskInstanceMapper;

    private final ToolsApiAct toolsApiAct;

    public TaskInstanceServiceImpl(TaskInstanceMapper taskInstanceMapper,@Lazy ToolsApiAct toolsApiAct) {
        this.taskInstanceMapper = taskInstanceMapper;
        this.toolsApiAct = toolsApiAct;
    }

    /**
     * 查询脚本任务运行实例
     *
     * @param id 脚本任务运行实例主键
     * @return 脚本任务运行实例
     */
    @Override
    public TaskInstanceDto selectTaskInstanceById(Long id) {
        return BeanUtils.copy(taskInstanceMapper.selectTaskInstanceById(id), TaskInstanceDto.class);
    }

    /**
     * 查询任务实例的总数
     * @param id instanceId
     * @return 查询任务实例的总数
     */
    @Override
    public Long selectIpsCount(Long id) {
        return taskInstanceMapper.selectIpsCount(id);
    }

    /**
     * 根据id查询instance实例
     * @param ids instanceId集合
     * @return instanceId集合
     */
    @Override
    public List<Long> selectTaskInstanceIdsById(Long [] ids) {
        return taskInstanceMapper.selectTaskInstanceIdsById(ids);
    }

    /**
     * 查询脚本任务运行实例
     *
     * @param id 脚本任务运行实例主键
     * @return 脚本任务运行实例
     */
    @Override
    public TaskInstanceDto selectTaskInstanceByTaskId(Long id) {
        return BeanUtils.copy(taskInstanceMapper.selectTaskInstanceByTaskId(id), TaskInstanceDto.class);
    }


    /**
     * 通过taskInfoId查询instance信息
     * @param taskInfoId    任务id
     * @return  脚本任务运行实例对象
     */
    @Override
    public TaskInstanceDto getTaskInstanceByTaskInfoId(Long taskInfoId) {
        return BeanUtils.copy(taskInstanceMapper.getTaskInstanceByTaskInfoId(taskInfoId), TaskInstanceDto.class);
    }

    /**
     * 获取任务实例数据
     * @param runtimeId agent实例id
     * @return 任务实例dto
     */
    @Override
    public TaskInstanceDto getTaskInstanceByRuntimeId(Long runtimeId){
        return BeanUtils.copy(taskInstanceMapper.getTaskInstanceByRuntimeId(runtimeId), TaskInstanceDto.class);
    }

    /**
     * 查询脚本任务运行实例列表
     *
     * @param taskInstanceDto 脚本任务运行实例
     * @return 脚本任务运行实例
     */
    @Override
    public PageInfo<TaskInstanceDto> selectTaskInstanceList(TaskInstanceDto taskInstanceDto, int pageNum, int pageSize) {
        PageMethod.startPage(pageNum, pageSize);
        List<TaskInstance> taskInstanceList = taskInstanceMapper.selectTaskInstanceList(BeanUtils.copy(taskInstanceDto, TaskInstance.class));
        return PageDataUtil.toDtoPage(taskInstanceList, TaskInstanceDto.class);
    }


    /**
     * 新增脚本任务运行实例
     *
     * @param taskInstanceDto 脚本任务运行实例
     * @return 结果
     */
    @Override
    public int insertTaskInstance(TaskInstanceDto taskInstanceDto) {
        TaskInstance taskInstance = BeanUtils.copy(taskInstanceDto, TaskInstance.class);
        int result = taskInstanceMapper.insertTaskInstance(taskInstance);
        taskInstanceDto.setId(taskInstance.getId());
        return result;

    }

    /**
     * 修改脚本任务运行实例
     *
     * @param taskInstanceDto 脚本任务运行实例
     * @return 结果
     */
    @Override
    public int updateTaskInstance(TaskInstanceDto taskInstanceDto) {
        toolsApiAct.updateTaskStateToTool(taskInstanceDto.getId(),taskInstanceDto.getStatus());
        TaskInstance taskInstance = BeanUtils.copy(taskInstanceDto, TaskInstance.class);
        return taskInstanceMapper.updateTaskInstance(taskInstance);
    }

    /**
     * 批量删除脚本任务运行实例
     *
     * @param ids 需要删除的脚本任务运行实例主键
     * @return 结果
     */
    @Override
    public int deleteTaskInstanceByIds(Long[] ids) {
        return taskInstanceMapper.deleteTaskInstanceByIds(ids);
    }

    /**
     * 删除脚本任务运行实例信息
     *
     * @param id 脚本任务运行实例主键
     * @return 结果
     */
    @Override
    public int deleteTaskInstanceById(Long id) {
        return taskInstanceMapper.deleteTaskInstanceById(id);
    }

    @Override
    public void updateServerNum(Long taskInstanceId, List<Integer> notInStates) {
        taskInstanceMapper.updateServerNum(taskInstanceId,notInStates);
    }

    @Override
    public StatusSummaryBean getStatusSummary(StatusSummaryParams statusSummaryParams ) {
        return taskInstanceMapper.getStatusSummary(statusSummaryParams);
    }

    @Override
    public void updateState(int state, Long taskInstanceId, int partialRunning) {
        toolsApiAct.updateTaskStateToTool(taskInstanceId,state);
        taskInstanceMapper.updateState(state,taskInstanceId,partialRunning);
    }

    /**
     * 批量更新脚本任务运行实例表的任务状态
     * @param state 状态
     * @param taskInstanceIds 任务实例Id集合
     * @param partialRunning 排除部分运行状态
     */
    @Override
    public void updateBatchTaskState(int state, Long [] taskInstanceIds, int partialRunning) {
        taskInstanceMapper.updateBatchTaskState(state,taskInstanceIds,partialRunning);
    }

    @Override
    public void updateRunNum(Long taskInstanceId) {
        taskInstanceMapper.updateRunNum(taskInstanceId);
    }

    /**
     * 功能描述：更新脚本任务实例表结束时间
     *
     * @param taskInstanceId 脚本实例表主键
     * @param notInStates    不包含分批执行的状态
     * <AUTHOR>
     */
    @Override
    public void updateEndTime(Long taskInstanceId, List<Integer> notInStates) {
        taskInstanceMapper.updateEndTime(taskInstanceId,notInStates);
    }

    /**
     * 功能描述： 更新任务级别状态
     *
     * @param taskRuntimeDto agent运行实例对象
     */
    @Override
    public void allFinish(TaskRuntimeDto taskRuntimeDto) throws ScriptException {
        // 查询任务下运行的agent数量，判断是否全部执行完成
        TaskInstanceDto taskInstanceDto = selectTaskInstanceById(taskRuntimeDto.getTaskInstanceId());
        // 比对ips表的数量与
        Long ipsCount = selectIpsCount(taskRuntimeDto.getTaskInstanceId());

        if(null==taskInstanceDto){
            return;
        }
        // 全部完成
        int state = 10;
        long killCount;
        long skipCount;
        long runCount;
        long failCount;
        long finishCount;
        StatusSummaryParams summaryParams = getStatusSummaryParams(taskRuntimeDto);
        StatusSummaryBean statusSummary = getStatusSummary(summaryParams);
        finishCount = statusSummary.getFinishTotal();
        failCount = statusSummary.getFailTotal();
        runCount = statusSummary.getRunTotal();
        killCount = statusSummary.getKillTotal();
        skipCount = statusSummary.getSkipTotal();
        //只要有异常，任务维度（ieai_script_task_instance表）就是异常
        if (failCount > 0) {
            state = Enums.TaskInstanceStatus.EXCEPTION.getValue();
        }
        //如果没有异常，但是有运行的，那就是运行状态
        else if (runCount > 0) {
            state = Enums.TaskInstanceStatus.RUNNING.getValue();
        }
        //没有异常，没有运行，如果还有没拉起来的agent，那就更新为运行状态
        else if ((ipsCount > (finishCount+failCount+runCount+killCount+skipCount))){
            state = Enums.TaskInstanceStatus.RUNNING.getValue();
        }
        // 如果有终止或者跳过的，那么设置状态为红色的完成
        else if( killCount>0 || skipCount>0){
            LOGGER.debug("script task was finished,taskId:{},taskInstanceId:{}",taskRuntimeDto.getScriptTaskId(),taskRuntimeDto.getTaskInstanceId());
            state = Enums.TaskInstanceStatus.COMPLETED_RED.getValue();
        }
        //如果没有异常、运行、终止、忽略的，都是正常跑完的，那就是完成
        else if (finishCount > 0) {
            LOGGER.debug("script task was finished,taskId:{},taskInstanceId:{}",taskRuntimeDto.getScriptTaskId(),taskRuntimeDto.getTaskInstanceId());
            state = Enums.TaskInstanceStatus.COMPLETED.getValue();
        }

        try {
            // 更新任务状态，设置为完成或者失败
            updateState(state, taskRuntimeDto.getTaskInstanceId(), Enums.TaskInstanceStatus.PARTIAL_RUNNING.getValue());
            // 更新run_nums
            updateRunNum(taskRuntimeDto.getTaskInstanceId());
        } catch (Exception e) {
            throw new ScriptException("update.task.state.error");
        }

    }

    /**
     * 组织agent运行实例状态信息
     * @param taskRuntimeDto    agent运行实例对象
     * @return  agent运行实例状态
     */
    private static StatusSummaryParams getStatusSummaryParams(TaskRuntimeDto taskRuntimeDto) {
        String tableName = "ieai_script_task_runtime";
        // fail状态集
        int scriptFailState = Enums.TaskRuntimeState.SCRIPT_FAIL_STATE.getValue();
        int scriptFinishFailState = 40;
        int scriptRunningFailState = 50;
        // 完成状态集
        int scriptFinishState = Enums.TaskRuntimeState.COMPLETED.getValue();
        int scriptSkipState = Enums.TaskRuntimeState.SKIP.getValue();
        // kill状态集
        int scriptKillState = Enums.TaskRuntimeState.TERMINATED.getValue();

        // 运行状态集
        int scriptRunningState = Enums.TaskRuntimeState.RUNNING.getValue();

        // 查询完成状态的agent数量、失败状态的agent数量、运行的agent数量
        StatusSummaryParams summaryParams = new StatusSummaryParams();
        summaryParams.setTableName(tableName);
        summaryParams.setScriptFailState(scriptFailState);
        summaryParams.setScriptFinishFailState(scriptFinishFailState);
        summaryParams.setScriptRunningFailState(scriptRunningFailState);
        summaryParams.setScriptFinishState(scriptFinishState);
        summaryParams.setScriptSkipState(scriptSkipState);
        summaryParams.setScriptKillState(scriptKillState);
        summaryParams.setScriptRunningState(scriptRunningState);
        summaryParams.setTaskInstanceId(taskRuntimeDto.getTaskInstanceId());
        return summaryParams;
    }

    /**
     * 功能描述：根据任务实例id，获取这个任务下需要终止的agent实例Id集合
     *
     * @param taskInstanceId 脚本实例表主键
     * @return {@link Long[] }
     */
    @Override
    public Long[] getToBeTerminatedRuntimeIds(Long taskInstanceId) {
       return taskInstanceMapper.getToBeTerminatedRuntimeIds(taskInstanceId);
    }

}

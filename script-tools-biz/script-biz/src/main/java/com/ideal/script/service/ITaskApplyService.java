package com.ideal.script.service;

import com.github.pagehelper.PageInfo;

import com.ideal.approval.dto.DoubleCheckApiDto;

import com.ideal.script.dto.RetryScriptInstanceApiDto;
import com.ideal.script.dto.ScriptTaskApplyApiDto;
import com.ideal.script.dto.TaskStartApiDto;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.exception.SystemException;
import com.ideal.script.model.bean.AuditResultBean;
import com.ideal.system.common.component.model.CurrentUser;
import com.ideal.script.model.dto.ScriptAuditDetailDto;
import com.ideal.script.model.dto.ScriptExecAuditDto;
import com.ideal.script.model.dto.TaskApplyDto;
import com.ideal.script.model.dto.TaskApplyQueryDto;
import com.ideal.script.model.dto.interaction.agent.UserInfoDto;

import com.ideal.system.dto.ServicePermissionApiQueryDto;
import com.ideal.system.dto.UserInfoApiDto;
import org.springframework.web.multipart.MultipartFile;


import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 任务申请Service接口
 * <AUTHOR>
 */
public interface ITaskApplyService {

    /**
     * 任务申请列表
     * @param pageNum 当前页
     * @param pageSize 每页条数
     * @param taskApplyQueryDto 任务申请Dto
     * @param user 用户
     * @return PageInfo<TaskApplyDto>
     */
    PageInfo<TaskApplyDto> selectTaskApplyList(TaskApplyQueryDto taskApplyQueryDto, Integer pageNum, Integer pageSize,CurrentUser user);

    /**
     * 任务申请-提交任务审核
     * @param scriptExecAuditDto 任务申请提交审核Dto
     * @param user 当前用户
     * @throws ScriptException 抛出自定义通知异常
     * @return int
     */
    Long scriptExecAuditing(ScriptExecAuditDto scriptExecAuditDto, CurrentUser user, AuditSource auditSource) throws ScriptException;

    /**
     * 根据审核关系id查询脚本的分类id
     * @param relationId 审核关系数据
     * @return 脚本分类id
     */
    Long getScriptInfoByAuditRelationId(Long relationId);

    /**
     * 根据id获取用户
     * @param userId
     * @return
     * @throws ScriptException
     */
    List<UserInfoDto> getUserByUserId(Long userId);

    /**
     * 接收审核结果，对脚本任务进行后续操作
     * @param auditResultBean 审核结果
     * @throws ScriptException 抛出自定义通知异常
     * @throws SystemException 抛出自定义异常
     */

    void receiveAuditResult(AuditResultBean auditResultBean) throws ScriptException, SystemException;


    /**
     * 双人复核-业务详情
     * @param serviceId 业务id，双人复核表主键
     * @return ScriptAuditDetailDto
     */
    ScriptAuditDetailDto getAuditDetail( Long serviceId, Long taskId) throws ScriptException;

    /**
     * 接收并处理审核服务的审批结果
     *
     * @param doubleCheckApiDto 审核服务的审批结果
     * @throws ScriptException 抛出自定义异常
     * @throws SystemException 抛出自定义异常
     */
    void doubleCheckCallBack(DoubleCheckApiDto doubleCheckApiDto) throws ScriptException, SystemException;

    /**
     * 根据服务权限码获取这个服务权限码所属角色下的所有人信息
     *
     * @param permissionCode 服务权限码
     * @return {@link List }<{@link UserInfoDto }>
     * <AUTHOR>
     */
    List<UserInfoDto> queryUserInfoListByPermissionCode(String permissionCode);

    /**
     * 脚本服务化启动任务API入口
     *
     * @param scriptTaskApplyDto 任务申请信息
     * @return {@link Map }<{@link String,String }>
     * <AUTHOR>
     */
    Long scriptTaskApplyApi(ScriptTaskApplyApiDto scriptTaskApplyDto) throws ScriptException;

    /**
     * 脚本服务化启动任务API入口
     *
     * @param taskStartApiDto 任务申请信息
     * @return {@link Map }<{@link String,String }>
     * <AUTHOR>
     */
    Map<String,String> startScriptTask(TaskStartApiDto taskStartApiDto);

    /**
     * 实例任务重试
     * @param retryScriptInstanceApiDto 参数
     * @throws ScriptException 脚本服务化异常
     */
    void reTryScriptTask(List<RetryScriptInstanceApiDto> retryScriptInstanceApiDto) throws ScriptException;
    /**
     * 任务申请删除临时附件
     * @param id task_attachment表id
     */
    void deleteAttachment(Long id);
    /**
     * 分页查询可选部门列表
     * @param servicePermissionApiQueryDto  查询条件
     * @return  部门列表
     */

    List<UserInfoApiDto> queryPermissionUserInfoList(ServicePermissionApiQueryDto servicePermissionApiQueryDto,Long categoryId);

    /**
     * 根据脚本id查询脚本创建人的部门领导
     * @param scriptInfoId 脚本id
     * @return 脚本创建人部门领导
     */
    List<UserInfoApiDto> queryDepartmentUserInfoList(Long scriptInfoId);

    /**
     * 任务申请导入服务器
     * @param file
     * @return
     * @throws IOException
     * @throws ScriptException
     */
    Map<String, Object> importServerExcel(MultipartFile file) throws IOException, ScriptException;

    /**
     * 下载excel模版
     * @param response
     */
    void downloadImportTemplate(HttpServletResponse response);

    /**
     * 校验执行用户
     * @param scriptInfoVersionId   版本id
     * @param execuser  任务申请配置的执行用户
     * @throws ScriptException  自定义异常
     */
    void checkExecuserPermission(Long scriptInfoVersionId, String execuser) throws ScriptException;

}

package com.ideal.script.controller;


import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.sc.util.CurrentUserUtil;
import com.ideal.script.common.constant.permission.MenuPermitConstant;
import com.ideal.script.common.constant.permission.MyScriptBtnPermitConstant;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.model.dto.IssuerecordDto;
import com.ideal.script.model.dto.IssuerecordQueryDto;
import com.ideal.script.service.IIssuerecordService;
import com.ideal.system.common.component.aop.MethodPermission;
import com.ideal.system.common.component.model.CurrentUser;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 脚本下发的Controller
 *
 * <AUTHOR>
 * @date 2024-04-13
 */
@RestController
@RequestMapping("${app.script-tools-url:}/script/issuerecord")
@MethodPermission(MenuPermitConstant.MY_SCRIPT_PER)
public class IssuerecordController {

    private final IIssuerecordService issuerecordService;

    public IssuerecordController(IIssuerecordService issuerecordService) {
        this.issuerecordService = issuerecordService;
    }

    /**
     * 查询脚本下发的列表
     *
     * @param tableQueryDto 查询条件
     * @return 查询结果
     */
    @PostMapping("/list")
    public R<PageInfo<IssuerecordDto>> list(@RequestBody TableQueryDto<IssuerecordQueryDto> tableQueryDto) {
        PageInfo<IssuerecordDto> list = issuerecordService.selectIssuerecordList(
                tableQueryDto.getQueryParam(),
                tableQueryDto.getPageNum(),
                tableQueryDto.getPageSize()
        );
        return R.ok(list);
    }


    /**
     * 脚本下发
     *
     * @param issuerecordQueryDto 脚本下发Dto
     * @return {@link R }<{@link String }>
     */
    @PostMapping("/sendScriptToAgents")
    @MethodPermission(MyScriptBtnPermitConstant.SEND_SCRIPT_TO_AGENTS_PER)
    public R<String> sendScriptToAgents(@RequestBody IssuerecordQueryDto issuerecordQueryDto) {
        CurrentUser currentUser = CurrentUserUtil.getCurrentUser();
        String batchNumber = "";
        try {
            batchNumber = issuerecordService.sendScriptToAgents(issuerecordQueryDto, currentUser);
        } catch (ScriptException e) {
            return R.fail();
        }
        return R.ok(batchNumber);
    }
}

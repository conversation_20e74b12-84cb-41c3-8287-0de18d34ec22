package com.ideal.script.common.util;

import org.quartz.CronExpression;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * v8 工具类
 * <AUTHOR>
 */
public class CronDateUtils {
    private static final String CRON_DATE_FORMAT = "ss mm HH dd MM ? yyyy";

    private CronDateUtils()
    {
        throw new IllegalStateException("CronDateUtils.class");
    }

    /***
     *
     * @param date 时间
     * @return  cron类型的日期
     */
    public static String getCron ( final Date date )
    {
        SimpleDateFormat sdf = new SimpleDateFormat(CRON_DATE_FORMAT);
        String formatTimeStr = "";
        if (date != null)
        {
            formatTimeStr = sdf.format(date);
        }
        return formatTimeStr;
    }

    /***
     *
     * @param cron Quartz cron的类型的日期
     * @return  Date日期
     */

    public static Date getDate ( final String cron )
    {

        if (cron == null)
        {
            return null;
        }

        SimpleDateFormat sdf = new SimpleDateFormat(CRON_DATE_FORMAT);
        Date date = null;
        try
        {
            date = sdf.parse(cron);
        } catch (ParseException e)
        {
            // 此处缺少异常处理,自己根据需要添加
            return null;
        }
        return date;
    }
    /**
     * 验证一个 Quartz cron 表达式是否合法（7字段）
     *
     * @param cronExpr cron 表达式
     * @return true 如果合法；false 如果非法
     */
    public static boolean isValid(String cronExpr) {
        if (cronExpr == null || cronExpr.trim().isEmpty()) {
            return false;
        }
        try {
            new CronExpression(cronExpr);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
}

package com.ideal.script.model.dto;


import com.ideal.script.common.validation.Create;
import com.ideal.script.common.validation.Update;
import org.apache.commons.lang3.builder.ToStringBuilder;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.sql.Timestamp;


/**
 * 函数库基础发布对象 ieai_script_functionpublish
 *
 * <AUTHOR>
 */
public class FunctionpublishDto implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 主键
     */
    @NotNull(groups = Update.class)
    private Long id;

    /** 函数名称 */
    @Size(min = 1, max = 30,groups = {Create.class, Update.class})
    private String name;
    /**
     * 语言类型 1-shell 2-python 3-powershell
     */
    private Integer languagetype;


    /** 描述 */
    @Size(max = 500,groups = {Create.class,Update.class})
    private String desc;


    /** 属性 1-自定义 2-内置 */
    private Integer attribute;


    /**
     * 状态 0-草稿 2-发布 3-发布后修改
     */
    private Integer status;


    /**
     * 服务器、资源组全域有效 1-全域有效
     */
    private Integer global;


    /**
     * 用户、用户组全域有效 1-全域有效
     */
    private Integer userglobal;


    /**
     * 分类id
     */
    private Long classid;


    /**
     * 唯一标识
     */
    @Size(min = 1, max = 50,groups = {Create.class,Update.class})
    private String functionMd;

    /**
     * 创建人id
     */
    private Long creatorId;


    /** 创建人名称 */
    @Size(min = 1, max = 50,groups = {Create.class,Update.class})
    private String creatorName;


    /**
     * 修改人id
     */
    private Long updatorId;


    /** 修改人名称 */
    @Size(min = 1, max = 50,groups = {Create.class,Update.class})
    private String updatorName;


    /** 创建时间 */
    private Timestamp createTime;


    /**
     * 修改时间
     */
    private Timestamp updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    @SuppressWarnings("unused")
    public void setLanguagetype(Integer languagetype) {
        this.languagetype = languagetype;
    }

    public Integer getLanguagetype() {
        return languagetype;
    }

    @SuppressWarnings("unused")
    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    @SuppressWarnings("unused")
    public void setAttribute(Integer attribute) {
        this.attribute = attribute;
    }

    public Integer getAttribute() {
        return attribute;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getStatus() {
        return status;
    }

    @SuppressWarnings("unused")
    public void setGlobal(Integer global) {
        this.global = global;
    }

    public Integer getGlobal() {
        return global;
    }

    @SuppressWarnings("unused")
    public void setUserglobal(Integer userglobal) {
        this.userglobal = userglobal;
    }

    public Integer getUserglobal() {
        return userglobal;
    }

    @SuppressWarnings("unused")
    public void setClassid(Long classid) {
        this.classid = classid;
    }

    public Long getClassid() {
        return classid;
    }

    @SuppressWarnings("unused")
    public void setFunctionMd(String functionMd) {
        this.functionMd = functionMd;
    }

    public String getFunctionMd() {
        return functionMd;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setUpdatorId(Long updatorId) {
        this.updatorId = updatorId;
    }

    public Long getUpdatorId() {
        return updatorId;
    }

    public void setUpdatorName(String updatorName) {
        this.updatorName = updatorName;
    }

    public String getUpdatorName() {
        return updatorName;
    }

    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }

    public Timestamp getCreateTime() {
        return createTime;
    }

    public void setUpdateTime(Timestamp updateTime) {
        this.updateTime = updateTime;
    }

    public Timestamp getUpdateTime() {
        return updateTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this)
                .append("id", getId())
                .append("name", getName())
                .append("languagetype", getLanguagetype())
                .append("desc", getDesc())
                .append("attribute", getAttribute())
                .append("status", getStatus())
                .append("global", getGlobal())
                .append("userglobal", getUserglobal())
                .append("classid", getClassid())
                .append("functionMd", getFunctionMd())
                .append("creatorId", getCreatorId())
                .append("creatorName", getCreatorName())
                .append("updatorId", getUpdatorId())
                .append("updatorName", getUpdatorName())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}

package com.ideal.script.model.bean;

import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

/**
 * 任务申请Bean
 * <AUTHOR>
 */
public class TaskApplyBean implements Serializable, CategoryPermissionAware {

    //======================脚本任务主表属性开始==================
    /**
     * 脚本服务化基础信息表主键
     */
    private Long scriptInfoId;

    /**
     * 脚本服务化基础信息表唯一uuid
     */
    private String uniqueUuid;

    /**
     * 脚本中文名称
     */
    private String scriptNameZh;

    /**
     * 脚本名称
     */
    private String scriptName;

    /**
     * 脚本类型(shell、bat、perl、python、powershell)
     */
    private String scriptType;

    /**
     * 脚本的执行用户
     */
    private String execuser;

    /**
     * 标签
     */
    private String scriptLabel;

    /**
     * 分类id
     */
    private Long categoryId;

    /**
     * 分类名称
     */
    private String scriptCategoryName;

    /**
     * 运行前校验 1- 校验 0 - 不校验
     */
    private Integer checkBeforeExec;


    /**
     * 版本号
     */
    private String version;


    /**
     * 风险级别  0 - 白名单  1 - 高级风险  2 - 中级风险  3 - 低级风险
     */
    private Integer level;


    /**
     * 适用平台  数据来源于ieai_platform_code表
     */
    private String platform;

    /**
     * 标签
     */
    private String label;




    //======================脚本任务主表属性结束==================

    // =====================版本属性开始==========================
    /**
     * 脚本版本表主键
     */
    private Long scriptInfoVersionId;

    /**
     * 每个版本的uuid
     */
    private String srcScriptUuid;


    /**
     * 创建人id
     */
    private Long creatorId;


    /**
     * 创建人名称
     */
    private String creatorName;
    /**
     * 当前分类Id以及子分类Id集合
     */
    private List<Long> categoryIdList;

    /**
     * 当前用户的同组用户Id集合
     */
    private  List<Long>  userIdList;

    private String sysOrgCode;

    /**
     * 预期类型  1 - lastLine  2 - exitCode
     */
    private Integer expectType;
    /**
     * 预期结果值
     */
    private String expectLastline;

    /**
     * 版本表修改时间
     */
    private Timestamp updateTime;


    /**
     * 获取默认版本脚本 [1]- 是 []- 否
     */
    private List<Integer> getDefault;
// =====================是否查询相关信息开始==========================

    /**
     * 接口返回值是否携带脚本参数，true为携带，false为不携带，默认为true
     */
    private boolean queryScriptParamsFlag = true;
    /**
     * 接口返回值是否携带脚本附件，true为携带，false为不携带，默认为true
     */
    private boolean queryScriptAttachmentFlag = true;
    /**
     * 接口返回值是否携带脚本内容，true为携带，false为不携带，默认为true
     */
    private boolean queryScriptContentFlag = true;

    // =====================是否查询相关信息结束==========================


    // ====================关联ieai_script_task=====================


    //计算出来的任务开始日期（根据配置中配置的周期从当前日期向前算）
    private Timestamp taskCreatePreTime;

    //任务发起人
    private String startUser;

    /**
     * 脚本分类绑定了部门筛选出来的分类路径列表
     */
    private List<String> orgCategoryPath;
    /**
     * 关键词
     */
    private String keyword;

    /**
     * 用户id
     */
    private Long userId;
    /**
     * 部门名
     */
    private String department;

    /**
     * 用户是否为管理员标识，true是，false不是，默认false
     */
    private boolean superUser = false;

    /**
     * 是否走角色权限查询
     */
    private boolean roleFlag = false;

    /**
     * 角色id集合（走角色权限使用）
     */
    private List<Long> roleIdList;

    public List<Long> getRoleIdList() {
        return roleIdList;
    }

    public void setRoleIdList(List<Long> roleIdList) {
        this.roleIdList = roleIdList;
    }

    public boolean isRoleFlag() {
        return roleFlag;
    }

    public void setRoleFlag(boolean roleFlag) {
        this.roleFlag = roleFlag;
    }

    public boolean isSuperUser() {
        return superUser;
    }

    @Override
    public void setSuperUser(boolean superUser) {
        this.superUser = superUser;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public List<String> getOrgCategoryPath() {
        return orgCategoryPath;
    }

    @Override
    public void setOrgCategoryPath(List<String> orgCategoryPath) {
        this.orgCategoryPath = orgCategoryPath;
    }

    public String getStartUser() {
        return startUser;
    }

    public void setStartUser(String startUser) {
        this.startUser = startUser;
    }

    public Timestamp getTaskCreatePreTime() {
        return taskCreatePreTime;
    }

    public void setTaskCreatePreTime(Timestamp taskCreatePreTime) {
        this.taskCreatePreTime = taskCreatePreTime;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public boolean isQueryScriptParamsFlag() {
        return queryScriptParamsFlag;
    }

    public void setQueryScriptParamsFlag(boolean queryScriptParamsFlag) {
        this.queryScriptParamsFlag = queryScriptParamsFlag;
    }

    public boolean isQueryScriptAttachmentFlag() {
        return queryScriptAttachmentFlag;
    }

    public void setQueryScriptAttachmentFlag(boolean queryScriptAttachmentFlag) {
        this.queryScriptAttachmentFlag = queryScriptAttachmentFlag;
    }

    public boolean isQueryScriptContentFlag() {
        return queryScriptContentFlag;
    }

    public void setQueryScriptContentFlag(boolean queryScriptContentFlag) {
        this.queryScriptContentFlag = queryScriptContentFlag;
    }

    public Integer getExpectType() {
        return expectType;
    }

    public void setExpectType(Integer expectType) {
        this.expectType = expectType;
    }

    public String getExpectLastline() {
        return expectLastline;
    }

    public void setExpectLastline(String expectLastline) {
        this.expectLastline = expectLastline;
    }

    public String getSysOrgCode() {
        return sysOrgCode;
    }
    @Override
    public void setSysOrgCode(String sysOrgCode) {
        this.sysOrgCode = sysOrgCode;
    }

    public List<Long> getUserIdList() {
        return userIdList;
    }

    public void setUserIdList(List<Long> userIdList) {
        this.userIdList = userIdList;
    }

    public List<Long> getCategoryIdList() {
        return categoryIdList;
    }
    @Override
    public void setCategoryIdList(List<Long> categoryIdList) {
        this.categoryIdList = categoryIdList;
    }

    public Long getScriptInfoId() {
        return scriptInfoId;
    }

    public void setScriptInfoId(Long scriptInfoId) {
        this.scriptInfoId = scriptInfoId;
    }

    public String getUniqueUuid() {
        return uniqueUuid;
    }

    public void setUniqueUuid(String uniqueUuid) {
        this.uniqueUuid = uniqueUuid;
    }

    public String getScriptNameZh() {
        return scriptNameZh;
    }

    public void setScriptNameZh(String scriptNameZh) {
        this.scriptNameZh = scriptNameZh;
    }

    public String getScriptName() {
        return scriptName;
    }

    public void setScriptName(String scriptName) {
        this.scriptName = scriptName;
    }

    public String getScriptType() {
        return scriptType;
    }

    public void setScriptType(String scriptType) {
        this.scriptType = scriptType;
    }

    public String getExecuser() {
        return execuser;
    }

    public void setExecuser(String execuser) {
        this.execuser = execuser;
    }

    public String getScriptLabel() {
        return scriptLabel;
    }

    public void setScriptLabel(String scriptLabel) {
        this.scriptLabel = scriptLabel;
    }
    @Override
    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public Integer getCheckBeforeExec() {
        return checkBeforeExec;
    }

    public void setCheckBeforeExec(Integer checkBeforeExec) {
        this.checkBeforeExec = checkBeforeExec;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }


    public Long getScriptInfoVersionId() {
        return scriptInfoVersionId;
    }

    public void setScriptInfoVersionId(Long scriptInfoVersionId) {
        this.scriptInfoVersionId = scriptInfoVersionId;
    }

    public String getSrcScriptUuid() {
        return srcScriptUuid;
    }

    public void setSrcScriptUuid(String srcScriptUuid) {
        this.srcScriptUuid = srcScriptUuid;
    }


    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }


    public String getScriptCategoryName() {
        return scriptCategoryName;
    }

    public void setScriptCategoryName(String scriptCategoryName) {
        this.scriptCategoryName = scriptCategoryName;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public List<Integer> getGetDefault() {
        return getDefault;
    }

    public void setGetDefault(List<Integer> getDefault) {
        this.getDefault = getDefault;
    }


    public Timestamp getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Timestamp updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this)
                .append("scriptInfoId", getScriptInfoId())
                .append("uniqueUuid", getUniqueUuid())
                .append("scriptNameZh", getScriptNameZh())
                .append("scriptName", getScriptName())
                .append("scriptType", getScriptType())
                .append("execuser", getExecuser())
                .append("scriptLabel", getScriptLabel())
                .append("categoryId", getCategoryId())
                .append("scriptCategoryName", getScriptCategoryName())
                .append("checkBeforeExec", getCheckBeforeExec())
                .append("version", getVersion())
                .append("level", getLevel())
                .append("platform", getPlatform())
                .append("scriptInfoVersionId", getScriptInfoVersionId())
                .append("srcScriptUuid", getSrcScriptUuid())
                .append("creatorId", getCreatorId())
                .append("creatorName", getCreatorName())
                .append("expectType", getExpectType())
                .append("expectLastline", getExpectLastline())
                .append("getDefault", getGetDefault())
                .append("updateTime",getUpdateTime())
                .toString();
    }
}

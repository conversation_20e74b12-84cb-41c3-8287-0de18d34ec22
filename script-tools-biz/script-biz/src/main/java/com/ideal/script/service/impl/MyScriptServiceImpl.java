package com.ideal.script.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.ideal.approval.api.IDoubleCheckApiService;
import com.ideal.approval.dto.ApprovalNode;
import com.ideal.approval.dto.AuditorApiDto;
import com.ideal.approval.dto.DoubleCheckApiDto;
import com.ideal.approval.dto.ResultApiDto;
import com.ideal.common.util.BeanUtils;
import com.ideal.common.util.EncryptUtils;
import com.ideal.common.util.FilePathValidator;
import com.ideal.common.util.PageDataUtil;
import com.ideal.common.util.spring.SpringUtil;
import com.ideal.message.center.exception.CommunicationException;
import com.ideal.sc.CustomerProperty;
import com.ideal.sc.util.CurrentUserUtil;
import com.ideal.sc.util.FileUtils;
import com.ideal.script.common.constant.Constants;
import com.ideal.script.common.constant.enums.AuditStateEnum;
import com.ideal.script.common.constant.enums.AuditTypeEnum;
import com.ideal.script.common.constant.enums.BindFuncVarTypeEnum;
import com.ideal.script.common.constant.enums.EditStateEnum;
import com.ideal.script.common.constant.enums.Enums;
import com.ideal.script.common.constant.enums.ExceptionMessage;
import com.ideal.script.common.constant.enums.ParameterType;
import com.ideal.script.common.constant.enums.ScriptSource;
import com.ideal.script.common.constant.enums.UseStateEnum;
import com.ideal.script.common.util.ScriptPlatformValidator;
import com.ideal.script.common.util.StateConverter;
import com.ideal.script.common.util.TransactionSyncUtil;
import com.ideal.script.dto.AttachmentApiDto;
import com.ideal.script.dto.AttachmentUploadDto;
import com.ideal.script.dto.BindFuncVarDto;
import com.ideal.script.dto.CategoryDto;
import com.ideal.script.dto.ParameterValidationDto;
import com.ideal.script.dto.PublishDto;
import com.ideal.script.dto.ScriptContentDto;
import com.ideal.script.dto.ScriptDubboInfoDto;
import com.ideal.script.dto.ScriptInfoApiDto;
import com.ideal.script.dto.ScriptInfoDto;
import com.ideal.script.dto.ScriptInfoQueryDto;
import com.ideal.script.dto.ScriptVersionDto;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.exception.SystemException;
import com.ideal.script.mapper.AttachmentEphemeralMapper;
import com.ideal.script.model.bean.*;
import com.ideal.script.model.dto.AuditRelationDto;
import com.ideal.script.model.dto.CreatorTransferDto;
import com.ideal.script.model.dto.DoubleCheckInfoDto;
import com.ideal.script.model.dto.ParameterValidationResultDto;
import com.ideal.script.model.dto.ScriptDeleteDto;
import com.ideal.script.model.dto.ScriptValidationResultDto;
import com.ideal.script.model.dto.ScriptVersionInfoDto;
import com.ideal.script.model.dto.StatementDto;
import com.ideal.script.model.dto.ValidationResultDto;
import com.ideal.script.model.entity.Attachment;
import com.ideal.script.model.entity.AuditRelation;
import com.ideal.script.model.entity.BindFuncVar;
import com.ideal.script.model.entity.Category;
import com.ideal.script.model.entity.DangerCmd;
import com.ideal.script.model.entity.Exectime;
import com.ideal.script.model.entity.Info;
import com.ideal.script.model.entity.InfoVersion;
import com.ideal.script.model.entity.InfoVersionText;
import com.ideal.script.model.entity.Parameter;
import com.ideal.script.service.IMyScriptService;
import com.ideal.script.service.impl.builders.MyScriptServiceScripts;
import com.ideal.script.service.impl.builders.MyScriptServiceScriptsBuilder;
import com.ideal.snowflake.util.SnowflakeIdWorker;
import com.ideal.system.common.component.model.CurrentUser;
import com.ideal.system.dto.RoleApiDto;
import com.ideal.system.dto.UserInfoApiDto;
import com.ideal.system.dto.UserInfoQueryDto;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RKeys;
import org.redisson.api.RLock;
import org.redisson.api.RScoredSortedSet;
import org.redisson.api.RScript;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.unit.DataSize;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static org.apache.commons.lang3.time.DateFormatUtils.format;


/**
 * <AUTHOR>
 */
@Service
public class MyScriptServiceImpl implements IMyScriptService {
    private static final Logger logger = LoggerFactory.getLogger(MyScriptServiceImpl.class);

    private static final String SCRIPT_LABEL_ZSET= "script_labelZSet";

    private static final String SCRIPT_NAME_ZH_EXISTS = "script.name.zh.exists";

    private final MyScriptServiceScripts scripts;

    private final AttachmentEphemeralMapper attachmentEphemeralMapper;

    private final IDoubleCheckApiService doubleCheckApiService;

    public MyScriptServiceImpl(IDoubleCheckApiService doubleCheckApiService,AttachmentEphemeralMapper attachmentEphemeralMapper,MyScriptServiceScriptsBuilder builder) {
        this.doubleCheckApiService = doubleCheckApiService;
        this.attachmentEphemeralMapper = attachmentEphemeralMapper;
        this.scripts = builder.build();
    }

    /**
     * 保存脚本
     *
     * @param scriptInfoDto 脚本信息Dto
     * @return 信息
     * @throws ScriptException exception
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ValidationResultDto saveScript(ScriptInfoDto scriptInfoDto) throws ScriptException {

        RLock scriptSaveLock = null;
        boolean isLocked = false;

        try {
            scriptSaveLock = scripts.getRedissonClient().getLock("scriptSaveLock-" + scriptInfoDto.getScriptName());
            logger.info("获取锁{}" , scriptInfoDto.getScriptName());
            isLocked = scriptSaveLock.tryLock(2, 10, TimeUnit.SECONDS);

            // 判断是新建还是发布后保存
            boolean isNewVersionScript = StringUtils.isEmpty(scriptInfoDto.getUniqueUuid());

            //校验分类是否属于当前用户或者是属于未绑定任何部门（控制权限）
            boolean categoryValidation = checkCategoryValidation(scriptInfoDto.getCategoryId());
            if(!categoryValidation){
                throw new ScriptException("invalid.category");
            }

            //校验保存权限
            validateCreateUserPermission(scriptInfoDto,isNewVersionScript);

            //针对redis中的标签数据进行处理（新增的标签在redis中增加这个标签，删除从redis中删除这个标签）
            handleRedisLabel(scriptInfoDto);

            //校验一次性调用保存
            boolean ensureCreate = ensureCreatedOnce(scriptInfoDto);

            //如果判断是多次调用则直接不走保存
            if (!ensureCreate) {
                logger.info("重复调用接口");
                return new ValidationResultDto();
            }

            //校验无修改
            boolean isChange = checkScript(scriptInfoDto);
            if (!isChange) {
                logger.info("script is no change");
                return null;
            }
            //验证附件总体大小
            if(scriptInfoDto.getScriptVersionDto().getAttachmentUploadDtoList() != null){
                validateFileSize(scriptInfoDto.getScriptVersionDto().getAttachmentUploadDtoList());
            }
            //验证
            ValidationResultDto validationResultDto = validateScriptAndUpdateInfo(scriptInfoDto);
            if (validationResultDto != null) {
                return validationResultDto;
            }



            // 验证脚本类型与适用平台是否匹配
            ScriptPlatformValidator.validatePlatformAndScript(scriptInfoDto.getPlatforms(), scriptInfoDto.getScriptType());

            // 生成uuid
            String uuid = isNewVersionScript ? UUID.randomUUID().toString() : scriptInfoDto.getUniqueUuid();
            String uuidVersion = UUID.randomUUID().toString();
            //设置版本表信息
            ScriptVersionDto scriptVersionDto = scriptInfoDto.getScriptVersionDto();
            scriptVersionDto.setSrcScriptUuid(uuidVersion);

            scriptInfoDto.setScriptVersionDto(scriptVersionDto);
            scriptInfoDto.setUniqueUuid(uuid);
            // info表新增数据
            createInfo(scriptInfoDto, isNewVersionScript);
            // version表新增数据
            createInfoVersion(scriptInfoDto);
            // 参数表增加数据
            scripts.getParameterService().createParameters(scriptInfoDto);
            // 脚本内容表增加数据
            scripts.getInfoVersionTextService().createInfoVersionText(scriptInfoDto);
            // 附件表更改数据
            scripts.getAttachmentService().createAttachments(scriptInfoDto,isNewVersionScript);
            // 函数变量绑定关系表增加数据
            scripts.getBindFuncVarService().createBindFuncVars(scriptInfoDto);
            // 脚本依赖
            logger.info(" MyScriptServiceImpl saveScript success");
        } catch (InterruptedException e) {
            logger.error("An InterruptedException occurred during the execution of the handle method.", e);
            Thread.currentThread().interrupt();
        } finally {
            releaseLocks(scriptSaveLock, isLocked);
            logger.info("结束释放锁");
        }
        return null;
    }


    /**
     * 校验用户修改权限
     * @param scriptInfoDto 脚本信息
     * @param isNewVersionScript    新增脚本标识
     * @throws ScriptException  自定义异常
     */
    public void validateCreateUserPermission(ScriptInfoDto scriptInfoDto,boolean isNewVersionScript) throws ScriptException {
        CurrentUser currentUser = CurrentUserUtil.getCurrentUser();
        //管理员权限不需要校验
        if(ObjectUtils.notEqual(currentUser.getSupervisor(),null) && currentUser.getSupervisor()){
            return;
        }
        //判断校验权限是creator（创建人），dept(部门和分类)
        String permissionPolicy = scripts.getScriptBusinessConfig().getPermissionPolicy();
        if(Objects.equals(permissionPolicy,"creator") && scriptInfoDto.getScriptSource() == 0){
            //创建人权限
            //验证当前用户是不是该脚本的创建人
            //判断是新增还是发布后的修改/编辑修改(新增不需要校验)
            if(!isNewVersionScript){
                //编辑修改校验脚本的创建人和当前的登录人（应对接口调用）
                //根据srcScriptUuid查询脚本(修改的是一个版本的脚本)
                ScriptVersionDto scriptVersionDto = scripts.getInfoVersionService().selectInfoVersionBySrcScriptUuid(scriptInfoDto.getScriptVersionDto().getSrcScriptUuid());
                boolean isSameUser = Objects.equals(currentUser.getFullName(), scriptVersionDto.getCreatorName());
                if(!isSameUser){
                    throw new ScriptException("current.user.no.have.permission");
                }
            }
        }else{
            //部门分类权限
            //判断是新增还是发布后的修改/编辑修改(新增不需要校验)
            if(!isNewVersionScript){
                //获取部门列表
                List<String> orgPermissionList = getOrgCodeList(scriptInfoDto);
                //判断当前用户的部门与有权限的部门
                boolean isPermission = orgPermissionList.contains(currentUser.getOrgCode());
                if(!isPermission){
                    throw new ScriptException("current.user.no.have.permission");
                }

            }
        }
    }


    /**
     * 组织组织机构编码集合
     * @param scriptInfoDto 脚本基本信息
     * @return  组织机构编码集合
     * @throws ScriptException  脚本自定义异常
     */
    private List<String> getOrgCodeList(ScriptInfoDto scriptInfoDto) throws ScriptException {
        // 获取当前脚本的创建用户的部门以及直接上级部门
        Info info = scripts.getInfoMapper().selectInfoByUniqueUuid(scriptInfoDto.getUniqueUuid());
        if (info == null) {
            throw new ScriptException("Script info not found for uniqueUuid: " + scriptInfoDto.getUniqueUuid());
        }
        String orgCode = info.getOrgCode();
        List<String> orgCodeList = splitOrgCode(orgCode);
        List<String> orgPermissionList = new ArrayList<>(orgCodeList);

        // 获取脚本分类绑定的部门和直系上级部门
        CategoryOrgBean categoryOrgRelations = scripts.getCategoryService().getCategoryOrgRelations(scriptInfoDto.getCategoryId());
        if (categoryOrgRelations == null) {
            throw new ScriptException("Category org relations not found for categoryId: " + scriptInfoDto.getCategoryId());
        }
        List<OrgBean> orgList = categoryOrgRelations.getOrgList();
        for (OrgBean orgBean : orgList) {
            String orgCodeByCategory = orgBean.getCode();
            List<String> orgCodeListByCategory = splitOrgCode(orgCodeByCategory);
            orgPermissionList.addAll(orgCodeListByCategory);
        }

        // 去重操作
        orgPermissionList = orgPermissionList.stream().distinct().collect(Collectors.toList());
        return orgPermissionList;
    }

    /**
     * 将组织机构编码本级以及直系上级编码获取
     * @param orgCode  组织机构编码
     * @return  组织机构编码的集合
     */
    public static List<String> splitOrgCode(String orgCode) {
        List<String> orgPermissionList = new ArrayList<>();

        // 如果 orgCode 为空，直接返回空列表
        if (orgCode == null || orgCode.isEmpty()) {
            return orgPermissionList;
        }

        // 将 orgCode 按照 "#" 拆分
        String[] parts = orgCode.split("#");

        // 生成拆分结果
        StringBuilder current = new StringBuilder();
        for (int i = 0; i < parts.length; i++) {
            current.append(parts[i]);
            current.append("#");
            orgPermissionList.add(current.toString());
        }

        return orgPermissionList;
    }

    /**
     * 处理脚本标签新增和修改时redis数据的变更
     */
    private void handleRedisLabel(ScriptInfoDto scriptInfoDto) throws ScriptException {

        //新增标签
        handleIncreaseLabel(scriptInfoDto);

        //删除标签
        handeleRemoveLabel(scriptInfoDto);

    }

    /**
     * 处理新增标签
     * @param scriptInfoDto 脚本信息
     */
    private void handleIncreaseLabel(ScriptInfoDto scriptInfoDto) throws ScriptException {
        List<String> insertedLabels = scriptInfoDto.getInsertedLabels();
        if (insertedLabels != null && !insertedLabels.isEmpty()) {

            //写入之前要判断redis中这个key是不是存在
            RKeys keys = scripts.getRedissonClient().getKeys();
            boolean exists = keys.countExists(SCRIPT_LABEL_ZSET) > 0;
            if (exists) {
                // 加载 Lua 脚本
                String luaScript = "local key = KEYS[1]\n" +
                        "local insertedLabels = ARGV\n" +
                        "for i, label in ipairs(insertedLabels) do\n" +
                        "    redis.call('ZINCRBY', key, 1, label)\n" +
                        "end\n" +
                        "return true";
                try {
                    scripts.getRedissonClient().getScript().eval(RScript.Mode.READ_WRITE, luaScript, RScript.ReturnType.BOOLEAN , Collections.singletonList(SCRIPT_LABEL_ZSET), insertedLabels.toArray(new String[0]));
                } catch (Exception e) {
                    logger.error("MyScriptServiceImpl.handleRedisLabel.error",e);
                    throw new ScriptException("handleRedisLabel.error");
                }
            }
        }
    }

    /**
     * 处理删除的标签
     * @param scriptInfoDto 脚本信息
     */
    private void handeleRemoveLabel(ScriptInfoDto scriptInfoDto) throws ScriptException {
        List<String> removedLabels = scriptInfoDto.getRemovedLabels();
        if (removedLabels != null && !removedLabels.isEmpty()) {
            // 写入之前要判断 Redis 中这个 key 是不是存在
            RKeys keys = scripts.getRedissonClient().getKeys();
            boolean exists = keys.countExists(SCRIPT_LABEL_ZSET) > 0;
            if (exists) {
                // 使用 Lua 脚本保证删除操作的原子性
                String luaScript = "local deletedLabels = {} " +
                        "for i, label in ipairs(ARGV) do " +
                        "  local score = tonumber(redis.call('ZSCORE', KEYS[1], label)) " +
                        "  if score ~= nil then " +
                        "    if score == 1 then " +
                        "      redis.call('ZREM', KEYS[1], label) " +
                        "      table.insert(deletedLabels, label) " +
                        "    elseif score > 1 then " +
                        "      redis.call('ZINCRBY', KEYS[1], -1, label) " +
                        "    end " +
                        "  end " +
                        "end " +
                        "return deletedLabels";

                try {
                    List<String> deletedLabels = scripts.getRedissonClient().getScript().eval(RScript.Mode.READ_WRITE, luaScript, RScript.ReturnType.MULTI, Collections.singletonList(SCRIPT_LABEL_ZSET), removedLabels.toArray(new String[0]));

                    // 更新数据库中的相关记录
                    if (!deletedLabels.isEmpty()) {
                        scripts.getDangerCmdService().updateDangerCmdByLabel(deletedLabels);
                    }
                } catch (Exception e) {
                    // 处理异常情况
                    logger.error("MyScriptServiceImpl.handleRedisLabel.error",e);
                    throw new ScriptException("handleRedisLabel.error");
                }
            }
        }
    }

    /**
     * 验证分类的可用性
     */

    public boolean checkCategoryValidation(Long categoryId) {
        CurrentUser currentUser = CurrentUserUtil.getCurrentUser();
        //管理员最大权限
        if (ObjectUtils.notEqual(currentUser.getSupervisor(),null) && currentUser.getSupervisor()) {
            return true;
        }

        //如果走的是角色-分类权限，那么需要判断当前用户所属角色是否绑定了该分类
        //只有绑定了当前用户所属角色的分类与未绑定任何角色的分类才可以验证通过
        if(getRolePermissionFlag()){
            //1、根据用户获取用户绑定的角色
            List<RoleApiDto> roleApiDtos = scripts.getiRole().selectRoleListByLoginName(currentUser.getLoginName());
            List<Long> idList = roleApiDtos.stream()
                    .map(RoleApiDto::getId)
                    .collect(Collectors.toList());
            //2、根据角色查询角色-分类表，获取所有绑定到角色的分类与所有未绑定任何角色的分类
            List<Long> categortIdList = scripts.getCategoryMapper().getSaveRoleCategoryIdsByRoleIds(idList);
            //3、判断当前保存的分类是否在上述结果集中，如果存在返回true，否则返回false
            return categortIdList.contains(categoryId);
        }

        //查找分类部门关系表中是否存在这个分类
        List<OrgBean> categoryOrgRelationsList = scripts.getCategoryMapper().getCategoryOrgRelations(categoryId);
        if (!categoryOrgRelationsList.isEmpty()) {
            //校验是不是当前用户的部门
            for (OrgBean orgBean : categoryOrgRelationsList) {
                if (Objects.equals(orgBean.getCode(), currentUser.getOrgCode())) {
                    return true;
                }
            }
            //如果不属于当前部门那么判定无效
            return false;
        }
        return true;
    }

    /**
     * 验证文件总大小
     * @param attachmentUploadDtoList 文件内容
     */
    private void validateFileSize(List<AttachmentUploadDto> attachmentUploadDtoList) throws ScriptException {
        long attachmentSize = 0;
        if(attachmentUploadDtoList != null && !attachmentUploadDtoList.isEmpty()){
            for (AttachmentUploadDto attachmentUploadDto : attachmentUploadDtoList) {
                attachmentSize += attachmentUploadDto.getSize();
            }
            DataSize totalScriptAttachmentLimitSize = scripts.getScriptBusinessConfig().getTotalScriptAttachmentLimitSize();
            long limitSizeMegabytes = totalScriptAttachmentLimitSize.toBytes();
            if(attachmentSize > limitSizeMegabytes){
                throw new ScriptException("file.too.large");
            }
        }
    }

    /**
     * 方法用于释放锁
     * 它根据锁的获取情况和当前线程是否持有锁来判断是否释放锁。如果锁被当前线程持有，则将其释放
     */
    private void releaseLocks(RLock lock, boolean isLocked) {
        if (isLocked && lock.isHeldByCurrentThread()) {
            lock.unlock(); // 释放锁
        }
    }

    /**
     * 通过redis锁限制脚本保存一次性添加
     *
     * @param scriptInfoDto 脚本新增信息
     * @return boolean
     */
    public boolean ensureCreatedOnce(ScriptInfoDto scriptInfoDto) throws ScriptException {
        //首先检验是新增还是发布后保存,没有id则为新增
        boolean isNewVersionScript = StringUtils.isEmpty(scriptInfoDto.getUniqueUuid());
        //新增——>判断info中有没有这条数据
        if (isNewVersionScript) {
            //判断数据是否已经插入到数据库
            int scriptNameZhCount = scripts.getInfoMapper().getSameScriptNameZhCount(scriptInfoDto.getScriptNameZh());
            if (scriptNameZhCount > 0) {
                logger.info("重复脚本");
                throw new ScriptException(SCRIPT_NAME_ZH_EXISTS);
            }
        } else {
            //发布保存——>判断info表中该条数据是不是草稿
            Info info = scripts.getInfoMapper().selectInfoById(scriptInfoDto.getId());
            logger.info("脚本状态:{}" , info.getEditState());
            if (info.getEditState() == 0) {
                logger.info("成功生成草稿");
                return false;
            }
        }
        return true;
    }


    /**
     * 更新脚本信息
     *
     * @param scriptInfoDto 脚本信息Dto
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ValidationResultDto updateMyScript(ScriptInfoDto scriptInfoDto) throws ScriptException {


        //校验分类是否属于当前用户或者是属于未绑定任何部门（控制权限）
        boolean categoryValidation = checkCategoryValidation(scriptInfoDto.getCategoryId());
        if(!categoryValidation){
            throw new ScriptException("invalid.category");
        }
        //校验保存权限
        validateCreateUserPermission(scriptInfoDto,false);

        //校验无修改
        boolean isChange = checkScript(scriptInfoDto);
        if (!isChange) {
            logger.info("script is no change");
            return null;
        }

        //针对redis中的标签数据进行处理（新增的标签在redis中增加这个标签，删除从redis中删除这个标签）
        handleRedisLabel(scriptInfoDto);


        //验证
        ValidationResultDto validationResultDto = validateScriptAndUpdateInfo(scriptInfoDto);
        if (validationResultDto != null) {
            return validationResultDto;
        }

        //验证附件总体大小
        validateFileSize(scriptInfoDto.getScriptVersionDto().getAttachmentUploadDtoList());
        // 更新基本信息
        boolean isUpdateInfo = updateInfo(scriptInfoDto);
        boolean isUpdateInfoVersion = updateInfoVersion(scriptInfoDto);
        // 更新内容
        boolean isUpdateInfoVersionText = scripts.getInfoVersionTextService().updateInfoVersionText(scriptInfoDto);

        // 删除原有参数关系
        scripts.getParameterMapper().deleteParameterByScriptUuid(scriptInfoDto.getScriptVersionDto().getSrcScriptUuid());
        // 更新参数
        scripts.getParameterService().createParameters(scriptInfoDto);

        // 更新附件
        boolean isUpdateAttachment = scripts.getAttachmentService().updateAttachments(scriptInfoDto);
        // 更新变量函数之前，将原有库中的变量函数绑定关系清除
        scripts.getBindFuncVarMapper().deleteBindFuncVarByScriptUuid(scriptInfoDto.getScriptVersionDto().getSrcScriptUuid());
        // 函数变量绑定关系表增加数据
        scripts.getBindFuncVarService().createBindFuncVars(scriptInfoDto);

        if (isUpdateInfo && isUpdateInfoVersion && isUpdateInfoVersionText && isUpdateAttachment) {
            logger.info("updateMyScript success");
        }
        return null;
    }

    /**
     * 验证脚本一致性
     *
     * @param scriptInfoDto 脚本基本信息dto
     * @return 比较结果
     */
    private boolean checkScript(ScriptInfoDto scriptInfoDto) {
        //判断如果是新建的脚本不进行这个验证
        if (scriptInfoDto.getUniqueUuid() == null) {
            return true;
        }
        //校验脚本有无改动start
        boolean isChange = false;
        //查询脚本信息
        String srcScriptUuid = scriptInfoDto.getScriptVersionDto().getSrcScriptUuid();
        InfoVersion infoVersion = scripts.getInfoVersionMapper().selectInfoVersionBysrcScriptUuid(srcScriptUuid);
        String uniqueUuid = infoVersion.getInfoUniqueUuid();
        Info info = scripts.getInfoMapper().selectInfoByUniqueUuid(uniqueUuid);

        //对比基本信息info表关联的
        boolean infoChange = checkInfoChange(info, scriptInfoDto);

        //对比版本信息version表关联的
        boolean infoVersionChange = checkInfoVersionChange(infoVersion, scriptInfoDto);

        if (infoChange && infoVersionChange) {
            return isChange;
        } else {
            return !isChange;
        }
        //检验脚本有无改动end
    }

    /**
     * 检查基础表改动情况
     * @param info  info表信息
     * @param scriptInfoDto 页面传递回来的数据
     * @return  boolean
     */
    private boolean checkInfoChange(Info info ,ScriptInfoDto scriptInfoDto){

        String label = info.getScriptLabel();

        boolean isScriptNameZh = Objects.equals(info.getScriptNameZh(), scriptInfoDto.getScriptNameZh());
        boolean isScriptName = Objects.equals(info.getScriptName(), scriptInfoDto.getScriptName());


        boolean isExecuser = Objects.equals(info.getExecuser(), scriptInfoDto.getExecuser());
        logger.info("Execuser is unChange: {}", isExecuser);


        boolean isLabel = Objects.equals(scriptInfoDto.getScriptLabel(), label);
        logger.info("Label is unChange: {}", isLabel);

        boolean isCategory = Objects.equals(scriptInfoDto.getCategoryId(),info.getCategoryId());
        logger.info("Category is unChange: {}", isCategory);

        String platforms = String.join(",", scriptInfoDto.getPlatforms());
        boolean idPlatforms = Objects.equals(platforms,info.getPlatform());

        //校验脚本类型
        boolean isScriptType = Objects.equals(scriptInfoDto.getScriptType(),info.getScriptType());


        return isExecuser && isLabel && isCategory && isScriptNameZh && isScriptName && idPlatforms && isScriptType;
    }

    private boolean checkInfoVersionChange(InfoVersion infoVersion,ScriptInfoDto scriptInfoDto){

        String srcScriptUuid = scriptInfoDto.getScriptVersionDto().getSrcScriptUuid();
        InfoVersionText infoVersionText = scripts.getInfoVersionTextMapper().selectInfoVersionTextByScriptUuid(srcScriptUuid);
        List<Parameter> parameterList = scripts.getParameterMapper().getParameterByUuid(srcScriptUuid);
        List<Attachment> attachmentList = scripts.getAttachmentMapper().getAttachmentByUuid(srcScriptUuid);


        boolean isExpectType = Objects.equals(infoVersion.getExpectType(), scriptInfoDto.getScriptVersionDto().getExpectType());
        logger.info("ExpectType is unChange: {}", isExpectType);
        boolean isExpectLine = Objects.equals(infoVersion.getExpectLastline(), scriptInfoDto.getScriptVersionDto().getExpectLastline());
        logger.info("ExpectLine is unChange: {}", isExpectLine);
        boolean isDescription = Objects.equals(infoVersion.getDescription(), scriptInfoDto.getScriptVersionDto().getDescription());
        logger.info("Description is unChange: {}", isDescription);
        boolean isScriptContent = Objects.equals(infoVersionText.getContent(), scriptInfoDto.getScriptVersionDto().getScriptContentDto().getContent());
        logger.info("ScriptContent is unChange: {}", isScriptContent);
        boolean isParam = false;

        List<Parameter> newParamList = new ArrayList<>();
        for (ParameterValidationDto parameterValidationDto : scriptInfoDto.getScriptVersionDto().getParameterValidationDtoList()) {
            newParamList.add(BeanUtils.copy(parameterValidationDto, Parameter.class));
        }
        if (parameterList.size() == newParamList.size()) {
            isParam = listEqualParams(parameterList, newParamList);
            logger.info("Param is unChange: {}", isParam);
        }
        //校验附件id有没有变化
        boolean isAttachment;
        List<Long> newAttachmentIdList = new ArrayList<>();
        List<Long> oldAttachmentIdList = new ArrayList<>();
        for (Attachment attachment : attachmentList) {
            newAttachmentIdList.add(attachment.getId());
        }
        for (AttachmentUploadDto attachmentUploadDto : scriptInfoDto.getScriptVersionDto().getAttachmentUploadDtoList()) {
            oldAttachmentIdList.add(attachmentUploadDto.getResponse().getId());
        }
        Collections.sort(newAttachmentIdList);
        Collections.sort(oldAttachmentIdList);
        isAttachment = newAttachmentIdList.equals(oldAttachmentIdList);

        //验证超时时间
        boolean isTimeout = Objects.equals(infoVersion.getTimeout(),scriptInfoDto.getScriptVersionDto().getTimeout());

        return isExpectType && isExpectLine && isDescription && isScriptContent && isParam && isAttachment && isTimeout;
    }


    /**
     * 验证参数一致性
     *
     * @param firstList  列表一
     * @param secondList 列表二
     * @return 比较结果
     */
    public boolean listEqualParams(List<Parameter> firstList, List<Parameter> secondList) {
        //判断顺序一致
        // 使用 HashMap 存储 secondList 中的参数
        HashMap<Integer, Parameter> secondMap = new HashMap<>();

        // 将第二个列表的参数放入 HashMap 中，以 paramOrder 为键
        for (Parameter parameter1 : secondList) {
            secondMap.put(parameter1.getParamOrder(), parameter1);
        }

        // 遍历第一个列表并进行比较
        for (Parameter parameter : firstList) {
            Parameter matchingParam = secondMap.get(parameter.getParamOrder());
            if (matchingParam != null) {
                boolean res = parametersEqual(parameter, matchingParam);
                if (!res) {
                    // 如果不相等，返回 false
                    return false;
                }
            }else{
                //没找到但原数据中存在，即为脚本被修改
                return false;
            }
        }

        return true;
    }

    /**
     * 拆分验证，降低代码复杂度
     *
     * @param parameter  参数一
     * @param parameter1 参数二
     * @return 比较结果
     */
    private boolean parametersEqual(Parameter parameter, Parameter parameter1) {
        boolean paramType = Objects.equals(parameter.getParamType(),parameter1.getParamType());
        boolean paramName = Objects.equals(parameter.getParamName(),parameter1.getParamName());
        boolean paramCheckIid = Objects.equals(parameter.getParamCheckIid(),parameter1.getParamCheckIid());
        boolean paramDesc = Objects.equals(parameter.getParamDesc(),parameter1.getParamDesc());
        boolean paramValue = false;
        if(ParameterType.STRING.getValue().equals(parameter.getParamType())){
            paramValue = Objects.equals(parameter.getParamDefaultValue(),parameter1.getParamDefaultValue());
        } else if (ParameterType.CIPHER.getValue().equals(parameter.getParamType())) {
            paramValue = EncryptUtils.sm4Decrypt(parameter.getParamDefaultValue()).equals(EncryptUtils.sm4Decrypt(parameter1.getParamDefaultValue()));
        }
        if (paramType && paramName && paramCheckIid && paramDesc && paramValue) {
            return true;
        }
        if (ParameterType.ENUMS.getValue().equals(parameter.getParamType())) {
            boolean parameterManagerId = Objects.equals(parameter.getScriptParameterManagerId(),parameter1.getScriptParameterManagerId());
            boolean paramDefaultValue = Objects.equals(parameter.getParamDefaultValue(),parameter1.getParamDefaultValue());
            return parameterManagerId && paramDefaultValue;
        }
        return false;
    }


    /**
     * 校验脚本参数名称是否重复
     *
     * @param scriptInfoDto 脚本基本信息
     * @return 校验值
     */
    @Override
    public boolean paramsNameExistCheck(ScriptInfoDto scriptInfoDto) {
        boolean returnFlag = false;
        List<ParameterValidationDto> parameterValidation = scriptInfoDto.getScriptVersionDto().getParameterValidationDtoList();
        List<String> paramsNameList = new ArrayList<>();
        for (ParameterValidationDto parameterValidationDto : parameterValidation) {
            paramsNameList.add(parameterValidationDto.getParamName());
        }
        // 使用HashSet去除重复项
        Set<String> set = new HashSet<>(paramsNameList);
        // 将Set转换回List
        List<String> listWithoutDuplicates = new ArrayList<>(set);
        //如果去重后的list与原来大小相同，说明没有重复参数名称
        if (paramsNameList.size() != listWithoutDuplicates.size()) {
            returnFlag = true;
        }
        return returnFlag;
    }

    /**
     * 验证参数验证，关键命令验证
     *
     * @param scriptInfoDto 脚本信息
     * @return 管理脚本服务化的验证结果Dto
     */
    private ValidationResultDto validateScriptAndUpdateInfo(ScriptInfoDto scriptInfoDto) throws ScriptException {
        ValidationResultDto validationResultDto = new ValidationResultDto();
        //验证关键命令
        List<ScriptValidationResultDto> scriptValidationResultDtos = validateScriptWithKeywords(scriptInfoDto);
        //验证参数
        ParameterValidationResultDto parameterValidationResultDto = scripts.getParameterService().validateParameter(scriptInfoDto);

        if (parameterValidationResultDto != null && parameterValidationResultDto.getLine() != null) {
            validationResultDto.setParameterValidationResultDto(parameterValidationResultDto);
            return validationResultDto;
        }

        if (scriptValidationResultDtos != null && !scriptValidationResultDtos.isEmpty()) {
            for (ScriptValidationResultDto scriptValidationResultDto : scriptValidationResultDtos) {
                if (scriptValidationResultDto.getType() == 1) {
                    validationResultDto.setScriptValidationResultDtoList(scriptValidationResultDtos);
                    return validationResultDto;
                }
            }
        }

        if (scriptValidationResultDtos != null && !scriptValidationResultDtos.isEmpty()) {
            boolean ignoreTipCmdIsNull = scriptInfoDto.getIgnoreTipCmd() == null;
            if (ignoreTipCmdIsNull || scriptInfoDto.getIgnoreTipCmd() == 0) {
                validationResultDto.setScriptValidationResultDtoList(scriptValidationResultDtos);
                return validationResultDto;
            }
        }
        //不是psbc不允许保存sql类型脚本
        String customerName  = SpringUtil.getBean(CustomerProperty.class).getName();
        if(customerName == null || !customerName.equals("psbc")) {
            if(Objects.equals(scriptInfoDto.getScriptType(), "sql")){
                throw new ScriptException("Creation.of.sql.type.scripts.is.not.allowed");
            }
        }


        return null;
    }

    /**
     * 获取我的脚本列表(分页)
     *
     * @param scriptInfoQueryDto 脚本查询信息Dto
     * @return 结果
     */
    @Override
    public PageInfo<ScriptInfoApiDto> selectScriptPageList(ScriptInfoQueryDto scriptInfoQueryDto) {
        ScriptInfoQueryBean scriptInfoQueryBean = BeanUtils.copy(scriptInfoQueryDto, ScriptInfoQueryBean.class);
        buildScriptInfoQuery(scriptInfoQueryBean);
        //获取当前分类以及所有子类的id
        PageMethod.startPage(scriptInfoQueryDto.getPageNum(), scriptInfoQueryDto.getPageSize());
        //查询逻辑
        List<MyScriptBean> myScriptBeans = scripts.getMyScriptMapper().selectMyScriptList(scriptInfoQueryBean);

        //查询长期未修改脚本报表
        String customerName  = SpringUtil.getBean(CustomerProperty.class).getName();
        if(customerName != null && customerName.equals("psbc")) {
            getScriptStatementData(myScriptBeans);
        }
        PageInfo<ScriptInfoApiDto> myScriptDtoPage = PageDataUtil.toDtoPage(myScriptBeans, ScriptInfoApiDto.class);
        //dubbo接口需要实现方法
        if(scriptInfoQueryDto.getDubboFlag()){
            //拼接参数
            getParamForScriptPage(myScriptDtoPage);
        }
        return myScriptDtoPage;
    }

    private void getScriptStatementData(List<MyScriptBean> myScriptBeanList) {
        myScriptBeanList.parallelStream()
                .forEach(myScriptBean -> {
                    StatementDto statementDto = scripts.getScriptStatementService().getScriptStatementByInfoId(myScriptBean.getScriptInfoId());
                    if (statementDto != null) {
                        myScriptBean.setConfirmState(statementDto.getConfirmState());
                    }
                });
    }


    /**
     * 拼接dubbo接口查询脚本列表需要的参数列表信息
     * @param myScriptDtoPage   我的脚本分页信息
     */
    public void getParamForScriptPage(PageInfo<ScriptInfoApiDto> myScriptDtoPage){
        for (ScriptInfoApiDto scriptInfoApiDto : myScriptDtoPage.getList()) {
            // 组织脚本参数
            InfoVersion infoVersion = new InfoVersion();
            infoVersion.setSrcScriptUuid(scriptInfoApiDto.getSrcScriptUuid());
            scriptInfoApiDto.setScriptParamApiDtoList(scripts.getParameterService().getParameterValidationDtos(infoVersion));
        }
    }
    /**
     * 获取我的脚本列表
     *
     * @param scriptInfoQueryDto 脚本查询信息Dto
     * @return 结果
     */
    @Override
    public List<ScriptInfoApiDto> selectScriptList(ScriptInfoQueryDto scriptInfoQueryDto) {
        ScriptInfoQueryBean scriptInfoQueryBean = BeanUtils.copy(scriptInfoQueryDto, ScriptInfoQueryBean.class);
        buildScriptInfoQuery(scriptInfoQueryBean);
        //查询逻辑
        List<MyScriptBean> myScriptBeans = scripts.getMyScriptMapper().selectMyScriptList(scriptInfoQueryBean);
        List<ScriptInfoApiDto> myScriptDtoPage = BeanUtils.copy(myScriptBeans, ScriptInfoApiDto.class);
        if(scriptInfoQueryDto.getDubboFlag()){
            //拼接参数
            getParamForScript(myScriptDtoPage);
        }
        return myScriptDtoPage;
    }

    /**
     * 拼接dubbo接口查询脚本列表需要的参数列表信息
     * @param scriptInfoApiDtoList  我的脚本信息
     */
    public void getParamForScript(List<ScriptInfoApiDto> scriptInfoApiDtoList){
        for (ScriptInfoApiDto scriptInfoApiDto : scriptInfoApiDtoList) {
            // 组织脚本参数
            InfoVersion infoVersion = new InfoVersion();
            infoVersion.setSrcScriptUuid(scriptInfoApiDto.getSrcScriptUuid());
            scriptInfoApiDto.setScriptParamApiDtoList(scripts.getParameterService().getParameterValidationDtos(infoVersion));
        }
    }

    /**
     * 判断权限是否走角色
     * @return 判断权限是否走角色布尔值
     */
    @Override
    public boolean getRolePermission() {
        //走角色-分类还是部门-分类，roleCategory为角色-分类，默认走部门-分类
        String classificationPermissionType = scripts.getScriptBusinessConfig().getQueryScriptPermission();
        //走角色-分类大项中的  角色（userRoleGroup）   还是   部门   默认走部门
        String roleDeptType = scripts.getScriptBusinessConfig().getDataPermissionPolicy();
        //走角色
        if(Objects.equals(classificationPermissionType,"roleCategory")
                && Objects.equals(roleDeptType,"userRoleGroup")){
            return true;
        }
        return false;
    }

    @Override
    public List<String> getCategoryPathByUserRole(CurrentUser user){
        //1、根据当前用户查询当前用户所有的角色
        List<RoleApiDto> roleApiDtos = scripts.getiRole().selectRoleListByLoginName(user.getLoginName());
        List<Long> idList = roleApiDtos.stream()
                .map(RoleApiDto::getId)
                .collect(Collectors.toList());
        //2、根据1中查到的角色去分类角色表、分类表中查询绑定到这些角色上的分类
        List<CategoryRoleBean> categortIdList = scripts.getCategoryMapper().getCategoryIdsByRole(idList);
        //3、查询2中分类相关的脚本 以及 没有绑定角色的分类
        List<String> categoryPathList = new ArrayList<>();
        for (CategoryRoleBean categoryRoleBean : categortIdList) {
            String categoryFullPath = getCategoryFullPath(categoryRoleBean.getCategoryId());
            //针对%和_这种拼接进行处理
            String escapedLikeCategoryPath = scripts.getCategoryService().handleCategoryPath(categoryFullPath);
            if(StringUtils.isNotBlank(escapedLikeCategoryPath) && !categoryPathList.contains(escapedLikeCategoryPath + "%")){
                categoryPathList.add(escapedLikeCategoryPath + "%");
            }
        }
        return categoryPathList;
    }

    /**
     * 组装脚本查询需要信息
     * @param scriptInfoQueryBean   脚本查询信息bean
     */
    private void buildScriptInfoQuery(ScriptInfoQueryBean scriptInfoQueryBean){
        //走角色-分类还是部门-分类，roleCategory为角色-分类，默认走部门-分类
        String classificationPermissionType = scripts.getScriptBusinessConfig().getQueryScriptPermission();
        //走角色-分类大项中的  角色（userRoleGroup）   还是   部门   默认走部门
        String roleDeptType = scripts.getScriptBusinessConfig().getDataPermissionPolicy();
        //走部门-分类大项中的  用户（creator）   还是    部门  默认走部门
        String permissionPolicy = scripts.getScriptBusinessConfig().getPermissionPolicy();

        //是否查询所有脚本标识
        boolean getAllScriptFlag = scripts.getScriptBusinessConfig().isGetAllScriptFlag();

        scriptInfoQueryBean.setPermissionPolicy(permissionPolicy);
        CurrentUser currentUser = null;

        if(scriptInfoQueryBean.getCurrentUserDto() != null && scriptInfoQueryBean.getCurrentUserDto().getLoginName() != null){
            currentUser = BeanUtils.copy(scriptInfoQueryBean.getCurrentUserDto(),CurrentUser.class);
        }else{
            currentUser = CurrentUserUtil.getCurrentUser();
        }

        //权限分为4大类（1、查询全部脚本；2、根据当前用户查询；3、根据当前用户部门查询；4、根据角色查询）
        //1、接口的请求 支持查看所有脚本  或者是  超级管理员、全部脚本查询，直接根据是否是超级用户查询所有脚本
        if((scriptInfoQueryBean.getDubboFlag() && getAllScriptFlag)
            || ((ObjectUtils.notEqual(currentUser.getSupervisor(),null) && currentUser.getSupervisor()) || (StringUtils.isNotEmpty(scriptInfoQueryBean.getFrom()) && "allScript".equals(scriptInfoQueryBean.getFrom())))){
            scriptInfoQueryBean.setSuperUser(true);
            scriptInfoQueryBean.setSysOrgCode("####");
            scriptInfoQueryBean.setPermissionType(1);
        //2、走部门-分类大项中的根据 用户 查询
        }else if(!Objects.equals(classificationPermissionType,"roleCategory") && Objects.equals(permissionPolicy,"creator")){
            //使用创建人筛选脚本
            scriptInfoQueryBean.setSysOrgCode(null);
            scriptInfoQueryBean.setCreatorName(currentUser.getFullName());
            scriptInfoQueryBean.setPermissionType(2);
        //3、走部门-分类大项中的根据部门查询  或者走   角色-分类大项中的根据部门查询 ，两个逻辑相同，都是根据当前登录用户部门作为查询条件
        }else if((!Objects.equals(classificationPermissionType,"roleCategory") && !Objects.equals(permissionPolicy,"creator"))
                || (Objects.equals(classificationPermissionType,"roleCategory") && !Objects.equals(roleDeptType,"userRoleGroup"))){
            scriptInfoQueryBean.setSysOrgCode(currentUser.getOrgCode());
            List<String> categoryPathList = new ArrayList<>();

            //计算分类的全路径作为权限匹配脚本
            List<Long> categortIdList = scripts.getCategoryMapper().getCategoryByOrgCode(currentUser.getOrgCode());
            for (Long categoryId : categortIdList) {
                String categoryFullPath = getCategoryFullPath(categoryId);
                //针对%和_这种拼接进行处理
                String escapedLikeCategoryPath = scripts.getCategoryService().handleCategoryPath(categoryFullPath);
                if(StringUtils.isNotBlank(escapedLikeCategoryPath) && !categoryPathList.contains(escapedLikeCategoryPath + "%")){
                    categoryPathList.add(escapedLikeCategoryPath + "%");
                    scriptInfoQueryBean.setOrgCategoryPath(categoryPathList);
                }
            }
            scriptInfoQueryBean.setPermissionType(3);
        //4、走角色-分类大项中的根据角色查询
        }else if(Objects.equals(classificationPermissionType,"roleCategory") && Objects.equals(roleDeptType,"userRoleGroup")){
            scriptInfoQueryBean.setOrgCategoryPath(getCategoryPathByUserRole(currentUser));
            scriptInfoQueryBean.setPermissionType(3);
        }

        //获取分类完整路径
        String categoryPath = getCategoryFullPath(scriptInfoQueryBean.getCategoryId());

        //针对%和_这种拼接进行处理
        String escapedLikeCategoryPath = scripts.getCategoryService().handleCategoryPath(categoryPath);

        scriptInfoQueryBean.setCategoryPath(categoryPath);
        scriptInfoQueryBean.setEscapedLikeCategoryPath(escapedLikeCategoryPath);

    }

    /**
     * 获取分类的全路径拼接
     * @param categoryId    分类id
     * @return      分类全路径
     */
    private String getCategoryFullPath(Long categoryId){
        //拼接分类的全路径（仅拼接到当前的分类）
        Category category = new Category();
        category.setId(categoryId);

        return scripts.getCategoryService().buildCategoryPath(category);
    }



    /**
     * 获取脚本详情
     *
     * @param scriptInfoQueryDto 脚本查询信息
     * @return 脚本信息
     */
    @Override
    public ScriptInfoDto getScriptDetail(ScriptInfoQueryDto scriptInfoQueryDto) throws ScriptException {
        if (scriptInfoQueryDto == null) {
            return new ScriptInfoDto();
        }
        //如果uuid存在@符号，则按照@拆分
        //格式可能为：f03111b4-f4b0-1004-8915-5ef0ad04ada4@[1037226357105360896]
        //查询临时附件使用
        String srcScriptUuid = "";
        if(ObjectUtils.notEqual(scriptInfoQueryDto.getSrcScriptUuid(),null)){
            if(scriptInfoQueryDto.getSrcScriptUuid().contains("@")){
                srcScriptUuid = scriptInfoQueryDto.getSrcScriptUuid().split("@")[0];
            }else{
                srcScriptUuid = scriptInfoQueryDto.getSrcScriptUuid();
            }
        }
        Info info;
        InfoVersion infoVersion = null;
        //查询基本信息
        //脚本版本uuid不为空用版本uuid查询
        if(!Objects.isNull(srcScriptUuid) && !srcScriptUuid.isEmpty()){
            //标识判断，true返回脚本默认版本的基本信息
            if(scriptInfoQueryDto.getShowDefaultVersion()){
                //根据版本信息查询脚本信息(当前默认版本)
                infoVersion = scripts.getInfoVersionMapper().selectDefaultInfoVersionBysrcScriptUuid(srcScriptUuid);
            }else{
                //根据版本信息查询脚本信息
                infoVersion = scripts.getInfoVersionMapper().selectInfoVersionBysrcScriptUuid(srcScriptUuid);
            }
        }
        //dubbo接口过来的请求，srcSscriptUuid没查到数据，脚本id、中文名、英文名都为空，则直接反馈空对象
        if(scriptInfoQueryDto.getDubboFlag()
                && !ObjectUtils.notEqual(infoVersion,null)
                && !ObjectUtils.notEqual(scriptInfoQueryDto.getScriptInfoId(),null)
                && !ObjectUtils.notEqual(scriptInfoQueryDto.getScriptName(),null)
                && !ObjectUtils.notEqual(scriptInfoQueryDto.getScriptNameZh(),null)){
            return new ScriptInfoDto();
        }

        //uniqueUuid赋值
        if(ObjectUtils.notEqual(infoVersion,null)){
            scriptInfoQueryDto.setInfoUniqueUuid(infoVersion.getInfoUniqueUuid());
        }

        //查询脚本详情数据
        info = scripts.getInfoMapper().getScriptInfo(scriptInfoQueryDto);

        //未查到详情数据返回空dto
        if(Objects.isNull(info)){
            return new ScriptInfoDto();
        }

        //如果不是dubbo接口过来的请求，则说明是页面的请求，此时查询草稿内容
        if(!scriptInfoQueryDto.getDubboFlag()
                && !ObjectUtils.notEqual(infoVersion,null)){
            infoVersion = scripts.getInfoVersionMapper().selectLastInfoVersionByInfoUuid(info.getUniqueUuid(), info.getEditState());
        }

        //构建详细信息
        if (infoVersion == null) {
            return new ScriptInfoDto();
        }

        //web接口特有处理逻辑
        if(!scriptInfoQueryDto.getDubboFlag()) {
            handleWebServiceForDetail(info);
        }

        return buildScriptDetails(info, infoVersion, scriptInfoQueryDto);
    }

    //dubbo接口获取返回值
    @Override
    public ScriptDubboInfoDto getScriptDetailForDubbo(ScriptInfoQueryDto scriptInfoQueryDto) throws ScriptException {
        ScriptInfoDto scriptDetail = getScriptDetail(scriptInfoQueryDto);
        //工具箱导入时查询脚本不存在时返回空对象保证工具箱后续操作
        if(Objects.isNull(scriptDetail.getScriptVersionDto())){
            return new ScriptDubboInfoDto();
        }
        return buildScriptDetailsForDubbo(scriptDetail, scriptInfoQueryDto);
    }


    /**
     * 构建脚本详细信息（dubbo）
     * @param scriptInfoDto 脚本基本信息
     * @param scriptInfoQueryDto    脚本查询信息
     * @return  脚本详细信息（dubbo）
     */
    public ScriptDubboInfoDto buildScriptDetailsForDubbo(ScriptInfoDto scriptInfoDto,ScriptInfoQueryDto scriptInfoQueryDto){
        ScriptDubboInfoDto scriptDubboInfoDto = new ScriptDubboInfoDto();
        ScriptContentDto scriptContentDto = new ScriptContentDto();

        if(scriptInfoQueryDto.isQueryScriptContentFlag()) {
            //  获取内容
            scriptContentDto.setContent(scriptInfoDto.getScriptVersionDto().getScriptContentDto().getContent());
        }
        if(scriptInfoQueryDto.isQueryScriptParamsFlag()) {
            //  获取参数
            scriptDubboInfoDto.setParamList(scriptInfoDto.getScriptVersionDto().getParameterValidationDtoList());
        }
        if(scriptInfoQueryDto.isQueryScriptAttachmentFlag()){
            //  获取附件
            scriptDubboInfoDto.setAttachmentList(BeanUtils.copy(scriptInfoDto.getScriptVersionDto().getAttachmentUploadDtoList(),AttachmentApiDto.class));
        }
        //  构建基本信息
        scriptDubboInfoDto.setScriptNameZh(scriptInfoDto.getScriptNameZh());
        scriptDubboInfoDto.setScriptName(scriptInfoDto.getScriptName());
        scriptDubboInfoDto.setScriptType(scriptInfoDto.getScriptType());
        scriptDubboInfoDto.setExecuser(scriptInfoDto.getExecuser());
        scriptDubboInfoDto.setId(scriptInfoDto.getId());
        scriptDubboInfoDto.setUniqueUuid(scriptInfoDto.getUniqueUuid());
        scriptDubboInfoDto.setSrcScriptUuid(scriptInfoDto.getScriptVersionDto().getSrcScriptUuid());
        scriptDubboInfoDto.setVersion(scriptInfoDto.getScriptVersionDto().getVersion());
        scriptDubboInfoDto.setContent(scriptContentDto.getContent());
        scriptDubboInfoDto.setPlatform(scriptInfoDto.getPlatform());
        return scriptDubboInfoDto;
    }

    /**
     * 构建详细信息
     * @param info  脚本基本信息
     * @param infoVersion   脚本版本信息
     * @param scriptInfoQueryDto 脚本查询信息
     *
     */
    private ScriptInfoDto buildScriptDetails(Info info,InfoVersion infoVersion,ScriptInfoQueryDto scriptInfoQueryDto){
        ScriptContentDto scriptContentDto = new ScriptContentDto();

        //  构建基本信息
        ScriptInfoDto scriptInfoDto = setScriptInfoValues(info);
        ScriptVersionDto scriptVersionDto = setScriptInfoVersionValues(scriptInfoDto, info, infoVersion);
        if(scriptInfoQueryDto.isQueryScriptContentFlag()) {
            //  获取内容
            InfoVersionText infoVersionText = scripts.getInfoVersionTextMapper().selectInfoVersionTextByScriptUuid(infoVersion.getSrcScriptUuid());
            setScriptInfoVersionTextValues(scriptContentDto, infoVersionText);
        }
        if(scriptInfoQueryDto.isQueryScriptParamsFlag()) {
            //  获取参数
            List<ParameterValidationDto> parameterValidationDtoList = scripts.getParameterService().getParameterValidationDtos(infoVersion);
            scriptVersionDto.setParameterValidationDtoList(parameterValidationDtoList);
        }
        if(scriptInfoQueryDto.isQueryScriptAttachmentFlag()){
            //  获取附件
            List<AttachmentUploadDto> attachmentUploadDtos = scripts.getAttachmentService().getAttachmentUploadDtos(infoVersion);
            //判断，如果uuid携带了临时附件id（通过dubbo接口上传的附件），则需要把这些附件带上
            getAttachmentsTemp(attachmentUploadDtos,scriptInfoQueryDto);
            scriptVersionDto.setAttachmentUploadDtoList(attachmentUploadDtos);
        }
        //  获取绑定变量关系
        BindFuncVarDto[] variableDtos = scripts.getBindFuncVarService().getBindFuncVarDtos(infoVersion, BindFuncVarTypeEnum.VARIABLE.getType());
        //  获取绑定函数关系
        BindFuncVarDto[] functionDtos = scripts.getBindFuncVarService().getBindFuncVarDtos(infoVersion, BindFuncVarTypeEnum.FUNCTION.getType());

        scriptVersionDto.setScriptContentDto(scriptContentDto);
        scriptVersionDto.setVariableList(variableDtos);
        scriptVersionDto.setFunctionList(functionDtos);
        scriptVersionDto.setDependentList(new ArrayList<>());
        scriptInfoDto.setScriptVersionDto(scriptVersionDto);
        //银行标识
        scriptInfoDto.setCustomerName(SpringUtil.getBean(CustomerProperty.class).getName());
        return scriptInfoDto;
    }

    public void handleWebServiceForDetail(Info info) throws ScriptException {
        //检查有无可用脚本（禁用全部脚本）
        checkAvailableScript(info);
    }

    /**
     * 根据srcScriputUuid与附件id查询附件
     * @param attachmentUploadDtos 附件集合
     * @param scriptInfoQueryDto 参数
     * @return 附件集合，包含脚本绑定的附件以及dubbo接口上传的附件
     */
    private List<AttachmentUploadDto> getAttachmentsTemp(List<AttachmentUploadDto> attachmentUploadDtos ,ScriptInfoQueryDto scriptInfoQueryDto ){
        if(ObjectUtils.notEqual(scriptInfoQueryDto.getSrcScriptUuid(),null)){
            String srcScriptUuid = scriptInfoQueryDto.getSrcScriptUuid();
            if(StringUtils.isNotEmpty(srcScriptUuid) && srcScriptUuid.contains("@")){
                String attachmentIdsStr = srcScriptUuid.split("@")[1];
                if(attachmentIdsStr.startsWith("[") && attachmentIdsStr.endsWith("]") && attachmentIdsStr.length() > 2){
                    String [] attachmentIds = attachmentIdsStr.substring(1,attachmentIdsStr.length()-1).split(",");
                    //将String数组换成Long数组
                    Long[] attachementIdsLong = Arrays.stream(attachmentIds)
                            .map(Long::valueOf)
                            .toArray(Long[]::new);
                    //根据id查询附件
                    List<Attachment> attachments = attachmentEphemeralMapper.selectAttachmentByIds(attachementIdsLong);
                    attachmentUploadDtos.addAll(BeanUtils.copy(attachments,AttachmentUploadDto.class));
                }
            }
        }
        return attachmentUploadDtos;
    }


    /**
     * 检查脚本是否存在可用脚本
     * @param info 脚本基础信息
     */
    public void checkAvailableScript(Info info) throws ScriptException {
        if(info.getEditState()!= null && info.getEditState().equals(1)) {
            //查询脚本存在可用版本数量（不存在草稿情况下）
            InfoVersion infoVersion = scripts.getInfoVersionMapper().selectLastEnableInfoVersion(info.getUniqueUuid());
            if (infoVersion == null) {
                throw new ScriptException("no.available.script");
            }
        }
    }
    /**
     * 删除脚本
     *
     * @param scriptDeleteDto 删除脚本所需信息
     * @param highPower       为true 可删除发布的脚本
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteMyScript(ScriptDeleteDto scriptDeleteDto, boolean highPower) throws ScriptException {
        try {
            Long[] ids = scriptDeleteDto.getIds();
            List<String> scriptVersionUuids = new ArrayList<>();
            //查询脚本信息
            List<InfoVersion> infoVersionList = scripts.getInfoVersionMapper().selectDefaultInfoVersionByIds(ids);
            for (InfoVersion infoVersion : infoVersionList) {
                //检查脚本状态，已发布和审核中的不允许删除
                checkScriptState(highPower, infoVersion);
                //根据脚本版本信息获取脚本基础信息
                Info info = scripts.getInfoMapper().selectInfoByUniqueUuid(infoVersion.getInfoUniqueUuid());

                if (highPower && infoVersion.getEditState() == EditStateEnum.PUBLISH.getType()) {
                    //将版本id从ids中取出，发布状态下最后一个脚本的操作在回更结果位置处理
                    List<Long> list = new ArrayList<>(Arrays.asList(ids));
                    list.remove(infoVersion.getId());
                    ids = list.toArray(new Long[0]);
                    //双人复核表,版本表数据处理
                    Long auditRelationId = updateDoubleCheckForDel(infoVersion, scriptDeleteDto);
                    //双人复核对接所需信息Dto
                    DoubleCheckInfoDto doubleCheckInfoDto = new DoubleCheckInfoDto();
                    doubleCheckInfoDto.setAuditRelationId(auditRelationId);
                    //设置双人复核对接所需信息Dto
                    setDoubleCheckInfoDtoProperties(doubleCheckInfoDto, info, scriptDeleteDto);
                    //调用双人复核
                    scriptSubmit(doubleCheckInfoDto,Enums.AuditType.SCRIPT_DELETE.getValue());
                } else {
                    scriptVersionUuids.add(infoVersion.getSrcScriptUuid());
                    //这里是删除的草稿，删除成功直接将info表状态置为发布
                    info.setEditState(1);
                    scripts.getInfoMapper().updateInfo(info);
                }
            }
            if (ids.length != 0) {
                String[] uuidArray = scriptVersionUuids.toArray(new String[0]);

                //删除的版本如果是最后一个将info表ideleted置1
                for (Long id : ids) {
                    updateInfoWithNoVersion(id);
                }

                for (Long id : ids) {
                    //删除脚本
                    scripts.getInfoVersionMapper().deleteInfoVersionById(id);

                }
                // 删除内容
                scripts.getInfoVersionTextMapper().deleteInfoVersionTextByScriptUuids(uuidArray);
                // 删除参数
                scripts.getParameterMapper().deleteParameterByScriptUuids(uuidArray);
                // 删除附件
                scripts.getAttachmentMapper().deleteAttachmentByScriptUuids(uuidArray);
            }
        } catch (Exception e) {
            logger.error("deleteMyScript error:", e);
            throw new ScriptException(e.getMessage());
        }
    }

    /**
     * 判断删除的脚本的默认版本属性，进行重置
     *
     * @param versionUuid 版本uuid
     */
    private void updateDefaultFromDelete(String versionUuid) {
        //查询脚本版本的信息
        InfoVersion infoVersion = scripts.getInfoVersionMapper().selectInfoVersionBysrcScriptUuid(versionUuid);
        if (infoVersion.getIsDefault().equals(1)) {
            //清除当前脚本默认字段
            InfoVersion infoVersion1 =  new InfoVersion();
            infoVersion1.setId(infoVersion.getId());
            infoVersion1.setIsDefault(0);
            scripts.getInfoVersionMapper().updateInfoVersion(infoVersion1);
            logger.info("清除该脚本的默认属性,id:{}",infoVersion.getId());
            //赋值给当前最新并未被禁用的版本,没有符合条件的数据就不赋值
            InfoVersion infoVersion2 = scripts.getInfoVersionMapper().selectLastEnableInfoVersion(infoVersion.getInfoUniqueUuid());
            if(infoVersion2 != null) {
                infoVersion2.setIsDefault(1);
                scripts.getInfoVersionMapper().updateInfoVersion(infoVersion2);
                logger.info("删除了默认脚本，默认脚本迁移至 id：{}",infoVersion2.getId());
            }
        }

    }

    /**
     * 检查脚本状态，已发布和审核中的不允许删除
     *
     * @param highPower   权限
     * @param infoVersion 版本信息
     * @throws ScriptException 脚本异常
     */
    private void checkScriptState(boolean highPower, InfoVersion infoVersion) throws ScriptException {
        if (!highPower && infoVersion.getEditState() == EditStateEnum.PUBLISH.getType()) {
            logger.error("发布状态的脚本不能删除！");
            throw new ScriptException("cannot.delete.published.script");
        } else if (infoVersion.getEditState() == EditStateEnum.REVIEW.getType()) {
            //审核状态脚本任何位置都不能删除
            logger.error("审核中状态的脚本不能删除！");
            throw new ScriptException("cannot.delete.review.script");
        }
    }

    /**
     * 更新infoVersion状态,同步更新info表状态
     *
     * @param infoVersionWithoutVersion 版本信息
     * @param publishDto                发布信息
     */
    private void updateScriptVersion(InfoVersion infoVersionWithoutVersion, PublishDto publishDto) {
        //更新版本表审核状态
        InfoVersion infoVersion = new InfoVersion();
        infoVersion.setId(infoVersionWithoutVersion.getId());
        infoVersion.setEditState(EditStateEnum.REVIEW.getType());
        infoVersion.setInfoUniqueUuid(infoVersionWithoutVersion.getInfoUniqueUuid());
        //更新风险级别
        infoVersion.setLevel(publishDto.getLevel());
        scripts.getInfoVersionMapper().updateInfoVersion(infoVersion);

        Info info = new Info();
        Info info1 = scripts.getInfoMapper().selectInfoByUniqueUuid(infoVersion.getInfoUniqueUuid());
        info.setId(info1.getId());
        info.setEditState(EditStateEnum.REVIEW.getType());
        scripts.getInfoMapper().updateInfo(info);
    }

    /**
     * 删除发布脚本时更新双人复核表
     *
     * @param infoVersionWithoutVersion 脚本版本信息
     */
    public Long updateDoubleCheckForDel(InfoVersion infoVersionWithoutVersion, ScriptDeleteDto scriptDeleteDto) {
        //设置双人复核实体信息
        AuditRelationDto auditRelationDto = new AuditRelationDto();
        auditRelationDto.setSrcScriptUuid(infoVersionWithoutVersion.getSrcScriptUuid());
        auditRelationDto.setAuditType(AuditTypeEnum.DELETE.getType());
        auditRelationDto.setState(AuditStateEnum.PENDING.getType());
        auditRelationDto.setAuditUserId(scriptDeleteDto.getAuditorId());
        auditRelationDto.setAuditUser(scriptDeleteDto.getAuditorName());
        CurrentUser currentUser = CurrentUserUtil.getCurrentUser();
        auditRelationDto.setApplyUserId(currentUser.getId());
        auditRelationDto.setApplyUser(currentUser.getLoginName());
        //更新双人复核表
        scripts.getAuditRelationService().insertAuditRelation(auditRelationDto);
        //更新版本表审核状态
        InfoVersion infoVersion = new InfoVersion();
        infoVersion.setId(infoVersionWithoutVersion.getId());
        infoVersion.setEditState(EditStateEnum.REVIEW.getType());
        scripts.getInfoVersionMapper().updateInfoVersion(infoVersion);
        return auditRelationDto.getId();
    }

    /**
     * 接收审核信息并更新脚本版本信息
     *
     * @param auditResultBean 复核结果Bean对象
     */
    @Override
    public void updateScriptReviewForDelete(AuditResultBean auditResultBean) throws SystemException {
        try {
            //更新双人复核表审批状态
            AuditRelationDto auditRelationDto = new AuditRelationDto();
            auditRelationDto.setId(auditResultBean.getAuditRelationId());
            auditRelationDto.setState(auditResultBean.getState());
            auditRelationDto.setApprWorkitemId(auditResultBean.getApprWorkitemId());
            scripts.getAuditRelationService().updateAuditRelation(auditRelationDto);

            //获取双人复核表审核状态
            AuditRelation auditRelation = scripts.getAuditRelationMapper().selectAuditRelationById(auditResultBean.getAuditRelationId());

            InfoVersion currentInfoVersion = scripts.getInfoVersionMapper().selectInfoVersionBysrcScriptUuid(auditRelation.getSrcScriptUuid());

            //通过 - 版本表删除该脚本
            if (auditRelation.getState() == AuditStateEnum.APPROVED.getType()) {
                //删除的脚本是唯一且不存在其他版本info表物理删除，反之逻辑删除
                updateInfoWithNoVersion(currentInfoVersion.getId());
                //删除发布的脚本(逻辑删除)
                InfoVersion infoVersion = new InfoVersion();
                infoVersion.setDeleted(1);
                infoVersion.setEditState(1);
                infoVersion.setSrcScriptUuid(auditRelation.getSrcScriptUuid());
                scripts.getInfoVersionMapper().updateInfoVersionBySrcUuid(infoVersion);
                logger.info("逻辑删除发布状态脚本成功");
                //判断删除的脚本的默认版本属性，进行重置
                updateDefaultFromDelete(auditRelation.getSrcScriptUuid());

                String[] uuidArray = new String[]{auditRelation.getSrcScriptUuid()};
                // 删除内容
                scripts.getInfoVersionTextMapper().deleteInfoVersionTextByScriptUuids(uuidArray);
                // 删除参数
                scripts.getParameterMapper().deleteParameterByScriptUuids(uuidArray);
                // 删除附件
                scripts.getAttachmentMapper().deleteAttachmentByScriptUuids(uuidArray);
            }
            //打回 - 版本表编辑状态置为发布
            InfoVersion infoVersion = new InfoVersion();
            if (auditRelation.getState() == AuditStateEnum.REJECTED.getType()) {
                infoVersion.setId(currentInfoVersion.getId());
                infoVersion.setEditState(EditStateEnum.PUBLISH.getType());
                scripts.getInfoVersionMapper().updateInfoVersion(infoVersion);
                logger.info("删除脚本被打回 id:{}",currentInfoVersion.getId());
            }
        } catch (Exception e) {
            logger.error("接收审核信息并更新脚本版本信息出错：", e);
            throw new SystemException("error.receive.audit.information");
        }
    }

    /**
     * 删除的版本如果是最后一个将info表ideleted置1
     *
     * @param versionId 版本id
     */
    public void updateInfoWithNoVersion(Long versionId) {
        //查询可用脚本数量
        int count = scripts.getInfoVersionMapper().countVersion(versionId);
        if (count <= 1) {
            InfoVersion infoVersion = scripts.getInfoVersionMapper().selectInfoVersionById(versionId);
            //先判断version表中是否存在版本数据，这种只更新info表逻辑删除字段，反之物理删除info表该数据
            int countVersionForPublish = scripts.getInfoVersionMapper().getCountVersionForPublish(infoVersion.getInfoUniqueUuid());
            Info info = scripts.getInfoMapper().selectInfoByUniqueUuid(infoVersion.getInfoUniqueUuid());
            Info info1 = new Info();
            info1.setId(info.getId());
            if(countVersionForPublish > 0){
                //存在版本数据，逻辑删除info表
                info1.setDeleted(1);
                info1.setEditState(1);
                scripts.getInfoMapper().updateInfo(info1);
            }else{
                //不存在版本，即为唯一的草稿会被物理删除，info表物理删除数据
                scripts.getInfoMapper().deleteInfoById(info.getId());
            }

        }
    }

    /**
     * 根据scriptInfoVersionId获取脚本发布的审核状态
     * @param scriptInfoVersionId 脚本的id
     * @return 是否存在已经发布信息标识
     */
    @Override
    public boolean getDoubleCheckScriptFlag(Long [] scriptInfoVersionId) {
        //根据脚本info_version_id查询脚本审核信息，如果存在已打回的发布脚本，返回true，否则返回false
        boolean doubleCheckFlag = false;
        if(scriptInfoVersionId == null || scriptInfoVersionId.length < 1){
            return doubleCheckFlag;
        }
        List<AuditRelation> auditRelations = scripts.getAuditRelationMapper().selectAuditRelationByScriptInfoVersionId(scriptInfoVersionId);
        for(AuditRelation auditRelation : auditRelations){
            if(Enums.AuditState.REJECTED.getValue().equals(auditRelation.getState())){
                return true;
            }
        }
        return doubleCheckFlag;
    }

    @Override
    public boolean getDelDoubleCheckScriptFlag(Long scriptRelationId) throws ScriptException{
        //根据脚本scriptRelationId查询脚本审核信息，如果存在已打回的发布脚本，返回true，否则返回false
        boolean doubleCheckFlag = false;
        if(scriptRelationId == null){
            return doubleCheckFlag;
        }
        AuditRelation auditRelation = scripts.getAuditRelationMapper().selectAuditRelationById(scriptRelationId);
        if(ObjectUtils.notEqual(auditRelation,null)){
            if(auditRelation.getAuditType().intValue() == 2 && auditRelation.getState().intValue() == 1){
                return true;
            }
        }
        return doubleCheckFlag;
    }

    /**
     * 发布脚本
     *
     * @param publishDto 发布信息Dto
     * @throws ScriptException exception
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void publishScript(PublishDto publishDto) throws ScriptException {
        //脚本校验发布状态，防止重复发布
        checkScriptForPublish(publishDto);

        List<InfoVersion> infoVersionList = new ArrayList<>();
        //工具箱
        if (publishDto.getScriptSource() != null && ScriptSource.TOOLBOX_SOURCE.getValue().equals(publishDto.getScriptSource())) {
            logger.info("toolbox start publish");
            InfoVersion infoVersion = scripts.getInfoVersionMapper().selectInfoVersionBysrcScriptUuid(publishDto.getSrcScriptUuid());
            infoVersionList.add(infoVersion);
        } else {
            //脚本
            logger.info("script start publish");
            infoVersionList = scripts.getInfoVersionMapper().selectInfoVersionByIds(publishDto.getIds());
        }
        if (infoVersionList != null && !infoVersionList.isEmpty()) {
            for (InfoVersion infoVersion : infoVersionList) {
                //双人复核表新增数据前先判断表中是否已经存在了本条脚本的发布审核数据，如果有，说明脚本之前发布过，且已被打回，此时不能再新增数据，需要对老数据进行修改
                Long auditRelationId = saveAuditRelation(infoVersion, publishDto);
                //更新infoVersion状态,同步更新info表状态
                updateScriptVersion(infoVersion, publishDto);
                //根据脚本版本信息获取脚本基础信息
                Info info = scripts.getInfoMapper().selectInfoByUniqueUuid(infoVersion.getInfoUniqueUuid());
                //双人复核对接所需信息Dto
                DoubleCheckInfoDto doubleCheckInfoDto = new DoubleCheckInfoDto();
                doubleCheckInfoDto.setAuditRelationId(auditRelationId);
                //设置双人复核对接所需信息Dto
                setDoubleCheckInfoDtoProperties(doubleCheckInfoDto, info, publishDto);
                //调用双人复核
                if (publishDto.getScriptSource() == null || !ScriptSource.TOOLBOX_SOURCE.getValue().equals(publishDto.getScriptSource())) {
                    Long workItemId = scriptSubmit(doubleCheckInfoDto,Enums.AuditType.SCRIPT_PUBLISH.getValue());
                    if(workItemId > 0){
                        //脚本发布直接审核通过
                        DoubleCheckApiDto doubleCheckApiDto = new DoubleCheckApiDto();
                        doubleCheckApiDto.setServiceId(doubleCheckInfoDto.getAuditRelationId());
                        doubleCheckApiDto.setApprovalState(1);
                        doubleCheckApiDto.setId(workItemId);
                        try {
                            this.doubleCheckScriptCallBack(doubleCheckApiDto,1);
                        } catch (SystemException e) {
                            throw new RuntimeException(e);
                        }
                    }
                }
            }
        }
    }

    /**
     * 脚本自动发布，跳过双人复核，目前仅为工具箱提供
     *
     * @param publishDto 发布信息Dto
     * @throws ScriptException exception
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void publishScriptAuto(PublishDto publishDto) throws ScriptException {
        if (publishDto.getScriptSource() == null || !ScriptSource.TOOLBOX_SOURCE.getValue().equals(publishDto.getScriptSource())) {
            return;
        }
        //脚本校验发布状态，防止重复发布
        checkScriptForPublish(publishDto);

        logger.info("toolbox start publish");

        // 查询草稿状态新版本脚本数据
        InfoVersion infoVersionnew = scripts.getInfoVersionMapper().selectInfoVersionBysrcScriptUuid(publishDto.getSrcScriptUuid());

        List<InfoVersion> infoVersionList = new ArrayList<>();

        // 工具箱传过来最新的草稿版本数据
        if (infoVersionnew.getVersion() == null || "".equals(infoVersionnew.getVersion())) {
            // 根据最新草稿版本的数据查询查询上一个已上线版本的数据
            InfoVersion infoVersionold = scripts.getInfoVersionMapper().selectMaxInfoVersionByInfoUniqueUuid(infoVersionnew.getInfoUniqueUuid());
            if(infoVersionold==null || infoVersionold.getVersion()== null || "".equals(infoVersionold.getVersion())){
                infoVersionList.add(infoVersionnew);
            }else{
                infoVersionList.add(infoVersionold);
                publishDto.setSrcScriptUuid(infoVersionold.getSrcScriptUuid());
            }
        }

        if (infoVersionList != null && !infoVersionList.isEmpty()) {
            for (InfoVersion infoVersionold : infoVersionList) {
                //更新infoVersion状态,同步更新info表状态
                updateScriptVersion(infoVersionold, publishDto);

                //脚本修改人改为申请人
                infoVersionnew.setUpdatorId(infoVersionold.getUpdatorId());
                infoVersionnew.setUpdatorName(infoVersionold.getUpdatorName());

                String lastVersion = infoVersionold.getVersion();
                String nextVersion = getNextVersion(lastVersion);
                infoVersionnew.setEditState(EditStateEnum.PUBLISH.getType());
                infoVersionnew.setVersion(nextVersion);
                infoVersionnew.setIsDefault(1);

                //重置默认版本
                InfoVersion infoVersionForUpdate = new InfoVersion();
                infoVersionForUpdate.setInfoUniqueUuid(infoVersionold.getInfoUniqueUuid());
                infoVersionForUpdate.setIsDefault(0);
                scripts.getInfoVersionMapper().updateInfoVersionDefaultValue(infoVersionForUpdate);

                //更新脚本执行次数统计表
                Exectime exectime = new Exectime();
                exectime.setSrcScriptUuid(infoVersionold.getSrcScriptUuid());
                scripts.getMyScriptMapper().insertExecTime(exectime);
                //禁用旧版本
                if ("1.0".equals(infoVersionold.getVersion())) {
                    List<String> versionUuids = scripts.getInfoVersionMapper().getAllVersionUuidByUuid(infoVersionold.getSrcScriptUuid());
                    scripts.getInfoVersionMapper().disableOldVersionByUuid(versionUuids, infoVersionold.getSrcScriptUuid());
                }
                scripts.getInfoVersionMapper().updateInfoVersionBySrcUuid(infoVersionnew);
                //更新Info表
                Info info1 = scripts.getInfoMapper().selectInfoByUniqueUuid(infoVersionold.getInfoUniqueUuid());
                //设置Info表
                Info infonew = new Info();
                //同步更新info表脚本的状态
                infonew.setEditState(EditStateEnum.PUBLISH.getType());
                infonew.setUpdatorId(info1.getUpdatorId());
                infonew.setUpdatorName(info1.getUpdatorName());
                infonew.setId(info1.getId());
                scripts.getInfoMapper().updateInfo(infonew);
            }
        }
    }

    /**
     * 校验脚本发布状态不能继续发布
     *
     * @param publishDto 发布信息
     */
    private void checkScriptForPublish(PublishDto publishDto) throws ScriptException {
        //获取脚本信息
        List<InfoVersion> infoVersionList = new ArrayList<>();
        if (publishDto.getScriptSource() != null && ScriptSource.TOOLBOX_SOURCE.getValue().equals(publishDto.getScriptSource())) {
            InfoVersion infoVersion = scripts.getInfoVersionMapper().selectInfoVersionBysrcScriptUuid(publishDto.getSrcScriptUuid());
            infoVersionList.add(infoVersion);
        } else {
            infoVersionList = scripts.getInfoVersionMapper().selectInfoVersionByIds(publishDto.getIds());
        }
        //已经发布的脚本不能继续发布
        if (infoVersionList != null && !infoVersionList.isEmpty()) {
            for (InfoVersion infoVersion : infoVersionList) {
                if (infoVersion.getVersion() != null) {
                    throw new ScriptException("script.published");
                }
            }
        }

    }

    /**
     * 调用双人复核
     *
     * @param doubleCheckInfoDto 双人复核对接所需信息Dto
     * @throws ScriptException exception
     */
    public Long scriptSubmit(DoubleCheckInfoDto doubleCheckInfoDto,Integer subType) throws ScriptException {
        Long workItemid = 0L;
        //开关控制，如果是发布直接通过审核，则生成workitemid，不走双人复核
        boolean getAllScriptFlag = scripts.getScriptBusinessConfig().isPublishScriptWithoutDoubleCheck();
        if(getAllScriptFlag && subType.equals(Enums.AuditType.SCRIPT_PUBLISH.getValue())){
            ResultApiDto resultApiDto = new ResultApiDto();
            //这里生成一个假的workitemid
            workItemid = SnowflakeIdWorker.generateId();
            resultApiDto.setTaskId(workItemid);
            updateAuditRelation(resultApiDto, doubleCheckInfoDto);
            return workItemid;
        }
        // 调用双人复核服务
        DoubleCheckApiDto doubleCheckDto = getDoubleCheckApiDto(doubleCheckInfoDto);

        logger.info("Sending data for double check: serviceId={}, taskSubject={}, originatorName={}, originatorId={}, " +
                        "auditorName={}, auditorId={}, callbackUrl={}, uuid={}",
                doubleCheckInfoDto.getAuditRelationId(), doubleCheckInfoDto.getTaskSubject(), doubleCheckInfoDto.getOriginatorName(), doubleCheckInfoDto.getOriginatorId(),
                doubleCheckInfoDto.getAuditUser(), doubleCheckInfoDto.getAuditUserId(), doubleCheckDto.getCallbackUrl(), doubleCheckInfoDto.getUuid());

        try {
            ResultApiDto resultApiDto = scripts.getRemoteCall().applyForDoubleCheck(doubleCheckInfoDto.getUuid(), doubleCheckDto);
            if (null != resultApiDto) {
                logger.info("Double check submission result: taskId={}, success={}, message={}",
                        resultApiDto.getTaskId(), resultApiDto.isSuccess(), resultApiDto.getMessage());
                if (!resultApiDto.isSuccess()) {
                    logger.error("Double check submission result is fail!");

                    throw new ScriptException(ExceptionMessage.DOUBLE_CHECK_SUBMISSION_ERROR.getValue());
                }
                //更新双人复核与脚本服务化关系表
                updateAuditRelation(resultApiDto, doubleCheckInfoDto);
            } else {
                logger.error("Double check submission result is null!");
                throw new ScriptException(ExceptionMessage.DOUBLE_CHECK_SUBMISSION_ERROR.getValue());
            }

        } catch (Exception e) {
            logger.error("Error occurred during double check submission!", e);
            throw new ScriptException(ExceptionMessage.DOUBLE_CHECK_SUBMISSION_ERROR.getValue());
        }
        return workItemid;
    }

    /**
     * 新增双人复核脚本服务化关系表数据
     *
     * @param infoVersion 脚本版本信息实体类
     * @param publishDto  发布信息dto
     * @return 双人复核脚本服务化关系表主键
     */
    private Long saveAuditRelation(InfoVersion infoVersion, PublishDto publishDto) {
        //根据脚本的uuid查询是否存在发布审批数据，如果存在，则更新数据并返回数据iid
        AuditRelation auditRelation = new AuditRelation();
        auditRelation.setSrcScriptUuid(infoVersion.getSrcScriptUuid());
        //1:脚本发布 2:脚本删除 3:脚本任务
        auditRelation.setAuditType(1);
        AuditRelation resAuditRelation = scripts.getAuditRelationService().selectAuditRelationForAudit(auditRelation);
        if(ObjectUtils.notEqual(resAuditRelation,null)){
            //1:审批中 2:已通过 3:已退回
            auditRelation.setState(1);
            scripts.getAuditRelationService().updateAuditRelationForAudit(auditRelation);
            return resAuditRelation.getId();
        }
        //设置双人复核实体信息
        AuditRelationDto auditRelationDto = new AuditRelationDto();
        auditRelationDto.setSrcScriptUuid(infoVersion.getSrcScriptUuid());
        auditRelationDto.setAuditType(AuditTypeEnum.PUBLISH.getType());
        auditRelationDto.setState(AuditStateEnum.PENDING.getType());
        auditRelationDto.setAuditUserId(publishDto.getAuditorId());
        auditRelationDto.setAuditUser(publishDto.getAuditorName());
        CurrentUser currentUser = CurrentUserUtil.getCurrentUser();
        auditRelationDto.setApplyUserId(currentUser.getId());
        auditRelationDto.setApplyUser(currentUser.getFullName());
        auditRelationDto.setBanOldVersion(publishDto.getIsForbidden());
        auditRelationDto.setPublicDesc(publishDto.getPublicDesc());
        if (publishDto.getIsForbidden() == 1) {
            auditRelationDto.setBanOldVersion(1);
        }
        if (publishDto.getScriptSource() != null && ScriptSource.TOOLBOX_SOURCE.getValue().equals(publishDto.getScriptSource())) {
            auditRelationDto.setApprWorkitemId(publishDto.getApprWorkitemId());
        }
        //插入双人复核表
        scripts.getAuditRelationService().insertAuditRelation(auditRelationDto);
        //返回双人复核表主键
        return auditRelationDto.getId();
    }

    /**
     * 更新双人复核脚本服务化关系表
     *
     * @param resultApiDto       回更结果信息
     * @param doubleCheckInfoDto 双人复核信息dto
     */
    private void updateAuditRelation(ResultApiDto resultApiDto, DoubleCheckInfoDto doubleCheckInfoDto) {
        AuditRelationDto auditRelationDto = new AuditRelationDto();
        auditRelationDto.setApprWorkitemId(resultApiDto.getTaskId());
        auditRelationDto.setId(doubleCheckInfoDto.getAuditRelationId());
        //更新双人复核脚本服务化关系表
        scripts.getAuditRelationService().updateAuditRelation(auditRelationDto);
    }

    /**
     * 接收审核信息并更新脚本版本信息
     *
     * @param auditResultBean 复核结果Bean对象
     */
    @Override
    public void updateScriptReview(AuditResultBean auditResultBean) throws SystemException {
        try {
            //更新双人复核表审批状态
            AuditRelationDto auditRelationDto = new AuditRelationDto();
            auditRelationDto.setId(auditResultBean.getAuditRelationId());
            auditRelationDto.setState(auditResultBean.getState());
            auditRelationDto.setApprWorkitemId(auditResultBean.getApprWorkitemId());
            scripts.getAuditRelationService().updateAuditRelationByWorkItemId(auditRelationDto);

            //获取双人复核脚本关系表信息
            AuditRelation auditRelation = scripts.getAuditRelationMapper().selectAuditRelationById(auditResultBean.getAuditRelationId());
            //设置脚本版本信息
            InfoVersion infoVersion = new InfoVersion();
            //脚本修改人改为申请人

            infoVersion.setUpdatorId(auditRelation.getApplyUserId());
            infoVersion.setUpdatorName(auditRelation.getApplyUser());
            infoVersion.setSrcScriptUuid(auditRelation.getSrcScriptUuid());
            Exectime exectime = new Exectime();
            //设置Info表
            Info info = new Info();


            //通过 - 版本表编辑状态置为发布
            // 每条数据都需要 对iversion 计算版本
            if (auditRelation.getState() == AuditStateEnum.APPROVED.getType()) {
                String lastVersion = scripts.getMyScriptMapper().getLastVersion(auditRelation.getSrcScriptUuid());
                String nextVersion = getNextVersion(lastVersion);
                infoVersion.setEditState(EditStateEnum.PUBLISH.getType());
                infoVersion.setVersion(nextVersion);
                infoVersion.setIsDefault(1);

                //重置默认版本
                InfoVersion infoVersion1 = scripts.getInfoVersionMapper().selectInfoVersionBysrcScriptUuid(auditRelation.getSrcScriptUuid());

                InfoVersion infoVersionForUpdate = new InfoVersion();
                infoVersionForUpdate.setInfoUniqueUuid(infoVersion1.getInfoUniqueUuid());
                infoVersionForUpdate.setIsDefault(0);
                scripts.getInfoVersionMapper().updateInfoVersionDefaultValue(infoVersionForUpdate);


                //同步更新info表脚本的状态
                info.setEditState(EditStateEnum.PUBLISH.getType());
                //更新脚本执行次数统计表
                exectime.setSrcScriptUuid(auditRelation.getSrcScriptUuid());
                //根据srcScriptUuid查询执行统计表，如果数据已经存在，则不再插入数据
                if(scripts.getMyScriptMapper().getExecTimeCountBySrcScriptUuid(auditRelation.getSrcScriptUuid()) < 1){
                    scripts.getMyScriptMapper().insertExecTime(exectime);
                }
                //禁用旧版本
                if (null != auditRelation.getBanOldVersion() && auditRelation.getBanOldVersion() == 1) {
                    List<String> versionUuids = scripts.getInfoVersionMapper().getAllVersionUuidByUuid(auditRelation.getSrcScriptUuid());
                    scripts.getInfoVersionMapper().disableOldVersionByUuid(versionUuids, auditRelation.getSrcScriptUuid());
                }
            }
            //打回 - 版本表编辑状态置为草稿
            if (auditRelation.getState() == AuditStateEnum.REJECTED.getType()) {
                infoVersion.setEditState(EditStateEnum.ENABLE.getType());
                //同步更新info表脚本的状态
                info.setEditState(EditStateEnum.ENABLE.getType());
            }
            scripts.getInfoVersionMapper().updateInfoVersionBySrcUuid(infoVersion);
            //更新Info表
            InfoVersion version = scripts.getInfoVersionMapper().selectInfoVersionBysrcScriptUuid(auditRelation.getSrcScriptUuid());
            Info info1 = scripts.getInfoMapper().selectInfoByUniqueUuid(version.getInfoUniqueUuid());
            info.setUpdatorId(auditRelation.getApplyUserId());
            info.setUpdatorName(auditRelation.getAuditUser());
            info.setId(info1.getId());
            scripts.getInfoMapper().updateInfo(info);
        } catch (Exception e) {
            logger.error("接收审核信息并更新脚本版本信息出错：", e);
            throw new SystemException("error.receive.audit.information");
        }

    }

    /**
     * 设置双人复核所需信息
     *
     * @param doubleCheckInfoDto 双人复核基本信息Dto
     * @return 双人复核任务信息实体
     */
    private static DoubleCheckApiDto getDoubleCheckApiDto(DoubleCheckInfoDto doubleCheckInfoDto) {
        DoubleCheckApiDto doubleCheckDto = new DoubleCheckApiDto();

        doubleCheckDto.setServiceId(doubleCheckInfoDto.getAuditRelationId());
        doubleCheckDto.setTaskSubject(doubleCheckInfoDto.getTaskSubject());
        doubleCheckDto.setDetailUrl(doubleCheckInfoDto.getDetailUrl());

        doubleCheckDto.setOriginatorName(doubleCheckInfoDto.getOriginatorName());
        doubleCheckDto.setOriginatorId(doubleCheckInfoDto.getOriginatorId());

        List<AuditorApiDto> auditorApiDtoList = new ArrayList<>();
        AuditorApiDto auditorApiDto = new AuditorApiDto();
        auditorApiDto.setAuditorName(doubleCheckInfoDto.getAuditUser());
        auditorApiDto.setAuditorId(doubleCheckInfoDto.getAuditUserId());
        auditorApiDtoList.add(auditorApiDto);

        ApprovalNode node = new ApprovalNode();
        List<ApprovalNode> nodes = new ArrayList<>();
        node.setNodeIndex(1);
        node.setIsAutoAssign(false);
        node.setAuditorApiDtoList(auditorApiDtoList);
        nodes.add(node);
        doubleCheckDto.setApprovalNodes(nodes);

        doubleCheckDto.setItemType(doubleCheckInfoDto.getItemType());
        doubleCheckDto.setCallbackUrl(doubleCheckInfoDto.getCallbackUrl());
        return doubleCheckDto;
    }

    /**
     * 查询脚本的全部版本的脚本信息
     *
     * @param serviceUuid 脚本的uuid
     * @return 列表
     */
    @Override
    public List<ScriptVersionInfoDto> getScriptServiceVersionListForAllScript(String serviceUuid) {
        List<ScriptVersionInfoBean> allInfolist = scripts.getMyScriptMapper().getScriptServiceVersionListForAllScript(serviceUuid);
        //添加最新版本的属性
        updateIsNewVersion(allInfolist);
        return BeanUtils.copy(allInfolist, ScriptVersionInfoDto.class);
    }

    /**
     * 最新版本无版本回退
     *
     * @param iid     回退到的版本的主键
     * @param oldId   回退的版本的主键
     * @param oldUuid 回退的版本的uuid
     * @param uuid    回退到的版本的uuid
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String,Object> noVersionRollBack(Long iid, Long oldId, String oldUuid, String uuid) {
        Map<String,Object> res = new HashMap<>();
        res.put("success",false);
        res.put("message","hasVersionRollBack error");
        //获取脚本基本信息，如果是禁用状态，则不允许回退
        InfoVersion oldInfoVersion = scripts.getInfoVersionMapper().selectInfoVersionById(iid);
        if(Objects.nonNull(oldInfoVersion)
                && Objects.nonNull(oldInfoVersion.getUseState())
                && 0 == oldInfoVersion.getUseState()){
            res.put("message","this version is disabled now");
            return res;
        }
        //获取当前的脚本的内容
        List<InfoVersionText> infoVersionTexts = scripts.getMyScriptMapper().getNewContentInfo(iid);
        String content = "";
        if (!infoVersionTexts.isEmpty()) {
            InfoVersionText infoVersionText = infoVersionTexts.get(0);
            content = infoVersionText.getContent();
        }
        //更新脚本内容
        scripts.getMyScriptMapper().updateContentInfo(content, oldId);
        //删除该版本的参数
        scripts.getParameterMapper().deleteParameterByUuid(oldUuid);
        //获取回退到的版本的参数
        List<Parameter> parameters = scripts.getParameterMapper().getParameterByUuid(uuid);
        //保存参数
        if (!parameters.isEmpty()) {
            for (Parameter parameter : parameters) {
                parameter.setSrcScriptUuid(oldUuid);
                scripts.getParameterMapper().insertParameter(parameter);
            }
        }
        //删除该版本的附件
        scripts.getAttachmentMapper().deleteAttachmentByUuid(oldUuid);
        //获取回退到的版本的附件
        List<Attachment> attachments = scripts.getAttachmentMapper().getAttachmentByUuid(uuid);
        //保存附件
        if (!attachments.isEmpty()) {
            for (Attachment attachment : attachments) {
                attachment.setSrcScriptUuid(oldUuid);
                scripts.getAttachmentMapper().insertAttachment(attachment);
            }
        }
        //获取回退到的版本的函数变量
        List<BindFuncVar> bindFuncVars = scripts.getBindFuncVarMapper().getBindFuncVarByUuid(uuid);
        //删除该版本的函数变量
        scripts.getBindFuncVarMapper().deleteBindFuncVarByUuid(oldUuid);
        //保存函数变量
        for (BindFuncVar bindFuncVar : bindFuncVars) {
            bindFuncVar.setSrcScriptUuid(oldUuid);
            scripts.getBindFuncVarMapper().insertBindFuncVar(bindFuncVar);
        }

        //回退完草稿脚本的信息之后将默认版本迁移到对应的版本上
        //获取info表uuid
        InfoVersion infoVersion = scripts.getInfoVersionMapper().selectInfoVersionBysrcScriptUuid(uuid);
        String uniqueUuid = infoVersion.getInfoUniqueUuid();
        //查找默认版本
        InfoVersion infoVersionForUpdate = new InfoVersion();
        infoVersionForUpdate.setInfoUniqueUuid(uniqueUuid);
        infoVersionForUpdate.setIsDefault(0);
        scripts.getInfoVersionMapper().updateInfoVersionDefaultValue(infoVersionForUpdate);
        //更新回退的目标脚本
        infoVersion.setIsDefault(1);
        scripts.getInfoVersionMapper().updateInfoVersion(infoVersion);
        //回退成功
        res.put("success",true);
        res.put("message","rollback success");
        return res;
    }

    /**
     * 最新脚本有版本回退
     *
     * @param iid   回退到的版本的主键
     * @param oldId 回退的版本的主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String,Object> hasVersionRollBack(Long iid, Long oldId){
        Map<String,Object> res = new HashMap<>();
        res.put("success",false);
        res.put("message","hasVersionRollBack error");
        //获取脚本基本信息，如果是禁用状态，则不允许回退
        InfoVersion oldInfoVersion = scripts.getInfoVersionMapper().selectInfoVersionById(iid);
        if(Objects.nonNull(oldInfoVersion)
                && Objects.nonNull(oldInfoVersion.getUseState())
                && 0 == oldInfoVersion.getUseState()){
            logger.error("this version is disabled now");
            res.put("message","this version is disabled now");
            return res;
        }
        //回退版本
        InfoVersion infoVersion = new InfoVersion();
        //将当前最新版本的默认脚本置为0
        infoVersion.setIsDefault(0);
        infoVersion.setId(oldId);
        scripts.getInfoVersionMapper().updateInfoVersion(infoVersion);
        infoVersion.setIsDefault(1);
        infoVersion.setId(iid);
        int isRollBack = scripts.getInfoVersionMapper().updateInfoVersion(infoVersion);
        if (isRollBack > 0) {
            logger.info("hasVersionRollBack success");
            MyScriptBean myScriptBean = new MyScriptBean();
            myScriptBean.setScriptInfoVersionId(iid);
            ///切换默认版本成功，发送MQ
            TransactionSyncUtil.execute(this::noticeScriptChangeDefaultVersion, null, null, myScriptBean);
            res.put("success",true);
            res.put("message","rollback success");
        }
        return res;
    }

    /**
     * 更新最新版本属性
     *
     * @param allInfoList 全部版本脚本信息列表
     */
    private void updateIsNewVersion(List<ScriptVersionInfoBean> allInfoList) {
        // 查找是否存在 version 为 null 的记录
        boolean hasNullVersion = allInfoList.stream().anyMatch(info -> info.getVersion() == null);

        if (hasNullVersion) {
            // 存在 version 为 null 的记录，将该记录的 isNewVersion 置为 1，其他记录置为 0
            allInfoList.forEach(info -> info.setIsNewVersion(info.getVersion() == null ? 1 : 0));
        } else {
            // 不存在 version 为 null 的情况，找出最大 version 对应的数据
            Optional<ScriptVersionInfoBean> maxVersionInfo = allInfoList.stream()
                    .max(Comparator.comparing(ScriptVersionInfoBean::getVersion));

            if (maxVersionInfo.isPresent()) {
                // 获取最大 version 对应的数据
                ScriptVersionInfoBean maxVersion = maxVersionInfo.get();

                // 将最大 version 对应的数据的 isNewVersion 置为 1，其他数据置为 0
                allInfoList.forEach(info -> info.setIsNewVersion(info == maxVersion ? 1 : 0));
            }
        }
    }

    /**
     * 计算下一个版本
     *
     * @param version 版本
     * @return 版本
     */
    private String getNextVersion(String version) {
        if (version == null) {
            return "1.0";
        }
        String[] split = version.split("\\.");
        int main = Integer.parseInt(split[0]);
        int min = Integer.parseInt(split[1]);
        int num = 9;
        if (min == num) {
            return (main + 1) + ".0";
        } else {
            return main + "." + (min + 1);
        }
    }

    /**
     * 下载脚本
     *
     * @param ids      脚本id
     * @param response 响应
     * @return 结果
     */
    @Override
    public boolean downloadScript(Long[] ids, HttpServletResponse response) {
        try {
            List<File> files = generateScriptFiles(ids);
            // 这个list中只有一个文件
            File fileForZip = files.get(0);
            String zipName = "脚本下载_" + format(new Date(), "yyyyMMddHHmmssms") + ".zip";
            File zipFile = FileUtils.createZipFile(fileForZip);
            //下载压缩文件并删除临时文件
            FileUtils.downloadAndCleanupFiles(zipFile, files, response, zipName);

        } catch (Exception e) {
            logger.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : ", e);
        }
        //删除临时文件
        FileUtils.deleteTemporaryFile();
        return true;
    }

    /**
     * 生成下载脚本的文件方法
     *
     * @param ids 脚本的id
     * @return 文件列表
     */
    public List<File> generateScriptFiles(Long[] ids) throws ScriptException {
        //获取要下载的列表
        List<DownloadScriptBean> downloadList = downloadScriptInfo(ids);
        //生成多个文件
        List<File> files = new ArrayList<>();
        for (DownloadScriptBean downloadScriptBean : downloadList) {
            String scriptName = downloadScriptBean.getScriptName();
            String scriptType = downloadScriptBean.getScriptType();
            String scriptContent = downloadScriptBean.getContent();
            List<Attachment> attachment = downloadScriptBean.getAttachments();
            String fileName = scriptName + "." + scriptType;

            String currentDir = System.getProperty("user.dir");

            String targetPath = currentDir + "/work/";

            String path = targetPath + "temp_" + format(new Date(), "yyyyMMddHHmmssms");
            File scriptFileForDownload = createFile(fileName, scriptContent, attachment, path, false);
            files.add(scriptFileForDownload);
        }
        return files;
    }

    /**
     * 获取下载列表基础信息和附件信息
     *
     * @param iid 版本id
     * @return 列表
     */
    @Override
    public List<DownloadScriptBean> downloadScriptInfo(Long[] iid) {
        //获取脚本信息
        List<DownloadScriptBean> downloadScriptBeanList = scripts.getMyScriptMapper().getScriptListForDownload(iid);
        for (DownloadScriptBean downloadScriptBean : downloadScriptBeanList) {
            //获取附件
            List<Attachment> attachmentList = scripts.getAttachmentMapper().getAttachmentListForDownload(downloadScriptBean.getSrcScriptUuid());
            downloadScriptBean.setAttachments(attachmentList);
        }
        return downloadScriptBeanList;
    }

    /**
     * 创建文件
     *
     * @param fileName       文件名
     * @param scriptContent  脚本内容
     * @param attachmentList 附件列表
     * @param path           路径
     * @return 文件
     */
    @Override
    public synchronized File createFile(String fileName, String scriptContent, List<Attachment> attachmentList, String path, boolean createScriptFiles) throws ScriptException {
        // 创建一个文件夹用来放置脚本文件及附件
        // 验证路径有效性
        if(!FilePathValidator.apply(path)){
            throw new ScriptException("Invalid path for zip file:" + path);
        }
        File file = new File(path);
        if (!file.exists()) {
            if (file.mkdirs()) {
                logger.info("文件创建成功");
            } else {
                logger.info("文件已存在，无需重复创建");
            }
        }

        // 脚本文件
        String scriptFilePath = path + File.separator + fileName;
        // 验证路径有效性
        zipFileValidOrThrowException(scriptFilePath);
        File scriptFile = new File(scriptFilePath);
        try (BufferedWriter fileWriter = new BufferedWriter(new OutputStreamWriter(Files.newOutputStream(scriptFile.toPath()), StandardCharsets.UTF_8))) {
            fileWriter.write(scriptContent);
            fileWriter.flush();
        } catch (IOException e) {
            logger.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : ", e);
        }
        //校验文件夹下是否导出脚本原文件
        if(!createScriptFiles){
            // 附件
            String attachmentDirPath = path + File.separator + fileName + "_attachment";
            // 验证路径有效性
            zipFileValidOrThrowException(attachmentDirPath);
            File attachmentDir = new File(attachmentDirPath);
            if (attachmentDir.mkdirs()) {
                logger.info("附件创建成功");
            } else {
                logger.info("附件创建失败");
            }
            for (Attachment attachment : attachmentList) {
                String attachmentName = attachment.getName();
                byte[] attachmentContent = attachment.getContents();
                String attachmentFilePath = path + File.separator + fileName + "_attachment/" + attachmentName;
                // 验证路径有效性
                zipFileValidOrThrowException(attachmentFilePath);
                File attachmentFile = new File(attachmentFilePath);
                try (FileOutputStream outputStream = new FileOutputStream(attachmentFile)) {
                    // 使用UTF-8编码写入文件内容
                    outputStream.write(new String(attachmentContent, StandardCharsets.UTF_8).getBytes(StandardCharsets.UTF_8));
                } catch (IOException e) {
                    logger.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " : ", e);
                }
            }
        }
        return file;
    }

    /**
     * 验证 zip文件路径，不符合校验抛出异常
     * @param filePath 文件路径
     */
    private void zipFileValidOrThrowException(String filePath) throws ScriptException {
        if(!FilePathValidator.apply(filePath)){
            throw new ScriptException("Invalid path for zip file: " + filePath);
        }
    }

    /**
     * 设置Info信息
     *
     * @param info          脚本基础信息
     */
    private ScriptInfoDto setScriptInfoValues( Info info) {
        //返回逗号分隔的字符串,拆分成列表
        ScriptInfoDto scriptInfoDto = BeanUtils.copy(info, ScriptInfoDto.class);
        String platformString = info.getPlatform();
        if(platformString != null) {
            List<String> platformList = Arrays.asList(platformString.split(","));
            scriptInfoDto.setPlatforms(platformList);
        }
        if(info.getCategoryId() != null) {
            Category finalcategory = scripts.getCategoryService().getCategory(info.getCategoryId());
            scriptInfoDto.setCategoryDto(BeanUtils.copy(finalcategory, CategoryDto.class));
            //获取分类全路径
            String categoryName = scripts.getCategoryService().getCategoryFullPath(info.getCategoryId());
            scriptInfoDto.setCategoryPath(categoryName);
        }
        return scriptInfoDto;
    }

    /**
     * 设置InfoVersion信息
     *
     * @param scriptInfoDto 脚本信息
     * @param info          脚本基础信息
     * @param infoVersion   脚本版本信息
     */
    private ScriptVersionDto setScriptInfoVersionValues(ScriptInfoDto scriptInfoDto, Info info, InfoVersion infoVersion) {
        ScriptVersionDto scriptVersionDto = BeanUtils.copy(infoVersion, ScriptVersionDto.class);
        scriptInfoDto.setHisVersionCount(scripts.getInfoVersionMapper().countPublishVersion(info.getUniqueUuid()));
        scriptVersionDto.setExpectLastline(infoVersion.getExpectLastline());
        scriptVersionDto.setExpectType(infoVersion.getExpectType());

        return scriptVersionDto;
    }

    /**
     * 设置InfoVersionText信息
     *
     * @param scriptContentDto 脚本内容
     * @param infoVersionText  脚本版本内容信息
     */
    private void setScriptInfoVersionTextValues(ScriptContentDto scriptContentDto, InfoVersionText infoVersionText) {
        scriptContentDto.setContent(infoVersionText.getContent());
    }

    /**
     * 保存脚本基本信息
     *
     * @param scriptInfoDto      脚本信息
     * @param isNewVersionScript 产生新脚本标识
     * @throws ScriptException exception
     */
    public void createInfo(ScriptInfoDto scriptInfoDto, boolean isNewVersionScript) throws ScriptException {
        List<String> platformList = scriptInfoDto.getPlatforms();
        String platformsString = generatePlatformString(platformList);
        //为info设置值
        Info info = BeanUtils.copy(scriptInfoDto, Info.class);
        //校验分类在库中是否存在
        isExistCategory(info);
        //构造分类路径属性值
        buildCategoryPath(info);
        info.setPlatform(platformsString);
        setInfoProperties(scriptInfoDto, info);
        // 存在历史发布版本无需执行基本信息存储
        if (isNewVersionScript) {
            //保存数据
            CurrentUser currentUser = CurrentUserUtil.getCurrentUser();
            info.setOrgCode(currentUser.getOrgCode());
            info.setEditState(0);
            scripts.getInfoMapper().insertInfo(info);
            //将基础表id回更
            scriptInfoDto.setId(info.getId());
        } else {
            //版本表新增数据基础表对应更新脚本状态为草稿
            info.setEditState(0);
            scripts.getInfoMapper().updateInfo(info);
        }
    }

    //校验是否存在该分类
    public void isExistCategory(Info info) throws ScriptException {
        Category category = new Category();
        category.setId(info.getCategoryId());
        List<Category> categoryList = scripts.getCategoryMapper().selectCategoryList(category);
        if(categoryList.isEmpty()){
            throw new ScriptException("category.not.exist");
        }
    }
    /**
     * 构造分类全路径
     * @param info  基本信息
     */
    public void buildCategoryPath(Info info){
        String categoryPath = scripts.getCategoryService().buildCategoryFullPath(info.getCategoryId());
        info.setCategoryPath(categoryPath);
    }
    /**
     * 保存版本信息
     *
     * @param scriptInfoDto 脚本信息
     */
    public void createInfoVersion(ScriptInfoDto scriptInfoDto) {

        InfoVersion infoVersion = BeanUtils.copy(scriptInfoDto.getScriptVersionDto(), InfoVersion.class);
        //InfoVersion中设置值
        setInfoVersionProperties(scriptInfoDto, infoVersion);
        //新增的脚本和产生新版本都不置为默认版本
        infoVersion.setIsDefault(0);
        scripts.getInfoVersionMapper().insertInfoVersion(infoVersion);
        ScriptVersionDto infoVersionDto = scriptInfoDto.getScriptVersionDto();
        //回更id
        infoVersionDto.setId(infoVersion.getId());
    }


    /**
     * 更新Info表信息
     *
     * @param scriptInfoDto 脚本信息
     * @return 结果
     */
    public boolean updateInfo(ScriptInfoDto scriptInfoDto) throws ScriptException {
        List<String> platformList = scriptInfoDto.getPlatforms();
        String platformsString = generatePlatformString(platformList);

        Info info = BeanUtils.copy(scriptInfoDto, Info.class);
        info.setPlatform(platformsString);
        //处理info数据
        handleInfoData(info);

        int count = scripts.getInfoMapper().getSameScriptNameZhCount(info.getScriptNameZh());
        if (count > 0) {
            //编辑脚本判定重名
            Info currentInfo = scripts.getInfoMapper().selectInfoByScriptNameZh(scriptInfoDto.getScriptNameZh());
            //脚本服务化
            if (scriptInfoDto.getId() != null && !currentInfo.getId().equals(scriptInfoDto.getId())) {
                throw new ScriptException(SCRIPT_NAME_ZH_EXISTS);
            }
            if (scriptInfoDto.getId() == null && scriptInfoDto.getScriptVersionDto().getSrcScriptUuid() != null) {
                //查询版本信息
                String versionUuid = scriptInfoDto.getScriptVersionDto().getSrcScriptUuid();
                InfoVersion infoVersion = scripts.getInfoVersionMapper().selectInfoVersionBysrcScriptUuid(versionUuid);
                //查询基本信息
                Info info1 = scripts.getInfoMapper().selectInfoByUniqueUuid(infoVersion.getInfoUniqueUuid());
                if (!info1.getId().equals(currentInfo.getId())) {
                    throw new ScriptException(SCRIPT_NAME_ZH_EXISTS);
                }
            }
        }
        scripts.getInfoMapper().updateInfo(info);
        return true;
    }

    private void handleInfoData(Info info) throws ScriptException {
        //校验执行人如果是只由空格组成，处理
        if (info.getExecuser() != null && info.getExecuser().trim().isEmpty()) {
            info.setExecuser("");
        }
        isExistCategory(info);
        //构造分类路径属性值
        buildCategoryPath(info);
    }

    /**
     * 更新InfoVersion表信息
     *
     * @param scriptInfoDto 脚本信息
     * @return 结果
     */
    public boolean updateInfoVersion(ScriptInfoDto scriptInfoDto) {
        InfoVersion infoVersion = BeanUtils.copy(scriptInfoDto.getScriptVersionDto(), InfoVersion.class);
        scripts.getInfoVersionMapper().updateInfoVersion(infoVersion);
        return true;
    }

    /**
     * 封装Info信息
     *
     * @param scriptInfoDto 脚本信息
     * @param info          脚本基础信息
     */
    private void setInfoProperties(ScriptInfoDto scriptInfoDto, Info info) {
        info.setScriptType(scriptInfoDto.getScriptType());
        info.setDeleted(0);
    }

    /**
     * 封装新增InfoVersion信息
     *
     * @param scriptInfoDto 脚本信息
     * @param infoVersion   脚本版本信息
     */
    private void setInfoVersionProperties(ScriptInfoDto scriptInfoDto, InfoVersion infoVersion) {
        infoVersion.setInfoUniqueUuid(scriptInfoDto.getUniqueUuid());
        infoVersion.setSrcScriptUuid(scriptInfoDto.getScriptVersionDto().getSrcScriptUuid());
        infoVersion.setEditState(EditStateEnum.ENABLE.getType());
        infoVersion.setUseState(UseStateEnum.ENABLE.ordinal());
        infoVersion.setLevel(scriptInfoDto.getScriptVersionDto().getLevel());
        infoVersion.setVersion(null);
        infoVersion.setId(null);
        infoVersion.setDeleted(0);

        infoVersion.setParamflag(scriptInfoDto.getScriptVersionDto().getParameterValidationDtoList().isEmpty() ? 0 : 1);
    }

    /**
     * 设置双人复核对接所需信息Dto
     *
     * @param doubleCheckInfoDto 双人复核对接所需信息Dto
     * @param info               脚本基本信息
     */
    public void setDoubleCheckInfoDtoProperties(DoubleCheckInfoDto doubleCheckInfoDto, Info info, PublishDto publishDto) {
        setDoubleCheckInfoDtoCommonProperties(doubleCheckInfoDto, publishDto.getAuditorName(), publishDto.getAuditorId());
        doubleCheckInfoDto.setTaskSubject(info.getScriptName() + "脚本发布发起双人复核操作");
    }

    /**
     * 设置双人复核对接所需信息Dto
     *
     * @param doubleCheckInfoDto 双人复核对接所需信息Dto
     * @param info               脚本基本信息
     */
    public void setDoubleCheckInfoDtoProperties(DoubleCheckInfoDto doubleCheckInfoDto, Info info, ScriptDeleteDto scriptDeleteDto) {
        setDoubleCheckInfoDtoCommonProperties(doubleCheckInfoDto, scriptDeleteDto.getAuditorName(), scriptDeleteDto.getAuditorId());
        doubleCheckInfoDto.setTaskSubject(info.getScriptName() + "脚本删除发起双人复核操作");
    }

    /**
     * 发布和删除公共代码提取
     *
     * @param doubleCheckInfoDto 双人复核对接所需信息Dto
     * @param auditorName        审核人
     * @param auditorId          审核人id
     */
    private void setDoubleCheckInfoDtoCommonProperties(DoubleCheckInfoDto doubleCheckInfoDto, String auditorName, Long auditorId) {
        //审核用户为空，需要查询用户名
        if(StringUtils.isBlank(auditorName)){
            List<Long> userIdList = new ArrayList<>();
            userIdList.add(auditorId);
            List<UserInfoApiDto> userInfoList = scripts.getiUserInfo().getUserInfoList(userIdList);
            if(!userInfoList.isEmpty()){
                auditorName = userInfoList.get(0).getFullName();
            }
        }
        doubleCheckInfoDto.setAuditUser(auditorName);
        doubleCheckInfoDto.setAuditUserId(auditorId);
        CurrentUser currentUser = CurrentUserUtil.getCurrentUser();
        doubleCheckInfoDto.setOriginatorName(currentUser.getFullName());
        doubleCheckInfoDto.setOriginatorId(currentUser.getId());
        doubleCheckInfoDto.setItemType("script");
        doubleCheckInfoDto.setCallbackUrl("script");
        doubleCheckInfoDto.setUuid("script_" + doubleCheckInfoDto.getAuditRelationId());
        doubleCheckInfoDto.setDetailUrl("publish");
    }

    /**
     * 校验关键命令
     *
     * @param scriptInfoDto 脚本内容Dto
     * @return 列表
     */
    public List<ScriptValidationResultDto> validateScriptWithKeywords(ScriptInfoDto scriptInfoDto) {
        // 初始化结果列表
        List<ScriptValidationResultDto> scriptValidationResultDtos = new ArrayList<>();

        // 将脚本内容拆分为行，并转换为行号与内容的映射关系
        String[] allLine = scriptInfoDto.getScriptVersionDto().getScriptContentDto().getContent().split("\\n");
        Map<Integer, String> lineMap = convertToLineMap(allLine);
        //校验绑定到特定分类下的危险命令
        List<DangerCmd> dangerCmds1 = scripts.getDangerCmdMapper().selectDangerCmdByCategoryIdList(scriptInfoDto.getCategoryId());
        // 获取脚本类型对应的危险命令列表
        List<DangerCmd> dangerCmds = fetchDangerCmds(scriptInfoDto.getScriptType(),scriptInfoDto.getScriptLabel());
        List<DangerCmd> intersection;
        //如果没有绑定过分类，说明此命令只校验类型与标签，分类绑定的都要去掉
        if(dangerCmds1.isEmpty() && !dangerCmds.isEmpty()){
            intersection = dangerCmds.stream()
                    .filter(dangerCmd -> (dangerCmd.getCategoryId() == null ))
                    .collect(Collectors.toList());
        }else if(!dangerCmds1.isEmpty() && dangerCmds.isEmpty()){
            intersection = dangerCmds1;
        }else if(dangerCmds1.isEmpty() && dangerCmds.isEmpty()){
            intersection = new ArrayList<>();
        }else{
            // 取交集
            intersection = dangerCmds1.stream()
                    .filter(cmd1 -> dangerCmds.stream()
                            .anyMatch(cmd -> cmd.getId().equals(cmd1.getId())))
                    .collect(Collectors.toList());
        }
        // 处理每一行脚本内容，检查是否包含危险命令
        processLineMap(lineMap, intersection, scriptValidationResultDtos);

        return scriptValidationResultDtos;
    }

    /**
     * 将脚本内容转换为行号与内容的映射关系
     *
     * @param allLine 拆分的 行
     * @return Map
     */
    private Map<Integer, String> convertToLineMap(String[] allLine) {
        Map<Integer, String> lineMap = new HashMap<>(20);
        if (null != allLine && allLine.length > 0) {
            for (int i = 0; i < allLine.length; i++) {
                lineMap.put(i + 1, allLine[i]);
            }
        }
        return lineMap;
    }

    /**
     * 查询数据库获取脚本类型对应的危险命令列表
     *
     * @param scriptType 脚本类型
     * @return 列表
     */
    private List<DangerCmd> fetchDangerCmds(String scriptType,String label) {
        DangerCmd dangerCmd = new DangerCmd();
        dangerCmd.setScriptType(scriptType);
        //如果有标签绑定增加筛选条件
        List<String> labelList = label == null ? Collections.emptyList() : Arrays.asList(label.split(","));

        List<DangerCmd> resDangerCmdList = new ArrayList<>();
        List<DangerCmd> dangerCmdList = scripts.getDangerCmdMapper().selectDangerCmdList(dangerCmd);
        for (DangerCmd cmd : dangerCmdList) {
            //取出标签
            String scriptLabel = cmd.getScriptLabel();
            if(labelList.isEmpty()){
                if(scriptLabel == null || scriptLabel.isEmpty()){
                    resDangerCmdList.add(cmd);
                }
            }else{
                // scriptLabel 不为空时，拆分并检查是否匹配目标标签
                if (scriptLabel != null && !scriptLabel.isEmpty()) {
                    String[] labels = scriptLabel.split(",");
                    boolean matched = false;

                    for (String label1 : labels) {
                        if (labelList.contains(label1)) {
                            matched = true;
                            // 一旦找到匹配的标签，立即停止检查
                            break;
                        }
                    }

                    // 如果找到匹配的标签，则将该 cmd 添加到结果列表
                    if (matched) {
                        resDangerCmdList.add(cmd);
                    }
                } else {
                    // 如果 scriptLabel 为空，将其加入结果
                    resDangerCmdList.add(cmd);
                }
            }

        }


        return resDangerCmdList;
    }

    /**
     * 处理每一行脚本内容，检查是否包含危险命令
     *
     * @param lineMap                    每行内容
     * @param dangerCmds                 关键命令
     * @param scriptValidationResultDtos 关键命令校验脚本结果
     */
    private void processLineMap(Map<Integer, String> lineMap, List<DangerCmd> dangerCmds, List<ScriptValidationResultDto> scriptValidationResultDtos) {
        if (null != dangerCmds && !dangerCmds.isEmpty()) {
            for (Map.Entry<Integer, String> entry : lineMap.entrySet()) {
                int key = entry.getKey();
                String value = entry.getValue() == null ? "" : entry.getValue();
                List<String> matchCommandWarnList = new ArrayList<>();
                List<String> matchCommandShieldList = new ArrayList<>();
                // 检查当前行是否包含危险命令
                processDangerCmds(value, dangerCmds, matchCommandWarnList, matchCommandShieldList);
                // 将检查结果添加到结果列表中
                addScriptValidationResult(scriptValidationResultDtos, key, matchCommandWarnList, 0, value);
                addScriptValidationResult(scriptValidationResultDtos, key, matchCommandShieldList, 1, value);
            }
        }
    }

    /**
     * 检查当前行是否包含危险命令
     *
     * @param value                  当前行的数据
     * @param dangerCmds             关键命令集合
     * @param matchCommandWarnList   提醒命令列表
     * @param matchCommandShieldList 屏蔽命令列表
     */
    private void processDangerCmds(String value, List<DangerCmd> dangerCmds, List<String> matchCommandWarnList, List<String> matchCommandShieldList) {
        for (DangerCmd currentDangerCmd : dangerCmds) {
            Pattern pattern = Pattern.compile(currentDangerCmd.getScriptCmd(), Pattern.DOTALL);
            Matcher matcher = pattern.matcher(value);
            while (matcher.find()) {
                String matchCommand = matcher.group();

                // 根据危险命令级别分类存储匹配结果
                if (currentDangerCmd.getScriptCmdLevel() == 0) {
                    // 匹配到的结果与已知的比较，一条关键命令内容包含另一条的内容，取大者
                    // 检查 matchCommand 是否包含列表中的某个值，如果包含，将那个值从列表中移除
                    matchCommandWarnList.removeIf(matchCommand::contains);
                    matchCommandWarnList.add(matchCommand);
                } else {
                    // 匹配到的结果与已知的比较，一条关键命令内容包含另一条的内容，取大者
                    // 检查 matchCommand 是否包含列表中的某个值，如果包含，将那个值从列表中移除
                    matchCommandShieldList.removeIf(matchCommand::contains);
                    matchCommandShieldList.add(matchCommand);
                }
            }
        }
    }

    /**
     * 将关键命令匹配的信息封装
     *
     * @param scriptValidationResultDtos 关键命令验证脚本内容信息存储
     * @param key                        行索引
     * @param matchCommandList           关键命令匹配列表
     * @param type                       关键命令类型
     * @param value                      当前行内容
     */
    private void addScriptValidationResult(List<ScriptValidationResultDto> scriptValidationResultDtos, int key, List<String> matchCommandList, int type, String value) {
        if (!matchCommandList.isEmpty()) {
            ScriptValidationResultDto scriptValidationResultDto = new ScriptValidationResultDto();
            scriptValidationResultDto.setLine(key);
            scriptValidationResultDto.setType(type);
            scriptValidationResultDto.setTargetContent(matchCommandList);
            scriptValidationResultDto.setData(value);
            scriptValidationResultDtos.add(scriptValidationResultDto);
        }
    }


    /**
     * 接收审核结果并处理
     *
     * @param doubleCheckApiDto 审核服务反馈的审批结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doubleCheckScriptCallBack(DoubleCheckApiDto doubleCheckApiDto, Integer typeValue) throws SystemException {
        logger.info("Acceptance of audit results : {}", doubleCheckApiDto);

        try {
            if (doubleCheckApiDto != null) {
                logger.info("Audit information: {}", doubleCheckApiDto);
                AuditResultBean auditResultBean = new AuditResultBean();
                //双人复核与脚本服务化关系表主键
                auditResultBean.setAuditRelationId(doubleCheckApiDto.getServiceId());
                //审核状态
                auditResultBean.setState(StateConverter.convertStatus(doubleCheckApiDto.getApprovalState()));
                //双人复核主键
                auditResultBean.setApprWorkitemId(doubleCheckApiDto.getId());
                //双人复核审批人
                auditResultBean.setTrackApiDtos(doubleCheckApiDto.getTrackApiDtos());
                if (typeValue.equals(Enums.AuditType.SCRIPT_PUBLISH.getValue())) {
                    updateScriptReview(auditResultBean);
                    logger.info("updateScriptReview success");
                } else if (typeValue.equals(Enums.AuditType.SCRIPT_DELETE.getValue())) {
                    updateScriptReviewForDelete(auditResultBean);
                    logger.info("updateScriptReviewForDelete success");
                }
            } else {
                logger.info("Audit information is empty");
            }
            logger.info("Receive audit results success");
        } catch(Exception e){
            logger.error("doubleCheckScriptCallBack error",e);
            throw new SystemException(e);
        }
    }

    /**
     * 创建者转移获取用户管理中所有用户 分页
     *
     * @param userInfoQueryDto
     * @return
     */
    @Override
    @Transactional(readOnly = true)
    public PageInfo<UserInfoApiDto> getUserInfoList(UserInfoQueryDto userInfoQueryDto) {
        userInfoQueryDto.getQueryParam().setFuzzySearch(true);
        return scripts.getiUserInfo().getUserInfoByLoginNameForApi(userInfoQueryDto);
    }

    /**
     * 创建者转移
     *
     * @param creatorTransferDto    创建者转移需要的实体Dto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void creatorTransfer(CreatorTransferDto creatorTransferDto) {
        CurrentUser currentUser = CurrentUserUtil.getCurrentUser();

        Info info = new Info();
        info.setCreatorId(creatorTransferDto.getUserInfoApiDto().getId());
        info.setCreatorName(creatorTransferDto.getUserInfoApiDto().getFullName());
        info.setUpdatorId(currentUser.getId());
        info.setUpdatorName(currentUser.getFullName());
        info.setOrgCode(creatorTransferDto.getUserInfoApiDto().getOrgCode());
        scripts.getInfoMapper().updateInfosByUuid(info, creatorTransferDto.getInfoUuids());


        InfoVersion infoVersion = new InfoVersion();
        infoVersion.setCreatorId(creatorTransferDto.getUserInfoApiDto().getId());
        infoVersion.setCreatorName(creatorTransferDto.getUserInfoApiDto().getFullName());
        infoVersion.setUpdatorId(currentUser.getId());
        infoVersion.setUpdatorName(currentUser.getFullName());
        scripts.getInfoVersionMapper().updateInfoVersionByInfoUuid(infoVersion, creatorTransferDto.getInfoUuids());
    }

    /**
     * 适用平台的列表逗号拼接成字符串
     *
     * @param platformList 适用平台列表
     * @return 拼接字符串
     */
    private String generatePlatformString(List<String> platformList) {
        StringBuilder platforms = new StringBuilder();
        for (int i = 0; i < platformList.size(); i++) {
            // 将当前元素添加到 platforms 中
            platforms.append(platformList.get(i));
            if (i < platformList.size() - 1) {
                // 在元素之间添加逗号（除了最后一个元素）
                platforms.append(",");
            }
        }
        return platforms.toString();
    }


    /**
     * 根据脚本id获取默认版本id
     *
     * @param scriptIds 脚本id
     * @return Long []
     */
    @Override
    public Long[] getDefaultVersionIds(Long[] scriptIds) {
        return getDefaultVersionIdsByScriptId(scriptIds);
    }

    /**
     * 根据脚本id获取当前默认已发布版本的脚本id
     *
     * @param scriptIds 脚本id
     * @return Long[]
     */
    private Long[] getDefaultVersionIdsByScriptId(Long[] scriptIds) {
        List<Long> idsList = scripts.getInfoMapper().getDefaultVersionIdsByScriptId(scriptIds);
        if (idsList.isEmpty()) {
            return new Long[0];
        } else {
            return idsList.toArray(new Long[0]);
        }
    }


    /**
     * 根据脚本任意的版本uuid查询脚本本的默认版本信息
     * @param scriptInfoQueryDto 脚本信息查询条件
     * @return  ScriptInfoDto
     */
    @Override
    public ScriptDubboInfoDto getDefaultScriptInfoApi(ScriptInfoQueryDto scriptInfoQueryDto) throws ScriptException {
        if(scriptInfoQueryDto.getSrcScriptUuid() == null){
            logger.error("getDefaultScriptInfoByUuid srcScriptUuid is null");
            return new ScriptDubboInfoDto();
        }
        //根据脚本版本uuid插叙默认版本脚本信息
        ScriptVersionDto infoVersionDto = scripts.getInfoVersionService().selectInfoVersionBySrcScriptUuid(scriptInfoQueryDto.getSrcScriptUuid());
        ScriptVersionDto infoDefaultVersionByUuid = scripts.getInfoVersionService().getInfoDefaultVersionByUuid(infoVersionDto.getInfoUniqueUuid());

        scriptInfoQueryDto.setSrcScriptUuid(infoDefaultVersionByUuid.getSrcScriptUuid());
        return getScriptDetailForDubbo(scriptInfoQueryDto);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void ignoreScriptRevision(List<Long> ids,CurrentUser currentUser) {
        StatementDto statementDto = new StatementDto();
        statementDto.setConfirmorId(currentUser.getId());
        statementDto.setConfirmorName(currentUser.getLoginName());
        statementDto.setConfirmState(1);
        scripts.getScriptStatementService().updateScriptStatementByIds(statementDto,ids);
    }

    @Override
    public void toAlarm(String isrcScriptUuid,Long userId){

        Info info = scripts.getInfoMapper().selectInfoByScriptUuid(isrcScriptUuid);
        Map<String,Object> res = new HashMap<>(6);
        res.put("moduleCode","scriptservice");
        res.put("typeCode","scanUnrevisedScript");
        res.put("warnMsg","脚本【"+info.getScriptName()+"】已超过规定时间未修改，请确认！");
        res.put("serviceUuid",isrcScriptUuid);
        res.put("extendInfoUrl","");
        Map<String,String> userMap = new HashMap<>();
        List<Long> userList = new ArrayList<>();
        userList.add(userId);
        List<UserInfoApiDto> userInfoList = scripts.getiUserInfo().getUserInfoList(userList);
        List<UserInfoApiDto> useList = new ArrayList<>();
        if(!userInfoList.isEmpty()){
            UserInfoApiDto userInfoApiDto = userInfoList.get(0);
            userMap.put("loginName",userInfoApiDto.getLoginName());
            userMap.put("phone",userInfoApiDto.getTelephone());
            userMap.put("email",userInfoApiDto.getEmail());
            userMap.put("id",userInfoApiDto.getId().toString());
            useList.add(userInfoApiDto);
        }
        res.put("userList",useList);
        String str = JSONObject.toJSONString(res);
        scripts.getWarn().receiveWarn(str);
        logger.info("alarm message to system success!!!, srcScriptUuid: {}, userId: {}", isrcScriptUuid, userId);
    }

    @Override
    public Set<String> getLabelList() {
        //查询数据库脚本表中的所有标签使用set集合去重
        Set<String> labelSet = new HashSet<>();
        List<String> labelList1 = new ArrayList<>();
        List<String> labelList = scripts.getInfoMapper().getLabelList();
        for (String labels : labelList) {
            String[] split = labels.split(",");
            labelSet.addAll(Arrays.asList(split));
            labelList1.addAll(Arrays.asList(split));
        }

        // 查询完之后，使用 Redis ZSet 存储标签和计数
        RScoredSortedSet<String> zSet = scripts.getRedissonClient().getScoredSortedSet(SCRIPT_LABEL_ZSET);

        // 加载 Lua 脚本
        String luaScript = "local key = KEYS[1]\n" +
                "local insertedLabels = ARGV\n" +
                "for i, label in ipairs(insertedLabels) do\n" +
                "    redis.call('ZINCRBY', key, 1, label)\n" +
                "end\n" +
                "return true";
        scripts.getRedissonClient().getScript().eval(RScript.Mode.READ_WRITE, luaScript, RScript.ReturnType.BOOLEAN, Collections.singletonList(SCRIPT_LABEL_ZSET), labelList1.toArray(new String[0]));

        // 设置 ZSet 的过期时间为 7天
        zSet.expire(7, TimeUnit.DAYS);

        return labelSet;
    }


    /**
     * 获取白名单分类，需要校验的高危执行用户，可以校验这类脚本的部门
     * @return 高危执行用户校验类信息
     */
    @Override
    public ExecutorValidation getExecutorValidationList() {

        ExecutorValidation executorValidation = new ExecutorValidation();
        //白名单分类列表
        List<String> whiteCategoryList = scripts.getScriptBusinessConfig().getWhiteCategoryList();
        //需要校验的高危用户列表
        List<String> dangerUserList = scripts.getScriptBusinessConfig().getDangerUserList();
        //可以校验高危用户脚本的部门
        List<String> specialReviewOrgList = scripts.getScriptBusinessConfig().getSpecialReviewOrgList();

        executorValidation.setWhiteCategoryList(whiteCategoryList);
        executorValidation.setDangerUserList(dangerUserList);
        executorValidation.setSpecialReviewOrgList(specialReviewOrgList);

        return executorValidation;
    }
    /**
     * 脚本默认版本切换通知接口,当前是版本回退、脚本发布审核通过后调用
     * @param myScriptBean myScriptBean参数bean
     */
    @Override
    public void noticeScriptChangeDefaultVersion(MyScriptBean myScriptBean)  {
        try {
            //巡检目前要求只要中文名、srcUuid、uniqueUuid，所以SQL没查询别的字段
            MyScriptBean res = scripts.getMyScriptMapper().getScriptNameAndUuid(myScriptBean);
            scripts.getiPublisher().apply(Constants.SCRIPT_DEFAULT_VERSION_CHANGE_CHANEL, JSON.toJSONString(res));
            logger.info("脚本srcUuid:{},切换默认版本，发送MQ成功!",myScriptBean);
        } catch (CommunicationException e ) {
            logger.error("发送mq出现异常：",e);
        }
    }

    /**
     * 获取是否展示sql类型脚本标识
     * @return 开关值
     */
    @Override
    public boolean getSqlShowFlag(){
        return scripts.getScriptBusinessConfig().isSqlShow();
    }

    /**
     * 调用双人复核归档
     * @param scriptInfoVersionId 脚本的版本id
     */
    @Override
    public void doubleCheckAutoArchive(Long [] scriptInfoVersionId){
        //根据脚本的scriptInfoVersionId获取双人复核id
        List<Long> workItemIds = scripts.getMyScriptMapper().getWorkitemIdByVersionIds(scriptInfoVersionId);
        if(!workItemIds.isEmpty()){
            for(Long workItemId : workItemIds){
                doubleCheckApiService.documentationDoubleCheck(workItemId);
            }
        }
    }

    /**
     * 获取发布脚本为白名单标识
     */
    @Override
    public boolean getPublishScriptWhiteFlag(){
        return scripts.getScriptBusinessConfig().isPublishScriptWhiteFlag();
    }

    /**
     * 是否走角色权限标识
     * @return 布尔标识
     */
    @Override
    public boolean getRolePermissionFlag(){
        //判断走用户-分类权限还是角色-分类（roleCategory）权限
        String queryScriptPermission = scripts.getScriptBusinessConfig().getQueryScriptPermission();
        //判断走部门权限还是角色权限（userRoleGroup）
        String dataPermissionPolicy = scripts.getScriptBusinessConfig().getDataPermissionPolicy();
        return "roleCategory".equals(queryScriptPermission) && "userRoleGroup".equals(dataPermissionPolicy);
    }

}

package com.ideal.script.controller;

import com.ideal.common.dto.R;
import com.ideal.sc.util.CurrentUserUtil;
import com.ideal.sc.util.ValidationUtils;
import com.ideal.script.common.constant.Constants;
import com.ideal.script.common.constant.permission.MenuPermitConstant;
import com.ideal.script.common.constant.permission.MyScriptBtnPermitConstant;
import com.ideal.script.common.constant.permission.ServiceRolloutBtnPermitConstant;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.model.dto.ToProductQueryDto;
import com.ideal.script.service.IReleaseMediaService;
import com.ideal.system.common.component.aop.MethodPermission;
import com.ideal.system.common.component.model.CurrentUser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

/**
 * 服务投产controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("${app.script-tools-url:}/releaseMedia")
@MethodPermission(MenuPermitConstant.MY_SCRIPT_OR_TASK_APPLY_PER)
public class ReleaseMediaController {

    private final IReleaseMediaService releaseMediaService;

    private static final Logger logger = LoggerFactory.getLogger(ReleaseMediaController.class);

    public ReleaseMediaController(IReleaseMediaService releaseMediaService) {
        this.releaseMediaService = releaseMediaService;
    }

    /**
     * 投产介质导出
     *
     * @param ids      版本id集合
     * @param response 响应
     */
    @GetMapping("/exportReleaseMedia")
    @MethodPermission(MyScriptBtnPermitConstant.EXPORT_RELEASE_MEDIA_PER)
    public void exportReleaseMedia(@RequestParam(value = "ids") Long[] ids, HttpServletResponse response) {
        try {
            releaseMediaService.exportReleaseMedia(ids, response);
            logger.info("exportReleaseMedia success");
        } catch (ScriptException e) {
            logger.error("exportReleaseMedia error", e);
        }
    }


    /**
     * 投产介质导入
     *
     * @param file              投产介质文件（.zip)
     * @param toProductQueryDto 包含所属人、投产描述
     * @return {@link R }<{@link Object }>
     * <AUTHOR>
     */
    @PostMapping("/importReleaseMedia")
    @MethodPermission(ServiceRolloutBtnPermitConstant.IMPORT_RELEASE_MEDIA_PER)
    public R<Object> importReleaseMedia(@RequestParam("file") MultipartFile file, ToProductQueryDto toProductQueryDto) {
        try {
            CurrentUser user = CurrentUserUtil.getCurrentUser();
            releaseMediaService.importReleaseMedia(file, user, toProductQueryDto);
        } catch (ScriptException e) {
            logger.error("importReleaseMedia is error:", e);
            return ValidationUtils.customFailResult("fileName",e.getMessage());
        }
        logger.info("importReleaseMedia success");
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, "投产介质导入成功！");
    }

}

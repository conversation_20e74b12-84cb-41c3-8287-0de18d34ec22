package com.ideal.script.common.constant;

/**
 * 常量类
 *
 * <AUTHOR>
 */
@SuppressWarnings("unused")
public class Constants {


    private Constants() {
        // 隐藏的私有构造函数
    }

    public static final String REPONSE_STATUS_SUSSCESS_CODE = "10000";
    public static final String REPONSE_STATUS_VALIDATA_CODE = "10605";
    public static final String REPONSE_STATUS_FAIL_CODE = "10602";


    /**
     * 接口名称类
     */
    public static final String ADAPTOR_SHELLCMD = "shellcmd";

    /**
     * 任务执行调用agent方法
     */
    public static final String RPC_METHOD = "IEAIAgent.executeAct";

    /**
     * 重试执行agent方法
     */
    public static final String RETRY_RPC_METHOD = "IEAIAgent.reExecuteAct";

    /**
     * 完成
     */
    public static final int SCRIPT_FINISH_STATE = 20;
    /**
     * 终止
     */
    public static final int SCRIPT_KILL_STATE = 60;
    /**
     * 这个状态问了下，说没用现在
     */
    public static final int SCRIPT_FINISH_FAIL_STATE = 40;
    /**
     * 异常
     */
    public static final int SCRIPT_FAIL_STATE = 30;
    /**
     * 失败
     */
    public static final int SCRIPT_RUNNING_FAIL_STATE = 50;

    /**
     * 忽略
     */
    public static final int SCRIPT_SKIP_STATE = 5;



    protected static final int[] SCRIPT_FINISH_SET = {
            SCRIPT_FINISH_STATE,
            SCRIPT_KILL_STATE,
            SCRIPT_FINISH_FAIL_STATE,
            SCRIPT_SKIP_STATE,
            SCRIPT_FAIL_STATE
    };

    public static int[] getScriptFinishSet() {
        return SCRIPT_FINISH_SET;
    }

    public static final String ALGORITHM = "AES/GCM/NoPadding";

    public static final String LIST_SUCCESS = "list.success";
    public static final String SHENANDOAH = "Constants.SHENANDOAH:";

    public static final String SCRIPT_EXECUTE_LOCK = "script-execute-result-lock";

    public static final String SCRIPT_ERROR_LOCK = "script-error-result-lock";

    public static final String SCRIPT_SEND_RESULT_LOCK = "script-send-result-lock";

    public static final String SCRIPT_WORK_DIR = "scriptWorkDir";
    public static final String COMMAND = "command";
    public static final String EXPECT_TYPE = "expectType";



    public static final String SERVICES_TYPE = "servicesType";

    public static final String AGENT_PORT = "agentPort";


    public static final String IS_SHUT_DOWN = "isShutdown";


    public static final String PARAM_VALUE = "@@script@@service@@";

    /**
     * 脚本服务化配置文件前缀
     */
    public static final String SCRIPT_PROP_PREFIX="ideal.script";

    public static final String REDIS_SCRIPT_TEMP_ATTACHMENT = "script:temp:attachment";

    /**
     * 是否已经消费了agent结果标识
     */
    public static final String CHECK_CONSUMPTION_SCRIPT_RUNTIME = "script:consumption:taskruntime:";


    /**
     * 脚本执行模式标识
     */
    public static final String DRIVER_MODE = "driveMode";
    /**
     * 任务信息dto标识
     */
    public static final String TASK_START_DTO = "taskStartDto";
    /**
     * 用户信息标识
     */
    public static final String USER = "user";
    /**
     * 脚本发布通过、切换版本版本推送的mq管道名
     */
    public static final String SCRIPT_DEFAULT_VERSION_CHANGE_CHANEL = "scriptDefaultVersionChange-out-0";

    /**
     * 连接agent异常推送mq管道名
     */
    public static final String CRONTABS_ERROR_RESULT_DEV = "crontabsErrorResult-dev";

    /**
     * 连接agent成功推送mq管道名
     */
    public static final String CRONTABS_SEND_RESULT_DEV = "crontabsSendResult-dev";

    /**
     * agent执行返回结果推送mq管道名
     */
    public static final String CRONTABS_EXECUTE_RESULT_DEV = "crontabsExecuteResult-dev";
    /**
     * 临时附件上传接口使用的分隔符
     */
    public static final String ATTACHMENT_SPLIT_FLAG = "@@@@";

    /**
     * 脚本执行时，redis中保存的数据的超时时间
     */
    public static final Long EXEC_TASK_TIMEOUT_DAY = 1L;

    /**
     * 投产介质导出，服务投产导入，脚本源文件文件夹名称
     */
    public static final String SCRIPT_FILE_DIR_NAME = "scriptFiles";

}

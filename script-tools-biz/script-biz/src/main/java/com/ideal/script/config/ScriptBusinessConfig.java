package com.ideal.script.config;


import com.ideal.script.annotation.ValidDataSize;
import com.ideal.script.common.constant.Constants;
import org.hibernate.validator.constraints.Range;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.util.unit.DataSize;
import org.springframework.validation.annotation.Validated;

import javax.annotation.PostConstruct;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 */
@ConfigurationProperties(prefix = Constants.SCRIPT_PROP_PREFIX)
@Validated
public class ScriptBusinessConfig {
    /**
     * 任务执行发往agent前，脚本参数是否用DES加密
     */
    private boolean sendScriptParametersDesEnc;
    /**
     * agent网关地址
     */
    @NotBlank
    private String agentGatewayIp;
    /**
     * agent网关端口
     */
    @Range(min = 0, max = 65535)
    private Integer agentGatewayPort;
    /** TODO 暂时将最大值限制放大点，等广发现场彻底把脚本附件缩小后改小，后面广发现场预期是优化配置成单个500K，总的3M
     * 脚本服务化每个脚本中每个附件大小上限
     */
    @ValidDataSize(max = "6MB")
    private DataSize eachScriptAttachmentLimitSize = DataSize.ofMegabytes(6);
    /** TODO 暂时将最大值限制放大点，等广发现场彻底把脚本附件缩小后改小，后面广发现场预期是优化配置成单个500K，总的3M
     * 脚本服务化每个脚本中所有附件大小上限
     */
    @ValidDataSize()
    private DataSize totalScriptAttachmentLimitSize = DataSize.ofMegabytes(15);

    /**
     * 脚本服务化任务申请按照周期内创建的任务量排序配置周期（天）,上限为30
     */
    private Integer scriptTaskApplyCycle = 7;


    /**
     *  白名单分类列表，配置的分类不校验脚本执行用户
     */
    private List<String> whiteCategoryList;

    /**
     *  脚本需要校验的高危执行用户列表（白名单分类不需要校验）
     */
    private List<String> dangerUserList;

    /**
     *  脚本带有高危执行用户发布时可以审批的部门
     */
    private List<String> specialReviewOrgList;

    /**
     *  脚本带有高危执行用户发布时可以审批的角色
     */
    private List<String> specialReviewRoleList;

    /**
     * 我的脚本筛选权限（creator:按照创建用户，dept:按照部门和分类）
     */
    private String permissionPolicy;

    /**
     * 我的脚本创建、编辑是否展示sql类型脚本，true展示，false不展示，默认false
     */
    private boolean sqlShow = false;

    /**
     * 关键（高危）命令扫面是否使用绑定到分类的命令，true使用绑定到分类的命令，false使用脚本类型绑定，默认false
     */
    private boolean dangerCmdCategoryFlag = false;

    /**
     * 发布脚本不需要通过双人复核，直接发布
     */
    private boolean publishScriptWithoutDoubleCheck = false;

    /**
     *  接口查询脚本，是否查询所有脚本标识
     */
    private boolean getAllScriptFlag = false;
    /**
     *  agent查找agent-gateway dubbo接口的bean名字
     */
    private String scriptInstance = "scriptInstance";

    /**
     * 发布的脚本默认都是白名单，不需要选择脚本风险等级
     */
    private boolean publishScriptWhiteFlag = false;

    /**
     * 查询权限，使用部门-分类(deptCategory)   还是使用   角色-分类(roleCategory)  默认使用部门-分类
     */
    private String queryScriptPermission;

    /**
     * 权限控制，查询权限为角色-部门的时候，控制走角色(userRoleGroup)还是部门（dept） ，默认走部门
     */
    private String dataPermissionPolicy;

    /**
     * 是否只查询自己的数据，true只查询自己的，默认false
     */
    private boolean selectSelfDataFlag = false;

    /*
     * 脚本任务监听到mq相关消息后是否推送mq
     */
    private boolean monitorMessageSendMqFlag = false;

    public boolean isMonitorMessageSendMqFlag() {
        return monitorMessageSendMqFlag;
    }

    public boolean isSelectSelfDataFlag() {
        return selectSelfDataFlag;
    }

    public void setSelectSelfDataFlag(boolean selectSelfDataFlag) {
        this.selectSelfDataFlag = selectSelfDataFlag;
    }

    public String getQueryScriptPermission() {
        return queryScriptPermission;
    }

    public void setQueryScriptPermission(String queryScriptPermission) {
        this.queryScriptPermission = queryScriptPermission;
    }

    public String getDataPermissionPolicy() {
        return dataPermissionPolicy;
    }

    public void setDataPermissionPolicy(String dataPermissionPolicy) {
        this.dataPermissionPolicy = dataPermissionPolicy;
    }

    public void setMonitorMessageSendMqFlag(boolean monitorMessageSendMqFlag) {
        this.monitorMessageSendMqFlag = monitorMessageSendMqFlag;
    }

    public boolean isPublishScriptWhiteFlag() {
        return publishScriptWhiteFlag;
    }

    public void setPublishScriptWhiteFlag(boolean publishScriptWhiteFlag) {
        this.publishScriptWhiteFlag = publishScriptWhiteFlag;
    }

    public boolean isPublishScriptWithoutDoubleCheck() {
        return publishScriptWithoutDoubleCheck;
    }

    public void setPublishScriptWithoutDoubleCheck(boolean publishScriptWithoutDoubleCheck) {
        this.publishScriptWithoutDoubleCheck = publishScriptWithoutDoubleCheck;
    }

    public boolean isGetAllScriptFlag() {
        return getAllScriptFlag;
    }

    public void setGetAllScriptFlag(boolean getAllScriptFlag) {
        this.getAllScriptFlag = getAllScriptFlag;
    }
    public boolean isDangerCmdCategoryFlag() {
        return dangerCmdCategoryFlag;
    }

    public void setDangerCmdCategoryFlag(boolean dangerCmdCategoryFlag) {
        this.dangerCmdCategoryFlag = dangerCmdCategoryFlag;
    }

    public boolean isSqlShow() {
        return sqlShow;
    }

    public void setSqlShow(boolean sqlShow) {
        this.sqlShow = sqlShow;
    }

    public String getPermissionPolicy() {
        return permissionPolicy;
    }

    public void setPermissionPolicy(String permissionPolicy) {
        this.permissionPolicy = permissionPolicy;
    }
    public List<String> getWhiteCategoryList() {
        return whiteCategoryList;
    }

    public void setWhiteCategoryList(List<String> whiteCategoryList) {
        this.whiteCategoryList = whiteCategoryList;
    }

    public List<String> getDangerUserList() {
        return dangerUserList;
    }

    public void setDangerUserList(List<String> dangerUserList) {
        this.dangerUserList = dangerUserList;
    }

    public List<String> getSpecialReviewOrgList() {
        return specialReviewOrgList;
    }

    public void setSpecialReviewOrgList(List<String> specialReviewOrgList) {
        this.specialReviewOrgList = specialReviewOrgList;
    }

    public List<String> getSpecialReviewRoleList() {
        return specialReviewRoleList;
    }

    public void setSpecialReviewRoleList(List<String> specialReviewRoleList) {
        this.specialReviewRoleList = specialReviewRoleList;
    }

    public DataSize getTotalScriptAttachmentLimitSize() {
        return totalScriptAttachmentLimitSize;
    }

    public void setTotalScriptAttachmentLimitSize(DataSize totalScriptAttachmentLimitSize) {
        this.totalScriptAttachmentLimitSize = totalScriptAttachmentLimitSize;
    }

    public DataSize getEachScriptAttachmentLimitSize() {
        return eachScriptAttachmentLimitSize;
    }

    public void setEachScriptAttachmentLimitSize(DataSize eachScriptAttachmentLimitSize) {
        this.eachScriptAttachmentLimitSize = eachScriptAttachmentLimitSize;
    }

    public boolean isSendScriptParametersDesEnc() {
        return sendScriptParametersDesEnc;
    }

    public void setSendScriptParametersDesEnc(boolean sendScriptParametersDesEnc) {
        this.sendScriptParametersDesEnc = sendScriptParametersDesEnc;
    }

    public String getAgentGatewayIp() {
        return agentGatewayIp;
    }

    public void setAgentGatewayIp(String agentGatewayIp) {
        this.agentGatewayIp = agentGatewayIp;
    }

    public Integer getAgentGatewayPort() {
        return agentGatewayPort;
    }

    public void setAgentGatewayPort(Integer agentGatewayPort) {
        this.agentGatewayPort = agentGatewayPort;
    }

    public Integer getScriptTaskApplyCycle() {
        return scriptTaskApplyCycle;
    }

    public void setScriptTaskApplyCycle(Integer scriptTaskApplyCycle) {
        this.scriptTaskApplyCycle = scriptTaskApplyCycle;
    }

    public String getScriptInstance() {
        return scriptInstance;
    }

    public void setScriptInstance(String scriptInstance) {
        this.scriptInstance = scriptInstance;
    }

    @PostConstruct
    public void validateLists() {
        boolean allListsNotEmpty = whiteCategoryList != null && !whiteCategoryList.isEmpty()
                && dangerUserList != null && !dangerUserList.isEmpty()
                && specialReviewOrgList != null && !specialReviewOrgList.isEmpty()
                && specialReviewRoleList != null && !specialReviewRoleList.isEmpty();

        boolean allListsEmpty = (whiteCategoryList == null || whiteCategoryList.isEmpty())
                && (dangerUserList == null || dangerUserList.isEmpty())
                && (specialReviewOrgList == null || specialReviewOrgList.isEmpty())
                && (specialReviewRoleList == null || specialReviewRoleList.isEmpty());

        if (!allListsNotEmpty && !allListsEmpty) {
            throw new IllegalStateException("白名单分类列表、高危执行人列表、有权审核部门列表、有权审核角色列表必须同时配置属性值！");
        }
    }
}
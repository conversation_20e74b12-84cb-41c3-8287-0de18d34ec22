package com.ideal.script.model.dto;

import java.io.Serializable;
import java.util.Arrays;

/**
 * <AUTHOR>
 * 脚本删除调用双人复核信息
 */
public class ScriptDeleteDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 脚本infoVersion表id
     */
    private Long[] ids;
    /**
     * 审核人
     */
    private String auditorName;

    /**
     * 审核人ID
     */
    private Long auditorId;

    /**
     * 删除说明
     */
    private String deleteDesc;

    public Long[] getIds() {
        return ids;
    }

    public void setIds(Long[] ids) {
        this.ids = ids;
    }

    public String getAuditorName() {
        return auditorName;
    }

    public void setAuditorName(String auditorName) {
        this.auditorName = auditorName;
    }

    public Long getAuditorId() {
        return auditorId;
    }

    public void setAuditorId(Long auditorId) {
        this.auditorId = auditorId;
    }

    public String getDeleteDesc() {
        return deleteDesc;
    }

    public void setDeleteDesc(String deleteDesc) {
        this.deleteDesc = deleteDesc;
    }

    @Override
    public String toString() {
        return "ScriptDeleteDto{" +
                "ids=" + Arrays.toString(ids) +
                ", auditorName='" + auditorName + '\'' +
                ", auditorId=" + auditorId +
                ", deleteDesc='" + deleteDesc + '\'' +
                '}';
    }
}

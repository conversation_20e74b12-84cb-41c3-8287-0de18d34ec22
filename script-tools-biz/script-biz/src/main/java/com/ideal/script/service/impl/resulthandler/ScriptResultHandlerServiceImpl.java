package com.ideal.script.service.impl.resulthandler;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ideal.common.util.spring.SpringUtil;
import com.ideal.script.common.constant.Constants;
import com.ideal.script.common.constant.enums.Enums;
import com.ideal.script.common.constant.enums.JsonKey;
import com.ideal.sc.util.Base64;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.mapper.TaskRuntimeStdoutMapper;
import com.ideal.script.model.bean.TaskHandleParam;
import com.ideal.script.model.dto.TaskRuntimeDto;
import com.ideal.script.model.entity.TaskRuntimeStdout;
import com.ideal.script.service.IAfterRuntimeHandlerService;
import com.ideal.script.service.ITaskInstanceService;
import com.ideal.script.service.ITaskRuntimeService;
import com.ideal.script.service.resulthandler.IScriptResultHandlerService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Stream;

/**
 * 脚本执行结果处理类
 *
 * <AUTHOR>
 */
@Service
public class ScriptResultHandlerServiceImpl implements IScriptResultHandlerService {
    private final Logger logger = LoggerFactory.getLogger(ScriptResultHandlerServiceImpl.class);

    private final RedissonClient redissonClient;

    private final ITaskRuntimeService taskRuntimeService;

    private final ITaskInstanceService taskInstanceService;

    private final TaskRuntimeStdoutMapper taskRuntimeStdoutMapper;
    private final ObjectMapper objectMapper;

    public ScriptResultHandlerServiceImpl(RedissonClient redissonClient, ITaskRuntimeService taskRuntimeService, ITaskInstanceService taskInstanceService, TaskRuntimeStdoutMapper taskRuntimeStdoutMapper, ObjectMapper objectMapper) {
        this.redissonClient = redissonClient;
        this.taskRuntimeService = taskRuntimeService;
        this.taskInstanceService = taskInstanceService;
        this.taskRuntimeStdoutMapper = taskRuntimeStdoutMapper;
        this.objectMapper = objectMapper;
    }

    /**
     * 处理脚本执行结果 This method processes the result of script execution.
     *
     * @param messageList 包含执行结果信息的JSON消息
     */
    @Override
    @Transactional(rollbackFor = ScriptException.class)
    public void handleScriptExecuteResult(List<String> messageList) throws ScriptException {
        logger.info("Script operation result processing begins");
        try {
            if (CollectionUtils.isNotEmpty(messageList)) {
                for (String message : messageList) {
                    validateScriptResultJson(message);

                    // 遍历每个JSON对象并进行转换
                    //记录是否更新instance成功 驱动下一个批次方法需要该值判断，如果等于1，才驱动
                    JsonNode obj = objectMapper.readTree(message);
                    String taskRuntimeId = obj.get("scriptId").asText();
                    //获取agent实例数据
                    TaskRuntimeDto taskRuntimeDto = taskRuntimeService.selectTaskRuntimeById(Long.parseLong(taskRuntimeId));
                    //校验是否多次消费
                    if(!taskRuntimeDto.getBizId().contains(Enums.AgentExecRunFlag.RETRY.getValue())){
                        if(!checkConsumptionFlag(taskRuntimeId)){
                            return;
                        }
                    }
                    String stdout = Base64.getFromBase64(obj.get("stdout").asText());
                    String stderr = Base64.getFromBase64(obj.get("stderr").asText());
                    String lastLine = Base64.getFromBase64(obj.get("lastLine").asText());
                    // 判断bizId是否是
                    int status = obj.get("status").asInt();
                    TaskHandleParam taskHandleParam = new TaskHandleParam();
                    taskHandleParam.setIstdout(stdout);
                    taskHandleParam.setIstderror(stderr);
                    taskHandleParam.setIlastline(lastLine);


                    String bizId = taskRuntimeDto.getBizId();
                    if (null == bizId) {
                        return;
                    }
                    logger.info("bizId: {} ", bizId);

                    //终止
                    if (bizId.startsWith("agent-script-kill")) {
                        handleScriptExecute(taskRuntimeId, Enums.TaskRuntimeState.TERMINATED.getValue(), taskHandleParam);
                    } else {
                        logger.debug("taskRuntimeId: {}, stdout: {}, stderr: {}, lastLine: {}",
                                taskRuntimeId, stdout, stderr, lastLine);
                        handleScriptExecute(taskRuntimeId, status, taskHandleParam);
                    }
                }
            }


        } catch (ScriptException | JsonProcessingException e) {
            logger.error("handleScriptExecuteResult error:", e);
            throw new ScriptException("handle.script.execute.result.error");
        }
    }

    /**
     * 根据taskRunTimeId写入redis，返回值为true时说明写入成功，false写入失败
     * @param taskRunTimeId agent实例id
     * @return 写入是否成功标识
     */
    private boolean checkConsumptionFlag(String taskRunTimeId){
        RBucket<String> bucket = redissonClient.getBucket(Constants.CHECK_CONSUMPTION_SCRIPT_RUNTIME + taskRunTimeId);
        return bucket.trySet("value",1,TimeUnit.HOURS);
    }

    /**
     * 功能描述： 处理脚本执行结果
     *
     * @param taskRuntimeId   agent运行实例id
     * @param status          状态
     * @param taskHandleParam 任务处理参数对象
     * <AUTHOR>
     */
    @SuppressWarnings({"java:S2222"})
    private void handleScriptExecute(String taskRuntimeId, int status, TaskHandleParam taskHandleParam) throws ScriptException {
        try {
            processScriptExecutionResult(taskRuntimeId, status, taskHandleParam);
        } catch (Exception e) {
            handleException(e);
        }
    }

    /**
     * 根据任务实例id查询agent状态，如果为运行状态，说明这是agent第一次返回更新，此时将标准输出存入本地业务表，并且更新任务数据
     * 如果实例状态不是运行，说明实例已经更新过了，此时不再对实例、任务数据等进行相关操作
     */
    private void processScriptExecutionResult(String taskRuntimeId, int status, TaskHandleParam taskHandleParam) throws ScriptException {

        TaskRuntimeDto taskRuntimeDto = taskRuntimeService.selectTaskRuntimeById(Long.parseLong(taskRuntimeId));
        String bizId = taskRuntimeDto.getBizId();
        if (null != bizId) {
            //如果agent实例状态是运行，说明此时是第一次回结果，执行插入标准输出数据、更新任务状态
            //如果是重试操作，执行更新标准输出数据、更新任务状态
            if(Enums.TaskRuntimeState.RUNNING.getValue() == taskRuntimeDto.getState()){
                //执行更新
                if(bizId.contains(Enums.AgentExecRunFlag.RETRY.getValue())){
                    updateStdout(taskHandleParam.getIstdout(),
                            taskHandleParam.getIstderror(),taskRuntimeDto.getId(),taskRuntimeDto.getTaskInstanceId());
                }else{
                    //保存标准输出
                    insertStdout(taskHandleParam.getIstdout(),
                            taskHandleParam.getIstderror(),taskRuntimeDto.getId(),taskRuntimeDto.getTaskInstanceId());
                }
                //更新相关业务数据
                handelResultForScript(taskRuntimeId, status);

                //定时任务启动的脚本任务发送正常推送mq，如果工具箱、脚本任务有什么特殊处理再加对应的接口实现类
                if(Enums.ScriptStartType.TIMETASK.getStartValue().equals(taskRuntimeDto.getStartType().toString())){
                    IAfterRuntimeHandlerService bean = SpringUtil.getBean(Enums.ScriptStartType.getValueByStartValue(taskRuntimeDto.getStartType().toString()));
                    bean.monitorMqToSend(Constants.CRONTABS_EXECUTE_RESULT_DEV,taskHandleParam,taskRuntimeDto);
                }

                //驱动下一批次
                taskRuntimeService.driverNextBatchAgent(Long.parseLong(taskRuntimeId));
            }else { // processed
                logger.info("taskRuntimeId {} has been processed, skipping.", taskRuntimeId);
            }
        }
    }

    /**
     * 保存标准输出
     * @param stdout
     * @param stderror
     * @param runTimeId
     * @param instanceId
     */
    private void insertStdout(String stdout,String stderror,Long runTimeId,Long instanceId){
        //保存标准输出
        TaskRuntimeStdout taskRuntimeStdout = new TaskRuntimeStdout();
        taskRuntimeStdout.setIstdout(stdout);
        taskRuntimeStdout.setIstderror(stderror);
        taskRuntimeStdout.setIruntimeId(runTimeId);
        taskRuntimeStdout.setItaskInstanceId(instanceId);
        taskRuntimeStdoutMapper.insert(taskRuntimeStdout);
    }

    /**
     * 更新标准输出
     * @param stdout
     * @param stderror
     * @param runTimeId
     * @param instanceId
     */
    private void updateStdout(String stdout,String stderror,Long runTimeId,Long instanceId){
        //保存标准输出
        TaskRuntimeStdout taskRuntimeStdout = new TaskRuntimeStdout();
        taskRuntimeStdout.setIstdout(stdout);
        taskRuntimeStdout.setIstderror(stderror);
        taskRuntimeStdout.setIruntimeId(runTimeId);
        taskRuntimeStdout.setItaskInstanceId(instanceId);
        taskRuntimeStdoutMapper.updateByRunTimeId(taskRuntimeStdout);
    }


    /**
     * handleException​方法用于处理异常。
     * 如果捕获到的异常是 ​InterruptedException​，则记录错误日志并中断当前线程。
     * 否则，记录错误日志并抛出 ​ScriptException
     */
    private void handleException(Exception e) throws ScriptException {
        if (e instanceof InterruptedException) {
            logger.error("An InterruptedException occurred during the execution of the handle method.", e);
            Thread.currentThread().interrupt();
        } else {
            logger.error("An Exception occurred during the execution of the handle method.", e);
            throw new ScriptException("handle.script.execute.result.error");
        }
    }

    /**
     * 方法用于释放锁
     * 它根据锁的获取情况和当前线程是否持有锁来判断是否释放锁。如果锁被当前线程持有，则将其释放
     */
    private void releaseLocks(RLock lock, boolean isLocked) {
        if (isLocked && lock.isHeldByCurrentThread()) {
            lock.unlock(); // 释放锁
        }
    }


    private void handelResultForScript(String taskRuntimeId, int status) throws ScriptException {
        // 执行后续操作
        TaskRuntimeDto taskRuntimeDto = taskRuntimeService.selectTaskRuntimeById(Long.parseLong(taskRuntimeId));
        if (!isBoolean(taskRuntimeDto, taskRuntimeDto.getTaskInstanceId())) {
            return;
        }
        if (isBoolean(taskRuntimeDto, taskRuntimeDto.getTaskInstanceId()) && null != taskRuntimeDto.getState()) {
            int oldStatus = taskRuntimeDto.getState();
            // ​这段代码表示如果 ​Constants.SCRIPT_FINISH_SET​数组中不包含 ​oldStatus​这个值，那么执行接下来的逻辑 。只有一种情况，原来agent实例状态是运行的。
            // 只有原来的agent实例的状态是运行状态的，才进行下面的操作。
            if (!ArrayUtils.contains(Constants.getScriptFinishSet(), oldStatus)) {
                taskRuntimeService.updateExecTimeAndState(taskRuntimeId, status, taskRuntimeDto);

                // 更新任务级别状态（ieai_script_task_instance )
                if (isStatusValid(status)) {
                    List<Integer> notInStates = new ArrayList<>();
                    notInStates.add(Enums.TaskRuntimeState.TERMINATED.getValue());
                    // 更新serverNum
                    taskInstanceService.updateServerNum(taskRuntimeDto.getTaskInstanceId(), notInStates);
                    taskInstanceService.allFinish(taskRuntimeDto);

                } else {
                    // 异常的状态回更没写呢，参照原主线的ScriptInstanceImpl.java类中的1276行
                    // 异常的第二步，更新实例表状态为异常。因为这个else方法就是异常的接收结果。（不包含部分运行的（分批执行的这种，echonum=100)，因为部分运行这种，还有一堆没执行的，整体状态还是部分运行）。微服务不用考虑这个，这个让我默认去掉了概念。
                    taskInstanceService.updateState(status, taskRuntimeDto.getTaskInstanceId(), Enums.TaskInstanceStatus.PARTIAL_RUNNING.getValue());

                    Integer count = taskRuntimeService.getRunningAgentInstanceCount(Long.parseLong(taskRuntimeId));
                    if (count == 0) {
                        // 如果实例下没有agent正在运行（即所有agent都已执行完），则更新主表的时间字段。
                        List<Integer> notInStates = new ArrayList<>();
                        notInStates.add(Enums.TaskInstanceStatus.PARTIAL_RUNNING.getValue());
                        taskInstanceService.updateEndTime(taskRuntimeDto.getTaskInstanceId(), notInStates);
                    }
                }
            }


        }
    }


    private boolean isStatusValid(int status) {
        return status != Enums.TaskRuntimeState.SCRIPT_FAIL_STATE.getValue()
                && status != Constants.SCRIPT_FINISH_FAIL_STATE
                && status != Constants.SCRIPT_RUNNING_FAIL_STATE;
    }

    /**
     * 检查给定的JSON字符串中是否包含指定的关键字，如果缺少任何一个关键字，方法就会抛出一个ScriptException异常
     *
     * @param jsonMessage 包含执行结果信息的JSON消息
     */

    public void validateScriptResultJson(String jsonMessage) throws ScriptException {
        try {
            JsonNode innerNode = objectMapper.readTree(jsonMessage);
            // 验证关键属性
            if (!innerNode.has(JsonKey.SCRIPT_ID.getKey())
                    || !innerNode.has(JsonKey.STDOUT.getKey())
                    || !innerNode.has(JsonKey.LAST_LINE.getKey())
                    || !innerNode.has(JsonKey.STDERR.getKey())
                    || !innerNode.has(JsonKey.TRANSCODING.getKey())
                    || !innerNode.has(JsonKey.STATUS.getKey())) {
                logger.error("The JSON returned from the script execution result is missing a key property!");
                throw new ScriptException("error.json.missing.key");
            }
        } catch (Exception e) {
            logger.error("Exception occurred while parsing the JSON returned from the script execution result.");
            throw new ScriptException("error.json.parsing.exception");
        }
    }

    /**
     * 管理服务发送agent执行后返回的调用结果
     *
     * @param message 管理服务发送agent执行后返回的调用结果
     */
    @Override
    @Transactional(rollbackFor = ScriptException.class)
    @SuppressWarnings({"java:S2222"})
    public void handleScriptSendResult(String message) throws ScriptException {
        logger.info("handle script send result processing begins");
        RLock lock = null;
        boolean isLocked = false;
        try {
            // 存储管理服务提供的taskId，（管理服务的ieai_agent_task_send_result表中的itask_id）
            validateScriptSendResultJson(message);
            // json解析
            JsonNode jsonNode = objectMapper.readTree(message);

            String bizId = jsonNode.get("bizId").asText();
            // 提取taskRuntimeId
            String taskRuntimeId = "";
            if (bizId != null && bizId.contains(Enums.Separator.DASH.getValue())) {
                String[] parts = bizId.split(Enums.Separator.DASH.getValue());
                taskRuntimeId = parts[parts.length - 1];
            }
            String agentIp = jsonNode.get("agentIp").asText();
            int agentPort = jsonNode.get("agentPort").asInt();
            long taskId = jsonNode.get("taskId").asLong();
            boolean send = jsonNode.get("send").asBoolean();
            logger.info("bizId: {}, agentIp: {}, agentPort: {}, taskId: {},send:{} ,taskRuntimeId:{}",
                    bizId, agentIp, agentPort, taskId, send, taskRuntimeId);

            if (taskRuntimeId.isEmpty()) {
                logger.error("method handleScriptSendResult , the taskRuntimeId extracted from bizId is empty.");
                throw new ScriptException("error.handle.script.send.result.taskRuntimeId.empty");
            }

            String lockKey = Constants.SCRIPT_SEND_RESULT_LOCK + Enums.Separator.DASH.getValue() + taskRuntimeId;
            lock = redissonClient.getLock(lockKey);

            isLocked = lock.tryLock(5, -1, TimeUnit.SECONDS);

            if (isLocked) {

                // 回更agent运行实例表（ieai_script_task_runtime）中taskId。将管理服务反馈的taskId存储到表中。
                TaskRuntimeDto taskRuntimeDto = new TaskRuntimeDto();
                taskRuntimeDto.setAgentTaskId(taskId);
                taskRuntimeDto.setId(Long.parseLong(taskRuntimeId));
                taskRuntimeService.updateTaskRuntime(taskRuntimeDto);

                //定时任务启动的脚本任务发送正常推送mq，如果工具箱、脚本任务有什么特殊处理再加对应的接口实现类
                taskRuntimeDto.setAgentIp(agentIp);
                taskRuntimeDto.setAgentPort(agentPort);
                TaskRuntimeDto taskRuntimeEntity = taskRuntimeService.selectTaskRuntimeById(Long.parseLong(taskRuntimeId));
                if(Enums.ScriptStartType.TIMETASK.getStartValue().equals(taskRuntimeEntity.getStartType().toString())){
                    IAfterRuntimeHandlerService bean = SpringUtil.getBean(Enums.ScriptStartType.getValueByStartValue(taskRuntimeEntity.getStartType().toString()));
                    bean.monitorMqToSend(Constants.CRONTABS_SEND_RESULT_DEV,null,taskRuntimeDto);
                }
            }

        } catch (JsonProcessingException e) {
            throw new ScriptException("JsonProcessingException :", e);
        } catch (InterruptedException e) {
            logger.error("An InterruptedException occurred during the execution of the handleScriptSendResult method.", e);
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            logger.error("An Exception occurred during the execution of the handleScriptSendResult method", e);
            throw new ScriptException("error.handle.script.send.result");
        } finally {
            if (isLocked && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }

    }

    public void validateScriptSendResultJson(String jsonData) throws ScriptException {
        try {
            JsonNode jsonNode = objectMapper.readTree(jsonData);

            boolean containsAllKeys = Stream.of("bizId", "agentIp", "agentPort", "taskId", "send")
                                            .allMatch(jsonNode::has);

            if (!containsAllKeys) {
                logger.error("method validateScriptSendResultJson ,The JSON returned from the script send result is missing a key property!");
                throw new ScriptException("error.json.missing.key");
            }
        } catch (Exception e) {
            logger.error("validateScriptSendResultJson error:", e);
            throw new ScriptException("error.json.parsing.exception");
        }
    }

    public long extractNumber(String bizId) throws ScriptException {
        if (bizId == null || bizId.isEmpty()) {
            logger.info("bizId is null or empty");
            throw new ScriptException("error.bizId.empty");
        }
        String[] parts = bizId.split(Enums.Separator.DASH.getValue());
        if (parts.length > 0) {
            String lastPart = parts[parts.length - 1];
            try {
                return Long.parseLong(lastPart);
            } catch (NumberFormatException e) {
                logger.error("A NumberFormatException occurred during the extraction of runtimeId from bizId!");
                throw new ScriptException("error.bizId.numberFormatException");
            }
        } else {
            logger.error("BizId parts array length is zero!");
            throw new ScriptException("error.zeroPartsArrayLength");
        }
    }

    public void bizIdValid(String bizId) throws ScriptException {
        if (bizId.isEmpty()) {
            logger.error("The taskRuntimeId extracted from bizId is empty.");
            throw new ScriptException("error.handle.script.send.result.taskRuntimeId.empty");
        }
    }

    /**
     * 管理服务发送异常返回的调用结果（script-error-result）
     *
     * @param message 管理服务发送异常返回的调用结果（script-error-result）
     */
    @Override
    @Transactional(rollbackFor = ScriptException.class)
    @SuppressWarnings({"java:S2222"})
    public void handleScriptErrorResult(String message, String bizId, Long agentTaskId) throws ScriptException {
        logger.info("handle script error result processing begins");

        logger.info("bizId: {},  taskId: {},taskRuntimeId:{}",
                bizId, agentTaskId, bizId);
        bizIdValid(bizId);
        String lockKey = Constants.SCRIPT_ERROR_LOCK + Enums.Separator.DASH.getValue() + bizId;

        RLock lock = null;
        boolean isLocked = false;
        try {
            lock = redissonClient.getLock(lockKey);
            isLocked = lock.tryLock(5, -1, TimeUnit.SECONDS);
            if (isLocked) {
                processScriptError(message, bizId, agentTaskId);
            } else {
                //竞争：如果多个服务器或线程同时尝试获取相同的锁，只有一个能够成功获取到锁，其他的请求可能会排队等待或者直接获取失败。
                //锁已经被其他进程或线程持有：如果在尝试获取锁的时候发现锁已经被其他进程或线程持有，并且没有设置重试机制或超时时间，那么当前请求将获取不到锁。
                //锁过期：在分布式系统中，锁通常可以设置过期时间。如果之前持有锁的进程异常退出或者未来得及释放锁，那么锁有可能过期，新的请求再次获取锁时可能会失败。
                logger.info("script-error-result topic,Failed to acquire lock for taskRuntimeId {}, skipping.", bizId);
            }
        } catch (InterruptedException e) {
            logger.error("An InterruptedException occurred during the script-error-result topic of the handleScriptErrorResult method.", e);
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            logger.error("An Exception occurred during the script-error-result topic of the handleScriptErrorResult method.", e);
            throw new ScriptException("fail.handle.script.error.result");
        } finally {
            if (isLocked && lock.isHeldByCurrentThread()) {
                lock.unlock(); // 释放锁
            }
        }
    }

    private void processScriptError(String message, String bizId, Long agentTaskId) throws ScriptException {

        // script-error-result主题返回的bizId是一个字符串
        long taskRuntimeId = extractNumber(bizId);
        TaskRuntimeDto taskRuntimeDto = taskRuntimeService.selectTaskRuntimeById(taskRuntimeId);

        //定时任务启动的脚本任务异常推送mq，如果工具箱、脚本任务有什么特殊处理再加对应的接口实现类
        if(Enums.ScriptStartType.TIMETASK.getStartValue().equals(taskRuntimeDto.getStartType().toString())){
            IAfterRuntimeHandlerService bean = SpringUtil.getBean(Enums.ScriptStartType.getValueByStartValue(taskRuntimeDto.getStartType().toString()));
            bean.monitorMqToSend(Constants.CRONTABS_ERROR_RESULT_DEV,null,taskRuntimeDto);
        }

        String key = taskRuntimeDto.getBizId();
        if (null != key) {
            //如果agent实例状态是运行，说明此时是第一次回结果，执行插入标准输出数据、更新任务状态
            //如果是重试操作，执行更新标准输出数据、更新任务状态
            if(Enums.TaskRuntimeState.RUNNING.getValue() == taskRuntimeDto.getState()){
                    //执行更新
                    if(bizId.contains(Enums.AgentExecRunFlag.RETRY.getValue())){
                        updateStdout("",message,taskRuntimeDto.getId(),taskRuntimeDto.getTaskInstanceId());
                    }else{
                        //保存标准输出,此方法为异常方法，所以stdout为空，stderror有值
                        insertStdout("",message,taskRuntimeDto.getId(),taskRuntimeDto.getTaskInstanceId());
                    }
                //更新相关业务数据
                handelErrorResultForScript(message, agentTaskId, taskRuntimeId);
                //调用计数器方法，确认是否驱动下一批，或者任务完成
                taskRuntimeService.driverNextBatchAgent(taskRuntimeId);
            }else { // processed
                logger.info("taskRuntimeId {} has been processed, skipping.", taskRuntimeId);
            }
        }
    }

    private void handelErrorResultForScript(String message, Long agentTaskId, long taskRuntimeId) throws ScriptException {
        // 回更agent运行实例表（ieai_script_task_runtime）中taskId。将管理服务反馈的taskId存储到表中。
        TaskRuntimeDto taskRuntimeDto = new TaskRuntimeDto();
        taskRuntimeDto.setAgentTaskId(agentTaskId);

        taskRuntimeDto.setId(taskRuntimeId);
        taskRuntimeService.updateTaskRuntime(taskRuntimeDto);

        if (StringUtils.deleteWhitespace(Enums.ExpectionMessage.AGENT_ERROR_MESSAGE.getDescription()).equals(StringUtils.deleteWhitespace(message))) {
            TaskRuntimeDto dto = taskRuntimeService.selectTaskRuntimeById(taskRuntimeId);
            if (isBoolean(dto, dto.getId())) {

                int status = Enums.TaskRuntimeState.SCRIPT_FAIL_STATE.getValue();

                updateData(String.valueOf(taskRuntimeId), status);

                List<Integer> notInStates = new ArrayList<>();
                notInStates.add(Enums.TaskRuntimeState.TERMINATED.getValue());
                // 更新serverNum
                taskInstanceService.updateServerNum(dto.getTaskInstanceId(), notInStates);

                // 更新任务状态，设置为完成或者失败
                taskInstanceService.updateState(Enums.TaskInstanceStatus.EXCEPTION.getValue(), dto.getTaskInstanceId(), Enums.TaskInstanceStatus.PARTIAL_RUNNING.getValue());


            }

        }
    }

    private boolean isBoolean(TaskRuntimeDto dto, Long id) {
        return null != dto && null != id;
    }

    public void updateData(String bizId, int status) throws ScriptException {
        TaskRuntimeDto dto = taskRuntimeService.selectTaskRuntimeById(Long.parseLong(bizId));
        if (isBoolean(dto, dto.getId())) {
            taskRuntimeService.updateExecTimeAndState(bizId, status, dto);
        } else {
            logger.info("No data found in the ieai_script_task_runtime table based on the Id {}", bizId);
            throw new ScriptException("no.task.runtime.record.find");
        }

    }


}

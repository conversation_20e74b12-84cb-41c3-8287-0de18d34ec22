package com.ideal.script.model.dto;


import com.ideal.script.common.validation.ExecAuditCreate;
import com.ideal.script.dto.AttachmentDto;
import org.apache.commons.lang3.builder.ToStringBuilder;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.io.Serializable;
import java.util.List;

/**
 * 脚本任务提交审核Dto
 *
 * <AUTHOR>
 */
public class ScriptExecAuditDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 脚本附件集合
     */
    @NotNull
//    @NotNull(groups = {ExecAuditCreate.class}, message = "scriptTempAttachments 不能为空")
    @Valid
    private List<AttachmentDto> scriptTempAttachments;

    /**
     * 脚本版本id
     */
    @NotNull(groups = {ExecAuditCreate.class})
//    @NotNull(groups = {ExecAuditCreate.class}, message = "scriptInfoVersionId 不能为空")
//    @Digits(integer = 19, fraction = 0, groups = {ExecAuditCreate.class}, message = "scriptInfoVersionId 字段内容必须为数字")
//    @Min(value = 1000000000000000000L, groups = {ExecAuditCreate.class}, message = "scriptInfoVersionId 大小范围需要在 1000000000000000000 到 9223372036854775807 之间")
//    @Max(value = 9223372036854775807L, groups = {ExecAuditCreate.class}, message = "scriptInfoVersionId 大小范围需要在 1000000000000000000 到 9223372036854775807 之间")
    private Long scriptInfoVersionId;

    /**
     * 脚本的执行用户
     */
    @Size(max = 50,groups = {ExecAuditCreate.class})
//    @NotNull(groups = {ExecAuditCreate.class}, message = "execuser 不能为空")
//    @NotBlank(groups = {ExecAuditCreate.class}, message = "execuser 不能为空")
//    @Size(max = 50, groups = {ExecAuditCreate.class}, message = "execuser 字段长度超长")
    private String execuser;

    /**
     * 脚本任务绑定的已选服务器集合
     */
    @Valid
    private List<AgentInfoDto> agents;

    /**
     * 脚本任务执行参数
     */
    @NotNull(groups = {ExecAuditCreate.class})
//    @NotNull(groups = {ExecAuditCreate.class}, message = "params 不能为空")
    @Valid
    private List<ParameterDto> params;

    /**
     * 任务申请时，记录任务的任务名称、并发数量、执行用户、执行方式，调度策略等
     */
    @NotNull(groups = {ExecAuditCreate.class})
//    @NotNull(groups = {ExecAuditCreate.class}, message = "taskInfo 不能为空")
    @Valid
    private TaskDto taskInfo;

    /**
     * 审核人
     */
    @NotNull(groups = {ExecAuditCreate.class})
    @Size(min = 1, max = 50,groups = {ExecAuditCreate.class})
    private String auditUser;

    /**
     * 审核人Id
     */
    @NotNull(groups = {ExecAuditCreate.class})
    private Long auditUserId;

    /**
     * 任务申请时，绑定的服务器信息（包括服务器id、执行用户）
     */
//    @NotNull(groups = {ExecAuditCreate.class}, message = "chosedAgentUsers 不能为空")
//    @Size(min = 1, groups = {ExecAuditCreate.class}, message = "chosedAgentUsers 不能为空")
    @Valid
    private List<AgentInfoDto> chosedAgentUsers;

    /**
     * 服务器是否来自资源组
     */
    @NotNull(groups = {ExecAuditCreate.class})
//    @NotNull(groups = {ExecAuditCreate.class}, message = "resGroupFlag 不能为空")
//    @Size(min = 1, max = 5, groups = {ExecAuditCreate.class}, message = "resGroupFlag 字段长度超长")
//    @Pattern(regexp = "^[A-Za-z0-9]*$", groups = {ExecAuditCreate.class}, message = "resGroupFlag 字段不能含有特殊字符")
    private Boolean resGroupFlag;

    /**
     * 任务绑定的资源组集合
     */
    @Valid
    private List<TaskGroupsDto> chosedResGroups;

    /**
     * 服务器是否来自资源组
     */
    @NotNull(groups = {ExecAuditCreate.class})
    private int startType;

    /**
     * 工单编号
     */
    @Size(max = 50,groups = {ExecAuditCreate.class})
    private String workOrderNum;

    /**
     * invokeid：itsm发起需要传递
     */
    private String invokeId;

    /**
     * 手机号码，创建itsm工单编号使用
     */
    @Size(max = 20,groups = {ExecAuditCreate.class})
    private String telephone;

    /**
     * 先试点、后执行
     */
    private Boolean checkBefore;
    /**
     * 审核人id（常用任务使用）
     */
    private Long auditorId;

    /**
     * 调用方任务id
     */
    private Long callerTaskId;

    public Long getCallerTaskId() {
        return callerTaskId;
    }

    public void setCallerTaskId(Long callerTaskId) {
        this.callerTaskId = callerTaskId;
    }

    public Long getAuditorId() {
        return auditorId;
    }

    public void setAuditorId(Long auditorId) {
        this.auditorId = auditorId;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String getInvokeId() {
        return invokeId;
    }

    public void setInvokeId(String invokeId) {
        this.invokeId = invokeId;
    }

    public String getWorkOrderNum() {
        return workOrderNum;
    }

    public void setWorkOrderNum(String workOrderNum) {
        this.workOrderNum = workOrderNum;
    }

    public int getStartType() {
        return startType;
    }

    public void setStartType(int startType) {
        this.startType = startType;
    }

    public List<AttachmentDto> getScriptTempAttachments() {
        return scriptTempAttachments;
    }

    @SuppressWarnings("unused")
    public void setScriptTempAttachments(List<AttachmentDto> scriptTempAttachments) {
        this.scriptTempAttachments = scriptTempAttachments;
    }

    public Long getScriptInfoVersionId() {
        return scriptInfoVersionId;
    }

    @SuppressWarnings("unused")
    public void setScriptInfoVersionId(Long scriptInfoVersionId) {
        this.scriptInfoVersionId = scriptInfoVersionId;
    }

    public String getExecuser() {
        return execuser;
    }

    public void setExecuser(String execuser) {
        this.execuser = execuser;
    }

    public List<AgentInfoDto> getAgents() {
        return agents;
    }

    @SuppressWarnings("unused")
    public void setAgents(List<AgentInfoDto> agents) {
        this.agents = agents;
    }

    public List<ParameterDto> getParams() {
        return params;
    }

    @SuppressWarnings("unused")
    public void setParams(List<ParameterDto> params) {
        this.params = params;
    }

    public TaskDto getTaskInfo() {
        return taskInfo;
    }

    @SuppressWarnings("unused")
    public void setTaskInfo(TaskDto taskInfo) {
        this.taskInfo = taskInfo;
    }

    public String getAuditUser() {
        return auditUser;
    }

    @SuppressWarnings("unused")
    public void setAuditUser(String auditUser) {
        this.auditUser = auditUser;
    }

    public Long getAuditUserId() {
        return auditUserId;
    }

    @SuppressWarnings("unused")
    public void setAuditUserId(Long auditUserId) {
        this.auditUserId = auditUserId;
    }

    public List<AgentInfoDto> getChosedAgentUsers() {
        return chosedAgentUsers;
    }

    @SuppressWarnings("unused")
    public void setChosedAgentUsers(List<AgentInfoDto> chosedAgentUsers) {
        this.chosedAgentUsers = chosedAgentUsers;
    }

    public Boolean getResGroupFlag() {
        return resGroupFlag;
    }

    @SuppressWarnings("unused")
    public void setResGroupFlag(Boolean resGroupFlag) {
        this.resGroupFlag = resGroupFlag;
    }

    public List<TaskGroupsDto> getChosedResGroups() {
        return chosedResGroups;
    }

    @SuppressWarnings("unused")
    public void setChosedResGroups(List<TaskGroupsDto> chosedResGroups) {
        this.chosedResGroups = chosedResGroups;
    }

    public Boolean getCheckBefore() {
        return checkBefore;
    }

    public void setCheckBefore(Boolean checkBefore) {
        this.checkBefore = checkBefore;
    }
    @Override
    public String toString() {
        return new ToStringBuilder(this)
                .append("scriptTempAttachments", getScriptTempAttachments())
                .append("scriptInfoVersionId", getScriptInfoVersionId())
                .append("execuser", getExecuser())
                .append("agents", getAgents())
                .append("params", getParams())
                .append("taskInfo", getTaskInfo())
                .append("auditUser", getAuditUser())
                .append("chosedAgentUsers", getChosedAgentUsers())
                .append("resGroupFlag", getResGroupFlag())
                .append("chosedResGroups", getChosedResGroups())
                .append("checkBefore", getCheckBefore())
                .toString();
    }
}

package com.ideal.script.model.dto;

import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * 任务申请对象
 * <AUTHOR>
 */
public class TaskApplyDto implements Serializable {

    //======================脚本任务主表属性开始==================
    /**
     * 脚本服务化基础信息表主键
     */
    private Long scriptInfoId;

    /**
     * 脚本服务化基础信息表唯一uuid
     */
    private String uniqueUuid;

    /**
     * 脚本中文名称
     */
    private String scriptNameZh;

    /**
     * 脚本名称
     */
    private String scriptName;

    /**
     * 脚本类型(shell、bat、perl、python、powershell)
     */
    private String scriptType;

    /**
     * 脚本的执行用户
     */
    private String execuser;

    /**
     * 标签
     */
    private String scriptLabel;

    /**
     * 分类id
     */
    private Long categoryId;

    /**
     * 分类名称
     */
    private String scriptCategoryName;


    /**
     * 风险级别  0 - 白名单  1 - 高级风险  2 - 中级风险  3 - 低级风险
     */
    private Integer level;


    /**
     * 适用平台  数据来源于ieai_platform_code表
     */
    private String platform;

    //======================脚本任务主表属性结束==================

    // =====================版本属性开始==========================
    /**
     * 脚本版本表主键
     */
    private Long scriptInfoVersionId;

    /**
     * 每个版本的uuid
     */
    private String srcScriptUuid;


    /**
     * 版本号
     */
    private String version;

    /**
     * 版本表修改时间
     */
    private Timestamp updateTime;


    // =========================版本属性结束========================



    /**
     * 创建人id
     */
    private Long creatorId;


    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private Timestamp createTime;


    /**
     * 是否走角色权限查询
     */
    private boolean roleFlag = false;

    public boolean isRoleFlag() {
        return roleFlag;
    }

    public void setRoleFlag(boolean roleFlag) {
        this.roleFlag = roleFlag;
    }


    public Long getScriptInfoId() {
        return scriptInfoId;
    }

    @SuppressWarnings("unused")
    public void setScriptInfoId(Long scriptInfoId) {
        this.scriptInfoId = scriptInfoId;
    }

    public String getUniqueUuid() {
        return uniqueUuid;
    }

    public void setUniqueUuid(String uniqueUuid) {
        this.uniqueUuid = uniqueUuid;
    }

    public String getScriptNameZh() {
        return scriptNameZh;
    }

    public void setScriptNameZh(String scriptNameZh) {
        this.scriptNameZh = scriptNameZh;
    }

    public String getScriptName() {
        return scriptName;
    }

    public void setScriptName(String scriptName) {
        this.scriptName = scriptName;
    }

    public String getScriptType() {
        return scriptType;
    }

    public void setScriptType(String scriptType) {
        this.scriptType = scriptType;
    }

    public String getExecuser() {
        return execuser;
    }

    public void setExecuser(String execuser) {
        this.execuser = execuser;
    }

    public String getScriptLabel() {
        return scriptLabel;
    }

    @SuppressWarnings("unused")
    public void setScriptLabel(String scriptLabel) {
        this.scriptLabel = scriptLabel;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public Long getScriptInfoVersionId() {
        return scriptInfoVersionId;
    }

    @SuppressWarnings("unused")
    public void setScriptInfoVersionId(Long scriptInfoVersionId) {
        this.scriptInfoVersionId = scriptInfoVersionId;
    }

    public String getSrcScriptUuid() {
        return srcScriptUuid;
    }

    public void setSrcScriptUuid(String srcScriptUuid) {
        this.srcScriptUuid = srcScriptUuid;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public Timestamp getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }

    @SuppressWarnings("unused")
    public String getScriptCategoryName() {
        return scriptCategoryName;
    }

    @SuppressWarnings("unused")
    public void setScriptCategoryName(String scriptCategoryName) {
        this.scriptCategoryName = scriptCategoryName;
    }

    public Timestamp getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Timestamp updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this)
                .append("scriptInfoId", getScriptInfoId())
                .append("uniqueUuid", getUniqueUuid())
                .append("scriptNameZh", getScriptNameZh())
                .append("scriptName", getScriptName())
                .append("scriptType", getScriptType())
                .append("execuser", getExecuser())
                .append("scriptLabel", getScriptLabel())
                .append("categoryId", getCategoryId())
                .append("scriptCategoryName", getScriptCategoryName())
                .append("level", getLevel())
                .append("platform", getPlatform())
                .append("scriptInfoVersionId", getScriptInfoVersionId())
                .append("srcScriptUuid", getSrcScriptUuid())
                .append("version", getVersion())
                .append("creatorId", getCreatorId())
                .append("creatorName", getCreatorName())
                .append("createTime", getCreateTime())
                .append("updateTime",getUpdateTime())
                .append("roleFlag",isRoleFlag())
                .toString();
    }
}

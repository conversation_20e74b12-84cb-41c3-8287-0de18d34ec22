package com.ideal.script.controller;

import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.sc.util.CurrentUserUtil;
import com.ideal.sc.util.ValidationUtils;
import com.ideal.script.common.constant.Constants;
import com.ideal.script.common.constant.permission.MenuPermitConstant;
import com.ideal.script.common.constant.permission.MyScriptBtnPermitConstant;
import com.ideal.script.model.dto.ScriptVersionShareDto;
import com.ideal.script.model.dto.interaction.agent.UserInfoDto;
import com.ideal.script.service.IScriptVersionShareService;
import com.ideal.system.common.component.aop.MethodPermission;
import com.ideal.system.common.component.model.CurrentUser;
import com.ideal.system.dto.RoleApiDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 【脚本共享】Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("${app.script-tools-url:}/scriptShare")
@MethodPermission(MenuPermitConstant.MY_SCRIPT_PER)
public class ScriptVersionShareController
{
    private final IScriptVersionShareService scriptVersionShareService;
    private static final Logger logger = LoggerFactory.getLogger(ScriptVersionShareController.class);


    public ScriptVersionShareController(IScriptVersionShareService scriptVersionShareService){
       this.scriptVersionShareService=scriptVersionShareService;
    }

    /**
     * 新增
     */
    @PostMapping("/saveScriptVersionShare")
    @MethodPermission(MyScriptBtnPermitConstant.SCRIPT_SHARE_PER)
    public R<Object> saveInfoVersion(@RequestBody List<ScriptVersionShareDto> scriptVersionShareDtoList)
    {
        try {
            scriptVersionShareService.insertScriptVersionShare(scriptVersionShareDtoList);
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, "", "save.success");
        } catch (Exception e) {
            logger.error("saveInfoVersion error",e);
            return ValidationUtils.customFailResult("saveInfoVersion",e.getMessage());
        }
    }

    /**
     * 获取共享数据
     * @param tableQueryDto 参数
     * @return 共享数据
     */
    @PostMapping("/selectShareScriptData")
    public R<PageInfo<ScriptVersionShareDto>> selectShareScriptData(@RequestBody TableQueryDto<ScriptVersionShareDto> tableQueryDto)
    {
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, scriptVersionShareService.selectShareScriptData(tableQueryDto.getQueryParam(), tableQueryDto.getPageNum(),
                tableQueryDto.getPageSize()), Constants.LIST_SUCCESS);
    }

    /**
     * 获取共享用户
     * @param tableQueryDto 参数
     * @return 共享用户信息
     */
    @PostMapping("/getShareUser")
    public R<PageInfo<UserInfoDto>> getShareUser(@RequestBody TableQueryDto<ScriptVersionShareDto> tableQueryDto)
    {
        CurrentUser user = CurrentUserUtil.getCurrentUser();
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, scriptVersionShareService.getShareUser(user.getId(), tableQueryDto.getPageNum(),
                tableQueryDto.getPageSize(),tableQueryDto.getQueryParam()), Constants.LIST_SUCCESS);
    }

    /**
     * 删除
     */
	@GetMapping("/deleteScriptVersionShare")
    @MethodPermission(MyScriptBtnPermitConstant.SCRIPT_SHARE_PER)
    public R<Object> removeInfoVersion(@RequestParam(value = "ids") Long[] ids)
    {
        scriptVersionShareService.deleteScriptVersionShareByIds(ids);
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, "remove.success");
    }

    /**
     * 获取未共享的角色列表
     * @param tableQueryDto 查询参数
     * @return 未共享角色列表
     */
    @PostMapping("/getNotShareRoles")
    public R<PageInfo<RoleApiDto>> getNotShareRoles(@RequestBody TableQueryDto<ScriptVersionShareDto> tableQueryDto)
    {
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, scriptVersionShareService.getNotShareRoles(tableQueryDto.getPageNum(),
                tableQueryDto.getPageSize(),tableQueryDto.getQueryParam()), Constants.LIST_SUCCESS);
    }

}

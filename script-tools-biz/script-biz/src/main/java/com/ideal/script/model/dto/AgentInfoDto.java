package com.ideal.script.model.dto;

import com.ideal.script.common.validation.ExecAuditCreate;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;


/**
 * Agent基本信息-冗余平台管理对象 ieai_script_agent_info
 *
 * <AUTHOR>
 */
public class AgentInfoDto implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 主键
     */
    private Long id;


    /**
     * ieai_sysm_agent_info表主键id
     */
    @NotNull(groups = ExecAuditCreate.class)
//    @NotNull(groups = ExecAuditCreate.class, message = "sysmAgentInfoId 不能为空")
//    @Digits(integer = 19, fraction = 0, groups = ExecAuditCreate.class, message = "sysmAgentInfoId 字段内容必须为数字")
//    @Min(value = 1000000000000000000L, groups = ExecAuditCreate.class, message = "sysmAgentInfoId 长度范围需要在 1000000000000000000 到 9223372036854775807 之间")
//    @Max(value = 9223372036854775807L, groups = ExecAuditCreate.class, message = "sysmAgentInfoId 长度范围需要在 1000000000000000000 到 9223372036854775807 之间")
    private Long sysmAgentInfoId;


    /**
     * ip
     */
    @NotNull(groups = ExecAuditCreate.class)
    @Size(min = 1, max = 30, groups = {ExecAuditCreate.class})
//    @NotNull(groups = ExecAuditCreate.class, message = "agentIp 不能为空")
//    @Size(min = 1, max = 30, groups = {ExecAuditCreate.class}, message = "agentIp 字段长度应在 1 到 30 之间")
    private String agentIp;


    /**
     * agent名称
     */
    private String agentName;


    /**
     * 端口
     */
    @NotNull(groups = ExecAuditCreate.class)
//    @NotNull(groups = ExecAuditCreate.class, message = "agentPort 不能为空")
//    @Digits(integer = 10, fraction = 0, groups = ExecAuditCreate.class, message = "agentPort 字段内容必须为数字")
//    @Min(value = 0, groups = ExecAuditCreate.class, message = "agentPort 大小范围需要在 0 到 2147483647 之间")
//    @Max(value = 2147483647, groups = ExecAuditCreate.class, message = "agentPort 大小范围需要在 0 到 2147483647 之间")
    private Integer agentPort;


    /**
     * 状态
     */
    private Integer agentState;


    /**
     * 创建时间
     */
    private Timestamp createTime;


    /**
     * agent的执行用户
     */
//    @NotNull(groups = ExecAuditCreate.class, message = "execUserName 不能为空")
//    @NotBlank(groups = ExecAuditCreate.class, message = "execUserName 不能为空")
//    @Size(min = 1, max = 20, groups = ExecAuditCreate.class, message = "execUserName 字段长度应在 1 到 20 之间")
    private String execUserName;


    /**
     * 设备名称
     */
    private String deviceName;


    /**
     * 设备操作系统
     */
    private String osName;

    /**
     * 脚本任务Id
     */
    private Long taskIpsId;
    /**
     * 数据中心id
     */
    private Long centerId;

    /**
     * 数据中心名称
     */
    private String centerName;

    /**
     * 业务系统名称
     */
    private List<String> businessSystemNameList;


    public Long getCenterId() {
        return centerId;
    }

    public void setCenterId(Long centerId) {
        this.centerId = centerId;
    }

    public String getCenterName() {
        return centerName;
    }

    public void setCenterName(String centerName) {
        this.centerName = centerName;
    }

    public List<String> getBusinessSystemNameList() {
        return businessSystemNameList;
    }

    public void setBusinessSystemNameList(List<String> businessSystemNameList) {
        this.businessSystemNameList = businessSystemNameList;
    }

    public Long getTaskIpsId() {
        return taskIpsId;
    }

    public void setTaskIpsId(Long taskIpsId) {
        this.taskIpsId = taskIpsId;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    @SuppressWarnings("unused")
    public void setSysmAgentInfoId(Long sysmAgentInfoId) {
        this.sysmAgentInfoId = sysmAgentInfoId;
    }

    public Long getSysmAgentInfoId() {
        return sysmAgentInfoId;
    }

    public void setAgentIp(String agentIp) {
        this.agentIp = agentIp;
    }

    public String getAgentIp() {
        return agentIp;
    }

    public void setAgentName(String agentName) {
        this.agentName = agentName;
    }

    public String getAgentName() {
        return agentName;
    }

    public void setAgentPort(Integer agentPort) {
        this.agentPort = agentPort;
    }

    public Integer getAgentPort() {
        return agentPort;
    }

    @SuppressWarnings("unused")
    public void setAgentState(Integer agentState) {
        this.agentState = agentState;
    }

    public Integer getAgentState() {
        return agentState;
    }

    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }

    public Timestamp getCreateTime() {
        return createTime;
    }

    @SuppressWarnings("unused")
    public void setExecUserName(String execUserName) {
        this.execUserName = execUserName;
    }

    public String getExecUserName() {
        return execUserName;
    }

    @SuppressWarnings("unused")
    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public String getDeviceName() {
        return deviceName;
    }

    @SuppressWarnings("unused")
    public void setOsName(String osName) {
        this.osName = osName;
    }

    public String getOsName() {
        return osName;
    }

    @Override
    public String toString() {
        return "AgentInfoDto{" +
                "id=" + id +
                ", sysmAgentInfoId=" + sysmAgentInfoId +
                ", agentIp='" + agentIp + '\'' +
                ", agentName='" + agentName + '\'' +
                ", agentPort=" + agentPort +
                ", agentState=" + agentState +
                ", createTime=" + createTime +
                ", execUserName='" + execUserName + '\'' +
                ", deviceName='" + deviceName + '\'' +
                ", osName='" + osName + '\'' +
                ", taskIpsId=" + taskIpsId +
                ", centerId=" + centerId +
                ", centerName='" + centerName + '\'' +
                ", businessSystemNameList=" + businessSystemNameList +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        AgentInfoDto that = (AgentInfoDto) o;

        if (!agentIp.equals(that.agentIp)) {
            return false;
        }
        return agentPort.equals(that.agentPort);
    }

    @Override
    public int hashCode() {
        int result = agentIp.hashCode();
        result = 31 * result + agentPort.hashCode();
        return result;
    }
}

package com.ideal.script.api;

import com.ideal.script.dto.*;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.service.ITaskApplyService;
import com.ideal.script.service.ITaskExecuteService;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.Map;

/**
 * 获取脚本信息
 *
 * <AUTHOR>
 */
@Service
@Primary
public class ScriptTaskApi implements IScriptTask {

    private final ITaskApplyService taskApplyService;
    private final ITaskExecuteService taskExecuteService;

    public ScriptTaskApi(ITaskApplyService taskApplyService,ITaskExecuteService taskExecuteService) {
        this.taskApplyService = taskApplyService;
        this.taskExecuteService = taskExecuteService;
    }

    /**
     * dubbo接口，任务申请接口
     *
     * @param scriptTaskApplyDto 任务申请信息
     * @return Map<String,String>
     * @see IScriptTask#scriptTaskApply(ScriptTaskApplyApiDto)
     */
    @Override
    public Long scriptTaskApply(ScriptTaskApplyApiDto scriptTaskApplyDto) throws ScriptException {
        return taskApplyService.scriptTaskApplyApi(scriptTaskApplyDto);
    }

    /**
     * dubbo接口，执行任务接口
     *
     * @param taskStartApiDto 任务启动信息
     * @return Map<String,String>
     * @see IScriptTask#startScriptTask(TaskStartApiDto)
     */
    @Override
    public Map<String,String> startScriptTask(TaskStartApiDto taskStartApiDto) {
        return taskApplyService.startScriptTask(taskStartApiDto);
    }

    /**
     * 终止脚本服务化任务
     *
     * @param taskInstanceId 任务实例Id
     * @see IScriptTask#stopScriptTaskByTaskInstanceId(List)
     */
    @Override
    public StopScriptTasksApiDto stopScriptTaskByTaskInstanceId(List<Long> taskInstanceId) throws ScriptException {
        return taskExecuteService.stopScriptTaskByTaskInstanceId(taskInstanceId);
    }

    /**
     * 终止其它模块脚本任务
     * @param stopCallerScriptTaskApiDtos 参数
     * @see IScriptTask#stopScriptTaskByCallerTaskIdAndAgent(List)
     */
    @Override
    public List<String> stopScriptTaskByCallerTaskIdAndAgent(List<RetryScriptInstanceApiDto> stopCallerScriptTaskApiDtos) throws ScriptException {
        return taskExecuteService.stopScriptTaskByCallerTaskIdAndAgent(stopCallerScriptTaskApiDtos);
    }

    /**
     * 重试脚本任务
     * @param retryScriptInstanceApiDto 重试任务参数
     * @see IScriptTask#reTryScriptTask(List)
     */
    @Override
    public void reTryScriptTask(List<RetryScriptInstanceApiDto> retryScriptInstanceApiDto) throws ScriptException {
        taskApplyService.reTryScriptTask(retryScriptInstanceApiDto);
    }

}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.script.mapper.TaskMapper">

    <resultMap type="com.ideal.script.model.entity.Task" id="TaskResult">
            <result property="id" column="iid"/>
            <result property="srcScriptUuid" column="isrc_script_uuid"/>
            <result property="taskName" column="itask_name"/>
            <result property="eachNum" column="ieach_num"/>
            <result property="execUser" column="iexec_user"/>
            <result property="taskScheduler" column="itask_scheduler"/>
            <result property="taskTime" column="itask_time"/>
            <result property="taskCron" column="itask_cron"/>
            <result property="publishDesc" column="ipublish_desc"/>
            <result property="startUser" column="istart_user"/>
            <result property="startTime" column="istart_time"/>
            <result property="timeout" column="itimeout"/>
            <result property="taskTimeForDispaly" column="itask_time_for_dispaly"/>
            <result property="type" column="itype"/>
            <result property="driveMode" column="idrive_mode"/>
            <result property="startType" column="istart_type"/>
            <result property="readyToExecute" column="iready_to_execute"/>
            <result property="creatorId" column="icreator_id"/>
            <result property="creatorName" column="icreator_name"/>
            <result property="updatorId" column="iupdator_id"/>
            <result property="updatorName" column="iupdator_name"/>
            <result property="createTime" column="icreate_time"/>
            <result property="updateTime" column="iupdate_time"/>
            <result property="scriptTaskSource" column="iscript_task_source"/>
            <result property="auditRelationId" column="iaudit_relation_Id"/>
    </resultMap>

    <sql id="selectTaskVo">
        select iid, isrc_script_uuid, itask_name, ieach_num, iexec_user, itask_scheduler, itask_time, itask_cron, ipublish_desc, istart_user, istart_time, itimeout, itask_time_for_dispaly, itype, idrive_mode, istart_type, iready_to_execute,icreator_id, icreator_name, iupdator_id, iupdator_name, icreate_time, iupdate_time,iscript_task_source
        from ieai_script_task
    </sql>

    <select id="selectTaskList" parameterType="com.ideal.script.model.entity.Task" resultMap="TaskResult">
         SELECT
            a.iid,
            a.isrc_script_uuid,
            a.itask_name,
            a.ieach_num,
            a.iexec_user,
            a.itask_scheduler,
            a.itask_time,
            a.itask_cron,
            a.ipublish_desc,
            a.istart_user,
            a.istart_time,
            a.itimeout,
            a.itask_time_for_dispaly,
            a.itype,
            a.idrive_mode,
            a.istart_type,
            a.iready_to_execute,
            a.icreator_id,
            a.icreator_name,
            a.iupdator_id,
            a.iupdator_name,
            a.icreate_time,
            a.iupdate_time,
            a.iscript_task_source,
            b.iid as iaudit_relation_id
        FROM
            ieai_script_task a,
            ieai_script_audit_relation b
        WHERE
            a.iid = b.iscript_task_id

            <if test="srcScriptUuid != null  and srcScriptUuid != ''">
                and a.isrc_script_uuid = #{srcScriptUuid}
            </if>
            <if test="taskName != null  and taskName != ''">
                <bind name="taskNameLike" value="'%' + taskName + '%'"/>
                and a.itask_name like #{taskNameLike}
            </if>
            <if test="eachNum != null ">
                and a.ieach_num = #{eachNum}
            </if>
            <if test="execUser != null  and execUser != ''">
                and a.iexec_user = #{execUser}
            </if>
            <if test="taskScheduler != null ">
                and a.itask_scheduler = #{taskScheduler}
            </if>
            <if test="taskTime != null ">
                and a.itask_time = #{taskTime}
            </if>
            <if test="taskCron != null  and taskCron != ''">
                and a.itask_cron = #{taskCron}
            </if>
            <if test="publishDesc != null  and publishDesc != ''">
                and a.ipublish_desc = #{publishDesc}
            </if>
            <if test="startUser != null  and startUser != ''">
                and a.istart_user = #{startUser}
            </if>
            <if test="startTime != null ">
                and a.istart_time = #{startTime}
            </if>
            <if test="timeout != null ">
                and a.itimeout = #{timeout}
            </if>
            <if test="taskTimeForDispaly != null  and taskTimeForDispaly != ''">
                and a.itask_time_for_dispaly = #{taskTimeForDispaly}
            </if>
            <if test="type != null ">
                and a.itype = #{type}
            </if>
            <if test="driveMode != null ">
                and a.idrive_mode = #{driveMode}
            </if>
            <if test="startType != null ">
                and a.istart_type = #{startType}
            </if>
            <if test="readyToExecute != null ">
                and a.iready_to_execute = #{readyToExecute}
            </if>
            <if test="creatorId != null ">
                and a.icreator_id = #{creatorId}
            </if>
            <if test="creatorName != null  and creatorName != ''">
                <bind name="creatorNameLike" value="'%' + creatorName + '%'"/>
                and a.icreator_name like #{creatorNameLike}
            </if>
            <if test="updatorId != null ">
                and a.iupdator_id = #{updatorId}
            </if>
            <if test="scriptTaskSource != null ">
                and a.iscript_task_source = #{scriptTaskSource}
            </if>

    </select>

    <select id="selectTaskById" parameterType="Long"
            resultMap="TaskResult">
            <include refid="selectTaskVo"/>
            where iid = #{id}
    </select>

    <select id="selectTaskByRuntimeId" parameterType="Long"
            resultMap="TaskResult">
        <include refid="selectTaskVo"/>
        where iid =(select iscript_task_id from ieai_script_task_runtime where iid = #{runtimeId})
    </select>

    <insert id="insertTask" parameterType="com.ideal.script.model.entity.Task" useGeneratedKeys="true"
            keyProperty="id">
        insert into ieai_script_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">
                        iid,
                    </if>
                    <if test="srcScriptUuid != null">isrc_script_uuid,
                    </if>
                    <if test="taskName != null">itask_name,
                    </if>
                    <if test="eachNum != null">ieach_num,
                    </if>
                    <if test="execUser != null">iexec_user,
                    </if>
                    <if test="taskScheduler != null">itask_scheduler,
                    </if>
                    <if test="taskTime != null">itask_time,
                    </if>
                    <if test="taskCron != null">itask_cron,
                    </if>
                    <if test="publishDesc != null">ipublish_desc,
                    </if>
                    <if test="startUser != null and startUser != ''">istart_user,
                    </if>
                    <if test="timeout != null">itimeout,
                    </if>
                    <if test="taskTimeForDispaly != null">itask_time_for_dispaly,
                    </if>
                    <if test="type != null">itype,
                    </if>
                    <if test="driveMode != null">idrive_mode,
                    </if>
                    <if test="startType != null">istart_type,
                    </if>
                    <if test="readyToExecute != null">iready_to_execute,
                    </if>
                    <if test="creatorId != null">icreator_id,
                    </if>
                    <if test="creatorName != null">icreator_name,
                    </if>
                    <if test="updatorId != null">iupdator_id,
                    </if>
                    <if test="updatorName != null">iupdator_name,
                    </if>
                    icreate_time,
                    iupdate_time,
                    <if test="scriptTaskSource != null">iscript_task_source,
                    </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">
                        #{id},
                    </if>
                    <if test="srcScriptUuid != null">#{srcScriptUuid},
                    </if>
                    <if test="taskName != null">#{taskName},
                    </if>
                    <if test="eachNum != null">#{eachNum},
                    </if>
                    <if test="execUser != null">#{execUser},
                    </if>
                    <if test="taskScheduler != null">#{taskScheduler},
                    </if>
                    <if test="taskTime != null">#{taskTime},
                    </if>
                    <if test="taskCron != null">#{taskCron},
                    </if>
                    <if test="publishDesc != null">#{publishDesc},
                    </if>
                    <if test="startUser != null and startUser != ''">#{startUser},
                    </if>
                    <if test="timeout != null">#{timeout},
                    </if>
                    <if test="taskTimeForDispaly != null">#{taskTimeForDispaly},
                    </if>
                    <if test="type != null">#{type},
                    </if>
                    <if test="driveMode != null">#{driveMode},
                    </if>
                    <if test="startType != null">#{startType},
                    </if>
                    <if test="readyToExecute != null">#{readyToExecute},
                    </if>
                    <if test="creatorId != null">#{creatorId},
                    </if>
                    <if test="creatorName != null">#{creatorName},
                    </if>
                    <if test="updatorId != null">#{updatorId},
                    </if>
                    <if test="updatorName != null">#{updatorName},
                    </if>
                    ${@com.ideal.common.util.DbUtils@getCurrentTime()},
                    ${@com.ideal.common.util.DbUtils@getCurrentTime()},
                    <if test="scriptTaskSource != null">#{scriptTaskSource},
                    </if>
        </trim>
    </insert>

    <update id="updateTask" parameterType="com.ideal.script.model.entity.Task">
        update ieai_script_task
        <trim prefix="SET" suffixOverrides=",">
                    <if test="srcScriptUuid != null">isrc_script_uuid =
                        #{srcScriptUuid},
                    </if>
                    <if test="taskName != null">itask_name =
                        #{taskName},
                    </if>
                    <if test="eachNum != null">ieach_num =
                        #{eachNum},
                    </if>
                    <if test="execUser != null">iexec_user =
                        #{execUser},
                    </if>
                    <if test="taskScheduler != null">itask_scheduler =
                        #{taskScheduler},
                    </if>
                    <if test="taskTime != null">itask_time =
                        #{taskTime},
                    </if>
                    <if test="taskCron != null">itask_cron =
                        #{taskCron},
                    </if>
                    <if test="publishDesc != null">ipublish_desc =
                        #{publishDesc},
                    </if>
                    <if test="startUser != null and startUser != ''">istart_user =
                        #{startUser},
                    </if>
                    istart_time =${@com.ideal.common.util.DbUtils@getCurrentTime()},
                    <if test="timeout != null">itimeout =
                        #{timeout},
                    </if>
                    <if test="taskTimeForDispaly != null">itask_time_for_dispaly =
                        #{taskTimeForDispaly},
                    </if>
                    <if test="type != null">itype =
                        #{type},
                    </if>
                    <if test="driveMode != null">idrive_mode =
                        #{driveMode},
                    </if>
                    <if test="startType != null">istart_type =
                        #{startType},
                    </if>
                    <if test="readyToExecute != null">iready_to_execute =
                        #{readyToExecute},
                    </if>
                    <if test="creatorId != null">icreator_id =
                        #{creatorId},
                    </if>
                    <if test="creatorName != null">icreator_name =
                        #{creatorName},
                    </if>
                    <if test="updatorId != null">iupdator_id =
                        #{updatorId},
                    </if>
                    <if test="updatorName != null">iupdator_name =
                        #{updatorName},
                    </if>
                    iupdate_time = ${@com.ideal.common.util.DbUtils@getCurrentTime()},
                     <if test="scriptTaskSource != null">iscript_task_source =
                        #{scriptTaskSource},
                    </if>
        </trim>
        where iid = #{id}
    </update>

    <delete id="deleteTaskById" parameterType="Long">
        delete
        from ieai_script_task where iid = #{id}
    </delete>

    <delete id="deleteTaskByIds" parameterType="String">
        delete from ieai_script_task where iid in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!--待执行任务列表-->
      <resultMap type="com.ideal.script.model.bean.TaskExecuteBean" id="TaskExecuteResult">
        <result property="scriptTaskId" column="iscript_task_id"/>
        <result property="taskName" column="itask_name"/>
        <result property="eachNum" column="ieach_num"/>
        <result property="execUser" column="iexec_user"/>
        <result property="taskScheduler" column="itask_scheduler"/>
        <result property="taskTime" column="itask_time"/>
        <result property="taskCron" column="itask_cron"/>
        <result property="publishDesc" column="ipublish_desc"/>
        <result property="startUser" column="istart_user"/>
        <result property="startTime" column="istart_time"/>
        <result property="timeout" column="itimeout"/>
        <result property="taskTimeForDispaly" column="itask_time_for_dispaly"/>
        <result property="type" column="itype"/>
        <result property="driveMode" column="idrive_mode"/>
        <result property="startType" column="istart_type"/>
        <result property="readyToExecute" column="iready_to_execute"/>
        <result property="creatorId" column="icreator_id"/>
        <result property="creatorName" column="icreator_name"/>
        <result property="updatorId" column="iupdator_id"/>
        <result property="updatorName" column="iupdator_name"/>
        <result property="createTime" column="icreate_time"/>
        <result property="updateTime" column="iupdate_time"/>

        <result property="iscriptAuditRelationId" column="iscript_audit_relation_id"/>
        <result property="apprWorkitemId" column="iappr_workitem_id"/>
        <result property="srcScriptUuid" column="isrc_script_uuid"/>
        <result property="auditType" column="iaudit_type"/>
        <result property="state" column="istate"/>
        <result property="applyTime" column="iapply_time"/>
        <result property="auditTime" column="iaudit_time"/>
        <result property="auditUser" column="iaudit_user"/>
        <result property="applyUser" column="iapply_user"/>
        <result property="auditUserId" column="iaudit_user_id"/>
        <result property="applyUserId" column="iapply_user_id"/>
        <result property="scriptName" column="iscript_name"/>
        <result property="scriptNameZh" column="iscript_name_zh"/>

        <result property="scriptTaskInstanceId" column="iscript_task_instance_id"/>
        <result  property="categoryId" column="icategory_id"/>
        <result property="scriptTaskInstanceState" column="iscript_task_instance_state"/>
        <result property="runAgentCount" column="run_agent_count"/>
        <result property="level" column="ilevel"/>
        <result property="scriptTaskSource" column="iscript_task_source"/>
        <result property="version" column="iversion"/>
        <result property="endTime" column="iend_time" />
        <result property="currentTime" column="currentTime" />

        <result property="workOrderNumber" column="iwork_order_number"/>
    </resultMap>

    <select id="selectTaskByServiceId"  parameterType="Long" resultMap="TaskResult">
        <include refid="selectTaskVo"/>
        <where>
            <if test="serviceId != null">
                iid =(
                    SELECT
                    iscript_task_id
                    FROM
                    ieai_script_audit_relation
                    WHERE
                    iid =  #{serviceId}
                )
            </if>
            <if test="serviceId == null and taskId != null">
                iid = #{taskId}
            </if>
        </where>

    </select>

    <select id="getStartTypeByTaskInstanceId"  parameterType="Long" resultType="java.lang.Integer">
        select istart_type from ieai_script_task_instance where iid = #{taskInstanceId}
    </select>

    <select id="selectTask" resultMap="TaskExecuteResult">
        select
        <!-- 个性化字段 start-->
        <if test="mode == 'pend'">
            <!--任务监控未运行 个性化字段-->
            r.iid as iscript_audit_relation_id,
            0 as iscript_task_instance_state,
            r.iwork_order_number,
        </if>
        <if test="mode == 'timetask'">
            <!--定时任务管理 个性化字段-->
            t.itask_cron,
            t.itask_time,
            case t.iready_to_execute
            when 1 then 1
            when 2 then 2
            when 3 then 1
            end as iready_to_execute,
        </if>
        <if test="mode == 'running'">
            <!--运行中-任务申请 个性化字段-->
            ins.istart_time,
            ${@com.ideal.common.util.DbUtils@getCurrentTime()} as currentTime,
            r.iaudit_time,
            t.istart_user,
            ins.istatus as iscript_task_instance_state,
            ins.iid as iscript_task_instance_id,
            r.iwork_order_number,
        </if>
        <if test="mode == 'running-test'">
            <!--运行中-测试 个性化字段-->
            ins.istart_time,
            ${@com.ideal.common.util.DbUtils@getCurrentTime()} as currentTime,
            r.iaudit_time,
            t.istart_user,
            ins.istatus as iscript_task_instance_state,
            ins.iid as iscript_task_instance_id,
        </if>
        <if test="mode == 'finish'">
            <!--执行历史-任务申请 个性化字段-->
            ins.istart_time,
            ins.iend_time,
            ${@com.ideal.common.util.DbUtils@getCurrentTime()} as currentTime,
            r.iaudit_time,
            t.istart_user,
            t.istart_type,
            ins.istatus as iscript_task_instance_state,
            ins.iid as iscript_task_instance_id,
        </if>
        <if test="mode == 'finish-test'">
            <!--执行历史-测试 个性化字段-->
            ins.istart_time,
            ins.iend_time,
            ${@com.ideal.common.util.DbUtils@getCurrentTime()} as currentTime,
            r.iaudit_time,
            t.istart_user,
            ins.istatus as iscript_task_instance_state,
            ins.iid as iscript_task_instance_id,
        </if>
        <!-- 个性化字段 start-->

        <!-- 通用字段 start-->
        t.iid as iscript_task_id,
        t.itask_name,
        t.itype,
        t.idrive_mode,
        t.itask_scheduler,
        r.iaudit_time,
        t.icreator_name as iapply_user,
        base.*,
        t.ieach_num,
        (select count(cc.iid) from ieai_script_task_runtime cc where (cc.istate = 10 or cc.istate = 11) and
        cc.iscript_task_id = t.iid ) as run_agent_count
        <!-- 通用字段 end-->

        from ieai_script_task t
        left join ieai_script_audit_relation r on t.iid = r.iscript_task_id,
        <if test="mode != 'pend' and mode != 'timetask'">
            ieai_script_task_instance ins,
        </if>
        (select info.isys_org_code,
        ver.isrc_script_uuid,
        ver.ilevel,
        ver.iversion,
        info.iscript_name_zh,
        info.iscript_name,
        info.icategory_id,
        info.icategory_path
        from ieai_script_info info,
        ieai_script_info_version ver
        where ver.iinfo_unique_uuid = info.iunique_uuid and info.iscript_source = 0) base
        where base.isrc_script_uuid = t.isrc_script_uuid
        <if test="taskExecuteBean.scriptTaskId != null and taskExecuteBean.scriptTaskId != ''">
            AND t.iid = ins.iscript_task_id
            AND t.iid = #{taskExecuteBean.scriptTaskId}
        </if>

        <if test="taskExecuteBean.scriptTaskId == null or taskExecuteBean.scriptTaskId == ''">
            <!-- 通用查询条件 start-->
            <if test="mode != 'pend' and mode != 'timetask'">
                and t.iid = ins.iscript_task_id
            </if>
            <if test="mode != null and mode == 'timetask'">
                <if test="taskExecuteBean.applyUser != null">
                    <bind name="applyUserLike" value="'%'+taskExecuteBean.applyUser+'%'"/>
                    and t.icreator_name like #{applyUserLike}
                </if>
            </if>
            <if test="taskExecuteBean.taskName != null">
                <bind name="taskNameLike" value="'%'+taskExecuteBean.taskName+'%'"/>
                and t.itask_name like #{taskNameLike}
            </if>
            <if test="taskExecuteBean.scriptNameZh != null">
                <bind name="scriptNameZhLike" value="'%'+taskExecuteBean.scriptNameZh+'%'"/>
                and base.iscript_name_zh like #{scriptNameZhLike}
            </if>
            <if test="taskExecuteBean.scriptName != null">
                <bind name="scriptNameLike" value="'%'+taskExecuteBean.scriptName+'%'"/>
                and base.iscript_name like #{scriptNameLike}
            </if>
            <if test="taskExecuteBean.categoryId != null">
                and base.icategory_id in
                <foreach collection="taskExecuteBean.categoryIdList" item="categoryId" open="(" close=")" separator=",">
                    #{categoryId}
                </foreach>
            </if>
            <if test="taskExecuteBean.taskScheduler != null">
                and t.itask_scheduler = #{taskExecuteBean.taskScheduler}
            </if>
            <if test="taskExecuteBean.level != null">
                and base.ilevel = #{taskExecuteBean.level}
            </if>
            <!-- 通用查询条件 end-->

            <!-- 个性化查询条件 start-->
            <if test="mode == 'pend'">
                <!-- 未运行 -->
                and t.iready_to_execute = 1
                and r.istate = 2
                and t.itask_scheduler = 0
            </if>
            <if test="mode == 'timetask'">
                <!-- 定时任务维护 -->
                and t.iready_to_execute in (1, 0, 3)
                and (t.itask_scheduler = 1 or (t.itask_scheduler = 2 and t.iready_to_execute in (1, 0)))
            </if>
            <if test="mode == 'running-test'">
                <!-- 运行中-测试 -->
                and ins.istatus in (10, 11, 30, 50)
                and t.iscript_task_source = 2
                <if test="taskExecuteBean.startTimeRange != null and taskExecuteBean.startTimeRange.size() == 2">
                    and ins.istart_time >= #{taskExecuteBean.startTimeRange[0]}
                    and ins.istart_time  &lt;= #{taskExecuteBean.startTimeRange[1]}
                </if>
                <if test="taskExecuteBean.endTimeRange != null and taskExecuteBean.endTimeRange.size() == 2">
                    and ins.iend_time >= #{taskExecuteBean.endTimeRange[0]}
                    and ins.iend_time  &lt;= #{taskExecuteBean.endTimeRange[1]}
                </if>

            </if>
            <if test="mode == 'running'">
                <!-- 运行中-任务申请 -->
                and ins.istatus in (10, 11, 30, 50)
                and t.iscript_task_source = 1
                <if test="taskExecuteBean.menuType != null and taskExecuteBean.menuType == 'ScheduledTaskMonitor'">
                    and t.itask_scheduler in(1,2)
                </if>
                <if test="taskExecuteBean.startTimeRange != null and taskExecuteBean.startTimeRange.size() == 2">
                    and ins.istart_time >= #{taskExecuteBean.startTimeRange[0]}
                    and ins.istart_time  &lt;= #{taskExecuteBean.startTimeRange[1]}
                </if>
                <if test="taskExecuteBean.endTimeRange != null and taskExecuteBean.endTimeRange.size() == 2">
                    and ins.iend_time >= #{taskExecuteBean.endTimeRange[0]}
                    and ins.iend_time  &lt;= #{taskExecuteBean.endTimeRange[1]}
                </if>
            </if>
            <if test="mode != 'finish-test' and mode != 'finish'">
                <if test="!currentUser.supervisor">
                    <!--如果开启了配置，那么只查询自己发起的任务，否则查询同部门的任务-->
                    <if test="taskExecuteBean.selectSelfDataFlag">
                        and t.icreator_id = #{currentUser.id}
                    </if>
                    <if test="!taskExecuteBean.selectSelfDataFlag">
                        <choose>
                            <when test="taskExecuteBean.orgCategoryPath != null and taskExecuteBean.orgCategoryPath.size() != 0">
                                <bind name="sysOrgCodeLike" value="taskExecuteBean.sysOrgCode + '%'"/>
                                and (base.isys_org_code like #{sysOrgCodeLike} or
                                <foreach collection="taskExecuteBean.orgCategoryPath" item="path" separator=" OR ">
                                    <!--                                <bind name="categoryPathLike" value="path"/>-->
                                    base.icategory_path like #{path}
                                </foreach>)
                            </when>
                            <otherwise>
                                <bind name="orgCode" value="currentUser.orgCode + '%'"/>
                                and base.isys_org_code like #{orgCode}
                            </otherwise>
                        </choose>
                    </if>
                </if>
            </if>
            <if test="mode == 'finish-test' or mode == 'finish'">
                <if test="taskExecuteBean.allTaskFlag != null and !taskExecuteBean.allTaskFlag">
                    <if test="!currentUser.supervisor">

                        <!--如果开启了配置，那么只查询自己发起的任务，否则查询同部门的任务-->
                        <if test="taskExecuteBean.selectSelfDataFlag">
                            and t.icreator_id = #{currentUser.id}
                        </if>
                        <if test="!taskExecuteBean.selectSelfDataFlag">
                            <choose>
                                <when test="taskExecuteBean.orgCategoryPath != null and taskExecuteBean.orgCategoryPath.size() != 0">
                                    <bind name="sysOrgCodeLike" value="taskExecuteBean.sysOrgCode + '%'"/>
                                    and (base.isys_org_code like #{sysOrgCodeLike} or
                                    <foreach collection="taskExecuteBean.orgCategoryPath" item="path" separator=" OR ">
                                        <!--                                    <bind name="categoryPathLike" value="path + '%'"/>-->
                                        base.icategory_path like #{path}
                                    </foreach>)
                                </when>
                                <otherwise>
                                    <bind name="orgCode" value="currentUser.orgCode + '%'"/>
                                    and base.isys_org_code like #{orgCode}
                                </otherwise>
                            </choose>
                        </if>
                    </if>
                </if>
            </if>
            <if test="mode == 'finish-test'">
                <!-- 执行历史-测试 -->
                and ins.istatus in (20, 60, 70)
                and t.iscript_task_source = 2
                <if test="taskExecuteBean.startTimeRange != null and taskExecuteBean.startTimeRange.size() == 2">
                    and ins.istart_time >= #{taskExecuteBean.startTimeRange[0]}
                    and ins.istart_time  &lt;= #{taskExecuteBean.startTimeRange[1]}
                </if>
                <if test="taskExecuteBean.endTimeRange != null and taskExecuteBean.endTimeRange.size() == 2">
                    and ins.iend_time >= #{taskExecuteBean.endTimeRange[0]}
                    and ins.iend_time  &lt;= #{taskExecuteBean.endTimeRange[1]}
                </if>
                <if test="taskExecuteBean.scriptName != null and taskExecuteBean.scriptName != ''">
                    <bind name="scriptName" value="'%'+ taskExecuteBean.scriptName +'%'"/>
                    and base.iscript_name like #{scriptName}
                </if>
                <if test="taskExecuteBean.applyUser != null and taskExecuteBean.applyUser != ''">
                    <bind name="applyUser" value="'%'+ taskExecuteBean.applyUser +'%'"/>
                    and t.icreator_name like #{applyUser}
                </if>
            </if>
            <if test="mode == 'finish'">
                <!-- 运行中-任务申请 -->
                and ins.istatus in (20, 60, 70)
                and t.iscript_task_source = 1
                <if test="taskExecuteBean.menuType != null and taskExecuteBean.menuType == 'ScheduledTaskMonitor'">
                    and t.itask_scheduler in(1,2)
                </if>
                <if test="taskExecuteBean.startTimeRange != null and taskExecuteBean.startTimeRange.size() == 2">
                    and ins.istart_time >= #{taskExecuteBean.startTimeRange[0]}
                    and ins.istart_time  &lt;= #{taskExecuteBean.startTimeRange[1]}
                </if>
                <if test="taskExecuteBean.endTimeRange != null and taskExecuteBean.endTimeRange.size() == 2">
                    and ins.iend_time >= #{taskExecuteBean.endTimeRange[0]}
                    and ins.iend_time  &lt;= #{taskExecuteBean.endTimeRange[1]}
                </if>
                <if test="taskExecuteBean.scriptName != null and taskExecuteBean.scriptName != ''">
                    <bind name="scriptName" value="'%'+ taskExecuteBean.scriptName +'%'"/>
                    and base.iscript_name like #{scriptName}
                </if>
                <if test="taskExecuteBean.applyUser != null and taskExecuteBean.applyUser != ''">
                    <bind name="applyUser" value="'%'+ taskExecuteBean.applyUser +'%'"/>
                    and t.icreator_name like #{applyUser}
                </if>
            </if>
            <!-- 个性化查询条件 end-->
        </if>

        order by t.iid desc
    </select>
    <select id="selectTaskByIds" resultMap="TaskExecuteResult">
        select
            t.itask_cron,
            t.itask_time,
            case
                t.iready_to_execute
                when 1 then 1
                when 2 then 2
                when 3 then 1
                end as iready_to_execute,
            t.iid as iscript_task_id,
            t.itask_name,
            t.itype,
            t.idrive_mode,
            t.itask_scheduler,
            r.iaudit_time,
            t.icreator_name as iapply_user,
            base.*,
            t.ieach_num,
            (
                select
                    count(cc.iid)
                from
                    ieai_script_task_runtime cc
                where
                    (cc.istate = 10
                        or cc.istate = 11)
                  and cc.iscript_task_id = t.iid ) as run_agent_count
        from
            ieai_script_task t
                left join ieai_script_audit_relation r on
                t.iid = r.iscript_task_id,
            (
                select
                    info.isys_org_code,
                    ver.isrc_script_uuid,
                    ver.ilevel,
                    ver.iversion,
                    info.iscript_name_zh,
                    info.iscript_name,
                    info.icategory_id
                from
                    ieai_script_info info,
                    ieai_script_info_version ver
                where
                    ver.iinfo_unique_uuid = info.iunique_uuid) base
        where
            base.isrc_script_uuid = t.isrc_script_uuid
            <if test="!currentUser.supervisor">
                <bind name="orgCode" value="currentUser.orgCode + '%'"/>
                and base.isys_org_code like #{orgCode}
            </if>
          and t.iready_to_execute in (1, 0, 3)
          and (t.itask_scheduler = 1
            or (t.itask_scheduler = 2
                and t.iready_to_execute in (1, 0)))
        order by
            t.iid desc;
    </select>

    <resultMap id="getTaskHisExcelExportMap" type="com.ideal.script.model.dto.TaskHisAgentExcelDto">
        <result column="iscript_name_zh" property="scriptNameZh"/>
        <result column="iscript_name" property="scriptName"/>
        <result column="itask_name" property="taskName"/>
        <result column="taskStartTime" property="taskStartTime"/>
        <result column="taskEndTime" property="taskEndTime"/>
        <result column="iagent_ip" property="agentIp"/>
        <result column="iagent_port" property="agentPort"/>
        <result column="istate" property="agentState"/>
        <result column="iagent_name" property="agentName"/>
        <result column="idevice_name" property="deviceName"/>
        <result column="agentStartTime" property="startTime"/>
        <result column="agentEndTime" property="endTime"/>
        <result column="ios_name" property="osName"/>
        <result column="icenter_name" property="centerName"/>
        <result column="istdout" property="istdout"/>
        <result column="istderror" property="istderror"/>
        <result column="ielapsed_time" property="elapsedTime"/>
    </resultMap>

    <select id="getTaskHisExcelExport" resultMap="getTaskHisExcelExportMap" >
        select a.istart_time as taskStartTime,
               a.iend_time as taskEndTime,
               c.itask_name,
               e.iscript_name_zh,
               e.iscript_name,
               b.iagent_ip,
               b.iagent_port,
               b.istate,
               f.iagent_name,
               f.idevice_name,
               f.ios_name,
               f.icenter_name,
               b.istart_time as agentStartTime,
               b.iend_time as agentEndTime,
               b.ielapsed_time,
               g.istdout,
               g.istderror
        from ieai_script_task_instance a
                 left join ieai_script_task_runtime b on a.iid = b.itask_instance_id
                 left join ieai_script_task c on a.iscript_task_id = c.iid
                 left join ieai_script_info_version d on a.isrc_script_uuid = d.isrc_script_uuid
                 left join ieai_script_info e on d.iinfo_unique_uuid = e.iunique_uuid
                 left join ieai_script_agent_info f on b.iscript_task_ips_id = f.iid
                 left join ieai_script_agent_stdout g on b.iid = g.iruntime_id
        <where>
            <foreach collection="taskInstanceIds" close=")" open="a.iid in (" separator="," item="id">
                #{id}
            </foreach>
        </where>
    </select>
</mapper>
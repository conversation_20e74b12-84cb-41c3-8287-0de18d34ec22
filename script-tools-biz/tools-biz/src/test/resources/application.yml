spring:
  profiles:
    active: test
  datasource:
    druid:
      main:
        driver-class-name: com.mysql.cj.jdbc.Driver
        url: ***********************************************************************************************
        username: root
        password: root
        initial-size: 5
        min-idle: 5
        maxActive: 20
        maxWait: 60000
        timeBetweenEvictionRunsMillis: 60000
        minEvictableIdleTimeMillis: 300000
        validationQuery: SELECT 1 FROM DUAL
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        poolPreparedStatements: true
        maxPoolPreparedStatementPerConnectionSize: 20
      second:
        driver-class-name: com.mysql.cj.jdbc.Driver
        url: *************************************************************************************************
        username: root
        password: root
        initial-size: 5
        min-idle: 5
        maxActive: 20
        maxWait: 60000
        timeBetweenEvictionRunsMillis: 60000
        minEvictableIdleTimeMillis: 300000
        validationQuery: SELECT 1 FROM DUAL
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        poolPreparedStatements: true
        maxPoolPreparedStatementPerConnectionSize: 20
  redis:
    host: 127.0.0.1
    port: 6379
    database: 0
    #password: 123456
    timeout: 10s
    lettuce:
      pool:
        min-idle: 10
        max-idle: 30
        max-active: 30
        max-wait: -1ms
  liquibase:
    contexts: dev
    enabled: true
    drop-first: false
rocketmq:
  name-server: ************:9876
  producer:
    group: rocketmq_test
  consumer:
    group: rocketmq_test
    topic: test
pagehelper:
  reasonable: true
  supportMethodsArguments: true
  params: count=countSql
server:
  port: 9999
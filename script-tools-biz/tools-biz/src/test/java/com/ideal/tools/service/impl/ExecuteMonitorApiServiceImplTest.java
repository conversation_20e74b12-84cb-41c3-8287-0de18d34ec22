package com.ideal.tools.service.impl;

import com.ideal.tools.exception.ExecuteMonitorException;
import com.ideal.tools.model.dto.ExecuteMonitorDto;
import com.ideal.tools.service.IExecuteMonitorService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ExecuteMonitorApiServiceImplTest {

    @Mock
    private IExecuteMonitorService mockExecuteMonitorService;

    private ExecuteMonitorApiServiceImpl executeMonitorApiServiceImplUnderTest;

    @BeforeEach
    void setUp() throws Exception {
        executeMonitorApiServiceImplUnderTest = new ExecuteMonitorApiServiceImpl(mockExecuteMonitorService);
    }

    @Test
    void testUpdateMonitorRunStatusForScript() throws Exception {
        // Setup
        when(mockExecuteMonitorService.updateStatus(any(ExecuteMonitorDto.class))).thenReturn(false);

        // Run the test
        final boolean result = executeMonitorApiServiceImplUnderTest.updateMonitorRunStatusForScript(0L, 0);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    void testUpdateMonitorRunStatusForScript_IExecuteMonitorServiceReturnsTrue() throws Exception {
        // Setup
        when(mockExecuteMonitorService.updateStatus(any(ExecuteMonitorDto.class))).thenReturn(true);

        // Run the test
        final boolean result = executeMonitorApiServiceImplUnderTest.updateMonitorRunStatusForScript(0L, 0);

        // Verify the results
        assertThat(result).isTrue();
    }

    @Test
    void testUpdateMonitorRunStatusForScript_IExecuteMonitorServiceThrowsExecuteMonitorException() throws Exception {
        // Setup
        when(mockExecuteMonitorService.updateStatus(any(ExecuteMonitorDto.class)))
                .thenThrow(ExecuteMonitorException.class);

        // Run the test
        final boolean result = executeMonitorApiServiceImplUnderTest.updateMonitorRunStatusForScript(0L, 0);

        // Verify the results
        assertThat(result).isFalse();
    }
}

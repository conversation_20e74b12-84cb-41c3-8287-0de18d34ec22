package com.ideal.tools.service.impl;

import com.github.pagehelper.PageInfo;
import com.ideal.common.util.BeanUtils;
import com.ideal.common.util.PageDataUtil;
import com.ideal.script.exception.ScriptException;
import com.ideal.tools.exception.EngineServiceException;
import com.ideal.tools.exception.ExecuteHistoryException;
import com.ideal.tools.exception.ExecuteMonitorException;
import com.ideal.tools.exception.StudioException;
import com.ideal.tools.mapper.AlarmInfoMapper;
import com.ideal.tools.mapper.ExecuteMonitorMapper;
import com.ideal.tools.mapper.ToolsInfoMapper;
import com.ideal.tools.model.bean.*;
import com.ideal.tools.model.dto.*;
import com.ideal.tools.model.entity.*;
import com.ideal.tools.model.enums.MonitorRunStatusEnum;
import com.ideal.tools.model.enums.ToolsTypeEnum;
import com.ideal.tools.model.interaction.ActivityOutPutResultDto;
import com.ideal.tools.model.interaction.MonitorFlowInteractDto;
import com.ideal.tools.model.interaction.StopScriptTasksDto;
import com.ideal.tools.model.interaction.TaskResultDto;
import com.ideal.tools.service.IExecuteHistoryService;
import com.ideal.tools.service.ILogAuditService;
import com.ideal.tools.service.IToolsActOutputService;
import com.ideal.tools.service.IUsageReportService;
import com.ideal.tools.service.interaction.EngineInteract;
import com.ideal.tools.service.interaction.ScriptInteract;
import com.ideal.tools.service.interaction.StudioInteract;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ExecuteMonitorServiceImplTest {

    @Mock
    private ExecuteMonitorMapper mockExecuteMonitorMapper;
    @Mock
    private IExecuteHistoryService mockHistoryService;
    @Mock
    private ScriptInteract mockScriptInteract;
    @Mock
    private IUsageReportService mockUsageReportService;
    @Mock
    private ILogAuditService mockLogAuditService;
    @Mock
    private ToolsInfoMapper mockToolsInfoMapper;
    @Mock
    private StudioInteract mockStudioInteract;
    @Mock
    private EngineInteract mockEngineInteract;
    @Mock
    private AlarmInfoMapper mockAlarmInfoMapper;
    @Mock
    private IToolsActOutputService mockToolsActOutputService;

    private ExecuteMonitorServiceImpl executeMonitorServiceImplUnderTest;

    @Mock
    private MockedStatic<PageDataUtil> mockedStaticPageDataUtil;
    private MonitorRunStatusEnum MonitorRunStatusEnum;

    @BeforeEach
    void setUp() throws Exception {
        executeMonitorServiceImplUnderTest = new ExecuteMonitorServiceImpl(mockExecuteMonitorMapper, mockHistoryService,
                mockScriptInteract, mockUsageReportService, mockLogAuditService, mockToolsInfoMapper,
                mockStudioInteract, mockEngineInteract, mockAlarmInfoMapper, mockToolsActOutputService);
    }

    @AfterEach
    void tearDown() {
        mockedStaticPageDataUtil.close();
    }

    @Test
    void testInsertMonitor_ScriptTool() throws ExecuteMonitorException {
        // Setup
        ExecuteMonitorDto executeMonitorDto = new ExecuteMonitorDto();
        executeMonitorDto.setType(ToolsTypeEnum.TYPE_SCRIPT.getCode().longValue());
        executeMonitorDto.setXml("workflowContent");

        ExecuteMonitorEntity monitorEntity = BeanUtils.copy(executeMonitorDto, ExecuteMonitorEntity.class);
        monitorEntity.setRunStatus(MonitorRunStatusEnum.RUNNING.getCode());
        monitorEntity.setSortTime(System.currentTimeMillis());

        when(mockExecuteMonitorMapper.insertMonitor(any(ExecuteMonitorEntity.class))).thenReturn(1);

        // Run the test
        Long monitorId = executeMonitorServiceImplUnderTest.insertMonitor(executeMonitorDto);

        // Verify the results
        verify(mockExecuteMonitorMapper, never()).insertProjects(anyList());
        //assertNotNull(monitorId);
    }

    @Test
    void testInsertMonitor_CombinedTool() throws ExecuteMonitorException {
        // Setup
        ExecuteMonitorDto executeMonitorDto = new ExecuteMonitorDto();
        executeMonitorDto.setType(ToolsTypeEnum.TYPE_COMBINED.getCode().longValue());
        executeMonitorDto.setXml("workflowContent");

        ExecuteMonitorEntity monitorEntity = BeanUtils.copy(executeMonitorDto, ExecuteMonitorEntity.class);
        monitorEntity.setRunStatus(MonitorRunStatusEnum.NOT_RUN.getCode());
        monitorEntity.setSortTime(System.currentTimeMillis());

        ExecuteMonitorProjectEntity projectRoot = BeanUtils.copy(executeMonitorDto, ExecuteMonitorProjectEntity.class);
        projectRoot.setParentId(0L);
        projectRoot.setTdMonitorId(1L);
        projectRoot.setToolType(executeMonitorDto.getType());
        projectRoot.setNodeStatus(executeMonitorDto.getRunStatus());

        when(mockExecuteMonitorMapper.insertMonitor(any(ExecuteMonitorEntity.class))).thenReturn(1);
        when(mockExecuteMonitorMapper.insertProjects(anyList())).thenReturn(1);

        // Run the test
        Long monitorId = executeMonitorServiceImplUnderTest.insertMonitor(executeMonitorDto);

        // Verify the results
        //verify(mockExecuteMonitorMapper).insertProjects(Collections.singletonList(projectRoot));
        assertNull(monitorId);
    }

    @Test
    void testInsertMonitor_TypeConversionException() {
        // Setup
        ExecuteMonitorDto executeMonitorDto = new ExecuteMonitorDto();
        executeMonitorDto.setType(null);

        // Run the test and verify the exception
        ExecuteMonitorException exception = assertThrows(ExecuteMonitorException.class, () -> {
            executeMonitorServiceImplUnderTest.insertMonitor(executeMonitorDto);
        });

        // Verify the results
        assertTrue(exception.getMessage().contains("toolbox.insertMonitor for tool type is null"));
    }

    @Test
    void testInsertMonitor_InsertMonitorException() throws ExecuteMonitorException {
        // Setup
        ExecuteMonitorDto executeMonitorDto = new ExecuteMonitorDto();
        executeMonitorDto.setType(ToolsTypeEnum.TYPE_SCRIPT.getCode().longValue());

        when(mockExecuteMonitorMapper.insertMonitor(any(ExecuteMonitorEntity.class))).thenThrow(new RuntimeException("DB Error"));

        // Run the test and verify the exception
        ExecuteMonitorException exception = assertThrows(ExecuteMonitorException.class, () -> {
            executeMonitorServiceImplUnderTest.insertMonitor(executeMonitorDto);
        });

        // Verify the results
        assertTrue(exception.getMessage().contains("toolbox.insertMonitor for monitor is error"));
    }



    @Test
    void testInsertMonitor_NoXmlForCombinedTool() throws ExecuteMonitorException {
        // Setup
        ExecuteMonitorDto executeMonitorDto = new ExecuteMonitorDto();
        executeMonitorDto.setType(ToolsTypeEnum.TYPE_COMBINED.getCode().longValue());
        executeMonitorDto.setXml(null);

        ExecuteMonitorEntity monitorEntity = BeanUtils.copy(executeMonitorDto, ExecuteMonitorEntity.class);
        monitorEntity.setRunStatus(MonitorRunStatusEnum.NOT_RUN.getCode());
        monitorEntity.setSortTime(System.currentTimeMillis());

        ExecuteMonitorProjectEntity projectRoot = BeanUtils.copy(executeMonitorDto, ExecuteMonitorProjectEntity.class);
        projectRoot.setParentId(0L);
        projectRoot.setTdMonitorId(1L);
        projectRoot.setToolType(executeMonitorDto.getType());
        projectRoot.setNodeStatus(executeMonitorDto.getRunStatus());

        when(mockExecuteMonitorMapper.insertMonitor(any(ExecuteMonitorEntity.class))).thenReturn(1);
        when(mockExecuteMonitorMapper.insertProjects(anyList())).thenReturn(1);

        // Run the test
        Long monitorId = executeMonitorServiceImplUnderTest.insertMonitor(executeMonitorDto);

        // Verify the results
        //verify(mockExecuteMonitorMapper, never()).insertProjectXml(anyList());
    }

    @Test
    void testInsertMonitor_ParamsAndProjects() throws ExecuteMonitorException {
        // Setup
        ExecuteMonitorDto executeMonitorDto = new ExecuteMonitorDto();
        executeMonitorDto.setType(ToolsTypeEnum.TYPE_SCRIPT.getCode().longValue());
        executeMonitorDto.setXml("workflowContent");
        executeMonitorDto.setParams(Collections.singletonList(new ExecuteMonitorParamsDto()));

        ExecuteMonitorEntity monitorEntity = BeanUtils.copy(executeMonitorDto, ExecuteMonitorEntity.class);
        monitorEntity.setRunStatus(MonitorRunStatusEnum.RUNNING.getCode());
        monitorEntity.setSortTime(System.currentTimeMillis());

        ExecuteMonitorProjectEntity projectRoot = BeanUtils.copy(executeMonitorDto, ExecuteMonitorProjectEntity.class);
        projectRoot.setParentId(0L);
        projectRoot.setTdMonitorId(1L);
        projectRoot.setToolType(executeMonitorDto.getType());
        projectRoot.setNodeStatus(executeMonitorDto.getRunStatus());

        when(mockExecuteMonitorMapper.insertMonitor(any(ExecuteMonitorEntity.class))).thenReturn(1);

        // Run the test
        Long monitorId = executeMonitorServiceImplUnderTest.insertMonitor(executeMonitorDto);

        // Verify the results
        verify(mockExecuteMonitorMapper, never()).insertProjects(anyList());
    }

    @Test
    void testSelectExecuteMonitorList() {
        // Setup
        final ExecuteMonitorQueryListDto executeMonitorDto = new ExecuteMonitorQueryListDto();
        executeMonitorDto.setClassification(0);
        executeMonitorDto.setExecutorName("executorName");
        executeMonitorDto.setCreatorName("creatorName");
        executeMonitorDto.setSearchStartTime("searchStartTime");
        executeMonitorDto.setSearchEndTime("searchEndTime");

        // Configure ExecuteMonitorMapper.selectExecuteMonitorList(...).
        final ExecuteMonitorEntity executeMonitorEntity = new ExecuteMonitorEntity();
        executeMonitorEntity.setSortTime(0L);
        executeMonitorEntity.setClassification(0);
        executeMonitorEntity.setId(0L);
        executeMonitorEntity.setRunStatus(0L);
        executeMonitorEntity.setRunTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<ExecuteMonitorEntity> executeMonitorEntities = Arrays.asList(executeMonitorEntity);
        when(mockExecuteMonitorMapper.selectExecuteMonitorList(any(ExecuteMonitorBean.class)))
                .thenReturn(executeMonitorEntities);

        // Run the test
        final PageInfo<ExecuteMonitorResultListDto> result = executeMonitorServiceImplUnderTest.selectExecuteMonitorList(
                executeMonitorDto, 0, 0);

        // Verify the results
    }

    @Test
    void testSelectExecuteMonitorList_ExecuteMonitorMapperReturnsNoItems() {
        // Setup
        final ExecuteMonitorQueryListDto executeMonitorDto = new ExecuteMonitorQueryListDto();
        executeMonitorDto.setClassification(0);
        executeMonitorDto.setExecutorName("executorName");
        executeMonitorDto.setCreatorName("creatorName");
        executeMonitorDto.setSearchStartTime("searchStartTime");
        executeMonitorDto.setSearchEndTime("searchEndTime");

        when(mockExecuteMonitorMapper.selectExecuteMonitorList(any(ExecuteMonitorBean.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final PageInfo<ExecuteMonitorResultListDto> result = executeMonitorServiceImplUnderTest.selectExecuteMonitorList(
                executeMonitorDto, 0, 0);

        // Verify the results
    }

    @Test
    void testSelectExecuteMonitorById() throws Exception {
        // Setup
        final UserDto user = new UserDto();
        user.setUserName("userName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        // Configure ExecuteMonitorMapper.selectExecuteMonitorById(...).
        final ExecuteMonitorBean executeMonitorBean = new ExecuteMonitorBean();
        executeMonitorBean.setRunningId(0L);
        final ExecuteParamsBean executeParamsBean = new ExecuteParamsBean();
        executeMonitorBean.setParams(Arrays.asList(executeParamsBean));
        final ExecuteProjectBean executeProjectBean = new ExecuteProjectBean();
        executeProjectBean.setParentId(0L);
        executeProjectBean.setProjectName("projectName");
        executeMonitorBean.setProjects(Arrays.asList(executeProjectBean));
        executeMonitorBean.setId(0L);
        executeMonitorBean.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        executeMonitorBean.setEstimateOperationalRisk("estimateOperationalRisk");
        executeMonitorBean.setEventNum("eventId");
        executeMonitorBean.setRunStatus(0L);
        executeMonitorBean.setRunTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        executeMonitorBean.setType(0L);
        final ExecuteFilesBean executeFilesBean = new ExecuteFilesBean();
        executeMonitorBean.setFiles(Arrays.asList(executeFilesBean));
        when(mockExecuteMonitorMapper.selectExecuteMonitorById(0L)).thenReturn(executeMonitorBean);

        // Configure ExecuteMonitorMapper.selectAct(...).
        final MonitorFlowActiveEntity monitorFlowActiveEntity = new MonitorFlowActiveEntity();
        monitorFlowActiveEntity.setActDefName("actDefName");
        monitorFlowActiveEntity.setReqId("reqId");
        monitorFlowActiveEntity.setId(0L);
        monitorFlowActiveEntity.setFlowId(0L);
        monitorFlowActiveEntity.setCallFlowId(0L);
        monitorFlowActiveEntity.setActName("name");
        monitorFlowActiveEntity.setActStatus("actStatus");
        monitorFlowActiveEntity.setActType("actType");
        final List<MonitorFlowActiveEntity> monitorFlowActiveEntities = Arrays.asList(monitorFlowActiveEntity);
        // Run the test
        final ExecuteMonitorDto result = executeMonitorServiceImplUnderTest.selectExecuteMonitorById(0L, user);

        // Verify the results
    }

    @Test
    void testSelectExecuteMonitorById_ExecuteMonitorMapperSelectExecuteMonitorByIdReturnsNull() {
        // Setup
        final UserDto user = new UserDto();
        user.setUserName("userName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        when(mockExecuteMonitorMapper.selectExecuteMonitorById(0L)).thenReturn(null);

        // Run the test
        final ExecuteMonitorDto result = executeMonitorServiceImplUnderTest.selectExecuteMonitorById(0L, user);

        // Verify the results
    }

    @Test
    void testSelectExecuteMonitorById_ExecuteMonitorMapperSelectActReturnsNoItems() throws Exception {
        // Setup
        final UserDto user = new UserDto();
        user.setUserName("userName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        // Configure ExecuteMonitorMapper.selectExecuteMonitorById(...).
        final ExecuteMonitorBean executeMonitorBean = new ExecuteMonitorBean();
        executeMonitorBean.setRunningId(0L);
        final ExecuteParamsBean executeParamsBean = new ExecuteParamsBean();
        executeMonitorBean.setParams(Arrays.asList(executeParamsBean));
        final ExecuteProjectBean executeProjectBean = new ExecuteProjectBean();
        executeProjectBean.setParentId(0L);
        executeProjectBean.setProjectName("projectName");
        executeMonitorBean.setProjects(Arrays.asList(executeProjectBean));
        executeMonitorBean.setId(0L);
        executeMonitorBean.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        executeMonitorBean.setEstimateOperationalRisk("estimateOperationalRisk");
        executeMonitorBean.setEventNum("eventId");
        executeMonitorBean.setRunStatus(0L);
        executeMonitorBean.setRunTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        executeMonitorBean.setType(0L);
        final ExecuteFilesBean executeFilesBean = new ExecuteFilesBean();
        executeMonitorBean.setFiles(Arrays.asList(executeFilesBean));
        when(mockExecuteMonitorMapper.selectExecuteMonitorById(0L)).thenReturn(executeMonitorBean);

        // Run the test
        final ExecuteMonitorDto result = executeMonitorServiceImplUnderTest.selectExecuteMonitorById(0L, user);

        // Verify the results
    }

    @Test
    void testSelectExecuteMonitorById_StudioInteractThrowsStudioException() throws Exception {
        // Setup
        final UserDto user = new UserDto();
        user.setUserName("userName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        // Configure ExecuteMonitorMapper.selectExecuteMonitorById(...).
        final ExecuteMonitorBean executeMonitorBean = new ExecuteMonitorBean();
        executeMonitorBean.setRunningId(0L);
        final ExecuteParamsBean executeParamsBean = new ExecuteParamsBean();
        executeMonitorBean.setParams(Arrays.asList(executeParamsBean));
        final ExecuteProjectBean executeProjectBean = new ExecuteProjectBean();
        executeProjectBean.setParentId(0L);
        executeProjectBean.setProjectName("projectName");
        executeMonitorBean.setProjects(Arrays.asList(executeProjectBean));
        executeMonitorBean.setId(0L);
        executeMonitorBean.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        executeMonitorBean.setEstimateOperationalRisk("estimateOperationalRisk");
        executeMonitorBean.setEventNum("eventId");
        executeMonitorBean.setRunStatus(0L);
        executeMonitorBean.setRunTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        executeMonitorBean.setType(0L);
        final ExecuteFilesBean executeFilesBean = new ExecuteFilesBean();
        executeMonitorBean.setFiles(Arrays.asList(executeFilesBean));
        when(mockExecuteMonitorMapper.selectExecuteMonitorById(0L)).thenReturn(executeMonitorBean);

        // Configure ExecuteMonitorMapper.selectAct(...).
        final MonitorFlowActiveEntity monitorFlowActiveEntity = new MonitorFlowActiveEntity();
        monitorFlowActiveEntity.setActDefName("actDefName");
        monitorFlowActiveEntity.setReqId("reqId");
        monitorFlowActiveEntity.setId(0L);
        monitorFlowActiveEntity.setFlowId(0L);
        monitorFlowActiveEntity.setCallFlowId(0L);
        monitorFlowActiveEntity.setActName("name");
        monitorFlowActiveEntity.setActStatus("actStatus");
        monitorFlowActiveEntity.setActType("actType");
        final List<MonitorFlowActiveEntity> monitorFlowActiveEntities = Arrays.asList(monitorFlowActiveEntity);
        lenient().when(mockExecuteMonitorMapper.selectAct(any(MonitorFlowActiveEntity.class)))
                .thenReturn(monitorFlowActiveEntities);

        lenient().when(mockStudioInteract.getWorkflowJson(0L)).thenThrow(StudioException.class);

        // Run the test
        final ExecuteMonitorDto result = executeMonitorServiceImplUnderTest.selectExecuteMonitorById(0L, user);

        // Verify the results
    }
    // 测试数据准备
    private ExecuteMonitorBean createTestBean() {
        ExecuteMonitorBean bean = new ExecuteMonitorBean();
        bean.setId(1L);
        bean.setEstimateOperationalRisk("1,2,3");

        // 准备文件数据
        ExecuteFilesBean fileBean = new ExecuteFilesBean();
        fileBean.setId(100L);
        bean.setFiles(Collections.singletonList(fileBean));

        // 准备项目数据
        ExecuteProjectBean projectDto = new ExecuteProjectBean();
        projectDto.setFlowId(500L);
        projectDto.setParentId(0L);
        projectDto.setXml("<workflow></workflow>");
        bean.setProjects(Collections.singletonList(projectDto));

        return bean;
    }

    @Test
    void selectExecuteMonitorById_WhenDataExists_ShouldReturnDto() throws Exception {
        // 1. 准备测试数据
        UserDto user = new UserDto();
        ExecuteMonitorBean mockBean = createTestBean();

        // 2. 配置mock行为
        when(mockExecuteMonitorMapper.selectExecuteMonitorById(1L)).thenReturn(mockBean);

        // 3. 调用被测方法
        ExecuteMonitorDto result = executeMonitorServiceImplUnderTest.selectExecuteMonitorById(1L, user);

        // 4. 验证结果
        assertNotNull(result);
        assertEquals(3, result.getEstimateOperationalRiskArray().length);
        assertFalse(result.getFiles().isEmpty());
        assertTrue(result.getProjects().isEmpty());

    }

    @Test
    void selectExecuteMonitorById_WhenDataNotExists_ShouldReturnEmptyDto() {
        // 配置mock返回空值
        when(mockExecuteMonitorMapper.selectExecuteMonitorById(999L)).thenReturn(null);

        ExecuteMonitorDto result = executeMonitorServiceImplUnderTest.selectExecuteMonitorById(999L, null);

        assertNotNull(result);
    }

    @Test
    void testGetRunStatus() {
        // Setup
        // Run the test
        final List<CommonStatusDto> result = executeMonitorServiceImplUnderTest.getRunStatus();

        // Verify the results
    }

    @Test
    void testUpdateStatusSelf() throws Exception {
        // Setup
        final ExecuteMonitorDto monitor = new ExecuteMonitorDto();
        monitor.setEstimateOperationalRiskArray(new Long[]{0L});
        monitor.setXml("workflowContent");
        monitor.setRunningId(0L);
        final ExecuteMonitorParamsDto executeMonitorParamsDto = new ExecuteMonitorParamsDto();
        monitor.setParams(Arrays.asList(executeMonitorParamsDto));
        final ExecuteMonitorProjectDto executeMonitorProjectDto = new ExecuteMonitorProjectDto();
        executeMonitorProjectDto.setActName("name");
        executeMonitorProjectDto.setActStatus("actStatus");
        executeMonitorProjectDto.setNodeId(0L);
        executeMonitorProjectDto.setFlowId(0L);
        executeMonitorProjectDto.setParentId(0L);
        executeMonitorProjectDto.setChildren(Arrays.asList(new ExecuteMonitorProjectDto()));
        executeMonitorProjectDto.setNodeName("name");
        executeMonitorProjectDto.setNodeStatus(0L);
        executeMonitorProjectDto.setToolType(0L);
        executeMonitorProjectDto.setXml("workflowContent");
        monitor.setProjects(Arrays.asList(executeMonitorProjectDto));
        monitor.setRunStatus(0L);
        monitor.setToolId(0L);
        monitor.setType(0L);
        final ExecuteHistoryFilesDto executeHistoryFilesDto = new ExecuteHistoryFilesDto();
        monitor.setFiles(Arrays.asList(executeHistoryFilesDto));

        // Configure ExecuteMonitorMapper.selectExecuteMonitorByRunningId(...).
        final ExecuteMonitorBean executeMonitorBean = new ExecuteMonitorBean();
        executeMonitorBean.setRunningId(0L);
        final ExecuteParamsBean executeParamsBean = new ExecuteParamsBean();
        executeMonitorBean.setParams(Arrays.asList(executeParamsBean));
        final ExecuteProjectBean executeProjectBean = new ExecuteProjectBean();
        executeProjectBean.setParentId(0L);
        executeProjectBean.setProjectName("projectName");
        executeMonitorBean.setProjects(Arrays.asList(executeProjectBean));
        executeMonitorBean.setId(0L);
        executeMonitorBean.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        executeMonitorBean.setEstimateOperationalRisk("estimateOperationalRisk");
        executeMonitorBean.setEventNum("eventId");
        executeMonitorBean.setRunStatus(0L);
        executeMonitorBean.setRunTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        executeMonitorBean.setType(0L);
        final ExecuteFilesBean executeFilesBean = new ExecuteFilesBean();
        executeMonitorBean.setFiles(Arrays.asList(executeFilesBean));
        final List<ExecuteMonitorBean> executeMonitorBeans = Arrays.asList(executeMonitorBean);
        when(mockExecuteMonitorMapper.selectExecuteMonitorByRunningId(any(Long[].class)))
                .thenReturn(executeMonitorBeans);

        when(mockExecuteMonitorMapper.updateStatus(any(ExecuteMonitorEntity.class))).thenReturn(0);

        // Run the test
        final boolean result = executeMonitorServiceImplUnderTest.updateStatusSelf(monitor);

        // Verify the results
        assertThat(result).isFalse();
        verify(mockUsageReportService).updateToolExecuteStatus(any(UsageReportDto.class));
    }

    @Test
    void testUpdateStatusSelf_ExecuteMonitorMapperSelectExecuteMonitorByRunningIdReturnsNoItems() throws Exception {
        // Setup
        final ExecuteMonitorDto monitor = new ExecuteMonitorDto();
        monitor.setEstimateOperationalRiskArray(new Long[]{0L});
        monitor.setXml("workflowContent");
        monitor.setRunningId(0L);
        final ExecuteMonitorParamsDto executeMonitorParamsDto = new ExecuteMonitorParamsDto();
        monitor.setParams(Arrays.asList(executeMonitorParamsDto));
        final ExecuteMonitorProjectDto executeMonitorProjectDto = new ExecuteMonitorProjectDto();
        executeMonitorProjectDto.setActName("name");
        executeMonitorProjectDto.setActStatus("actStatus");
        executeMonitorProjectDto.setNodeId(0L);
        executeMonitorProjectDto.setFlowId(0L);
        executeMonitorProjectDto.setParentId(0L);
        executeMonitorProjectDto.setChildren(Arrays.asList(new ExecuteMonitorProjectDto()));
        executeMonitorProjectDto.setNodeName("name");
        executeMonitorProjectDto.setNodeStatus(0L);
        executeMonitorProjectDto.setToolType(0L);
        executeMonitorProjectDto.setXml("workflowContent");
        monitor.setProjects(Arrays.asList(executeMonitorProjectDto));
        monitor.setRunStatus(0L);
        monitor.setToolId(0L);
        monitor.setType(0L);
        final ExecuteHistoryFilesDto executeHistoryFilesDto = new ExecuteHistoryFilesDto();
        monitor.setFiles(Arrays.asList(executeHistoryFilesDto));

        when(mockExecuteMonitorMapper.selectExecuteMonitorByRunningId(any(Long[].class)))
                .thenReturn(Collections.emptyList());
        when(mockExecuteMonitorMapper.updateStatus(any(ExecuteMonitorEntity.class))).thenReturn(0);

        // Run the test
        final boolean result = executeMonitorServiceImplUnderTest.updateStatusSelf(monitor);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    void testUpdateStatusSelf_IExecuteHistoryServiceThrowsExecuteHistoryException() throws Exception {
        // Setup
        final ExecuteMonitorDto monitor = new ExecuteMonitorDto();
        monitor.setEstimateOperationalRiskArray(new Long[]{0L});
        monitor.setXml("workflowContent");
        monitor.setRunStatus(0L);
        monitor.setToolId(0L);
        monitor.setType(0L);
        final ExecuteHistoryFilesDto executeHistoryFilesDto = new ExecuteHistoryFilesDto();
        monitor.setFiles(Arrays.asList(executeHistoryFilesDto));

        // Configure ExecuteMonitorMapper.selectExecuteMonitorByRunningId(...).
        when(mockExecuteMonitorMapper.selectExecuteMonitorByRunningId(any(Long[].class)))
                .thenReturn(Collections.emptyList());

        // Configure ExecuteMonitorMapper.updateStatus(...).
        when(mockExecuteMonitorMapper.updateStatus(any(ExecuteMonitorEntity.class))).thenReturn(0);

        // Configure HistoryService.insertHistory(...) to throw an exception.
        //when(mockHistoryService.insertHistory(any(ExecuteMonitorBean.class), eq(0L)))
        //        .thenThrow(ExecuteHistoryException.class);

        // Run the test
        final boolean result = executeMonitorServiceImplUnderTest.updateStatusSelf(monitor);

        // Verify the results
        // 如果业务逻辑要求在异常情况下返回 true，则断言如下：
        assertThat(result).isFalse();

        // 验证交互
       // verify(mockHistoryService).insertHistory(any(ExecuteMonitorBean.class), eq(0L));
        //verify(mockExecuteMonitorMapper).deleteMonitor(null);
        //verify(mockExecuteMonitorMapper).deleteMonitorParams(any(Long[].class));
        //verify(mockExecuteMonitorMapper).deleteMonitorXml(any(Long[].class));
        //verify(mockExecuteMonitorMapper).deleteMonitorProject(any(Long[].class));
    }

    @Test
    void testUpdateStatus1() throws Exception {
        // Setup
        final ExecuteMonitorDto monitor = new ExecuteMonitorDto();
        monitor.setEstimateOperationalRiskArray(new Long[]{0L});
        monitor.setXml("workflowContent");
        monitor.setRunningId(0L);
        final ExecuteMonitorParamsDto executeMonitorParamsDto = new ExecuteMonitorParamsDto();
        monitor.setParams(Arrays.asList(executeMonitorParamsDto));
        final ExecuteMonitorProjectDto executeMonitorProjectDto = new ExecuteMonitorProjectDto();
        executeMonitorProjectDto.setActName("name");
        executeMonitorProjectDto.setActStatus("actStatus");
        executeMonitorProjectDto.setNodeId(0L);
        executeMonitorProjectDto.setFlowId(0L);
        executeMonitorProjectDto.setParentId(0L);
        executeMonitorProjectDto.setChildren(Arrays.asList(new ExecuteMonitorProjectDto()));
        executeMonitorProjectDto.setNodeName("name");
        executeMonitorProjectDto.setNodeStatus(0L);
        executeMonitorProjectDto.setToolType(0L);
        executeMonitorProjectDto.setXml("workflowContent");
        monitor.setProjects(Arrays.asList(executeMonitorProjectDto));
        monitor.setRunStatus(0L);
        monitor.setToolId(0L);
        monitor.setType(0L);
        final ExecuteHistoryFilesDto executeHistoryFilesDto = new ExecuteHistoryFilesDto();
        monitor.setFiles(Arrays.asList(executeHistoryFilesDto));

        // Configure ExecuteMonitorMapper.selectExecuteMonitorByRunningId(...).
        final ExecuteMonitorBean executeMonitorBean = new ExecuteMonitorBean();
        executeMonitorBean.setRunningId(0L);
        final ExecuteParamsBean executeParamsBean = new ExecuteParamsBean();
        executeMonitorBean.setParams(Arrays.asList(executeParamsBean));
        final ExecuteProjectBean executeProjectBean = new ExecuteProjectBean();
        executeProjectBean.setParentId(0L);
        executeProjectBean.setProjectName("projectName");
        executeMonitorBean.setProjects(Arrays.asList(executeProjectBean));
        executeMonitorBean.setId(0L);
        executeMonitorBean.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        executeMonitorBean.setEstimateOperationalRisk("estimateOperationalRisk");
        executeMonitorBean.setEventNum("eventId");
        executeMonitorBean.setRunStatus(0L);
        executeMonitorBean.setRunTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        executeMonitorBean.setType(0L);
        final ExecuteFilesBean executeFilesBean = new ExecuteFilesBean();
        executeMonitorBean.setFiles(Arrays.asList(executeFilesBean));
        final List<ExecuteMonitorBean> executeMonitorBeans = Arrays.asList(executeMonitorBean);
        when(mockExecuteMonitorMapper.selectExecuteMonitorByRunningId(any(Long[].class)))
                .thenReturn(executeMonitorBeans);

        when(mockExecuteMonitorMapper.updateStatus(any(ExecuteMonitorEntity.class))).thenReturn(0);

        // Run the test
        final boolean result = executeMonitorServiceImplUnderTest.updateStatus(monitor);
        Long[] monitorId = {0L};
        // Verify the results
        assertThat(result).isFalse();
        verify(mockUsageReportService).updateToolExecuteStatus(any(UsageReportDto.class));
        //verify(mockAlarmInfoMapper).updateHealingStatus("eventId", 0);
        //verify(mockHistoryService).insertHistory(any(ExecuteMonitorBean.class), eq(0L));
        //verify(mockExecuteMonitorMapper).deleteMonitor(monitorId);
        //verify(mockExecuteMonitorMapper).deleteMonitorParams(monitorId);
        //verify(mockExecuteMonitorMapper).deleteMonitorXml(monitorId);
        //verify(mockExecuteMonitorMapper).deleteMonitorProject(monitorId);
    }

    @Test
    void testUpdateStatus1_ExecuteMonitorMapperSelectExecuteMonitorByRunningIdReturnsNoItems() throws Exception {
        // Setup
        final ExecuteMonitorDto monitor = new ExecuteMonitorDto();
        monitor.setRunStatus(0L);
        monitor.setToolId(0L);
        monitor.setType(0L);
        final ExecuteHistoryFilesDto executeHistoryFilesDto = new ExecuteHistoryFilesDto();
        monitor.setFiles(Arrays.asList(executeHistoryFilesDto));

        // Configure ExecuteMonitorMapper.selectExecuteMonitorByRunningId(...).
        when(mockExecuteMonitorMapper.selectExecuteMonitorByRunningId(any(Long[].class)))
                .thenReturn(Collections.emptyList());

        // Configure ExecuteMonitorMapper.updateStatus(...) to return failure.
        when(mockExecuteMonitorMapper.updateStatus(any(ExecuteMonitorEntity.class))).thenReturn(0);

        // Configure deleteMonitor method to do nothing.
        //when(mockExecuteMonitorMapper.deleteMonitor(any(Long[].class))).thenReturn(1);;

        // Run the test
        final boolean result = executeMonitorServiceImplUnderTest.updateStatus(monitor);

        // Verify the results
        assertThat(result).isFalse();

        // Verify interactions
        verify(mockExecuteMonitorMapper).selectExecuteMonitorByRunningId(any(Long[].class));
        verify(mockExecuteMonitorMapper).updateStatus(any(ExecuteMonitorEntity.class));
        //verify(mockExecuteMonitorMapper).deleteMonitor(null);
    }

    @Test
    void testUpdateStatus1_IExecuteHistoryServiceThrowsExecuteHistoryException() throws Exception {
        // Setup
        final ExecuteMonitorDto monitor = new ExecuteMonitorDto();
        monitor.setEstimateOperationalRiskArray(new Long[]{0L});
        monitor.setXml("workflowContent");
        monitor.setRunStatus(0L);
        monitor.setToolId(0L);
        monitor.setType(0L);
        final ExecuteHistoryFilesDto executeHistoryFilesDto = new ExecuteHistoryFilesDto();
        monitor.setFiles(Arrays.asList(executeHistoryFilesDto));

        // Configure ExecuteMonitorMapper.selectExecuteMonitorByRunningId(...).
        when(mockExecuteMonitorMapper.selectExecuteMonitorByRunningId(any(Long[].class)))
                .thenReturn(Collections.emptyList());

        // Configure ExecuteMonitorMapper.updateStatus(...).
        when(mockExecuteMonitorMapper.updateStatus(any(ExecuteMonitorEntity.class))).thenReturn(0);

        // Configure HistoryService.insertHistory(...) to throw an exception.
        //when(mockHistoryService.insertHistory(any(ExecuteMonitorBean.class), eq(0L)))
        //        .thenThrow(ExecuteHistoryException.class);

        // Run the test
        final boolean result = executeMonitorServiceImplUnderTest.updateStatusSelf(monitor);

        // Verify the results
        // 如果业务逻辑要求在异常情况下返回 true，则断言如下：

        // 验证交互
        //verify(mockExecuteMonitorMapper).deleteMonitorParams(any(Long[].class));
        //verify(mockExecuteMonitorMapper).deleteMonitorXml(any(Long[].class));
        //verify(mockExecuteMonitorMapper).deleteMonitorProject(any(Long[].class));
    }

    @Test
    void testUpdateStatus2() throws Exception {
        // Setup
        final MonitorFlowInteractDto monitorFlowInteract = new MonitorFlowInteractDto();
        monitorFlowInteract.setProjectName("projectName");
        monitorFlowInteract.setBizUniqueId(0L);
        monitorFlowInteract.setFlowStatus(0);
        monitorFlowInteract.setFlowId(0L);
        monitorFlowInteract.setFlowStartTime(0L);
        monitorFlowInteract.setFlowEndTime(0L);
        // Configure ExecuteMonitorMapper.selectExecuteMonitorByRunningId(...).
        final ExecuteMonitorBean executeMonitorBean = new ExecuteMonitorBean();
        executeMonitorBean.setRunningId(0L);
        final ExecuteParamsBean executeParamsBean = new ExecuteParamsBean();
        executeMonitorBean.setParams(Arrays.asList(executeParamsBean));
        final ExecuteProjectBean executeProjectBean = new ExecuteProjectBean();
        executeProjectBean.setParentId(0L);
        executeProjectBean.setProjectName("projectName");
        executeMonitorBean.setProjects(Arrays.asList(executeProjectBean));
        executeMonitorBean.setId(0L);
        executeMonitorBean.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        executeMonitorBean.setEstimateOperationalRisk("estimateOperationalRisk");
        executeMonitorBean.setEventNum("eventId");
        executeMonitorBean.setRunStatus(0L);
        executeMonitorBean.setRunTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        executeMonitorBean.setType(0L);
        final ExecuteFilesBean executeFilesBean = new ExecuteFilesBean();
        executeMonitorBean.setFiles(Arrays.asList(executeFilesBean));
        final List<ExecuteMonitorBean> executeMonitorBeans = Arrays.asList(executeMonitorBean);
        when(mockExecuteMonitorMapper.selectExecuteMonitorByRunningId(any(Long[].class)))
                .thenReturn(executeMonitorBeans);
        // Run the test
        final boolean result = executeMonitorServiceImplUnderTest.updateStatus(monitorFlowInteract);
        // Verify the results
        assertThat(result).isTrue();
        //verify(mockUsageReportService).updateToolExecuteStatus(any(UsageReportDto.class));
        //verify(mockAlarmInfoMapper).updateHealingStatus("1", 0);
        //verify(mockExecuteMonitorMapper).updateProject(any(ExecuteMonitorProjectEntity.class));
        //verify(mockHistoryService).insertHistory(any(ExecuteMonitorBean.class), eq(0L));
        ////verify(mockExecuteMonitorMapper).deleteMonitor(any(Long[].class));
        //verify(mockExecuteMonitorMapper).deleteMonitorParams(any(Long[].class));
        //verify(mockExecuteMonitorMapper).deleteMonitorXml(any(Long[].class));
        //verify(mockExecuteMonitorMapper).deleteMonitorProject(any(Long[].class));
        //verify(mockExecuteMonitorMapper).updateMonitorByRunningId(any(ExecuteMonitorEntity.class));
    }

    @Test
    void testUpdateStatus2_ExecuteMonitorMapperSelectExecuteMonitorByRunningIdReturnsNoItems() throws Exception {
        // Setup
        final MonitorFlowInteractDto monitorFlowInteract = new MonitorFlowInteractDto();
        monitorFlowInteract.setProjectName("projectName");
        monitorFlowInteract.setBizUniqueId(0L);
        monitorFlowInteract.setFlowStatus(0);
        monitorFlowInteract.setFlowId(0L);
        monitorFlowInteract.setFlowStartTime(0L);
        monitorFlowInteract.setFlowEndTime(0L);

        when(mockExecuteMonitorMapper.selectExecuteMonitorByRunningId(any(Long[].class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final boolean result = executeMonitorServiceImplUnderTest.updateStatus(monitorFlowInteract);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    void testUpdateStatus2_IExecuteHistoryServiceThrowsExecuteHistoryException() throws Exception {
        // Setup
        final MonitorFlowInteractDto monitorFlowInteract = new MonitorFlowInteractDto();
        monitorFlowInteract.setProjectName("projectName");
        monitorFlowInteract.setBizUniqueId(0L);
        monitorFlowInteract.setFlowStatus(0);
        monitorFlowInteract.setFlowId(0L);
        monitorFlowInteract.setFlowStartTime(0L);
        monitorFlowInteract.setFlowEndTime(0L);

        // Configure ExecuteMonitorMapper.selectExecuteMonitorByRunningId(...).
        final ExecuteMonitorBean executeMonitorBean = new ExecuteMonitorBean();
        executeMonitorBean.setRunningId(0L);
        final ExecuteParamsBean executeParamsBean = new ExecuteParamsBean();
        executeMonitorBean.setParams(Arrays.asList(executeParamsBean));
        final ExecuteProjectBean executeProjectBean = new ExecuteProjectBean();
        executeProjectBean.setParentId(0L);
        executeProjectBean.setProjectName("projectName");
        executeMonitorBean.setProjects(Arrays.asList(executeProjectBean));
        executeMonitorBean.setId(0L);
        executeMonitorBean.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        executeMonitorBean.setEstimateOperationalRisk("estimateOperationalRisk");
        executeMonitorBean.setEventNum("eventId");
        executeMonitorBean.setRunStatus(0L);
        executeMonitorBean.setRunTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        executeMonitorBean.setType(0L);
        final ExecuteFilesBean executeFilesBean = new ExecuteFilesBean();
        executeMonitorBean.setFiles(Arrays.asList(executeFilesBean));
        final List<ExecuteMonitorBean> executeMonitorBeans = Arrays.asList(executeMonitorBean);
        when(mockExecuteMonitorMapper.selectExecuteMonitorByRunningId(any(Long[].class)))
                .thenReturn(executeMonitorBeans);


        // Run the test
        final boolean result = executeMonitorServiceImplUnderTest.updateStatus(monitorFlowInteract);

        // Verify the results
        assertThat(result).isTrue();
        verify(mockUsageReportService).updateToolExecuteStatus(any(UsageReportDto.class));
    }

    @Test
    void testExportMonitorList() {
        // Setup
        final ExecuteMonitorQueryListDto monitor = new ExecuteMonitorQueryListDto();
        monitor.setClassification(0);
        monitor.setExecutorName("executorName");
        monitor.setCreatorName("creatorName");
        monitor.setSearchStartTime("searchStartTime");
        monitor.setSearchEndTime("searchEndTime");

        // Configure ExecuteMonitorMapper.selectExecuteMonitorList(...).
        final ExecuteMonitorEntity executeMonitorEntity = new ExecuteMonitorEntity();
        executeMonitorEntity.setSortTime(0L);
        executeMonitorEntity.setClassification(0);
        executeMonitorEntity.setId(0L);
        executeMonitorEntity.setRunStatus(0L);
        executeMonitorEntity.setRunTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<ExecuteMonitorEntity> executeMonitorEntities = Arrays.asList(executeMonitorEntity);
        when(mockExecuteMonitorMapper.selectExecuteMonitorList(any(ExecuteMonitorBean.class)))
                .thenReturn(executeMonitorEntities);

        // Run the test
        final List<ExecuteMonitorExportDto> result = executeMonitorServiceImplUnderTest.exportMonitorList(monitor);

        // Verify the results
    }

    @Test
    void testExportMonitorList_ExecuteMonitorMapperReturnsNoItems() {
        // Setup
        final ExecuteMonitorQueryListDto monitor = new ExecuteMonitorQueryListDto();
        monitor.setClassification(0);
        monitor.setExecutorName("executorName");
        monitor.setCreatorName("creatorName");
        monitor.setSearchStartTime("searchStartTime");
        monitor.setSearchEndTime("searchEndTime");

        when(mockExecuteMonitorMapper.selectExecuteMonitorList(any(ExecuteMonitorBean.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<ExecuteMonitorExportDto> result = executeMonitorServiceImplUnderTest.exportMonitorList(monitor);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testMatchAuditId() {
        // Setup
        when(mockExecuteMonitorMapper.selectNumByAuditId(0L)).thenReturn(0);
        when(mockHistoryService.matchAuditId(0L)).thenReturn(0);

        // Run the test
        final boolean result = executeMonitorServiceImplUnderTest.matchAuditId(0L);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    void testGetExecuteDetailByToolsId() throws Exception {
        // Setup
        final UserDto userDto = new UserDto();
        userDto.setUserName("userName");
        userDto.setUserId(0L);
        userDto.setUserCode("userCode");
        userDto.setLoginName("loginName");

        // Configure ExecuteMonitorMapper.selectExecuteMonitorByToolsId(...).
        final ExecuteMonitorBean executeMonitorBean = new ExecuteMonitorBean();
        executeMonitorBean.setRunningId(0L);
        final ExecuteParamsBean executeParamsBean = new ExecuteParamsBean();
        executeMonitorBean.setParams(Arrays.asList(executeParamsBean));
        final ExecuteProjectBean executeProjectBean = new ExecuteProjectBean();
        executeProjectBean.setParentId(0L);
        executeProjectBean.setProjectName("projectName");
        executeMonitorBean.setProjects(Arrays.asList(executeProjectBean));
        executeMonitorBean.setId(0L);
        executeMonitorBean.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        executeMonitorBean.setEstimateOperationalRisk("estimateOperationalRisk");
        executeMonitorBean.setEventNum("eventId");
        executeMonitorBean.setRunStatus(0L);
        executeMonitorBean.setRunTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        executeMonitorBean.setType(0L);
        final ExecuteFilesBean executeFilesBean = new ExecuteFilesBean();
        executeMonitorBean.setFiles(Arrays.asList(executeFilesBean));
        final List<ExecuteMonitorBean> executeMonitorBeans = Arrays.asList(executeMonitorBean);
        when(mockExecuteMonitorMapper.selectExecuteMonitorByToolsId(0L, 0L)).thenReturn(executeMonitorBeans);

        // Configure ToolsInfoMapper.selectToolsInfoById(...).
        final ToolsInfoEntity toolsInfoEntity = new ToolsInfoEntity();
        toolsInfoEntity.setId(0L);
        toolsInfoEntity.setCode("code");
        toolsInfoEntity.setName("name");
        toolsInfoEntity.setType(0);
        toolsInfoEntity.setClassification(0);
        when(mockToolsInfoMapper.selectToolsInfoById(0L)).thenReturn(toolsInfoEntity);

        // Configure ExecuteMonitorMapper.selectAct(...).
        final MonitorFlowActiveEntity monitorFlowActiveEntity = new MonitorFlowActiveEntity();
        monitorFlowActiveEntity.setActDefName("actDefName");
        monitorFlowActiveEntity.setReqId("reqId");
        monitorFlowActiveEntity.setId(0L);
        monitorFlowActiveEntity.setFlowId(0L);
        monitorFlowActiveEntity.setCallFlowId(0L);
        monitorFlowActiveEntity.setActName("name");
        monitorFlowActiveEntity.setActStatus("actStatus");
        monitorFlowActiveEntity.setActType("actType");
        final List<MonitorFlowActiveEntity> monitorFlowActiveEntities = Arrays.asList(monitorFlowActiveEntity);

        // Run the test
        final ExecuteMonitorDto result = executeMonitorServiceImplUnderTest.getExecuteDetailByToolsId(0L, 0L, userDto);

        // Verify the results
        verify(mockLogAuditService).insertLogAudit(any(LogAuditDto.class));
    }

    @Test
    void testGetExecuteDetailByToolsId_ExecuteMonitorMapperSelectExecuteMonitorByToolsIdReturnsNoItems() throws Exception {
        // Setup
        final UserDto userDto = new UserDto();
        userDto.setUserName("userName");
        userDto.setUserId(0L);
        userDto.setUserCode("userCode");
        userDto.setLoginName("loginName");

        when(mockExecuteMonitorMapper.selectExecuteMonitorByToolsId(0L, 0L)).thenReturn(Collections.emptyList());

        // Configure IExecuteHistoryService.getHistoryDetailToolsId(...).
        final ExecuteHistoryBean executeHistoryBean = new ExecuteHistoryBean();
        executeHistoryBean.setClassification(0);
        executeHistoryBean.setSearchStartTime("searchStartTime");
        executeHistoryBean.setSearchEndTime("searchEndTime");
        executeHistoryBean.setRunningId(0L);
        final ExecuteFilesBean executeFilesBean = new ExecuteFilesBean();
        executeHistoryBean.setFiles(Arrays.asList(executeFilesBean));
        final List<ExecuteHistoryBean> executeHistoryBeans = Arrays.asList(executeHistoryBean);
        when(mockHistoryService.getHistoryDetailToolsId(0L, 0L)).thenReturn(executeHistoryBeans);

        // Configure ToolsInfoMapper.selectToolsInfoById(...).
        final ToolsInfoEntity toolsInfoEntity = new ToolsInfoEntity();
        toolsInfoEntity.setId(0L);
        toolsInfoEntity.setCode("code");
        toolsInfoEntity.setName("name");
        toolsInfoEntity.setType(0);
        toolsInfoEntity.setClassification(0);
        when(mockToolsInfoMapper.selectToolsInfoById(0L)).thenReturn(toolsInfoEntity);

        // Configure ExecuteMonitorMapper.selectAct(...).
        final MonitorFlowActiveEntity monitorFlowActiveEntity = new MonitorFlowActiveEntity();
        monitorFlowActiveEntity.setActDefName("actDefName");
        monitorFlowActiveEntity.setReqId("reqId");
        monitorFlowActiveEntity.setId(0L);
        monitorFlowActiveEntity.setFlowId(0L);
        monitorFlowActiveEntity.setCallFlowId(0L);
        monitorFlowActiveEntity.setActName("name");
        monitorFlowActiveEntity.setActStatus("actStatus");
        monitorFlowActiveEntity.setActType("actType");
        final List<MonitorFlowActiveEntity> monitorFlowActiveEntities = Arrays.asList(monitorFlowActiveEntity);

        // Run the test
        final ExecuteMonitorDto result = executeMonitorServiceImplUnderTest.getExecuteDetailByToolsId(0L, 0L, userDto);

        // Verify the results
        verify(mockLogAuditService).insertLogAudit(any(LogAuditDto.class));
    }

    @Test
    void testGetExecuteDetailByToolsId_IExecuteHistoryServiceReturnsNoItems() throws Exception {
        // Setup
        final UserDto userDto = new UserDto();
        userDto.setUserName("userName");
        userDto.setUserId(0L);
        userDto.setUserCode("userCode");
        userDto.setLoginName("loginName");

        when(mockExecuteMonitorMapper.selectExecuteMonitorByToolsId(0L, 0L)).thenReturn(Collections.emptyList());
        when(mockHistoryService.getHistoryDetailToolsId(0L, 0L)).thenReturn(Collections.emptyList());
        final ToolsInfoEntity toolsInfoEntity = new ToolsInfoEntity();
        toolsInfoEntity.setId(0L);
        toolsInfoEntity.setCode("code");
        toolsInfoEntity.setName("name");
        toolsInfoEntity.setType(0);
        toolsInfoEntity.setClassification(0);
        when(mockToolsInfoMapper.selectToolsInfoById(0L)).thenReturn(toolsInfoEntity);
        // Configure ExecuteMonitorMapper.selectAct(...).
        final MonitorFlowActiveEntity monitorFlowActiveEntity = new MonitorFlowActiveEntity();
        monitorFlowActiveEntity.setActDefName("actDefName");
        monitorFlowActiveEntity.setReqId("reqId");
        monitorFlowActiveEntity.setId(0L);
        monitorFlowActiveEntity.setFlowId(0L);
        monitorFlowActiveEntity.setCallFlowId(0L);
        monitorFlowActiveEntity.setActName("name");
        monitorFlowActiveEntity.setActStatus("actStatus");
        monitorFlowActiveEntity.setActType("actType");
        final List<MonitorFlowActiveEntity> monitorFlowActiveEntities = Arrays.asList(monitorFlowActiveEntity);

        // Run the test
        final ExecuteMonitorDto result = executeMonitorServiceImplUnderTest.getExecuteDetailByToolsId(0L, 0L, userDto);

    }

    @Test
    void testGetExecuteDetailByToolsId_ExecuteMonitorMapperSelectActReturnsNoItems() throws Exception {
        // Setup
        final UserDto userDto = new UserDto();
        userDto.setUserName("userName");
        userDto.setUserId(0L);
        userDto.setUserCode("userCode");
        userDto.setLoginName("loginName");

        // Configure ExecuteMonitorMapper.selectExecuteMonitorByToolsId(...).
        final ExecuteMonitorBean executeMonitorBean = new ExecuteMonitorBean();
        executeMonitorBean.setRunningId(0L);
        final ExecuteParamsBean executeParamsBean = new ExecuteParamsBean();
        executeMonitorBean.setParams(Arrays.asList(executeParamsBean));
        final ExecuteProjectBean executeProjectBean = new ExecuteProjectBean();
        executeProjectBean.setParentId(0L);
        executeProjectBean.setProjectName("projectName");
        executeMonitorBean.setProjects(Arrays.asList(executeProjectBean));
        executeMonitorBean.setId(0L);
        executeMonitorBean.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        executeMonitorBean.setEstimateOperationalRisk("estimateOperationalRisk");
        executeMonitorBean.setEventNum("eventId");
        executeMonitorBean.setRunStatus(0L);
        executeMonitorBean.setRunTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        executeMonitorBean.setType(0L);
        final ExecuteFilesBean executeFilesBean = new ExecuteFilesBean();
        executeMonitorBean.setFiles(Arrays.asList(executeFilesBean));
        final List<ExecuteMonitorBean> executeMonitorBeans = Arrays.asList(executeMonitorBean);
        when(mockExecuteMonitorMapper.selectExecuteMonitorByToolsId(0L, 0L)).thenReturn(executeMonitorBeans);

        // Configure ToolsInfoMapper.selectToolsInfoById(...).
        final ToolsInfoEntity toolsInfoEntity = new ToolsInfoEntity();
        toolsInfoEntity.setId(0L);
        toolsInfoEntity.setCode("code");
        toolsInfoEntity.setName("name");
        toolsInfoEntity.setType(0);
        toolsInfoEntity.setClassification(0);
        when(mockToolsInfoMapper.selectToolsInfoById(0L)).thenReturn(toolsInfoEntity);

        // Run the test
        final ExecuteMonitorDto result = executeMonitorServiceImplUnderTest.getExecuteDetailByToolsId(0L, 0L, userDto);

        // Verify the results
        verify(mockLogAuditService).insertLogAudit(any(LogAuditDto.class));
    }

    @Test
    void testGetExecuteDetailByToolsId_StudioInteractThrowsStudioException() throws Exception {
        // Setup
        final UserDto userDto = new UserDto();
        userDto.setUserName("userName");
        userDto.setUserId(0L);
        userDto.setUserCode("userCode");
        userDto.setLoginName("loginName");

        // Configure ExecuteMonitorMapper.selectExecuteMonitorByToolsId(...).
        final ExecuteMonitorBean executeMonitorBean = new ExecuteMonitorBean();
        executeMonitorBean.setRunningId(0L);
        final ExecuteParamsBean executeParamsBean = new ExecuteParamsBean();
        executeMonitorBean.setParams(Arrays.asList(executeParamsBean));
        final ExecuteProjectBean executeProjectBean = new ExecuteProjectBean();
        executeProjectBean.setParentId(0L);
        executeProjectBean.setProjectName("projectName");
        executeMonitorBean.setProjects(Arrays.asList(executeProjectBean));
        executeMonitorBean.setId(0L);
        executeMonitorBean.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        executeMonitorBean.setEstimateOperationalRisk("estimateOperationalRisk");
        executeMonitorBean.setEventNum("eventId");
        executeMonitorBean.setRunStatus(0L);
        executeMonitorBean.setRunTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        executeMonitorBean.setType(0L);
        final ExecuteFilesBean executeFilesBean = new ExecuteFilesBean();
        executeMonitorBean.setFiles(Arrays.asList(executeFilesBean));
        final List<ExecuteMonitorBean> executeMonitorBeans = Arrays.asList(executeMonitorBean);
        when(mockExecuteMonitorMapper.selectExecuteMonitorByToolsId(0L, 0L)).thenReturn(executeMonitorBeans);

        // Configure ToolsInfoMapper.selectToolsInfoById(...).
        final ToolsInfoEntity toolsInfoEntity = new ToolsInfoEntity();
        toolsInfoEntity.setId(0L);
        toolsInfoEntity.setCode("code");
        toolsInfoEntity.setName("name");
        toolsInfoEntity.setType(0);
        toolsInfoEntity.setClassification(0);
        when(mockToolsInfoMapper.selectToolsInfoById(0L)).thenReturn(toolsInfoEntity);

        // Configure ExecuteMonitorMapper.selectAct(...).
        final MonitorFlowActiveEntity monitorFlowActiveEntity = new MonitorFlowActiveEntity();
        monitorFlowActiveEntity.setActDefName("actDefName");
        monitorFlowActiveEntity.setReqId("reqId");
        monitorFlowActiveEntity.setId(0L);
        monitorFlowActiveEntity.setFlowId(0L);
        monitorFlowActiveEntity.setCallFlowId(0L);
        monitorFlowActiveEntity.setActName("name");
        monitorFlowActiveEntity.setActStatus("actStatus");
        monitorFlowActiveEntity.setActType("actType");
        final List<MonitorFlowActiveEntity> monitorFlowActiveEntities = Arrays.asList(monitorFlowActiveEntity);

        // Run the test
        final ExecuteMonitorDto result = executeMonitorServiceImplUnderTest.getExecuteDetailByToolsId(0L, 0L, userDto);

        // Verify the results
        verify(mockLogAuditService).insertLogAudit(any(LogAuditDto.class));
    }

    @Test
    void testVerifyRunMonitory() {
        // Setup
        when(mockExecuteMonitorMapper.verifyRunMonitory(0L)).thenReturn(0);

        // Run the test
        final boolean result = executeMonitorServiceImplUnderTest.verifyRunMonitory(0L);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    void testGetTaskList() {
        // Setup
        // Configure ScriptInteract.getListTaskRuntime(...).
        final TaskResultDto taskResultDto = new TaskResultDto();
        taskResultDto.setRetry(false);
        taskResultDto.setScriptTaskIpsId(0L);
        taskResultDto.setAgentTaskId(0L);
        taskResultDto.setId(0L);
        taskResultDto.setScriptTaskId(0L);
        final PageInfo<TaskResultDto> taskResultDtoPageInfo = new PageInfo<>(Arrays.asList(taskResultDto));
        when(mockScriptInteract.getListTaskRuntime(0L, 0, 0)).thenReturn(taskResultDtoPageInfo);

        // Run the test
        final PageInfo<TaskResultDto> result = executeMonitorServiceImplUnderTest.getTaskList(0L, 0, 0);

        // Verify the results
    }

    @Test
    void testGetTaskList_ScriptInteractReturnsNoItem() {
        // Setup
        when(mockScriptInteract.getListTaskRuntime(0L, 0, 0)).thenReturn(PageInfo.emptyPageInfo());

        // Run the test
        final PageInfo<TaskResultDto> result = executeMonitorServiceImplUnderTest.getTaskList(0L, 0, 0);

        // Verify the results
    }

    @Test
    void testGetOutPutMessage() throws Exception {
        // Setup
        when(mockScriptInteract.getOutPutMessage(0L)).thenReturn("result");

        // Run the test
        final String result = executeMonitorServiceImplUnderTest.getOutPutMessage(0L);

        // Verify the results
        assertThat(result).isEqualTo("result");
    }

    @Test
    void testGetOutPutMessage_ScriptInteractThrowsScriptException() throws Exception {
        // Setup
        when(mockScriptInteract.getOutPutMessage(0L)).thenThrow(ScriptException.class);

        // Run the test
        assertThatThrownBy(() -> executeMonitorServiceImplUnderTest.getOutPutMessage(0L))
                .isInstanceOf(ScriptException.class);
    }

    @Test
    void testKillTask() throws Exception {
        // Setup
        final UserDto userDto = new UserDto();
        userDto.setUserName("userName");
        userDto.setUserId(0L);
        userDto.setUserCode("userCode");
        userDto.setLoginName("loginName");

        // Configure ExecuteMonitorMapper.selectExecuteMonitorByRunningId(...).
        final ExecuteMonitorBean executeMonitorBean = new ExecuteMonitorBean();
        executeMonitorBean.setRunningId(0L);
        final ExecuteParamsBean executeParamsBean = new ExecuteParamsBean();
        executeMonitorBean.setParams(Arrays.asList(executeParamsBean));
        final ExecuteProjectBean executeProjectBean = new ExecuteProjectBean();
        executeProjectBean.setParentId(0L);
        executeProjectBean.setProjectName("projectName");
        executeMonitorBean.setProjects(Arrays.asList(executeProjectBean));
        executeMonitorBean.setId(0L);
        executeMonitorBean.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        executeMonitorBean.setEstimateOperationalRisk("estimateOperationalRisk");
        executeMonitorBean.setEventNum("eventId");
        executeMonitorBean.setRunStatus(0L);
        executeMonitorBean.setRunTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        executeMonitorBean.setType(0L);
        final ExecuteFilesBean executeFilesBean = new ExecuteFilesBean();
        executeMonitorBean.setFiles(Arrays.asList(executeFilesBean));
        final List<ExecuteMonitorBean> executeMonitorBeans = Arrays.asList(executeMonitorBean);
        when(mockExecuteMonitorMapper.selectExecuteMonitorByRunningId(any(Long[].class)))
                .thenReturn(executeMonitorBeans);

        // Configure ScriptInteract.killScriptTask(...).
        final StopScriptTasksDto stopScriptTasksDto = new StopScriptTasksDto();
        stopScriptTasksDto.setSuccess(Arrays.asList(0L));
        stopScriptTasksDto.setFail(Arrays.asList(0L));

        // Configure ExecuteMonitorMapper.selectMonitorParentProjects(...).
        final ExecuteMonitorProjectEntity executeMonitorProjectEntity = new ExecuteMonitorProjectEntity();
        executeMonitorProjectEntity.setFlowId(0L);
        executeMonitorProjectEntity.setId(0L);
        executeMonitorProjectEntity.setParentId(0L);
        executeMonitorProjectEntity.setTdMonitorId(0L);
        executeMonitorProjectEntity.setNodeStatus(0L);
        executeMonitorProjectEntity.setToolType(0L);
        final List<ExecuteMonitorProjectEntity> executeMonitorProjectEntities = Arrays.asList(
                executeMonitorProjectEntity);
        when(mockExecuteMonitorMapper.selectMonitorParentProjects(any(Long[].class)))
                .thenReturn(executeMonitorProjectEntities);

        when(mockEngineInteract.engineKillPauseResumeFlow(any(Long[].class), eq(1L))).thenReturn("1");

        // Configure ExecuteMonitorMapper.selectExecuteMonitorByIds(...).
        final ExecuteMonitorBean executeMonitorBean1 = new ExecuteMonitorBean();
        executeMonitorBean1.setRunningId(0L);
        final ExecuteParamsBean executeParamsBean1 = new ExecuteParamsBean();
        executeMonitorBean1.setParams(Arrays.asList(executeParamsBean1));
        final ExecuteProjectBean executeProjectBean1 = new ExecuteProjectBean();
        executeProjectBean1.setParentId(0L);
        executeProjectBean1.setProjectName("projectName");
        executeMonitorBean1.setProjects(Arrays.asList(executeProjectBean1));
        executeMonitorBean1.setId(0L);
        executeMonitorBean1.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        executeMonitorBean1.setEstimateOperationalRisk("estimateOperationalRisk");
        executeMonitorBean1.setEventNum("eventId");
        executeMonitorBean1.setRunStatus(0L);
        executeMonitorBean1.setRunTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        executeMonitorBean1.setType(0L);
        final ExecuteFilesBean executeFilesBean1 = new ExecuteFilesBean();
        executeMonitorBean1.setFiles(Arrays.asList(executeFilesBean1));
        final List<ExecuteMonitorBean> executeMonitorBeans1 = Arrays.asList(executeMonitorBean1);
        when(mockExecuteMonitorMapper.selectExecuteMonitorByIds(any(Long[].class))).thenReturn(executeMonitorBeans1);

        // Run the test
        final Object result = executeMonitorServiceImplUnderTest.killTask(Arrays.asList(0L), userDto);

        // Verify the results
        verify(mockExecuteMonitorMapper).deleteMonitor(any(Long[].class));
        verify(mockExecuteMonitorMapper).deleteMonitorParams(any(Long[].class));
        verify(mockExecuteMonitorMapper).deleteMonitorXml(any(Long[].class));
        verify(mockExecuteMonitorMapper).deleteMonitorProject(any(Long[].class));
        verify(mockLogAuditService).insertLogAudit(any(LogAuditDto.class));
    }

    @Test
    void testKillTask_ExecuteMonitorMapperSelectExecuteMonitorByRunningIdReturnsNoItems() throws Exception {
        // Setup
        final UserDto userDto = new UserDto();
        userDto.setUserName("userName");
        userDto.setUserId(0L);
        userDto.setUserCode("userCode");
        userDto.setLoginName("loginName");

        when(mockExecuteMonitorMapper.selectExecuteMonitorByRunningId(any(Long[].class)))
                .thenReturn(Collections.emptyList());

        // Configure ScriptInteract.killScriptTask(...).
        final StopScriptTasksDto stopScriptTasksDto = new StopScriptTasksDto();
        stopScriptTasksDto.setSuccess(Arrays.asList(0L));
        stopScriptTasksDto.setFail(Arrays.asList(0L));

        // Configure ExecuteMonitorMapper.selectMonitorParentProjects(...).
        final ExecuteMonitorProjectEntity executeMonitorProjectEntity = new ExecuteMonitorProjectEntity();
        executeMonitorProjectEntity.setFlowId(0L);
        executeMonitorProjectEntity.setId(0L);
        executeMonitorProjectEntity.setParentId(0L);
        executeMonitorProjectEntity.setTdMonitorId(0L);
        executeMonitorProjectEntity.setNodeStatus(0L);
        executeMonitorProjectEntity.setToolType(0L);
        final List<ExecuteMonitorProjectEntity> executeMonitorProjectEntities = Arrays.asList(
                executeMonitorProjectEntity);


        // Configure ExecuteMonitorMapper.selectExecuteMonitorByIds(...).
        final ExecuteMonitorBean executeMonitorBean = new ExecuteMonitorBean();
        executeMonitorBean.setRunningId(0L);
        final ExecuteParamsBean executeParamsBean = new ExecuteParamsBean();
        executeMonitorBean.setParams(Arrays.asList(executeParamsBean));
        final ExecuteProjectBean executeProjectBean = new ExecuteProjectBean();
        executeProjectBean.setParentId(0L);
        executeProjectBean.setProjectName("projectName");
        executeMonitorBean.setProjects(Arrays.asList(executeProjectBean));
        executeMonitorBean.setId(0L);
        executeMonitorBean.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        executeMonitorBean.setEstimateOperationalRisk("estimateOperationalRisk");
        executeMonitorBean.setEventNum("eventId");
        executeMonitorBean.setRunStatus(0L);
        executeMonitorBean.setRunTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        executeMonitorBean.setType(0L);
        final ExecuteFilesBean executeFilesBean = new ExecuteFilesBean();
        executeMonitorBean.setFiles(Arrays.asList(executeFilesBean));
        final List<ExecuteMonitorBean> executeMonitorBeans = Arrays.asList(executeMonitorBean);

        // Run the test
        final Object result = executeMonitorServiceImplUnderTest.killTask(Arrays.asList(0L), userDto);

        // Verify the results
    }

    @Test
    void testKillTask_ExecuteMonitorMapperSelectMonitorParentProjectsReturnsNoItems() throws Exception {
        // Setup
        final UserDto userDto = new UserDto();
        userDto.setUserName("userName");
        userDto.setUserId(0L);
        userDto.setUserCode("userCode");
        userDto.setLoginName("loginName");

        // Configure ExecuteMonitorMapper.selectExecuteMonitorByRunningId(...).
        final ExecuteMonitorBean executeMonitorBean = new ExecuteMonitorBean();
        executeMonitorBean.setRunningId(0L);
        final ExecuteParamsBean executeParamsBean = new ExecuteParamsBean();
        executeMonitorBean.setParams(Arrays.asList(executeParamsBean));
        final ExecuteProjectBean executeProjectBean = new ExecuteProjectBean();
        executeProjectBean.setParentId(0L);
        executeProjectBean.setProjectName("projectName");
        executeMonitorBean.setProjects(Arrays.asList(executeProjectBean));
        executeMonitorBean.setId(0L);
        executeMonitorBean.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        executeMonitorBean.setEstimateOperationalRisk("estimateOperationalRisk");
        executeMonitorBean.setEventNum("eventId");
        executeMonitorBean.setRunStatus(0L);
        executeMonitorBean.setRunTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        executeMonitorBean.setType(0L);
        final ExecuteFilesBean executeFilesBean = new ExecuteFilesBean();
        executeMonitorBean.setFiles(Arrays.asList(executeFilesBean));
        final List<ExecuteMonitorBean> executeMonitorBeans = Arrays.asList(executeMonitorBean);
        when(mockExecuteMonitorMapper.selectExecuteMonitorByRunningId(any(Long[].class)))
                .thenReturn(executeMonitorBeans);

        // Configure ScriptInteract.killScriptTask(...).
        final StopScriptTasksDto stopScriptTasksDto = new StopScriptTasksDto();
        stopScriptTasksDto.setSuccess(Arrays.asList(0L));
        stopScriptTasksDto.setFail(Arrays.asList(0L));

        when(mockExecuteMonitorMapper.selectMonitorParentProjects(any(Long[].class)))
                .thenReturn(Collections.emptyList());

        // Configure ExecuteMonitorMapper.selectExecuteMonitorByIds(...).
        final ExecuteMonitorBean executeMonitorBean1 = new ExecuteMonitorBean();
        executeMonitorBean1.setRunningId(0L);
        final ExecuteParamsBean executeParamsBean1 = new ExecuteParamsBean();
        executeMonitorBean1.setParams(Arrays.asList(executeParamsBean1));
        final ExecuteProjectBean executeProjectBean1 = new ExecuteProjectBean();
        executeProjectBean1.setParentId(0L);
        executeProjectBean1.setProjectName("projectName");
        executeMonitorBean1.setProjects(Arrays.asList(executeProjectBean1));
        executeMonitorBean1.setId(0L);
        executeMonitorBean1.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        executeMonitorBean1.setEstimateOperationalRisk("estimateOperationalRisk");
        executeMonitorBean1.setEventNum("eventId");
        executeMonitorBean1.setRunStatus(0L);
        executeMonitorBean1.setRunTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        executeMonitorBean1.setType(0L);
        final ExecuteFilesBean executeFilesBean1 = new ExecuteFilesBean();
        executeMonitorBean1.setFiles(Arrays.asList(executeFilesBean1));
        final List<ExecuteMonitorBean> executeMonitorBeans1 = Arrays.asList(executeMonitorBean1);

        // Run the test
        final Object result = executeMonitorServiceImplUnderTest.killTask(Arrays.asList(0L), userDto);
        Long[] monitorId = {0L};
        // Verify the results
        //verify(mockExecuteMonitorMapper).deleteMonitor(monitorId);
        //verify(mockExecuteMonitorMapper).deleteMonitorParams(any(Long[].class));
        //verify(mockExecuteMonitorMapper).deleteMonitorXml(any(Long[].class));
        //verify(mockExecuteMonitorMapper).deleteMonitorProject(any(Long[].class));
        //verify(mockLogAuditService).insertLogAudit(any(LogAuditDto.class));
    }

    @Test
    void testKillTask_EngineInteractThrowsEngineServiceException() throws Exception {
        // Setup
        final UserDto userDto = new UserDto();
        userDto.setUserName("userName");
        userDto.setUserId(0L);
        userDto.setUserCode("userCode");
        userDto.setLoginName("loginName");

        // Configure ExecuteMonitorMapper.selectExecuteMonitorByRunningId(...).
        final ExecuteMonitorBean executeMonitorBean = new ExecuteMonitorBean();
        executeMonitorBean.setRunningId(0L);
        final ExecuteParamsBean executeParamsBean = new ExecuteParamsBean();
        executeMonitorBean.setParams(Arrays.asList(executeParamsBean));
        final ExecuteProjectBean executeProjectBean = new ExecuteProjectBean();
        executeProjectBean.setParentId(0L);
        executeProjectBean.setProjectName("projectName");
        executeMonitorBean.setProjects(Arrays.asList(executeProjectBean));
        executeMonitorBean.setId(0L);
        executeMonitorBean.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        executeMonitorBean.setEstimateOperationalRisk("estimateOperationalRisk");
        executeMonitorBean.setEventNum("eventId");
        executeMonitorBean.setRunStatus(0L);
        executeMonitorBean.setRunTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        executeMonitorBean.setType(0L);
        final ExecuteFilesBean executeFilesBean = new ExecuteFilesBean();
        executeMonitorBean.setFiles(Arrays.asList(executeFilesBean));
        final List<ExecuteMonitorBean> executeMonitorBeans = Arrays.asList(executeMonitorBean);
        when(mockExecuteMonitorMapper.selectExecuteMonitorByRunningId(any(Long[].class)))
                .thenReturn(executeMonitorBeans);

        // Configure ScriptInteract.killScriptTask(...).
        final StopScriptTasksDto stopScriptTasksDto = new StopScriptTasksDto();
        stopScriptTasksDto.setSuccess(Arrays.asList(0L));
        stopScriptTasksDto.setFail(Arrays.asList(0L));
        // Configure ExecuteMonitorMapper.selectMonitorParentProjects(...).
        final ExecuteMonitorProjectEntity executeMonitorProjectEntity = new ExecuteMonitorProjectEntity();
        executeMonitorProjectEntity.setFlowId(0L);
        executeMonitorProjectEntity.setId(0L);
        executeMonitorProjectEntity.setParentId(0L);
        executeMonitorProjectEntity.setTdMonitorId(0L);
        executeMonitorProjectEntity.setNodeStatus(0L);
        executeMonitorProjectEntity.setToolType(0L);
        final List<ExecuteMonitorProjectEntity> executeMonitorProjectEntities = Arrays.asList(
                executeMonitorProjectEntity);
        when(mockExecuteMonitorMapper.selectMonitorParentProjects(any(Long[].class)))
                .thenReturn(executeMonitorProjectEntities);

        when(mockEngineInteract.engineKillPauseResumeFlow(any(Long[].class), eq(1L)))
                .thenThrow(EngineServiceException.class);

        // Run the test
        final Object result = executeMonitorServiceImplUnderTest.killTask(Arrays.asList(0L), userDto);

        // Verify the results
        verify(mockLogAuditService).insertLogAudit(any(LogAuditDto.class));
    }

    @Test
    void testKillTask_ExecuteMonitorMapperSelectExecuteMonitorByIdsReturnsNoItems() throws Exception {
        // Setup
        final UserDto userDto = new UserDto();
        userDto.setUserName("userName");
        userDto.setUserId(0L);
        userDto.setUserCode("userCode");
        userDto.setLoginName("loginName");

        // Configure ExecuteMonitorMapper.selectExecuteMonitorByRunningId(...).
        final ExecuteMonitorBean executeMonitorBean = new ExecuteMonitorBean();
        executeMonitorBean.setRunningId(0L);
        final ExecuteParamsBean executeParamsBean = new ExecuteParamsBean();
        executeMonitorBean.setParams(Arrays.asList(executeParamsBean));
        final ExecuteProjectBean executeProjectBean = new ExecuteProjectBean();
        executeProjectBean.setParentId(0L);
        executeProjectBean.setProjectName("projectName");
        executeMonitorBean.setProjects(Arrays.asList(executeProjectBean));
        executeMonitorBean.setId(0L);
        executeMonitorBean.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        executeMonitorBean.setEstimateOperationalRisk("estimateOperationalRisk");
        executeMonitorBean.setEventNum("eventId");
        executeMonitorBean.setRunStatus(0L);
        executeMonitorBean.setRunTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        executeMonitorBean.setType(0L);
        final ExecuteFilesBean executeFilesBean = new ExecuteFilesBean();
        executeMonitorBean.setFiles(Arrays.asList(executeFilesBean));
        final List<ExecuteMonitorBean> executeMonitorBeans = Arrays.asList(executeMonitorBean);
        when(mockExecuteMonitorMapper.selectExecuteMonitorByRunningId(any(Long[].class)))
                .thenReturn(executeMonitorBeans);

        // Configure ScriptInteract.killScriptTask(...).
        final StopScriptTasksDto stopScriptTasksDto = new StopScriptTasksDto();
        stopScriptTasksDto.setSuccess(Arrays.asList(0L));
        stopScriptTasksDto.setFail(Arrays.asList(0L));

        // Configure ExecuteMonitorMapper.selectMonitorParentProjects(...).
        final ExecuteMonitorProjectEntity executeMonitorProjectEntity = new ExecuteMonitorProjectEntity();
        executeMonitorProjectEntity.setFlowId(0L);
        executeMonitorProjectEntity.setId(0L);
        executeMonitorProjectEntity.setParentId(0L);
        executeMonitorProjectEntity.setTdMonitorId(0L);
        executeMonitorProjectEntity.setNodeStatus(0L);
        executeMonitorProjectEntity.setToolType(0L);
        final List<ExecuteMonitorProjectEntity> executeMonitorProjectEntities = Arrays.asList(
                executeMonitorProjectEntity);
        when(mockExecuteMonitorMapper.selectMonitorParentProjects(any(Long[].class)))
                .thenReturn(executeMonitorProjectEntities);

        when(mockEngineInteract.engineKillPauseResumeFlow(any(Long[].class), eq(1L))).thenReturn("1");
        when(mockExecuteMonitorMapper.selectExecuteMonitorByIds(any(Long[].class))).thenReturn(Collections.emptyList());

        // Run the test
        final Object result = executeMonitorServiceImplUnderTest.killTask(Arrays.asList(0L), userDto);

        // Verify the results
        verify(mockHistoryService).insertHistory(any(ExecuteMonitorBean.class), eq(4L));
        verify(mockExecuteMonitorMapper).deleteMonitor(any(Long[].class));
        verify(mockExecuteMonitorMapper).deleteMonitorParams(any(Long[].class));
        verify(mockExecuteMonitorMapper).deleteMonitorXml(any(Long[].class));
        verify(mockExecuteMonitorMapper).deleteMonitorProject(any(Long[].class));
        verify(mockLogAuditService).insertLogAudit(any(LogAuditDto.class));
    }

    @Test
    void testKillTask_IExecuteHistoryServiceThrowsExecuteHistoryException() throws Exception {
        // Setup
        final UserDto userDto = new UserDto();
        userDto.setUserName("userName");
        userDto.setUserId(0L);
        userDto.setUserCode("userCode");
        userDto.setLoginName("loginName");

        // Configure ExecuteMonitorMapper.selectExecuteMonitorByRunningId(...).
        final ExecuteMonitorBean executeMonitorBean = new ExecuteMonitorBean();
        executeMonitorBean.setRunningId(0L);
        final ExecuteParamsBean executeParamsBean = new ExecuteParamsBean();
        executeMonitorBean.setParams(Arrays.asList(executeParamsBean));
        final ExecuteProjectBean executeProjectBean = new ExecuteProjectBean();
        executeProjectBean.setParentId(0L);
        executeProjectBean.setProjectName("projectName");
        executeMonitorBean.setProjects(Arrays.asList(executeProjectBean));
        executeMonitorBean.setId(0L);
        executeMonitorBean.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        executeMonitorBean.setEstimateOperationalRisk("estimateOperationalRisk");
        executeMonitorBean.setEventNum("eventId");
        executeMonitorBean.setRunStatus(0L);
        executeMonitorBean.setRunTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        executeMonitorBean.setType(0L);
        final ExecuteFilesBean executeFilesBean = new ExecuteFilesBean();
        executeMonitorBean.setFiles(Arrays.asList(executeFilesBean));
        final List<ExecuteMonitorBean> executeMonitorBeans = Arrays.asList(executeMonitorBean);
        lenient().when(mockExecuteMonitorMapper.selectExecuteMonitorByRunningId(any(Long[].class)))
                .thenReturn(executeMonitorBeans);

        // Configure ScriptInteract.killScriptTask(...).
        final StopScriptTasksDto stopScriptTasksDto = new StopScriptTasksDto();
        stopScriptTasksDto.setSuccess(Arrays.asList(0L));
        stopScriptTasksDto.setFail(Arrays.asList(0L));
        lenient().when(mockScriptInteract.killScriptTask(Arrays.asList(0L))).thenReturn(stopScriptTasksDto);

        // Configure ExecuteMonitorMapper.selectMonitorParentProjects(...).
        final ExecuteMonitorProjectEntity executeMonitorProjectEntity = new ExecuteMonitorProjectEntity();
        executeMonitorProjectEntity.setFlowId(0L);
        executeMonitorProjectEntity.setId(0L);
        executeMonitorProjectEntity.setParentId(0L);
        executeMonitorProjectEntity.setTdMonitorId(0L);
        executeMonitorProjectEntity.setNodeStatus(0L);
        executeMonitorProjectEntity.setToolType(0L);
        final List<ExecuteMonitorProjectEntity> executeMonitorProjectEntities = Arrays.asList(
                executeMonitorProjectEntity);
        lenient().when(mockExecuteMonitorMapper.selectMonitorParentProjects(any(Long[].class)))
                .thenReturn(executeMonitorProjectEntities);

        lenient().when(mockEngineInteract.engineKillPauseResumeFlow(any(Long[].class), eq(1L))).thenReturn("result");

        // Configure ExecuteMonitorMapper.selectExecuteMonitorByIds(...).
        final ExecuteMonitorBean executeMonitorBean1 = new ExecuteMonitorBean();
        executeMonitorBean1.setRunningId(0L);
        final ExecuteParamsBean executeParamsBean1 = new ExecuteParamsBean();
        executeMonitorBean1.setParams(Arrays.asList(executeParamsBean1));
        final ExecuteProjectBean executeProjectBean1 = new ExecuteProjectBean();
        executeProjectBean1.setParentId(0L);
        executeProjectBean1.setProjectName("projectName");
        executeMonitorBean1.setProjects(Arrays.asList(executeProjectBean1));
        executeMonitorBean1.setId(0L);
        executeMonitorBean1.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        executeMonitorBean1.setEstimateOperationalRisk("estimateOperationalRisk");
        executeMonitorBean1.setEventNum("eventId");
        executeMonitorBean1.setRunStatus(0L);
        executeMonitorBean1.setRunTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        executeMonitorBean1.setType(0L);
        final ExecuteFilesBean executeFilesBean1 = new ExecuteFilesBean();
        executeMonitorBean1.setFiles(Arrays.asList(executeFilesBean1));
        final List<ExecuteMonitorBean> executeMonitorBeans1 = Arrays.asList(executeMonitorBean1);
        lenient().when(mockExecuteMonitorMapper.selectExecuteMonitorByIds(any(Long[].class))).thenReturn(executeMonitorBeans1);

        lenient().when(mockHistoryService.insertHistory(any(ExecuteMonitorBean.class), eq(0L)))
                .thenThrow(ExecuteHistoryException.class);


    }

    @Test
    void testGetNodeDetail() throws Exception {
        // Setup
        // Configure ExecuteMonitorMapper.selectAct(...).
        final MonitorFlowActiveEntity monitorFlowActiveEntity = new MonitorFlowActiveEntity();
        monitorFlowActiveEntity.setActDefName("actDefName");
        monitorFlowActiveEntity.setReqId("reqId");
        monitorFlowActiveEntity.setId(0L);
        monitorFlowActiveEntity.setFlowId(0L);
        monitorFlowActiveEntity.setCallFlowId(0L);
        monitorFlowActiveEntity.setActName("name");
        monitorFlowActiveEntity.setActStatus("actStatus");
        monitorFlowActiveEntity.setActType("actType");
        final List<MonitorFlowActiveEntity> monitorFlowActiveEntities = Arrays.asList(monitorFlowActiveEntity);
        when(mockExecuteMonitorMapper.selectAct(any(MonitorFlowActiveEntity.class)))
                .thenReturn(monitorFlowActiveEntities);

        // Configure IToolsActOutputService.selectToolsActOutputList(...).
        final ActivityOutPutResultDto activityOutPutResultDto = new ActivityOutPutResultDto();
        activityOutPutResultDto.setRet(0);
        activityOutPutResultDto.setStdout("stdout");
        activityOutPutResultDto.setLastLine("lastLine");
        activityOutPutResultDto.setIsTimeout(false);
        activityOutPutResultDto.setStderr("stderr");
        final List<ActivityOutPutResultDto> activityOutPutResultDtos = Arrays.asList(activityOutPutResultDto);
        when(mockToolsActOutputService.selectToolsActOutputList(any(ActivityOutPutQueryDto.class)))
                .thenReturn(activityOutPutResultDtos);

        // Run the test
        final List<ActivityOutPutResultDto> result = executeMonitorServiceImplUnderTest.getNodeDetail("name", 0L,
                "reqId");

        // Verify the results
    }

    @Test
    void testGetNodeDetail_ExecuteMonitorMapperReturnsNoItems() throws Exception {
        // Setup
        when(mockExecuteMonitorMapper.selectAct(any(MonitorFlowActiveEntity.class)))
                .thenReturn(Collections.emptyList());

        // Configure IToolsActOutputService.selectToolsActOutputList(...).
        final ActivityOutPutResultDto activityOutPutResultDto = new ActivityOutPutResultDto();
        activityOutPutResultDto.setRet(0);
        activityOutPutResultDto.setStdout("stdout");
        activityOutPutResultDto.setLastLine("lastLine");
        activityOutPutResultDto.setIsTimeout(false);
        activityOutPutResultDto.setStderr("stderr");
        final List<ActivityOutPutResultDto> activityOutPutResultDtos = Arrays.asList(activityOutPutResultDto);
        when(mockToolsActOutputService.selectToolsActOutputList(any(ActivityOutPutQueryDto.class)))
                .thenReturn(activityOutPutResultDtos);

        // Run the test
        final List<ActivityOutPutResultDto> result = executeMonitorServiceImplUnderTest.getNodeDetail("name", 0L,
                "reqId");

        // Verify the results
    }

    @Test
    void testGetNodeDetail_IToolsActOutputServiceReturnsNoItems() throws Exception {
        // Setup
        // Configure ExecuteMonitorMapper.selectAct(...).
        final MonitorFlowActiveEntity monitorFlowActiveEntity = new MonitorFlowActiveEntity();
        monitorFlowActiveEntity.setActDefName("actDefName");
        monitorFlowActiveEntity.setReqId("reqId");
        monitorFlowActiveEntity.setId(0L);
        monitorFlowActiveEntity.setFlowId(0L);
        monitorFlowActiveEntity.setCallFlowId(0L);
        monitorFlowActiveEntity.setActName("name");
        monitorFlowActiveEntity.setActStatus("actStatus");
        monitorFlowActiveEntity.setActType("actType");
        final List<MonitorFlowActiveEntity> monitorFlowActiveEntities = Arrays.asList(monitorFlowActiveEntity);
        when(mockExecuteMonitorMapper.selectAct(any(MonitorFlowActiveEntity.class)))
                .thenReturn(monitorFlowActiveEntities);

        when(mockToolsActOutputService.selectToolsActOutputList(any(ActivityOutPutQueryDto.class)))
                .thenReturn(Collections.emptyList());

        // Configure EngineInteract.engineActivityOutPut(...).
        final ActivityOutPutResultDto activityOutPutResultDto = new ActivityOutPutResultDto();
        activityOutPutResultDto.setRet(0);
        activityOutPutResultDto.setStdout("stdout");
        activityOutPutResultDto.setLastLine("lastLine");
        activityOutPutResultDto.setIsTimeout(false);
        activityOutPutResultDto.setStderr("stderr");
        final List<ActivityOutPutResultDto> activityOutPutResultDtos = Arrays.asList(activityOutPutResultDto);
        when(mockEngineInteract.engineActivityOutPut(Arrays.asList("value"))).thenReturn(activityOutPutResultDtos);

        // Run the test
        final List<ActivityOutPutResultDto> result = executeMonitorServiceImplUnderTest.getNodeDetail("name", 0L,
                "value");

        // Verify the results
    }

    @Test
    void testGetNodeDetail_EngineInteractReturnsNoItems() throws Exception {
        // Setup
        // Configure ExecuteMonitorMapper.selectAct(...).
        final MonitorFlowActiveEntity monitorFlowActiveEntity = new MonitorFlowActiveEntity();
        monitorFlowActiveEntity.setActDefName("actDefName");
        monitorFlowActiveEntity.setReqId("reqId");
        monitorFlowActiveEntity.setId(0L);
        monitorFlowActiveEntity.setFlowId(0L);
        monitorFlowActiveEntity.setCallFlowId(0L);
        monitorFlowActiveEntity.setActName("name");
        monitorFlowActiveEntity.setActStatus("actStatus");
        monitorFlowActiveEntity.setActType("actType");
        final List<MonitorFlowActiveEntity> monitorFlowActiveEntities = Arrays.asList(monitorFlowActiveEntity);
        when(mockExecuteMonitorMapper.selectAct(any(MonitorFlowActiveEntity.class)))
                .thenReturn(monitorFlowActiveEntities);

        when(mockToolsActOutputService.selectToolsActOutputList(any(ActivityOutPutQueryDto.class)))
                .thenReturn(Collections.emptyList());
        when(mockEngineInteract.engineActivityOutPut(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Run the test
        final List<ActivityOutPutResultDto> result = executeMonitorServiceImplUnderTest.getNodeDetail("name", 0L,
                "value");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetNodeDetail_EngineInteractThrowsEngineServiceException() throws Exception {
        // Setup
        // Configure ExecuteMonitorMapper.selectAct(...).
        final MonitorFlowActiveEntity monitorFlowActiveEntity = new MonitorFlowActiveEntity();
        monitorFlowActiveEntity.setActDefName("actDefName");
        monitorFlowActiveEntity.setReqId("reqId");
        monitorFlowActiveEntity.setId(0L);
        monitorFlowActiveEntity.setFlowId(0L);
        monitorFlowActiveEntity.setCallFlowId(0L);
        monitorFlowActiveEntity.setActName("name");
        monitorFlowActiveEntity.setActStatus("actStatus");
        monitorFlowActiveEntity.setActType("actType");
        final List<MonitorFlowActiveEntity> monitorFlowActiveEntities = Arrays.asList(monitorFlowActiveEntity);
        when(mockExecuteMonitorMapper.selectAct(any(MonitorFlowActiveEntity.class)))
                .thenReturn(monitorFlowActiveEntities);

        when(mockToolsActOutputService.selectToolsActOutputList(any(ActivityOutPutQueryDto.class)))
                .thenReturn(Collections.emptyList());
        when(mockEngineInteract.engineActivityOutPut(Arrays.asList("reqId"))).thenThrow(EngineServiceException.class);

        // Run the test
        assertThatThrownBy(() -> executeMonitorServiceImplUnderTest.getNodeDetail("name", 0L, "reqId"))
                .isInstanceOf(EngineServiceException.class);
    }

    @Test
    void testGetScriptAgentList11() {
        // Setup
        final MonitorFlowActiveQueryDto query = new MonitorFlowActiveQueryDto();
        query.setFlowId(0L);
        query.setScriptId("scriptId");
        query.setActName("name");

        final UserDto user = new UserDto();
        user.setUserName("userName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        // Configure ExecuteMonitorMapper.selectAct(...).
        final MonitorFlowActiveEntity monitorFlowActiveEntity = new MonitorFlowActiveEntity();
        monitorFlowActiveEntity.setActDefName("actDefName");
        monitorFlowActiveEntity.setReqId("reqId");
        monitorFlowActiveEntity.setId(0L);
        monitorFlowActiveEntity.setFlowId(0L);
        monitorFlowActiveEntity.setCallFlowId(0L);
        monitorFlowActiveEntity.setActName("name");
        monitorFlowActiveEntity.setActStatus("actStatus");
        monitorFlowActiveEntity.setActType("actType");
        final List<MonitorFlowActiveEntity> monitorFlowActiveEntities = Arrays.asList(monitorFlowActiveEntity);
        when(mockExecuteMonitorMapper.selectAct(any(MonitorFlowActiveEntity.class)))
                .thenReturn(monitorFlowActiveEntities);

        // Configure IToolsActOutputService.selectToolsActOutputList(...).
        final ActivityOutPutResultDto activityOutPutResultDto = new ActivityOutPutResultDto();
        activityOutPutResultDto.setRet(0);
        activityOutPutResultDto.setStdout("stdout");
        activityOutPutResultDto.setLastLine("lastLine");
        activityOutPutResultDto.setIsTimeout(false);
        activityOutPutResultDto.setStderr("stderr");
        final List<ActivityOutPutResultDto> activityOutPutResultDtos = Arrays.asList(activityOutPutResultDto);
        when(mockToolsActOutputService.selectToolsActOutputList(any(ActivityOutPutQueryDto.class)))
                .thenReturn(activityOutPutResultDtos);

        // Run the test
        final PageInfo<MonitorFlowActiveDto> result = executeMonitorServiceImplUnderTest.getScriptAgentList11(query, 1,
                2, user);

        // Verify the results
    }

    @Test
    void testGetScriptAgentList11_ExecuteMonitorMapperReturnsNoItems() {
        // Setup
        final MonitorFlowActiveQueryDto query = new MonitorFlowActiveQueryDto();
        query.setFlowId(0L);
        query.setScriptId("scriptId");
        query.setActName("name");

        final UserDto user = new UserDto();
        user.setUserName("userName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        when(mockExecuteMonitorMapper.selectAct(any(MonitorFlowActiveEntity.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final PageInfo<MonitorFlowActiveDto> result = executeMonitorServiceImplUnderTest.getScriptAgentList11(query, 0,
                0, user);

        // Verify the results
    }

    @Test
    void testGetScriptAgentList11_IToolsActOutputServiceReturnsNoItems() throws Exception {
        // Setup
        final MonitorFlowActiveQueryDto query = new MonitorFlowActiveQueryDto();
        query.setFlowId(0L);
        query.setScriptId("scriptId");
        query.setActName("name");

        final UserDto user = new UserDto();
        user.setUserName("userName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        // Configure ExecuteMonitorMapper.selectAct(...).
        final MonitorFlowActiveEntity monitorFlowActiveEntity = new MonitorFlowActiveEntity();
        monitorFlowActiveEntity.setActDefName("actDefName");
        monitorFlowActiveEntity.setReqId("value");
        monitorFlowActiveEntity.setId(0L);
        monitorFlowActiveEntity.setFlowId(0L);
        monitorFlowActiveEntity.setCallFlowId(0L);
        monitorFlowActiveEntity.setActName("name");
        monitorFlowActiveEntity.setActStatus("actStatus");
        monitorFlowActiveEntity.setActType("actType");
        final List<MonitorFlowActiveEntity> monitorFlowActiveEntities = Arrays.asList(monitorFlowActiveEntity);
        when(mockExecuteMonitorMapper.selectAct(any(MonitorFlowActiveEntity.class)))
                .thenReturn(monitorFlowActiveEntities);

        when(mockToolsActOutputService.selectToolsActOutputList(any(ActivityOutPutQueryDto.class)))
                .thenReturn(Collections.emptyList());

        // Configure EngineInteract.engineActivityOutPut(...).
        final ActivityOutPutResultDto activityOutPutResultDto = new ActivityOutPutResultDto();
        activityOutPutResultDto.setRet(0);
        activityOutPutResultDto.setStdout("stdout");
        activityOutPutResultDto.setLastLine("lastLine");
        activityOutPutResultDto.setIsTimeout(false);
        activityOutPutResultDto.setStderr("stderr");
        final List<ActivityOutPutResultDto> activityOutPutResultDtos = Arrays.asList(activityOutPutResultDto);
        when(mockEngineInteract.engineActivityOutPut(Arrays.asList("value"))).thenReturn(activityOutPutResultDtos);

        // Run the test
        final PageInfo<MonitorFlowActiveDto> result = executeMonitorServiceImplUnderTest.getScriptAgentList11(query, 1,
                2, user);

        // Verify the results
    }

    @Test
    void testGetScriptAgentList11_EngineInteractReturnsNoItems() throws Exception {
        // Setup
        final MonitorFlowActiveQueryDto query = new MonitorFlowActiveQueryDto();
        query.setFlowId(0L);
        query.setScriptId("scriptId");
        query.setActName("name");

        final UserDto user = new UserDto();
        user.setUserName("userName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        // Configure ExecuteMonitorMapper.selectAct(...).
        final MonitorFlowActiveEntity monitorFlowActiveEntity = new MonitorFlowActiveEntity();
        monitorFlowActiveEntity.setActDefName("actDefName");
        monitorFlowActiveEntity.setReqId("reqId");
        monitorFlowActiveEntity.setId(0L);
        monitorFlowActiveEntity.setFlowId(0L);
        monitorFlowActiveEntity.setCallFlowId(0L);
        monitorFlowActiveEntity.setActName("name");
        monitorFlowActiveEntity.setActStatus("actStatus");
        monitorFlowActiveEntity.setActType("actType");
        final List<MonitorFlowActiveEntity> monitorFlowActiveEntities = Arrays.asList(monitorFlowActiveEntity);
        when(mockExecuteMonitorMapper.selectAct(any(MonitorFlowActiveEntity.class)))
                .thenReturn(monitorFlowActiveEntities);

        when(mockToolsActOutputService.selectToolsActOutputList(any(ActivityOutPutQueryDto.class)))
                .thenReturn(Collections.emptyList());
        when(mockEngineInteract.engineActivityOutPut(Arrays.asList("reqId"))).thenReturn(Collections.emptyList());

        // Run the test
        final PageInfo<MonitorFlowActiveDto> result = executeMonitorServiceImplUnderTest.getScriptAgentList11(query, 1,
                2, user);

        // Verify the results
    }

    @Test
    void testGetScriptAgentList11_EngineInteractThrowsEngineServiceException() throws Exception {
        // Setup
        final MonitorFlowActiveQueryDto query = new MonitorFlowActiveQueryDto();
        query.setFlowId(0L);
        query.setScriptId("scriptId");
        query.setActName("name");

        final UserDto user = new UserDto();
        user.setUserName("userName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        // Configure ExecuteMonitorMapper.selectAct(...).
        final MonitorFlowActiveEntity monitorFlowActiveEntity = new MonitorFlowActiveEntity();
        monitorFlowActiveEntity.setActDefName("actDefName");
        monitorFlowActiveEntity.setReqId("reqId");
        monitorFlowActiveEntity.setId(0L);
        monitorFlowActiveEntity.setFlowId(0L);
        monitorFlowActiveEntity.setCallFlowId(0L);
        monitorFlowActiveEntity.setActName("name");
        monitorFlowActiveEntity.setActStatus("actStatus");
        monitorFlowActiveEntity.setActType("actType");
        final List<MonitorFlowActiveEntity> monitorFlowActiveEntities = Arrays.asList(monitorFlowActiveEntity);
        when(mockExecuteMonitorMapper.selectAct(any(MonitorFlowActiveEntity.class)))
                .thenReturn(monitorFlowActiveEntities);

        when(mockToolsActOutputService.selectToolsActOutputList(any(ActivityOutPutQueryDto.class)))
                .thenReturn(Collections.emptyList());
        when(mockEngineInteract.engineActivityOutPut(any())).thenThrow(EngineServiceException.class);

        // Run the test
        final PageInfo<MonitorFlowActiveDto> result = executeMonitorServiceImplUnderTest.getScriptAgentList11(query, 1,
                2, user);

        // Verify the results
    }

    @Test
    void testGetScriptAgentList1() {
        // Setup
        final MonitorFlowActiveQueryDto query = new MonitorFlowActiveQueryDto();
        query.setFlowId(0L);
        query.setScriptId("scriptId");
        query.setActName("name");

        final UserDto user = new UserDto();
        user.setUserName("userName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        // Configure ExecuteMonitorMapper.selectAct(...).
        final MonitorFlowActiveEntity monitorFlowActiveEntity = new MonitorFlowActiveEntity();
        monitorFlowActiveEntity.setActDefName("actDefName");
        monitorFlowActiveEntity.setReqId("reqId");
        monitorFlowActiveEntity.setId(0L);
        monitorFlowActiveEntity.setFlowId(0L);
        monitorFlowActiveEntity.setCallFlowId(0L);
        monitorFlowActiveEntity.setActName("name");
        monitorFlowActiveEntity.setActStatus("actStatus");
        monitorFlowActiveEntity.setActType("actType");
        final List<MonitorFlowActiveEntity> monitorFlowActiveEntities = Arrays.asList(monitorFlowActiveEntity);
        when(mockExecuteMonitorMapper.selectAct(any(MonitorFlowActiveEntity.class)))
                .thenReturn(monitorFlowActiveEntities);

        // Configure IToolsActOutputService.selectToolsActOutputList(...).
        final ActivityOutPutResultDto activityOutPutResultDto = new ActivityOutPutResultDto();
        activityOutPutResultDto.setRet(0);
        activityOutPutResultDto.setStdout("stdout");
        activityOutPutResultDto.setLastLine("lastLine");
        activityOutPutResultDto.setIsTimeout(false);
        activityOutPutResultDto.setStderr("stderr");
        final List<ActivityOutPutResultDto> activityOutPutResultDtos = Arrays.asList(activityOutPutResultDto);
        when(mockToolsActOutputService.selectToolsActOutputList(any(ActivityOutPutQueryDto.class)))
                .thenReturn(activityOutPutResultDtos);

        // Run the test
        final PageInfo<MonitorFlowActiveDto> result = executeMonitorServiceImplUnderTest.getScriptAgentList1(query, 0,
                0, user);

        // Verify the results
    }

    @Test
    void testGetScriptAgentList1_ExecuteMonitorMapperReturnsNoItems() {
        // Setup
        final MonitorFlowActiveQueryDto query = new MonitorFlowActiveQueryDto();
        query.setFlowId(0L);
        query.setScriptId("scriptId");
        query.setActName("name");

        final UserDto user = new UserDto();
        user.setUserName("userName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        when(mockExecuteMonitorMapper.selectAct(any(MonitorFlowActiveEntity.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final PageInfo<MonitorFlowActiveDto> result = executeMonitorServiceImplUnderTest.getScriptAgentList1(query, 0,
                0, user);

        // Verify the results
    }

    @Test
    void testGetScriptAgentList1_IToolsActOutputServiceReturnsNoItems() throws Exception {
        // Setup
        final MonitorFlowActiveQueryDto query = new MonitorFlowActiveQueryDto();
        query.setFlowId(0L);
        query.setScriptId("scriptId");
        query.setActName("name");

        final UserDto user = new UserDto();
        user.setUserName("userName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        // Configure ExecuteMonitorMapper.selectAct(...).
        final MonitorFlowActiveEntity monitorFlowActiveEntity = new MonitorFlowActiveEntity();
        monitorFlowActiveEntity.setActDefName("actDefName");
        monitorFlowActiveEntity.setReqId("reqId");
        monitorFlowActiveEntity.setId(0L);
        monitorFlowActiveEntity.setFlowId(0L);
        monitorFlowActiveEntity.setCallFlowId(0L);
        monitorFlowActiveEntity.setActName("name");
        monitorFlowActiveEntity.setActStatus("actStatus");
        monitorFlowActiveEntity.setActType("actType");
        final List<MonitorFlowActiveEntity> monitorFlowActiveEntities = Arrays.asList(monitorFlowActiveEntity);
        when(mockExecuteMonitorMapper.selectAct(any(MonitorFlowActiveEntity.class)))
                .thenReturn(monitorFlowActiveEntities);

        when(mockToolsActOutputService.selectToolsActOutputList(any(ActivityOutPutQueryDto.class)))
                .thenReturn(Collections.emptyList());

        // Configure EngineInteract.engineActivityOutPut(...).
        final ActivityOutPutResultDto activityOutPutResultDto = new ActivityOutPutResultDto();
        activityOutPutResultDto.setRet(0);
        activityOutPutResultDto.setStdout("stdout");
        activityOutPutResultDto.setLastLine("lastLine");
        activityOutPutResultDto.setIsTimeout(false);
        activityOutPutResultDto.setStderr("stderr");
        final List<ActivityOutPutResultDto> activityOutPutResultDtos = Arrays.asList(activityOutPutResultDto);
        when(mockEngineInteract.engineActivityOutPut(Arrays.asList("reqId"))).thenReturn(activityOutPutResultDtos);

        // Run the test
        final PageInfo<MonitorFlowActiveDto> result = executeMonitorServiceImplUnderTest.getScriptAgentList1(query, 0,
                0, user);

        // Verify the results
    }

    @Test
    void testGetScriptAgentList1_EngineInteractReturnsNoItems() throws Exception {
        // Setup
        final MonitorFlowActiveQueryDto query = new MonitorFlowActiveQueryDto();
        query.setFlowId(0L);
        query.setScriptId("scriptId");
        query.setActName("name");

        final UserDto user = new UserDto();
        user.setUserName("userName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        // Configure ExecuteMonitorMapper.selectAct(...).
        final MonitorFlowActiveEntity monitorFlowActiveEntity = new MonitorFlowActiveEntity();
        monitorFlowActiveEntity.setActDefName("actDefName");
        monitorFlowActiveEntity.setReqId("reqId");
        monitorFlowActiveEntity.setId(0L);
        monitorFlowActiveEntity.setFlowId(0L);
        monitorFlowActiveEntity.setCallFlowId(0L);
        monitorFlowActiveEntity.setActName("name");
        monitorFlowActiveEntity.setActStatus("actStatus");
        monitorFlowActiveEntity.setActType("actType");
        final List<MonitorFlowActiveEntity> monitorFlowActiveEntities = Arrays.asList(monitorFlowActiveEntity);
        when(mockExecuteMonitorMapper.selectAct(any(MonitorFlowActiveEntity.class)))
                .thenReturn(monitorFlowActiveEntities);

        when(mockToolsActOutputService.selectToolsActOutputList(any(ActivityOutPutQueryDto.class)))
                .thenReturn(Collections.emptyList());
        when(mockEngineInteract.engineActivityOutPut(Arrays.asList("reqId"))).thenReturn(Collections.emptyList());

        // Run the test
        final PageInfo<MonitorFlowActiveDto> result = executeMonitorServiceImplUnderTest.getScriptAgentList1(query, 0,
                0, user);

        // Verify the results
    }

    @Test
    void testGetScriptAgentList1_EngineInteractThrowsEngineServiceException() throws Exception {
        // Setup
        final MonitorFlowActiveQueryDto query = new MonitorFlowActiveQueryDto();
        query.setFlowId(0L);
        query.setScriptId("scriptId");
        query.setActName("name");

        final UserDto user = new UserDto();
        user.setUserName("userName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        // Configure ExecuteMonitorMapper.selectAct(...).
        final MonitorFlowActiveEntity monitorFlowActiveEntity = new MonitorFlowActiveEntity();
        monitorFlowActiveEntity.setActDefName("actDefName");
        monitorFlowActiveEntity.setReqId("reqId");
        monitorFlowActiveEntity.setId(0L);
        monitorFlowActiveEntity.setFlowId(0L);
        monitorFlowActiveEntity.setCallFlowId(0L);
        monitorFlowActiveEntity.setActName("name");
        monitorFlowActiveEntity.setActStatus("actStatus");
        monitorFlowActiveEntity.setActType("actType");
        final List<MonitorFlowActiveEntity> monitorFlowActiveEntities = Arrays.asList(monitorFlowActiveEntity);
        when(mockExecuteMonitorMapper.selectAct(any(MonitorFlowActiveEntity.class)))
                .thenReturn(monitorFlowActiveEntities);

        when(mockToolsActOutputService.selectToolsActOutputList(any(ActivityOutPutQueryDto.class)))
                .thenReturn(Collections.emptyList());
        when(mockEngineInteract.engineActivityOutPut(Arrays.asList("reqId"))).thenThrow(EngineServiceException.class);

        // Run the test
        final PageInfo<MonitorFlowActiveDto> result = executeMonitorServiceImplUnderTest.getScriptAgentList1(query, 0,
                0, user);

        // Verify the results
    }

    @Test
    void testGetComToolNodes() throws Exception {
        // Setup
        final UserDto user = new UserDto();
        user.setUserName("userName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        // Configure ExecuteMonitorMapper.selectAct(...).
        final MonitorFlowActiveEntity monitorFlowActiveEntity = new MonitorFlowActiveEntity();
        monitorFlowActiveEntity.setActDefName("actDefName");
        monitorFlowActiveEntity.setReqId("reqId");
        monitorFlowActiveEntity.setId(0L);
        monitorFlowActiveEntity.setFlowId(0L);
        monitorFlowActiveEntity.setCallFlowId(0L);
        monitorFlowActiveEntity.setActName("name");
        monitorFlowActiveEntity.setActStatus("actStatus");
        monitorFlowActiveEntity.setActType("actType");
        final List<MonitorFlowActiveEntity> monitorFlowActiveEntities = Arrays.asList(monitorFlowActiveEntity);
        when(mockExecuteMonitorMapper.selectAct(any(MonitorFlowActiveEntity.class)))
                .thenReturn(monitorFlowActiveEntities);

        when(mockStudioInteract.getWorkflowJson(0L)).thenReturn("workflowContent");

        // Run the test
        final ExecuteMonitorProjectDto result = executeMonitorServiceImplUnderTest.getComToolNodes(0L, 0L, user);

        // Verify the results
    }

    @Test
    void testGetComToolNodes_ExecuteMonitorMapperReturnsNoItems() throws Exception {
        // Setup
        final UserDto user = new UserDto();
        user.setUserName("userName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        when(mockExecuteMonitorMapper.selectAct(any(MonitorFlowActiveEntity.class)))
                .thenReturn(Collections.emptyList());
        when(mockStudioInteract.getWorkflowJson(0L)).thenReturn("workflowContent");

        // Run the test
        final ExecuteMonitorProjectDto result = executeMonitorServiceImplUnderTest.getComToolNodes(0L, 0L, user);

        // Verify the results
    }

    @Test
    void testGetComToolNodes_StudioInteractThrowsStudioException() throws Exception {
        // Setup
        final UserDto user = new UserDto();
        user.setUserName("userName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        // Configure ExecuteMonitorMapper.selectAct(...).
        final MonitorFlowActiveEntity monitorFlowActiveEntity = new MonitorFlowActiveEntity();
        monitorFlowActiveEntity.setActDefName("actDefName");
        monitorFlowActiveEntity.setReqId("reqId");
        monitorFlowActiveEntity.setId(0L);
        monitorFlowActiveEntity.setFlowId(0L);
        monitorFlowActiveEntity.setCallFlowId(0L);
        monitorFlowActiveEntity.setActName("name");
        monitorFlowActiveEntity.setActStatus("actStatus");
        monitorFlowActiveEntity.setActType("actType");
        final List<MonitorFlowActiveEntity> monitorFlowActiveEntities = Arrays.asList(monitorFlowActiveEntity);
        when(mockExecuteMonitorMapper.selectAct(any(MonitorFlowActiveEntity.class)))
                .thenReturn(monitorFlowActiveEntities);

        when(mockStudioInteract.getWorkflowJson(0L)).thenThrow(StudioException.class);

        // Run the test
        final ExecuteMonitorProjectDto result = executeMonitorServiceImplUnderTest.getComToolNodes(0L, 0L, user);

        // Verify the results
    }
    
    @Test
    void testUpdateStatus3() throws Exception {
        // Setup
        final ExecuteMonitorDto monitor = new ExecuteMonitorDto();
        monitor.setEstimateOperationalRiskArray(new Long[]{0L});
        monitor.setXml("workflowContent");
        monitor.setRunningId(0L);
        final ExecuteMonitorParamsDto executeMonitorParamsDto = new ExecuteMonitorParamsDto();
        monitor.setParams(Arrays.asList(executeMonitorParamsDto));
        final ExecuteMonitorProjectDto executeMonitorProjectDto = new ExecuteMonitorProjectDto();
        executeMonitorProjectDto.setActName("name");
        executeMonitorProjectDto.setActStatus("actStatus");
        executeMonitorProjectDto.setNodeId(0L);
        executeMonitorProjectDto.setFlowId(0L);
        executeMonitorProjectDto.setParentId(0L);
        executeMonitorProjectDto.setChildren(Arrays.asList(new ExecuteMonitorProjectDto()));
        executeMonitorProjectDto.setNodeName("name");
        executeMonitorProjectDto.setNodeStatus(0L);
        executeMonitorProjectDto.setToolType(0L);
        executeMonitorProjectDto.setXml("workflowContent");
        monitor.setProjects(Arrays.asList(executeMonitorProjectDto));
        monitor.setToolId(0L);
        monitor.setType(0L);
        final ExecuteHistoryFilesDto executeHistoryFilesDto = new ExecuteHistoryFilesDto();
        monitor.setFiles(Arrays.asList(executeHistoryFilesDto));
        // Configure ExecuteMonitorMapper.selectExecuteMonitorByRunningId(...).
        final ExecuteMonitorBean executeMonitorBean = new ExecuteMonitorBean();
        executeMonitorBean.setRunningId(0L);
        final ExecuteParamsBean executeParamsBean = new ExecuteParamsBean();
        executeMonitorBean.setParams(Arrays.asList(executeParamsBean));
        final ExecuteProjectBean executeProjectBean = new ExecuteProjectBean();
        executeProjectBean.setParentId(0L);
        executeProjectBean.setProjectName("projectName");
        executeMonitorBean.setProjects(Arrays.asList(executeProjectBean));
        executeMonitorBean.setId(0L);
        executeMonitorBean.setEndTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        executeMonitorBean.setEstimateOperationalRisk("estimateOperationalRisk");
        executeMonitorBean.setEventNum("eventId");
        executeMonitorBean.setRunStatus(2L);
        executeMonitorBean.setRunTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        executeMonitorBean.setType(0L);
        final ExecuteFilesBean executeFilesBean = new ExecuteFilesBean();
        executeMonitorBean.setFiles(Arrays.asList(executeFilesBean));
        List<ExecuteMonitorBean> executeMonitorBeans = Arrays.asList(executeMonitorBean);
        when(mockExecuteMonitorMapper.selectExecuteMonitorByRunningId(any(Long[].class)))
                .thenReturn(executeMonitorBeans);
        when(mockExecuteMonitorMapper.updateStatus(any(ExecuteMonitorEntity.class))).thenReturn(0);
        monitor.setRunStatus(0L);
        monitor.setType(1L);
        // Run the test
        boolean result = executeMonitorServiceImplUnderTest.updateStatus(monitor);
        // Verify the results
        assertThat(result).isFalse();
        monitor.setType(2L);
        // Run the test
        result = executeMonitorServiceImplUnderTest.updateStatus(monitor);
        // Verify the results
        assertThat(result).isFalse();
        monitor.setRunStatus(1L);
        // Run the test
        result = executeMonitorServiceImplUnderTest.updateStatus(monitor);
        // Verify the results
        assertThat(result).isTrue();
        monitor.setRunStatus(0L);
        executeMonitorBean.setRunStatus(1L);
        executeMonitorBeans = Arrays.asList(executeMonitorBean);
        when(mockExecuteMonitorMapper.selectExecuteMonitorByRunningId(any(Long[].class)))
                .thenReturn(executeMonitorBeans);
        // Run the test
        result = executeMonitorServiceImplUnderTest.updateStatus(monitor);
        // Verify the results
        assertThat(result).isFalse();
    }
    
    @Test
    void testGetExecuteTimeParam() throws Exception {
    	Map<String,Object> selectMap=new HashMap<String,Object>();
    	selectMap.put("createTime", "20200101");
    	selectMap.put("iid", "1");
    	List<Map<String,Object>> selectExecuteParams=Collections.singletonList(selectMap);
    	when(mockExecuteMonitorMapper.selectExecuteParams(anyMap())).thenReturn(selectExecuteParams);
    	List<Map<String, Object>> retListMap= executeMonitorServiceImplUnderTest.getExecuteTimeParam(anyMap());
    	assertNotNull(retListMap);
    }
}

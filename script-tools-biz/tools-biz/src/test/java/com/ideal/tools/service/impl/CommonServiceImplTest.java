package com.ideal.tools.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.ideal.common.dto.R;
import com.ideal.script.exception.ScriptException;
import com.ideal.tools.common.SFTPUtil;
import com.ideal.tools.config.BusinessConfig;
import com.ideal.tools.exception.StudioException;
import com.ideal.tools.exception.ToolsException;
import com.ideal.tools.mapper.ExecuteHistoryMapper;
import com.ideal.tools.model.bean.ExecuteHistoryBean;
import com.ideal.tools.model.dto.AuditEverybodyQueryDto;
import com.ideal.tools.model.dto.ToolsAgentResultDto;
import com.ideal.tools.model.dto.ToolsDto;
import com.ideal.tools.model.dto.ToolsProjectInfoDto;
import com.ideal.tools.model.entity.ToolsInfoCategoryEntity;
import com.ideal.tools.model.enums.FtpTypeEnum;
import com.ideal.tools.model.enums.ToolsEnvEnum;
import com.ideal.tools.model.interaction.ScriptFileInfo;
import com.ideal.tools.model.interaction.StudioFileProjectInteractDto;
import com.ideal.tools.service.IToolsInfoService;
import com.ideal.tools.service.interaction.ScriptInteract;
import com.ideal.tools.service.interaction.StudioInteract;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockHttpServletResponse;

import java.io.File;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CommonServiceImplTest {

    @Mock
    private IToolsInfoService mockToolsInfoService;
    @Mock
    private BusinessConfig mockBusinessConfig;
    @Mock
    private ScriptInteract mockScriptInteract;
    @Mock
    private StudioInteract mockStudioInteract;
    @Mock
    private ExecuteHistoryMapper mockExecuteHistoryMapper;
    @Mock
    private SFTPUtil sftputil;

    private CommonServiceImpl commonServiceImplUnderTest;

    @BeforeEach
    void setUp() throws Exception {
        commonServiceImplUnderTest = new CommonServiceImpl(mockToolsInfoService, mockBusinessConfig, mockScriptInteract,
                mockStudioInteract, mockExecuteHistoryMapper);
        commonServiceImplUnderTest.setSftp(sftputil);
    }

    @Test
    void testPublishMigrationExportTools() throws Exception {
        // Setup
        final MockHttpServletResponse response = new MockHttpServletResponse();
        when(mockBusinessConfig.isToolEvnMainSwitch()).thenReturn(true);
        when(mockBusinessConfig.getToolsEvn()).thenReturn("DEV环境");

        // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
        final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
        toolsInfoCategoryEntity.setPermissionLevel(0);
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setCode("code");
        toolsInfoCategoryEntity.setName("name");
        toolsInfoCategoryEntity.setType(0);
        when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);

        // Configure IToolsInfoService.getToolsCombinedInfo(...).
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setCode("code");
        toolsDto.setName("name");
        toolsDto.setType(0);
        toolsDto.setClassification(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        toolsDto.setStatus(0);
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setProjectVersionId(0L);
        toolsProjectInfoDto.setProjectName("projectName");
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        when(mockToolsInfoService.getToolsCombinedInfo(0L)).thenReturn(toolsDto);

        when(mockBusinessConfig.getFtpType()).thenReturn("result");
        when(mockBusinessConfig.getFtpUser()).thenReturn("result");
        when(mockBusinessConfig.getFtpPw()).thenReturn("result");
        when(mockBusinessConfig.getFtpIp()).thenReturn("result");
        when(mockBusinessConfig.getFtpPort()).thenReturn("result");
        when(mockBusinessConfig.getSftpDataPath()).thenReturn("result");

        // Configure ScriptInteract.exportScriptProduction(...).
        final ScriptFileInfo scriptFileInfo = new ScriptFileInfo();
        scriptFileInfo.setFileName("fileName");
        scriptFileInfo.setFileSuffix("fileSuffix");
        scriptFileInfo.setFileContentByte("content".getBytes());

        // Configure StudioInteract.exportStudioData(...).
        final StudioFileProjectInteractDto studioFileProjectInteractDto = new StudioFileProjectInteractDto("fileName",
                "fileSuffix", "content".getBytes());
        // Configure ExecuteHistoryMapper.selectHistoryMonitorSuccessByToolsId(...).
        final ExecuteHistoryBean executeHistoryBean = new ExecuteHistoryBean();
        executeHistoryBean.setClassification(0);
        executeHistoryBean.setSearchStartTime("searchStartTime");
        executeHistoryBean.setSearchEndTime("searchEndTime");
        executeHistoryBean.setRunningId(0L);
        executeHistoryBean.setExecFrom(0L);
        final List<ExecuteHistoryBean> executeHistoryBeans = Arrays.asList(executeHistoryBean);
        // Run the test
        final R<Object> result = commonServiceImplUnderTest.publishMigrationExportTools(response, new Long[]{0L}, 1, 0);
        assertThat(result).isNotNull();
    }

    @Test
    void testPublishMigrationExportTools_BusinessConfigIsToolEvnMainSwitchReturnsTrue() throws Exception {
        // Setup
        final MockHttpServletResponse response = new MockHttpServletResponse();
        when(mockBusinessConfig.isToolEvnMainSwitch()).thenReturn(true);
        when(mockBusinessConfig.getToolsEvn()).thenReturn("DEV环境");

        // Configure ExecuteHistoryMapper.selectHistoryMonitorSuccessByToolsId(...).
        final ExecuteHistoryBean executeHistoryBean = new ExecuteHistoryBean();
        executeHistoryBean.setClassification(0);
        executeHistoryBean.setSearchStartTime("searchStartTime");
        executeHistoryBean.setSearchEndTime("searchEndTime");
        executeHistoryBean.setRunningId(0L);
        executeHistoryBean.setExecFrom(0L);
        final List<ExecuteHistoryBean> executeHistoryBeans = Arrays.asList(executeHistoryBean);

        // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
        final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
        toolsInfoCategoryEntity.setPermissionLevel(0);
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setCode("code");
        toolsInfoCategoryEntity.setName("name");
        toolsInfoCategoryEntity.setType(0);
        when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);

        // Configure IToolsInfoService.getToolsCombinedInfo(...).
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setCode("code");
        toolsDto.setName("name");
        toolsDto.setType(0);
        toolsDto.setClassification(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        toolsDto.setStatus(0);
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setProjectVersionId(0L);
        toolsProjectInfoDto.setProjectName("projectName");
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        when(mockToolsInfoService.getToolsCombinedInfo(0L)).thenReturn(toolsDto);

        when(mockBusinessConfig.getFtpType()).thenReturn("result");
        when(mockBusinessConfig.getFtpUser()).thenReturn("result");
        when(mockBusinessConfig.getFtpPw()).thenReturn("result");
        when(mockBusinessConfig.getFtpIp()).thenReturn("result");
        when(mockBusinessConfig.getFtpPort()).thenReturn("result");
        when(mockBusinessConfig.getSftpDataPath()).thenReturn("result");

        // Configure ScriptInteract.exportScriptProduction(...).
        final ScriptFileInfo scriptFileInfo = new ScriptFileInfo();
        scriptFileInfo.setFileName("fileName");
        scriptFileInfo.setFileSuffix("fileSuffix");
        scriptFileInfo.setFileContentByte("content".getBytes());

        // Configure StudioInteract.exportStudioData(...).
        final StudioFileProjectInteractDto studioFileProjectInteractDto = new StudioFileProjectInteractDto("fileName",
                "fileSuffix", "content".getBytes());

        // Run the test
        final R<Object> result = commonServiceImplUnderTest.publishMigrationExportTools(response, new Long[]{0L}, 1, 0);
        assertThat(result).isNotNull();
    }


    @Test
    void testPublishMigrationExportTools_IToolsInfoServiceSelectToolsInfoCategoryByIdReturnsNull() {
        // Setup
        final MockHttpServletResponse response = new MockHttpServletResponse();
        when(mockBusinessConfig.isToolEvnMainSwitch()).thenReturn(false);
        when(mockBusinessConfig.getToolsEvn()).thenReturn("result");
        // Run the test
        assertThatThrownBy(() -> commonServiceImplUnderTest.publishMigrationExportTools(response, new Long[]{0L}, 0,
                0)).isInstanceOf(ToolsException.class);
    }

    @Test
    void testPublishMigrationExportTools_ScriptInteractReturnsNull() throws Exception {
        // Setup
        final MockHttpServletResponse response = new MockHttpServletResponse();
        when(mockBusinessConfig.isToolEvnMainSwitch()).thenReturn(false);
        when(mockBusinessConfig.getToolsEvn()).thenReturn("result");

        // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
        final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
        toolsInfoCategoryEntity.setPermissionLevel(0);
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setCode("code");
        toolsInfoCategoryEntity.setName("name");
        toolsInfoCategoryEntity.setType(0);

        // Configure IToolsInfoService.getToolsCombinedInfo(...).
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setCode("code");
        toolsDto.setName("name");
        toolsDto.setType(0);
        toolsDto.setClassification(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        toolsDto.setStatus(0);
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setProjectVersionId(0L);
        toolsProjectInfoDto.setProjectName("projectName");
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Run the test
        assertThatThrownBy(() -> commonServiceImplUnderTest.publishMigrationExportTools(response, new Long[]{0L}, 0,
                0)).isInstanceOf(ToolsException.class);
    }

    @Test
    void testPublishMigrationExportTools_ScriptInteractThrowsScriptException() throws Exception {
        // Setup
        final MockHttpServletResponse response = new MockHttpServletResponse();
        when(mockBusinessConfig.isToolEvnMainSwitch()).thenReturn(false);
        when(mockBusinessConfig.getToolsEvn()).thenReturn("result");

        // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
        final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
        toolsInfoCategoryEntity.setPermissionLevel(0);
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setCode("code");
        toolsInfoCategoryEntity.setName("name");
        toolsInfoCategoryEntity.setType(0);

        // Configure IToolsInfoService.getToolsCombinedInfo(...).
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setCode("code");
        toolsDto.setName("name");
        toolsDto.setType(0);
        toolsDto.setClassification(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        toolsDto.setStatus(0);
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setProjectVersionId(0L);
        toolsProjectInfoDto.setProjectName("projectName");
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Run the test
        assertThatThrownBy(() -> commonServiceImplUnderTest.publishMigrationExportTools(response, new Long[]{0L}, 0,
                0)).isInstanceOf(ToolsException.class);
    }

    @Test
    void testPublishMigrationExportTools_StudioInteractThrowsStudioException() throws Exception {
        // Setup
        final MockHttpServletResponse response = new MockHttpServletResponse();
        when(mockBusinessConfig.isToolEvnMainSwitch()).thenReturn(false);
        when(mockBusinessConfig.getToolsEvn()).thenReturn("result");

        // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
        final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
        toolsInfoCategoryEntity.setPermissionLevel(0);
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setCode("code");
        toolsInfoCategoryEntity.setName("name");
        toolsInfoCategoryEntity.setType(0);

        // Configure IToolsInfoService.getToolsCombinedInfo(...).
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setCode("code");
        toolsDto.setName("name");
        toolsDto.setType(0);
        toolsDto.setClassification(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        toolsDto.setStatus(0);
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setProjectVersionId(0L);
        toolsProjectInfoDto.setProjectName("projectName");
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        // Run the test
        assertThatThrownBy(() -> commonServiceImplUnderTest.publishMigrationExportTools(response, new Long[]{0L}, 0,
                0)).isInstanceOf(ToolsException.class);
    }

    @Test
    void testConvertToolJsonFile() throws Exception {
    	Map aaMap=new HashMap<>();
        // Setup
        final JSONObject jSONObject = new JSONObject(aaMap);
        final File expectedResult = new File("filename.txt");

        // Run the test
        final File result = commonServiceImplUnderTest.convertToolJsonFile("fileName", jSONObject);

        // Verify the results
        assertThat(result).isNotNull();
    }

    @Test
    void testConvertToolJsonFile_ThrowsIOException() {
        // Setup
        final JSONObject jSONObject = null;

        // Run the test
        assertThatThrownBy(() -> commonServiceImplUnderTest.convertToolJsonFile("fileName", jSONObject))
                .isInstanceOf(Exception.class);
    }
    
	@Test
	void testExportTools_ToolsEvn1() throws ToolsException {
		final MockHttpServletResponse response = new MockHttpServletResponse();
		assertThat(commonServiceImplUnderTest.exportTools(response, new Long[] {0L}, null, "制品库", null)).isNotNull();
	}
	@Test
	void testExportTools_ToolsEvn2()  throws ToolsException{
		final MockHttpServletResponse response = new MockHttpServletResponse();
		assertThat(commonServiceImplUnderTest.exportTools(response, new Long[] {0L}, null, "大制品库", null)).isNotNull();
	}
	@Test
	void testExportTools_ToolsEvn4() {
		final MockHttpServletResponse response = new MockHttpServletResponse();
		assertThatThrownBy(() -> commonServiceImplUnderTest.exportTools(response, new Long[] {0L}, null, "无", null))
		.isInstanceOf(ToolsException.class);
	}
    
    @Test
    void testPublishMigrationExportTools1() throws Exception {
        when(mockBusinessConfig.getToolsEvn()).thenReturn("DEV环境");
        when(mockBusinessConfig.isToolEvnMainSwitch()).thenReturn(true);
//        when(mockExecuteHistoryMapper.selectHistoryMonitorSuccessByToolsId(0L, 1)).thenReturn(null);
        // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
        final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
        toolsInfoCategoryEntity.setPermissionLevel(0);
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setCode("code");
        toolsInfoCategoryEntity.setName("name");
        toolsInfoCategoryEntity.setType(0);
        when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);
     // Configure IToolsInfoService.getToolsCombinedInfo(...).
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setCode("code");
        toolsDto.setName("name");
        toolsDto.setType(0);
        toolsDto.setClassification(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        toolsDto.setStatus(0);
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setProjectVersionId(0L);
        toolsProjectInfoDto.setProjectName("projectName");
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        when(mockToolsInfoService.getToolsCombinedInfo(0L)).thenReturn(toolsDto);
        final MockHttpServletResponse response = new MockHttpServletResponse();
        assertThatThrownBy(
                () -> commonServiceImplUnderTest.publishMigrationExportTools(response, new Long[]{0L}, 0, 0))
                .isInstanceOf(ToolsException.class);  
        
    }
    
    @Test
    void testPublishMigrationExportToolsFtp1() throws Exception {
        when(mockBusinessConfig.getToolsEvn()).thenReturn("DEV环境");
        when(mockBusinessConfig.isToolEvnMainSwitch()).thenReturn(true);
        final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
        toolsInfoCategoryEntity.setPermissionLevel(0);
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setCode("code");
        toolsInfoCategoryEntity.setName("name");
        toolsInfoCategoryEntity.setType(0);
        when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setCode("code");
        toolsDto.setName("name");
        toolsDto.setType(0);
        toolsDto.setClassification(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        toolsDto.setStatus(0);
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setProjectVersionId(0L);
        toolsProjectInfoDto.setProjectName("projectName");
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        when(mockToolsInfoService.getToolsCombinedInfo(0L)).thenReturn(toolsDto);
        final MockHttpServletResponse response = new MockHttpServletResponse();
        assertThatThrownBy(
                () -> commonServiceImplUnderTest.publishMigrationExportTools(response, new Long[]{0L}, 0, 0))
                .isInstanceOf(ToolsException.class);  
    }
    
    @Test
    void testPublishMigrationExportToolsFtp2() throws Exception {
        when(mockBusinessConfig.getToolsEvn()).thenReturn("DEV环境");
        when(mockBusinessConfig.isToolEvnMainSwitch()).thenReturn(true);
        final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
        toolsInfoCategoryEntity.setPermissionLevel(0);
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setCode("code");
        toolsInfoCategoryEntity.setName("name");
        toolsInfoCategoryEntity.setType(0);
        when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setCode("code");
        toolsDto.setName("name");
        toolsDto.setType(0);
        toolsDto.setClassification(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        toolsDto.setStatus(0);
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setProjectVersionId(0L);
        toolsProjectInfoDto.setProjectName("projectName");
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        when(mockToolsInfoService.getToolsCombinedInfo(0L)).thenReturn(toolsDto);
        final MockHttpServletResponse response = new MockHttpServletResponse();
        when(mockBusinessConfig.getFtpType()).thenReturn("result");
        assertThatThrownBy(
                () -> commonServiceImplUnderTest.publishMigrationExportTools(response, new Long[]{0L}, 0, 0))
                .isInstanceOf(ToolsException.class);  
    }
    
    @Test
    void testPublishMigrationExportToolsFtp3() throws Exception {
        when(mockBusinessConfig.getToolsEvn()).thenReturn("DEV环境");
        when(mockBusinessConfig.isToolEvnMainSwitch()).thenReturn(true);
        final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
        toolsInfoCategoryEntity.setPermissionLevel(0);
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setCode("code");
        toolsInfoCategoryEntity.setName("name");
        toolsInfoCategoryEntity.setType(0);
        when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setCode("code");
        toolsDto.setName("name");
        toolsDto.setType(0);
        toolsDto.setClassification(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        toolsDto.setStatus(0);
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setProjectVersionId(0L);
        toolsProjectInfoDto.setProjectName("projectName");
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        when(mockToolsInfoService.getToolsCombinedInfo(0L)).thenReturn(toolsDto);
        final MockHttpServletResponse response = new MockHttpServletResponse();
        when(mockBusinessConfig.getFtpType()).thenReturn("result");
        when(mockBusinessConfig.getFtpUser()).thenReturn("result");
        assertThatThrownBy(
                () -> commonServiceImplUnderTest.publishMigrationExportTools(response, new Long[]{0L}, 0, 0))
                .isInstanceOf(ToolsException.class);  
    }
    
    @Test
    void testPublishMigrationExportToolsFtp4() throws Exception {
        when(mockBusinessConfig.getToolsEvn()).thenReturn("DEV环境");
        when(mockBusinessConfig.isToolEvnMainSwitch()).thenReturn(true);
        final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
        toolsInfoCategoryEntity.setPermissionLevel(0);
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setCode("code");
        toolsInfoCategoryEntity.setName("name");
        toolsInfoCategoryEntity.setType(0);
        when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setCode("code");
        toolsDto.setName("name");
        toolsDto.setType(0);
        toolsDto.setClassification(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        toolsDto.setStatus(0);
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setProjectVersionId(0L);
        toolsProjectInfoDto.setProjectName("projectName");
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        when(mockToolsInfoService.getToolsCombinedInfo(0L)).thenReturn(toolsDto);
        final MockHttpServletResponse response = new MockHttpServletResponse();
        when(mockBusinessConfig.getFtpType()).thenReturn("result");
        when(mockBusinessConfig.getFtpUser()).thenReturn("result");
        when(mockBusinessConfig.getFtpPw()).thenReturn("result");
        assertThatThrownBy(
                () -> commonServiceImplUnderTest.publishMigrationExportTools(response, new Long[]{0L}, 0, 0))
                .isInstanceOf(ToolsException.class);  
    }
    
    @Test
    void testPublishMigrationExportToolsFtp5() throws Exception {
        when(mockBusinessConfig.getToolsEvn()).thenReturn("DEV环境");
        when(mockBusinessConfig.isToolEvnMainSwitch()).thenReturn(true);
        final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
        toolsInfoCategoryEntity.setPermissionLevel(0);
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setCode("code");
        toolsInfoCategoryEntity.setName("name");
        toolsInfoCategoryEntity.setType(0);
        when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setCode("code");
        toolsDto.setName("name");
        toolsDto.setType(0);
        toolsDto.setClassification(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        toolsDto.setStatus(0);
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setProjectVersionId(0L);
        toolsProjectInfoDto.setProjectName("projectName");
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        when(mockToolsInfoService.getToolsCombinedInfo(0L)).thenReturn(toolsDto);
        final MockHttpServletResponse response = new MockHttpServletResponse();
        when(mockBusinessConfig.getFtpType()).thenReturn("result");
        when(mockBusinessConfig.getFtpUser()).thenReturn("result");
        when(mockBusinessConfig.getFtpPw()).thenReturn("result");
        when(mockBusinessConfig.getFtpIp()).thenReturn("result");
        assertThatThrownBy(
                () -> commonServiceImplUnderTest.publishMigrationExportTools(response, new Long[]{0L}, 0, 0))
                .isInstanceOf(ToolsException.class);  
    }
    
    @Test
    void testPublishMigrationExportToolsFtp() throws Exception {
        when(mockBusinessConfig.getToolsEvn()).thenReturn("DEV环境");
        when(mockBusinessConfig.isToolEvnMainSwitch()).thenReturn(true);
        final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
        toolsInfoCategoryEntity.setPermissionLevel(0);
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setCode("code");
        toolsInfoCategoryEntity.setName("name");
        toolsInfoCategoryEntity.setType(0);
        when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setCode("code");
        toolsDto.setName("name");
        toolsDto.setType(0);
        toolsDto.setClassification(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        toolsDto.setStatus(0);
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setProjectVersionId(0L);
        toolsProjectInfoDto.setProjectName("projectName");
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        when(mockToolsInfoService.getToolsCombinedInfo(0L)).thenReturn(toolsDto);
        final MockHttpServletResponse response = new MockHttpServletResponse();
        when(mockBusinessConfig.getFtpType()).thenReturn("result");
        when(mockBusinessConfig.getFtpUser()).thenReturn("result");
        when(mockBusinessConfig.getFtpPw()).thenReturn("result");
        when(mockBusinessConfig.getFtpIp()).thenReturn("result");
        when(mockBusinessConfig.getFtpPort()).thenReturn("result");
        // Run the test
        final R<Object> result = commonServiceImplUnderTest.publishMigrationExportTools(response, new Long[]{0L}, 1, 0);
        assertThat(result).isNotNull();
    }
    
    @Test
    void testPublishMigrationExportTools_ExecuteHistoryMapperReturnsNull() {
        // Setup
        final MockHttpServletResponse response = new MockHttpServletResponse();
        when(mockBusinessConfig.isToolEvnMainSwitch()).thenReturn(true);
        when(mockExecuteHistoryMapper.selectHistoryMonitorSuccessByToolsId(0L, 1)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> commonServiceImplUnderTest.publishMigrationExportTools(response, new Long[]{0L}, 0,
                0)).isInstanceOf(ToolsException.class);
    }
    
    @Test
    void testPublishMigrationExportTools_ExecuteHistoryMapperReturnsNull2() {
        // Setup
        final MockHttpServletResponse response = new MockHttpServletResponse();
        when(mockBusinessConfig.isToolEvnMainSwitch()).thenReturn(true);
        when(mockExecuteHistoryMapper.selectHistoryMonitorSuccessByToolsId(0L, 1)).thenReturn(new LinkedList<>());

        // Run the test
        assertThatThrownBy(() -> commonServiceImplUnderTest.publishMigrationExportTools(response, new Long[]{0L}, 0,
                0)).isInstanceOf(ToolsException.class);
    }
    @Test
	void testExportTools_null1() {
		final MockHttpServletResponse response = new MockHttpServletResponse();
		assertThatThrownBy(() -> commonServiceImplUnderTest.exportTools(response, null, null, null, null))
				.isInstanceOf(ToolsException.class);
	}
	@Test
	void testExportTools_null2() {
		final MockHttpServletResponse response = new MockHttpServletResponse();
		assertThatThrownBy(() -> commonServiceImplUnderTest.exportTools(response, new Long[] {}, null, null, null))
		.isInstanceOf(ToolsException.class);
	}
	@Test
	void testExportTools_null3() {
		final MockHttpServletResponse response = new MockHttpServletResponse();
		when(mockBusinessConfig.getToolsEvn()).thenReturn("");
		assertThatThrownBy(() -> commonServiceImplUnderTest.exportTools(response, new Long[] {0L}, null, "SFTP", null))
		.isInstanceOf(ToolsException.class);
	}
	
	@Test
	void testExportTools_ToolInfonull1() {
		final MockHttpServletResponse response = new MockHttpServletResponse();
        when(mockToolsInfoService.selectToolsInfoCategoryById(anyLong())).thenReturn(null);
        when(mockBusinessConfig.isToolEvnMainSwitch()).thenReturn(true);
        when(mockBusinessConfig.getToolsEvn()).thenReturn("DEV环境");
		assertThatThrownBy(() -> commonServiceImplUnderTest.exportTools(response, new Long[] {0L}, null, "SFTP", null))
		.isInstanceOf(ToolsException.class);
	}
	@Test
	void testExportTools_ToolInfonull2() {
		final MockHttpServletResponse response = new MockHttpServletResponse();
		when(mockToolsInfoService.selectToolsInfoCategoryById(anyLong())).thenReturn(new ToolsInfoCategoryEntity());
		when(mockBusinessConfig.isToolEvnMainSwitch()).thenReturn(true);
		when(mockBusinessConfig.getToolsEvn()).thenReturn("DEV环境");
		assertThatThrownBy(() -> commonServiceImplUnderTest.exportTools(response, new Long[] {0L}, null, "SFTP", null))
		.isInstanceOf(ToolsException.class);
	}
	
	@Test
	void testExportTools_ToolsEvn3() throws ToolsException {
		final MockHttpServletResponse response = new MockHttpServletResponse();
		assertThatThrownBy(() -> commonServiceImplUnderTest.exportTools(response, new Long[] { 0L }, null, "本地", null));
	}

	@Test
	void testExportTools_validateToolExists() throws ToolsException {
		final MockHttpServletResponse response = new MockHttpServletResponse();
		final ToolsDto toolsDto = new ToolsDto();
		toolsDto.setId(0L);
		toolsDto.setCode("code");
		toolsDto.setName("name");
		toolsDto.setType(0);
		toolsDto.setClassification(0);
		final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
		toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
		toolsDto.setScriptName("scriptName");
		toolsDto.setScriptIds("scriptIds");
		final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
		toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
		toolsDto.setStatus(0);
		final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
		toolsProjectInfoDto.setProjectVersionId(0L);
		toolsProjectInfoDto.setProjectName("projectName");
		toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
		toolsDto.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
		toolsDto.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
		when(mockToolsInfoService.getToolsCombinedInfo(0L)).thenReturn(null);
		assertThatThrownBy(() -> commonServiceImplUnderTest.exportTools(response, new Long[] { 0L }, null, "本地", null))
				.isInstanceOf(ToolsException.class);
	}
	
	@Test
	void testExportTools_validateToolExists2() throws ToolsException {
		final MockHttpServletResponse response = new MockHttpServletResponse();
		final ToolsDto toolsDto = new ToolsDto();
		toolsDto.setCode("code");
		toolsDto.setName("name");
		toolsDto.setType(0);
		toolsDto.setClassification(0);
		final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
		toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
		toolsDto.setScriptName("scriptName");
		toolsDto.setScriptIds("scriptIds");
		final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
		toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
		toolsDto.setStatus(0);
		final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
		toolsProjectInfoDto.setProjectVersionId(0L);
		toolsProjectInfoDto.setProjectName("projectName");
		toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
		toolsDto.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
		toolsDto.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
		when(mockToolsInfoService.getToolsCombinedInfo(0L)).thenReturn(toolsDto);
		assertThatThrownBy(() -> commonServiceImplUnderTest.exportTools(response, new Long[] { 0L }, null, "本地", null))
				.isInstanceOf(ToolsException.class);
	}
	
	@Test
	void testExportTools_Zip1() throws ToolsException {
		final MockHttpServletResponse response = new MockHttpServletResponse();
		final ToolsDto toolsDto = new ToolsDto();
		toolsDto.setId(0L);
		toolsDto.setCode("code");
		toolsDto.setName("name");
		toolsDto.setType(3);
		toolsDto.setClassification(0);
		final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
		toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
		toolsDto.setScriptName("scriptName");
		toolsDto.setScriptIds("scriptIds");
		final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
		toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
		toolsDto.setStatus(0);
		final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
		toolsProjectInfoDto.setProjectVersionId(0L);
		toolsProjectInfoDto.setProjectName("projectName");
		toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
		toolsDto.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
		toolsDto.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
		when(mockToolsInfoService.getToolsCombinedInfo(0L)).thenReturn(toolsDto);
		assertThatThrownBy(() -> commonServiceImplUnderTest.exportTools(response, new Long[] { 0L }, null, "本地", null))
				.isInstanceOf(ToolsException.class);
	}
	@Test
	void testExportTools_Zip2() throws ToolsException, ScriptException {
		final MockHttpServletResponse response = new MockHttpServletResponse();
		final ToolsDto toolsDto = new ToolsDto();
		toolsDto.setId(0L);
		toolsDto.setCode("code");
		toolsDto.setName("name");
		toolsDto.setType(3);
		toolsDto.setClassification(0);
		final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
		toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
		toolsDto.setScriptName("scriptName");
		toolsDto.setScriptIds("scriptIds");
		final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
		toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
		toolsDto.setStatus(0);
		final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
		toolsProjectInfoDto.setProjectVersionId(0L);
		toolsProjectInfoDto.setProjectName("projectName");
		toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
		toolsDto.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
		toolsDto.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
		when(mockToolsInfoService.getToolsCombinedInfo(0L)).thenReturn(toolsDto);
        final ScriptFileInfo scriptFileInfo = new ScriptFileInfo();
		when(mockScriptInteract.exportScriptProduction(anyList())).thenReturn(scriptFileInfo);
		assertThatThrownBy(() -> commonServiceImplUnderTest.exportTools(response, new Long[] { 0L }, null, "本地", null))
				.isInstanceOf(ToolsException.class);
	}
	@Test
	void testExportTools_Zip3() throws ToolsException, ScriptException {
		final MockHttpServletResponse response = new MockHttpServletResponse();
		final ToolsDto toolsDto = new ToolsDto();
		toolsDto.setId(0L);
		toolsDto.setCode("code");
		toolsDto.setName("name");
		toolsDto.setType(3);
		toolsDto.setClassification(0);
		final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
		toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
		toolsDto.setScriptName("scriptName");
		toolsDto.setScriptIds("scriptIds");
		final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
		toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
		toolsDto.setStatus(0);
		final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
		toolsProjectInfoDto.setProjectVersionId(0L);
		toolsProjectInfoDto.setProjectName("projectName");
		toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
		toolsDto.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
		toolsDto.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
		when(mockToolsInfoService.getToolsCombinedInfo(0L)).thenReturn(toolsDto);
        final ScriptFileInfo scriptFileInfo = new ScriptFileInfo();
        scriptFileInfo.setFileName("fileName");
        scriptFileInfo.setFileSuffix("fileSuffix");
        scriptFileInfo.setFileContentByte("content".getBytes());
		when(mockScriptInteract.exportScriptProduction(anyList())).thenReturn(scriptFileInfo);
        final R<Object> result =commonServiceImplUnderTest.exportTools(response, new Long[] { 0L }, null, "本地", null);
		assertNull(result);
	}
	
	@Test
	void testExportTools_Zip4() throws ToolsException, ScriptException {
		final MockHttpServletResponse response = new MockHttpServletResponse();
		final ToolsDto toolsDto = new ToolsDto();
		toolsDto.setId(0L);
		toolsDto.setCode("code");
		toolsDto.setName("name");
		toolsDto.setType(2);
		toolsDto.setClassification(0);
		final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
		toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
		toolsDto.setScriptName("scriptName");
		toolsDto.setScriptIds("scriptIds");
		final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
		toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
		toolsDto.setStatus(0);
		final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
		toolsProjectInfoDto.setProjectVersionId(0L);
		toolsProjectInfoDto.setProjectName("projectName");
		toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
		toolsDto.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
		toolsDto.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
		when(mockToolsInfoService.getToolsCombinedInfo(0L)).thenReturn(toolsDto);
        final ScriptFileInfo scriptFileInfo = new ScriptFileInfo();
        scriptFileInfo.setFileName("fileName");
        scriptFileInfo.setFileSuffix("fileSuffix");
        scriptFileInfo.setFileContentByte("content".getBytes());
		assertThatThrownBy(() -> commonServiceImplUnderTest.exportTools(response, new Long[] { 0L }, null, "本地", null))
		.isInstanceOf(Exception.class);
	}
	
	@Test
	void testExportTools_Zip5() throws ToolsException, ScriptException, StudioException {
		final MockHttpServletResponse response = new MockHttpServletResponse();
		final ToolsDto toolsDto = new ToolsDto();
		toolsDto.setId(0L);
		toolsDto.setCode("code");
		toolsDto.setName("name");
		toolsDto.setType(2);
		toolsDto.setClassification(0);
		final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
		toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
		toolsDto.setScriptName("scriptName");
		toolsDto.setScriptIds("scriptIds");
		final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
		toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
		toolsDto.setStatus(0);
		final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
		toolsProjectInfoDto.setProjectVersionId(0L);
		toolsProjectInfoDto.setProjectName("projectName");
		toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
		toolsDto.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
		toolsDto.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
		when(mockToolsInfoService.getToolsCombinedInfo(0L)).thenReturn(toolsDto);
        final ScriptFileInfo scriptFileInfo = new ScriptFileInfo();
        scriptFileInfo.setFileName("fileName");
        scriptFileInfo.setFileSuffix("fileSuffix");
        scriptFileInfo.setFileContentByte("content".getBytes());
		when(mockStudioInteract.exportStudioData(anyLong())).thenReturn(new StudioFileProjectInteractDto("1","1","1".getBytes()));
		 final R<Object> result =commonServiceImplUnderTest.exportTools(response, new Long[] { 0L }, null, "本地", null);
		assertNull(result);
	}
	
	@Test
	void testExportTools_Zip6() throws ToolsException, ScriptException, StudioException {
		final MockHttpServletResponse response = new MockHttpServletResponse();
		final ToolsDto toolsDto = new ToolsDto();
		toolsDto.setId(0L);
		toolsDto.setCode("code");
		toolsDto.setName("name");
		toolsDto.setType(2);
		toolsDto.setClassification(0);
		final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
		toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
		toolsDto.setScriptName("scriptName");
		toolsDto.setScriptIds("scriptIds");
		final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
		toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
		toolsDto.setStatus(0);
		toolsDto.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
		toolsDto.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
		when(mockToolsInfoService.getToolsCombinedInfo(0L)).thenReturn(toolsDto);
        final ScriptFileInfo scriptFileInfo = new ScriptFileInfo();
        scriptFileInfo.setFileName("fileName");
        scriptFileInfo.setFileSuffix("fileSuffix");
        scriptFileInfo.setFileContentByte("content".getBytes());
		 final R<Object> result =commonServiceImplUnderTest.exportTools(response, new Long[] { 0L }, null, "本地", null);
		assertNull(result);
	}
	
	@Test
	void testExportTools_Zip7() throws ToolsException, ScriptException, StudioException {
		final MockHttpServletResponse response = new MockHttpServletResponse();
		final ToolsDto toolsDto = new ToolsDto();
		toolsDto.setId(0L);
		toolsDto.setCode("code");
		toolsDto.setName("name");
		toolsDto.setType(2);
		toolsDto.setClassification(0);
		final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
		toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
		toolsDto.setScriptName("scriptName");
		toolsDto.setScriptIds("scriptIds");
		final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
		toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
		toolsDto.setStatus(0);
		toolsDto.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
		toolsDto.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
		when(mockToolsInfoService.getToolsCombinedInfo(0L)).thenReturn(toolsDto);
        final ScriptFileInfo scriptFileInfo = new ScriptFileInfo();
        scriptFileInfo.setFileName("fileName");
        scriptFileInfo.setFileSuffix("fileSuffix");
        scriptFileInfo.setFileContentByte("content".getBytes());
		 final R<Object> result =commonServiceImplUnderTest.exportTools(response, new Long[] { 0L,0L }, null, "本地", null);
		assertNull(result);
	}
	@Test
    void exportToolsNull() throws Exception {
        // Setup
        final MockHttpServletResponse response = new MockHttpServletResponse();
        when(mockBusinessConfig.isToolEvnMainSwitch()).thenReturn(true);
        when(mockBusinessConfig.getToolsEvn()).thenReturn("生产环境");

        // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
        final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
        toolsInfoCategoryEntity.setPermissionLevel(0);
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setCode("code");
        toolsInfoCategoryEntity.setName("name");
        toolsInfoCategoryEntity.setType(0);
        when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);

        // Configure IToolsInfoService.getToolsCombinedInfo(...).
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setCode("code");
        toolsDto.setName("name");
        toolsDto.setType(0);
        toolsDto.setClassification(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        toolsDto.setStatus(0);
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setProjectVersionId(0L);
        toolsProjectInfoDto.setProjectName("projectName");
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        when(mockToolsInfoService.getToolsCombinedInfo(0L)).thenReturn(toolsDto);
        // Run the test
        assertThatThrownBy(() -> commonServiceImplUnderTest.exportTools(response, new Long[]{0L}, 1, "SFTP",1)).isInstanceOf(ToolsException.class);
    }
  @Test
  void exportToolsNull2() throws Exception {
	  // Setup
	  final MockHttpServletResponse response = new MockHttpServletResponse();
	  when(mockBusinessConfig.isToolEvnMainSwitch()).thenReturn(true);
	  when(mockBusinessConfig.getToolsEvn()).thenReturn("DEV环境");
	  
	  // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
	  final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
	  toolsInfoCategoryEntity.setPermissionLevel(0);
	  toolsInfoCategoryEntity.setId(1L);
	  toolsInfoCategoryEntity.setCode("code");
	  toolsInfoCategoryEntity.setName("name");
	  toolsInfoCategoryEntity.setType(0);
	  when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);
	  
	  // Configure IToolsInfoService.getToolsCombinedInfo(...).
	  final ToolsDto toolsDto = new ToolsDto();
	  toolsDto.setId(0L);
	  toolsDto.setCode("code");
	  toolsDto.setName("name");
	  toolsDto.setType(0);
	  toolsDto.setClassification(0);
	  final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
	  toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
	  toolsDto.setScriptName("scriptName");
	  toolsDto.setScriptIds("scriptIds");
	  final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
	  toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
	  toolsDto.setStatus(0);
	  final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
	  toolsProjectInfoDto.setProjectVersionId(0L);
	  toolsProjectInfoDto.setProjectName("projectName");
	  toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
	  toolsDto.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
	  toolsDto.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
	  when(mockToolsInfoService.getToolsCombinedInfo(0L)).thenReturn(toolsDto);
	  // Run the test
	  assertThatThrownBy(() -> commonServiceImplUnderTest.exportTools(response, new Long[]{0L}, 1, "SFTP",1)).isInstanceOf(ToolsException.class);
  }
  
  @Test
  void exportToolsNull3() throws Exception {
	  // Setup
	  final MockHttpServletResponse response = new MockHttpServletResponse();
	  when(mockBusinessConfig.isToolEvnMainSwitch()).thenReturn(true);
	  when(mockBusinessConfig.getToolsEvn()).thenReturn("DEV环境");
	  
	  // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
	  final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
	  toolsInfoCategoryEntity.setPermissionLevel(0);
	  toolsInfoCategoryEntity.setId(1L);
	  toolsInfoCategoryEntity.setCode("code");
	  toolsInfoCategoryEntity.setName("name");
	  toolsInfoCategoryEntity.setType(0);
	  when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);
	  
	  // Configure IToolsInfoService.getToolsCombinedInfo(...).
	  final ToolsDto toolsDto = new ToolsDto();
	  toolsDto.setId(0L);
	  toolsDto.setCode("code");
	  toolsDto.setName("name");
	  toolsDto.setType(1);
	  toolsDto.setClassification(0);
	  final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
	  toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
	  toolsDto.setScriptName("scriptName");
	  toolsDto.setScriptIds("scriptIds");
	  final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
	  toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
	  toolsDto.setStatus(0);
	  final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
	  toolsProjectInfoDto.setProjectVersionId(0L);
	  toolsProjectInfoDto.setProjectName("projectName");
	  toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
	  toolsDto.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
	  toolsDto.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
	  when(mockToolsInfoService.getToolsCombinedInfo(0L)).thenReturn(toolsDto);
	  // Run the test
	  assertThatThrownBy(() -> commonServiceImplUnderTest.exportTools(response, new Long[]{0L}, 1, "SFTP",1)).isInstanceOf(ToolsException.class);
  }
  @Test
  void exportToolsNull4() throws Exception {
	  // Setup
	  final MockHttpServletResponse response = new MockHttpServletResponse();
	  when(mockBusinessConfig.isToolEvnMainSwitch()).thenReturn(true);
	  when(mockBusinessConfig.getToolsEvn()).thenReturn("DEV环境");
	  
	  // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
	  final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
	  toolsInfoCategoryEntity.setPermissionLevel(0);
	  toolsInfoCategoryEntity.setId(1L);
	  toolsInfoCategoryEntity.setCode("code");
	  toolsInfoCategoryEntity.setName("name");
	  toolsInfoCategoryEntity.setType(0);
	  when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);
	  
	  // Configure IToolsInfoService.getToolsCombinedInfo(...).
	  final ToolsDto toolsDto = new ToolsDto();
	  toolsDto.setId(0L);
	  toolsDto.setCode("code");
	  toolsDto.setName("name");
	  toolsDto.setType(2);
	  toolsDto.setClassification(0);
	  final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
	  toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
	  toolsDto.setScriptName("scriptName");
	  toolsDto.setScriptIds("scriptIds");
	  final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
	  toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
	  toolsDto.setStatus(0);
	  final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
	  toolsProjectInfoDto.setProjectVersionId(0L);
	  toolsProjectInfoDto.setProjectName("projectName");
	  toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
	  toolsDto.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
	  toolsDto.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
	  when(mockToolsInfoService.getToolsCombinedInfo(0L)).thenReturn(toolsDto);
	  // Run the test
	  assertThatThrownBy(() -> commonServiceImplUnderTest.exportTools(response, new Long[]{0L}, 1, "SFTP",2)).isInstanceOf(ToolsException.class);
  }
  @Test
  void TestCheckStatus() throws Exception {
	  // Setup
	  final MockHttpServletResponse response = new MockHttpServletResponse();
	  when(mockBusinessConfig.isToolEvnMainSwitch()).thenReturn(true);
	  // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
	  final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
	  toolsInfoCategoryEntity.setPermissionLevel(0);
	  toolsInfoCategoryEntity.setId(1L);
	  toolsInfoCategoryEntity.setCode("code");
	  toolsInfoCategoryEntity.setName("name");
	  toolsInfoCategoryEntity.setType(0);
	  when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);
	  // Configure IToolsInfoService.getToolsCombinedInfo(...).
	  final ToolsDto toolsDto = new ToolsDto();
	  toolsDto.setId(0L);
	  toolsDto.setCode("code");
	  toolsDto.setName("name");
	  toolsDto.setType(2);
	  toolsDto.setClassification(0);
	  final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
	  toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
	  toolsDto.setScriptName("scriptName");
	  toolsDto.setScriptIds("scriptIds");
	  final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
	  toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
	  toolsDto.setStatus(1);
	  when(mockBusinessConfig.getToolsEvn()).thenReturn("DEV环境");
	  final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
	  toolsProjectInfoDto.setProjectVersionId(0L);
	  toolsProjectInfoDto.setProjectName("projectName");
	  toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
	  toolsDto.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
	  toolsDto.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
	  when(mockToolsInfoService.getToolsCombinedInfo(0L)).thenReturn(toolsDto);
	  // Run the test
	  assertThatThrownBy(() -> commonServiceImplUnderTest.exportTools(response, new Long[]{0L}, 1, "SFTP",2)).isInstanceOf(ToolsException.class);
	  
	  when(mockBusinessConfig.getToolsEvn()).thenReturn("DEV环境");
	  toolsDto.setStatus(3);
	  // Run the test
	  assertThatThrownBy(() -> commonServiceImplUnderTest.exportTools(response, new Long[]{0L}, 1, "SFTP",2)).isInstanceOf(ToolsException.class);
	  
	  toolsDto.setStatus(9);
	  // Run the test
	  assertThatThrownBy(() -> commonServiceImplUnderTest.exportTools(response, new Long[]{0L}, 1, "SFTP",2)).isInstanceOf(ToolsException.class);
	  toolsDto.setStatus(10);
	  // Run the test
	  assertThatThrownBy(() -> commonServiceImplUnderTest.exportTools(response, new Long[]{0L}, 1, "SFTP",2)).isInstanceOf(ToolsException.class);
	  when(mockBusinessConfig.getToolsEvn()).thenReturn("QA环境");
	  // Run the test
	  assertThatThrownBy(() -> commonServiceImplUnderTest.exportTools(response, new Long[]{0L}, 1, "SFTP",2)).isInstanceOf(ToolsException.class);
	  when(mockBusinessConfig.getToolsEvn()).thenReturn("验证环境");
	  // Run the test
	  assertThatThrownBy(() -> commonServiceImplUnderTest.exportTools(response, new Long[]{0L}, 1, "SFTP",2)).isInstanceOf(ToolsException.class);
	  when(mockBusinessConfig.getToolsEvn()).thenReturn("生产环境");
	  // Run the test
	  assertThatThrownBy(() -> commonServiceImplUnderTest.exportTools(response, new Long[]{0L}, 1, "SFTP",2)).isInstanceOf(ToolsException.class);
  }
  
  @Test
  void testPublishMigrationExportToolsb() throws Exception {
      // Setup
      final MockHttpServletResponse response = new MockHttpServletResponse();
      when(mockBusinessConfig.isToolEvnMainSwitch()).thenReturn(true);
      when(mockBusinessConfig.getToolsEvn()).thenReturn(ToolsEnvEnum.QA_ENV.getDesc());

      // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
      final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
      toolsInfoCategoryEntity.setPermissionLevel(0);
      toolsInfoCategoryEntity.setId(0L);
      toolsInfoCategoryEntity.setCode("code");
      toolsInfoCategoryEntity.setName("name");
      toolsInfoCategoryEntity.setType(0);
      when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);

      // Configure IToolsInfoService.getToolsCombinedInfo(...).
      final ToolsDto toolsDto = new ToolsDto();
      toolsDto.setId(0L);
      toolsDto.setCode("code");
      toolsDto.setName("name");
      toolsDto.setType(2);
      toolsDto.setClassification(0);
      final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
      toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
      toolsDto.setScriptName("scriptName");
      toolsDto.setScriptIds("scriptIds");
      final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
      toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
      toolsDto.setStatus(0);
      final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
      toolsProjectInfoDto.setProjectVersionId(0L);
      toolsProjectInfoDto.setProjectName("projectName");
      toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
      toolsDto.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
      toolsDto.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
      when(mockToolsInfoService.getToolsCombinedInfo(0L)).thenReturn(toolsDto);
      when(mockBusinessConfig.getFtpType()).thenReturn(FtpTypeEnum.SFTP.getDesc());
      when(mockBusinessConfig.getFtpUser()).thenReturn("result");
      when(mockBusinessConfig.getFtpPw()).thenReturn("result");
      when(mockBusinessConfig.getFtpIp()).thenReturn("result");
      when(mockBusinessConfig.getFtpPort()).thenReturn("80");
      when(mockBusinessConfig.getSftpDataPath()).thenReturn("result");

      // Configure ScriptInteract.exportScriptProduction(...).
      final ScriptFileInfo scriptFileInfo = new ScriptFileInfo();
      scriptFileInfo.setFileName("fileName");
      scriptFileInfo.setFileSuffix("fileSuffix");
      scriptFileInfo.setFileContentByte("content".getBytes());

      // Configure StudioInteract.exportStudioData(...).
      final StudioFileProjectInteractDto studioFileProjectInteractDto = new StudioFileProjectInteractDto("fileName",
              "fileSuffix", "content".getBytes());
      final ExecuteHistoryBean executeHistoryBean = new ExecuteHistoryBean();
      executeHistoryBean.setClassification(0);
      executeHistoryBean.setSearchStartTime("searchStartTime");
      executeHistoryBean.setSearchEndTime("searchEndTime");
      executeHistoryBean.setRunningId(0L);
      executeHistoryBean.setExecFrom(0L);
      final List<ExecuteHistoryBean> executeHistoryBeans = Arrays.asList(executeHistoryBean);
      when(mockExecuteHistoryMapper.selectHistoryMonitorSuccessByToolsId(0L, 1)).thenReturn(executeHistoryBeans);
		when(mockStudioInteract.exportStudioData(anyLong())).thenReturn(new StudioFileProjectInteractDto("1","1","1".getBytes()));
		when(sftputil.uploadZipFile(any(),any(),any())).thenReturn(true);
		when(sftputil.uploadFile(any(),any())).thenReturn(true);
      // Run the test
      R<Object> result = commonServiceImplUnderTest.publishMigrationExportTools(response, new Long[]{0L}, 1, 0);
      assertThat(result).isNotNull();
		commonServiceImplUnderTest = new CommonServiceImpl(mockToolsInfoService, mockBusinessConfig, mockScriptInteract,
				mockStudioInteract, mockExecuteHistoryMapper);
      // Run the test
      assertThatThrownBy(
              () -> commonServiceImplUnderTest.publishMigrationExportTools(response, new Long[]{0L}, 1, 0));
  }
}

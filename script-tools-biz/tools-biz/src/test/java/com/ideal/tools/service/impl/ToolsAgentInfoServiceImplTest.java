package com.ideal.tools.service.impl;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import com.github.pagehelper.PageInfo;
import com.ideal.tools.exception.InteractServiceException;
import com.ideal.tools.exception.ToolsException;
import com.ideal.tools.mapper.ToolsAgentInfoMapper;
import com.ideal.tools.model.dto.AgentQueryDto;
import com.ideal.tools.model.dto.AgentSelectedDto;
import com.ideal.tools.model.dto.ToolsAgentInfoDto;
import com.ideal.tools.model.dto.ToolsAgentInfoQueryDto;
import com.ideal.tools.model.dto.ToolsAgentResultDto;
import com.ideal.tools.model.dto.UserDto;
import com.ideal.tools.model.entity.ToolsAgentInfoEntity;
import com.ideal.tools.model.interaction.AgentListResultDto;
import com.ideal.tools.service.interaction.AgentInteract;
import com.ideal.tools.service.interaction.SystemComputerInteract;

class ToolsAgentInfoServiceImplTest {

    @Mock
    ToolsAgentInfoMapper toolsAgentInfoMapper;
    @Mock
    AgentInteract agentInteract;
    @Mock
    SystemComputerInteract systemComputerInteract;
    @InjectMocks
    ToolsAgentInfoServiceImpl toolsAgentInfoServiceImpl;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testSelectToolsAgentInfoById() {
        when(toolsAgentInfoMapper.selectToolsAgentInfoById(anyLong())).thenReturn(new ToolsAgentInfoEntity());

        ToolsAgentInfoDto result = toolsAgentInfoServiceImpl.selectToolsAgentInfoById(Long.valueOf(1));
        // Verify the results
        Assertions.assertNotNull( result);
    }


    @Test
    void testInsertToolsAgentInfo() {
        when(toolsAgentInfoMapper.insertToolsAgentInfo(any())).thenReturn(0);

        int result = toolsAgentInfoServiceImpl.insertToolsAgentInfo(new ToolsAgentInfoDto());
        assertEquals(0, result);
    }

    @Test
    void testUpdateToolsAgentInfo() {
        when(toolsAgentInfoMapper.updateToolsAgentInfo(any())).thenReturn(0);

        int result = toolsAgentInfoServiceImpl.updateToolsAgentInfo(new ToolsAgentInfoDto());
        assertEquals(0, result);
    }

    @Test
    void testDeleteToolsAgentInfoByIds() {
        when(toolsAgentInfoMapper.deleteToolsAgentInfoByIds(any(Long[].class))).thenReturn(0);

        int result = toolsAgentInfoServiceImpl.deleteToolsAgentInfoByIds(new Long[]{Long.valueOf(1)});
        assertEquals(0, result);
    }

 /*   @Test
    void testSelectStandardTaskComputerIdLists() throws InteractServiceException {
        final UserDto userDto = new UserDto();
        userDto.setUserName("admin");
        userDto.setUserId(1L);
        userDto.setUserCode("userCode");
        userDto.setLoginName("loginName");
        when(toolsAgentInfoMapper.selectToolsAgentInfoList(any())).thenReturn(Arrays.<ToolsAgentInfoEntity>asList(new ToolsAgentInfoEntity()));
        when(agentInteract.getAgentList(any(), anyInt(), anyInt())).thenReturn(null);
        // Run the test
        PageInfo<ToolsAgentResultDto> result =   toolsAgentInfoServiceImpl.selectStandardTaskComputerIdLists(new AgentQueryDto(), userDto,Integer.valueOf(0), Integer.valueOf(0));
        // Verify the results
        Assertions.assertNotNull( result);
    }*/

  /*  @Test
    void testStandardAgent() throws InteractServiceException {
        final UserDto userDto = new UserDto();
        userDto.setUserName("admin");
        userDto.setUserId(1L);
        userDto.setUserCode("userCode");
        userDto.setLoginName("loginName");
        final AgentQueryDto agentQueryDto = new AgentQueryDto();
        when(agentInteract.getAgentList(any(), anyInt(), anyInt())).thenReturn(new PageInfo<AgentListResultDto>(Collections.emptyList()));

        PageInfo<ToolsAgentResultDto> result = toolsAgentInfoServiceImpl.standardAgent(agentQueryDto, userDto, Integer.valueOf(0), Integer.valueOf(0));
        // Verify the results
        Assertions.assertNotNull(result);
    }*/

    @Test
    void testBatchInsertToolsAgent(){

        // Run the test
        assertThatThrownBy(
                () -> toolsAgentInfoServiceImpl.batchInsertToolsAgent(Long.valueOf(1), Arrays.<ToolsAgentInfoDto>asList(new ToolsAgentInfoDto())))
                .isInstanceOf(ToolsException.class);


    }

    @Test
    void testDeleteAgentToolId() {
        when(toolsAgentInfoMapper.deleteAgentToolId(anyLong())).thenReturn(0);

        int result = toolsAgentInfoServiceImpl.deleteAgentToolId(Long.valueOf(1));
        assertEquals(0, result);
    }

    @Test
    void testSelectAgentIpByToolIdAndAgentIp() {
        when(toolsAgentInfoMapper.selectAgentIpByToolIdAndAgentIp(any())).thenReturn(Arrays.<String>asList("String"));

        List<String> result = toolsAgentInfoServiceImpl.selectAgentIpByToolIdAndAgentIp(new ToolsAgentInfoEntity());
        assertEquals(Arrays.<String>asList("String"), result);
    }

    @Test
    void testSelectAgentCountByToolId_WhenValidToolId_ReturnsCount() {
        Long validToolId = 1001L;
        int expectedCount = 3;
        when(toolsAgentInfoMapper.selectAgentCountByToolId(validToolId))
                .thenReturn(expectedCount);

        int actualCount = toolsAgentInfoServiceImpl.selectAgentCountByToolId(validToolId);

        assertThat(actualCount).isEqualTo(expectedCount);
    }

    @Test
    void testSelectAgentCountByToolId_WhenNoRecords_ReturnsZero() {
        Long nonExistToolId = 9999L;
        when(toolsAgentInfoMapper.selectAgentCountByToolId(nonExistToolId))
                .thenReturn(0);

        int actualCount = toolsAgentInfoServiceImpl.selectAgentCountByToolId(nonExistToolId);

        assertThat(actualCount).isZero();
    }

    @Test
    void testSelectAgentCountByToolId_WhenNullInput_ReturnsZero() {
        int actualCount = toolsAgentInfoServiceImpl.selectAgentCountByToolId(null);

        assertThat(actualCount).isZero();
        verify(toolsAgentInfoMapper, never()).selectAgentCountByToolId(anyLong());
    }

    @Test
    void testSelectToolsAgentInfoList() throws InteractServiceException {
        AgentQueryDto query = new AgentQueryDto();
        query.setTdToolsId(0L);
        query.setComputerIp("***********");
        query.setAgentIpStart("***********");
        query.setAgentIpEnd("***********");
        ToolsAgentInfoEntity toolsAgentInfoEntity = new ToolsAgentInfoEntity();
        toolsAgentInfoEntity.setTdToolsId(0L);
        toolsAgentInfoEntity.setAgentIp("***********");
        toolsAgentInfoEntity.setAgentIpStart(3232235777L);
        toolsAgentInfoEntity.setAgentIpEnd(3232235777L);
        List<ToolsAgentInfoEntity> toolsAgentInfoEntityList = new ArrayList<>();
        toolsAgentInfoEntityList.add(toolsAgentInfoEntity);
        when(toolsAgentInfoMapper.selectToolsAgentInfoList(any())).thenReturn(toolsAgentInfoEntityList);

        List<ToolsAgentInfoEntity> result = toolsAgentInfoServiceImpl.selectToolsAgentInfoList(query);

        assertEquals(toolsAgentInfoEntityList, result);
    }

    @Test
    void testSelectAgentLists() throws InteractServiceException {
        AgentQueryDto query = new AgentQueryDto();
        final UserDto userDto = new UserDto();
        userDto.setUserName("admin");
        userDto.setUserId(1L);
        userDto.setUserCode("userCode");
        userDto.setLoginName("loginName");
        query.setTdToolsId(0L);
        query.setSelected(2);
        ToolsAgentInfoEntity toolsAgentInfoEntity=new ToolsAgentInfoEntity();
        toolsAgentInfoEntity.setAgentIp("*******");
        List<ToolsAgentInfoEntity> retToolsAgentInfoEntity=new LinkedList<ToolsAgentInfoEntity>();
        retToolsAgentInfoEntity.add(toolsAgentInfoEntity);
        when(toolsAgentInfoMapper.selectToolsAgentInfoList(any())).thenReturn(retToolsAgentInfoEntity);
//        when(agentInteract.getAgentList(any(), anyInt(), anyInt())).thenReturn(null);
        AgentListResultDto agentListResultDto=new AgentListResultDto();
        agentListResultDto.setAgentId(1L);
        agentListResultDto.setAgentIp("*******");
        List<AgentListResultDto> agentResult = Collections.singletonList(agentListResultDto);
        when(systemComputerInteract.getAgentAllListBySysId(any(), any())).thenReturn(agentResult);
        // Run the test
        PageInfo<ToolsAgentResultDto> result =   toolsAgentInfoServiceImpl.selectAgentLists(query, Integer.valueOf(1), Integer.valueOf(1),1L);
        // Verify the results
        Assertions.assertNotNull( result);
    }

    @Test
    void testGetAgemtInfoEntityList() throws InteractServiceException {
        ToolsAgentInfoEntity toolsAgentInfoEntity = new ToolsAgentInfoEntity();
        toolsAgentInfoEntity.setTdToolsId(0L);
        toolsAgentInfoEntity.setAgentIp("***********");
        toolsAgentInfoEntity.setAgentIpStart(3232235777L);
        toolsAgentInfoEntity.setAgentIpEnd(3232235777L);
        List<ToolsAgentInfoEntity> toolsAgentInfoEntityList = new ArrayList<>();
        toolsAgentInfoEntityList.add(toolsAgentInfoEntity);

        AgentSelectedDto agentSelectedDto = new AgentSelectedDto();
        List<AgentSelectedDto> agentSelectedDtoList = new ArrayList<>();
        agentSelectedDtoList.add(agentSelectedDto);
        List<ToolsAgentInfoEntity> result =   toolsAgentInfoServiceImpl.getAgemtInfoEntityList(toolsAgentInfoEntityList, agentSelectedDtoList, Integer.valueOf(1));
        // Verify the results
        Assertions.assertNotNull( result);
    }

    @Test
    void testGetAgemtInfoEntitySelectedList() throws InteractServiceException {
        ToolsAgentInfoEntity toolsAgentInfoEntity = new ToolsAgentInfoEntity();
        toolsAgentInfoEntity.setTdToolsId(0L);
        toolsAgentInfoEntity.setAgentIp("***********");
        toolsAgentInfoEntity.setAgentPort(8080);
        toolsAgentInfoEntity.setAgentIpStart(3232235777L);
        toolsAgentInfoEntity.setAgentIpEnd(3232235777L);

        AgentSelectedDto agentSelectedDto = new AgentSelectedDto();
        agentSelectedDto.setAgentIp("***********");
        agentSelectedDto.setAgentPort(8080);
        ToolsAgentInfoEntity result =   toolsAgentInfoServiceImpl.getAgemtInfoEntitySelectedList(agentSelectedDto, toolsAgentInfoEntity, Integer.valueOf(1));
        // Verify the results
        Assertions.assertNotNull( result);
    }

    @Test
    void testIpToLong() throws InteractServiceException {
        String ip = "***********";
        Long expected = 3232235777L;
        long result = ToolsAgentInfoServiceImpl.ipToLong(ip);
        assertEquals(expected, result);
    }

    @Test
    void testIsValidIp() {
        assertFalse(ToolsAgentInfoServiceImpl.isValidIp("300.300.300.300"));
    }

    @Test
    void testSelectToolsAgentInfoList2() throws InteractServiceException {
        ToolsAgentInfoQueryDto query = new ToolsAgentInfoQueryDto ();
        query.setAgentIp("***********");
        query.setAgentPort(8080);

        when(toolsAgentInfoMapper.selectToolsAgentInfoList(any())).thenReturn(null);

        PageInfo<ToolsAgentInfoDto> result = toolsAgentInfoServiceImpl.selectToolsAgentInfoList(query, Integer.valueOf(1), Integer.valueOf(1));

        Assertions.assertNotNull( result);
    }

/*
    @Test
    void testStandardAgent2() throws InteractServiceException {
        AgentListResultDto agentListResultDto = new AgentListResultDto();
        agentListResultDto.setAgentIp("***********");
        agentListResultDto.setPort(8080);
        List<AgentListResultDto> agentListResultDtoList = new ArrayList<>();
        agentListResultDtoList.add(agentListResultDto);
        PageInfo<AgentListResultDto> agentRespPage = new PageInfo<AgentListResultDto>();
        agentRespPage.setList(agentListResultDtoList);

        UserDto userDto = new UserDto();
        userDto.setUserName("admin");
        userDto.setUserId(1L);
        userDto.setUserCode("userCode");
        userDto.setLoginName("loginName");

        when(agentInteract.getAgentExcludeList(any(), anyInt(), anyInt())).thenReturn(agentRespPage);

        AgentQueryDto query = new AgentQueryDto();
        query.setTdToolsId(0L);
        query.setSelected(1);
        AgentSelectedDto agentSelectedDto = new AgentSelectedDto();
        agentSelectedDto.setAgentIp("***********");
        agentSelectedDto.setAgentPort(8080);
        List<AgentSelectedDto> agentList = new ArrayList<>();
        agentList.add(agentSelectedDto);
        query.setAgentList(agentList);
        PageInfo<ToolsAgentResultDto> result = toolsAgentInfoServiceImpl.standardAgent(query, userDto, Integer.valueOf(1), Integer.valueOf(1));
        // Verify the results
        Assertions.assertNotNull( result);
    }
*/

}

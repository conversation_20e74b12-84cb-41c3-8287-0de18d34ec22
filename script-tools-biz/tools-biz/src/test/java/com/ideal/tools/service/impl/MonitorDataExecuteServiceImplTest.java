package com.ideal.tools.service.impl;

import com.ideal.monitor.mq.MonitorFlowDto;
import com.ideal.monitor.mq.MonitorFowActiveNodeDto;
import com.ideal.tools.exception.ExecuteMonitorException;
import com.ideal.tools.model.interaction.MonitorFlowInteractDto;
import com.ideal.tools.service.IExecuteMonitorService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.sql.Timestamp;
import java.time.LocalDateTime;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class MonitorDataExecuteServiceImplTest {

    @Mock
    private IExecuteMonitorService mockExecuteMonitorService;

    private MonitorDataExecuteServiceImpl monitorDataExecuteServiceImplUnderTest;

    @BeforeEach
    void setUp() throws Exception {
        monitorDataExecuteServiceImplUnderTest = new MonitorDataExecuteServiceImpl(mockExecuteMonitorService);
    }
/*
    @Test
    void testExecuteMonitorData() throws Exception {
        // Setup
        final MonitorFlowDto monitorFlowDto = new MonitorFlowDto();
        monitorFlowDto.setBizUniqueId(0L);
        monitorFlowDto.setFlowStatus(0);
        monitorFlowDto.setFlowId(0L);
        monitorFlowDto.setDateTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        final MonitorFowActiveNodeDto monitorFowActiveNodeDto = new MonitorFowActiveNodeDto();
        monitorFowActiveNodeDto.setFlowId(0L);
        monitorFowActiveNodeDto.setActId(0L);
        monitorFowActiveNodeDto.setActName("actName");
        monitorFowActiveNodeDto.setActStatus("actStatus");
        monitorFlowDto.setMonitorFowActiveNodeDto(monitorFowActiveNodeDto);

        // Run the test
        monitorDataExecuteServiceImplUnderTest.executeMonitorData(monitorFlowDto);

        // Verify the results
        verify(mockExecuteMonitorService).updateStatus(any(MonitorFlowInteractDto.class));
    }*/

    @Test
    void testExecuteMonitorData_IExecuteMonitorServiceThrowsExecuteMonitorException() throws Exception {
        // Setup
        final MonitorFlowDto monitorFlowDto = new MonitorFlowDto();
        monitorFlowDto.setBizUniqueId(0L);
        monitorFlowDto.setFlowStatus(0);
        monitorFlowDto.setFlowId(0L);
        monitorFlowDto.setDateTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        final MonitorFowActiveNodeDto monitorFowActiveNodeDto = new MonitorFowActiveNodeDto();
        monitorFowActiveNodeDto.setFlowId(0L);
        monitorFowActiveNodeDto.setActId(0L);
        monitorFowActiveNodeDto.setActName("actName");
        monitorFowActiveNodeDto.setActStatus("actStatus");
        monitorFlowDto.setMonitorFowActiveNodeDto(monitorFowActiveNodeDto);

//        when(mockExecuteMonitorService.updateStatus(any(MonitorFlowInteractDto.class)))
//                .thenThrow(ExecuteMonitorException.class);

        // Run the test
        monitorDataExecuteServiceImplUnderTest.executeMonitorData(monitorFlowDto);

        // Verify the results
    }
}

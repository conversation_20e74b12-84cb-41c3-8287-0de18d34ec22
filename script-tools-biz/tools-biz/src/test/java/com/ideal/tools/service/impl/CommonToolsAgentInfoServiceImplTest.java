package com.ideal.tools.service.impl;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.Arrays;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import com.ideal.tools.exception.ToolsException;
import com.ideal.tools.mapper.CommonToolsAgentInfoMapper;
import com.ideal.tools.model.dto.AuditEverybodyQueryDto;
import com.ideal.tools.model.dto.CommonToolsAgentInfoDto;
import com.ideal.tools.model.dto.UserDto;
import com.ideal.tools.model.entity.CommonToolsAgentInfoEntity;

@ExtendWith(MockitoExtension.class)
class CommonToolsAgentInfoServiceImplTest {

    @Mock(lenient=true)
    private CommonToolsAgentInfoMapper mockCommonToolsAgentInfoMapper;

    private CommonToolsAgentInfoServiceImpl commonToolsAgentInfoServiceImplUnderTest;

    @BeforeEach
    void setUp() throws Exception {
        commonToolsAgentInfoServiceImplUnderTest = new CommonToolsAgentInfoServiceImpl(mockCommonToolsAgentInfoMapper);
    }

    @Test
    void testSelectToolsAgentCountByCommonToolsid() {
        // Setup
        when(mockCommonToolsAgentInfoMapper.selectToolsAgentCountByCommonToolsid(0L)).thenReturn(0);

        // Run the test
        final int result = commonToolsAgentInfoServiceImplUnderTest.selectToolsAgentCountByCommonToolsid(0L);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

   @Test
    void testBatchInsertCommonToolsAgent_ToolsException() throws Exception {
        assertThatThrownBy(
                () -> commonToolsAgentInfoServiceImplUnderTest.batchInsertCommonToolsAgent(0L, null, null))
                .isInstanceOf(ToolsException.class);
    }
   
   @Test
   void testBatchInsertCommonToolsAgent() throws Exception {
       // 创建mapper mock
       CommonToolsAgentInfoMapper mapper = Mockito.mock(CommonToolsAgentInfoMapper.class);
       // 创建 impl 的 spy 对象
       CommonToolsAgentInfoServiceImpl impl = Mockito.spy(new CommonToolsAgentInfoServiceImpl(mapper));
       // Mock 父类的 void 方法，使其不执行任何操作
   	   doNothing().when(impl).batchData(anyList(), any());
   	   //---声明对象开始---
       final CommonToolsAgentInfoDto commonToolsAgentInfoDto = new CommonToolsAgentInfoDto();
       commonToolsAgentInfoDto.setId(0L);
       commonToolsAgentInfoDto.setTitleName("titleName");
       commonToolsAgentInfoDto.setAgentIp("***********");
       commonToolsAgentInfoDto.setAgentPort(8080);
       commonToolsAgentInfoDto.setTdCommonToolsId(0L);
       final UserDto userDto = new UserDto();
       userDto.setUserName("execUserName");
       userDto.setUserId(0L);
       userDto.setUserCode("userCode");
       userDto.setLoginName("loginName");
        final List<CommonToolsAgentInfoDto> auditEverybodyDtoList = Arrays.asList(commonToolsAgentInfoDto);
      //---声明对象结束---
       impl.batchInsertCommonToolsAgent(1L, auditEverybodyDtoList,userDto);
       // 验证父类方法是否被调用
      verify(impl, times(1)).batchInsertCommonToolsAgent(1L, auditEverybodyDtoList,userDto);
   }

    @Test
    void testSelectCommonToolsAgentInfoListByCommonToolsId() {
        // Setup
        // Configure CommonToolsAgentInfoMapper.selectCommonToolsAgentInfoListByCommonToolsId(...).
        final CommonToolsAgentInfoEntity commonToolsAgentInfoEntity = new CommonToolsAgentInfoEntity();
        commonToolsAgentInfoEntity.setId(0L);
        commonToolsAgentInfoEntity.setTitleName("titleName");
        commonToolsAgentInfoEntity.setAgentIp("agentIp");
        commonToolsAgentInfoEntity.setTdCommonToolsId(0L);
        commonToolsAgentInfoEntity.setExecUserName("execUserName");
        final List<CommonToolsAgentInfoEntity> commonToolsAgentInfoEntities = Arrays.asList(commonToolsAgentInfoEntity);
        when(mockCommonToolsAgentInfoMapper.selectCommonToolsAgentInfoListByCommonToolsId(0L))
                .thenReturn(commonToolsAgentInfoEntities);

        // Run the test
        final List<CommonToolsAgentInfoEntity> result = commonToolsAgentInfoServiceImplUnderTest.selectCommonToolsAgentInfoListByCommonToolsId(
                0L);

        // Verify the results
    }

    
    @Test
    void testSelectCommonToolsAgentInfoListByCommonToolsId_CommonToolsAgentInfoMapperReturnsNoItems() {
        // Setup
        when(mockCommonToolsAgentInfoMapper.selectCommonToolsAgentInfoListByCommonToolsId(0L))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<CommonToolsAgentInfoEntity> result = commonToolsAgentInfoServiceImplUnderTest.selectCommonToolsAgentInfoListByCommonToolsId(
                0L);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }
    
    @Test
    void testSelectCommonToolsAgentInfoListByCommonToolsId_Null() {
        // Verify the results
        assertNull(commonToolsAgentInfoServiceImplUnderTest.selectCommonToolsAgentInfoListByCommonToolsId(null));
    }

    @Test
    void testSelectToolsAgentCountByCommonToolsIdAndScriptToolsId() {
        // Setup
        when(mockCommonToolsAgentInfoMapper.selectToolsAgentCountByCommonToolsIdAndScriptToolsId(0L, 0L)).thenReturn(0);

        // Run the test
        final int result = commonToolsAgentInfoServiceImplUnderTest.selectToolsAgentCountByCommonToolsIdAndScriptToolsId(
                0L, 0L);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }
}

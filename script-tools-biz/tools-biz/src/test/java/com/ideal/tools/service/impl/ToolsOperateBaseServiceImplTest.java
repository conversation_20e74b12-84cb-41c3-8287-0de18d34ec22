package com.ideal.tools.service.impl;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.ideal.tools.config.BusinessConfig;
import com.ideal.tools.exception.ToolsException;
import com.ideal.tools.mapper.ToolsInfoMapper;
import com.ideal.tools.mapper.ToolsProjectMapper;
import com.ideal.tools.model.dto.AuditEverybodyQueryDto;
import com.ideal.tools.model.dto.ToolsAgentInfoDto;
import com.ideal.tools.model.dto.ToolsAgentResultDto;
import com.ideal.tools.model.dto.ToolsDto;
import com.ideal.tools.model.dto.ToolsFilesDto;
import com.ideal.tools.model.dto.ToolsFilesQueryDto;
import com.ideal.tools.model.dto.ToolsInfoDto;
import com.ideal.tools.model.dto.ToolsParamDto;
import com.ideal.tools.model.dto.ToolsParamResultDto;
import com.ideal.tools.model.dto.ToolsProjectInfoDto;
import com.ideal.tools.model.dto.UserDto;
import com.ideal.tools.model.entity.ToolsInfoEntity;
import com.ideal.tools.model.entity.ToolsProjectEntity;
import com.ideal.tools.model.interaction.ScriptContentDto;
import com.ideal.tools.service.IToolsAgentInfoService;
import com.ideal.tools.service.IToolsFilesService;
import com.ideal.tools.service.IToolsInfoService;
import com.ideal.tools.service.IToolsParamService;
import com.ideal.tools.service.producter.ICommonToolsAgentInfoService;

@ExtendWith(MockitoExtension.class)
class ToolsOperateBaseServiceImplTest {

    @Mock(lenient=true)
    private ToolsInfoMapper mockToolsInfoMapper;
    @Mock(lenient=true)
    private IToolsFilesService mockToolsFilesService;
    @Mock(lenient=true)
    private IToolsParamService mockToolsParamService;
    @Mock(lenient=true)
    private IToolsAgentInfoService mockToolsAgentInfoService;
    @Mock(lenient=true)
    private IToolsInfoService mockToolsInfoService;
    @Mock(lenient=true)
    private ICommonToolsAgentInfoService mockCommonToolsAgentInfoService;
    @Mock(lenient=true)
    private ToolsProjectMapper mockToolsProjectMapper;
    @Mock(lenient=true)
    private BusinessConfig mockBusinessConfig;

    private ToolsOperateBaseServiceImpl toolsOperateBaseServiceImplUnderTest;

    @BeforeEach
    void setUp() throws Exception {
        toolsOperateBaseServiceImplUnderTest = new ToolsOperateBaseServiceImpl(mockToolsInfoMapper,
                mockToolsFilesService, mockToolsParamService, mockToolsAgentInfoService, mockToolsInfoService,
                mockCommonToolsAgentInfoService, mockToolsProjectMapper, mockBusinessConfig);
    }

    @Test
    void testCheckParams() {
        // Setup
        ToolsDto toolsDto = getToolsDto();

        UserDto userDto = getUserDto();

        when(mockToolsInfoService.selectToolsInfoList(any(ToolsDto.class)))
                .thenReturn("checkParams tools The application system cannot be empty!");

        // Run the test
        final String result = toolsOperateBaseServiceImplUnderTest.checkParams(toolsDto, userDto);

        // Verify the results
        assertThat(result).isEqualTo("checkParams tools The application system cannot be empty!");
    }

    @Test
    void testCheckParams_WithNULL() {
        // Setup
        ToolsDto toolsDto = getToolsDto();

        when(mockToolsInfoService.selectToolsInfoList(any(ToolsDto.class)))
                .thenReturn("checkParams tools The application system cannot be empty!");

        // Run the test
        final String result = toolsOperateBaseServiceImplUnderTest.checkParams(toolsDto, null);

        // Verify the results
        assertThat(result).isEqualTo("checkParams userDto is null");
    }

    @Test
    void testCheckParams_WithUserNameBlank() {
        // Setup
        ToolsDto toolsDto = getToolsDto();

        UserDto userDto = getUserDto();
        userDto.setUserName(null);

        when(mockToolsInfoService.selectToolsInfoList(any(ToolsDto.class)))
                .thenReturn("checkParams tools The application system cannot be empty!");

        // Run the test
        final String result = toolsOperateBaseServiceImplUnderTest.checkParams(toolsDto, userDto);

        // Verify the results
        assertThat(result).isEqualTo("checkParams login user name is null");
    }

    @Test
    void testCheckParams_WithUserIdNULL() {
        // Setup
        ToolsDto toolsDto = getToolsDto();

        UserDto userDto = getUserDto();
        userDto.setUserId(null);

        when(mockToolsInfoService.selectToolsInfoList(any(ToolsDto.class)))
                .thenReturn("checkParams tools The application system cannot be empty!");

        // Run the test
        final String result = toolsOperateBaseServiceImplUnderTest.checkParams(toolsDto, userDto);

        // Verify the results
        assertThat(result).isEqualTo("checkParams login user name is null");
    }

    @Test
    void testCheckParams_WithToolsDtoNULL() {
        UserDto userDto = getUserDto();

        when(mockToolsInfoService.selectToolsInfoList(any(ToolsDto.class)))
                .thenReturn("checkParams tools The application system cannot be empty!");

        // Run the test
        final String result = toolsOperateBaseServiceImplUnderTest.checkParams(null, userDto);

        // Verify the results
        assertThat(result).isEqualTo("checkParams ToolsDto is null");
    }

    @Test
    void testCheckParams_WithToolsDtoNameLong() {
        // Setup
        ToolsDto toolsDto = getToolsDto();
        toolsDto.setName("111111111111111111111111111111111111111111111111111111111111111111111111111");
        UserDto userDto = getUserDto();

        when(mockToolsInfoService.selectToolsInfoList(any(ToolsDto.class)))
                .thenReturn("checkParams tools The application system cannot be empty!");

        // Run the test
        final String result = toolsOperateBaseServiceImplUnderTest.checkParams(toolsDto, userDto);

        // Verify the results
        assertThat(result).isEqualTo("checkParams ToolsName greater than 40");
    }

    @Test
    void testCheckParamsUpdate() {
        // Setup
        ToolsDto toolsDto = getToolsDto();

        when(mockToolsInfoService.selectToolsInfoList(any(ToolsDto.class)))
                .thenReturn("checkParams tools The application system cannot be empty!");

        // Run the test
        final String result = toolsOperateBaseServiceImplUnderTest.checkParamsUpdate(toolsDto);

        // Verify the results
        assertThat(result).isEqualTo("checkParams tools The application system cannot be empty!");
    }

    @Test
    void testCheckParamsUpdate_WithToolsDtoTypeNULL() {
        // Setup
        ToolsDto toolsDto = getToolsDto();
        toolsDto.setType(null);
        when(mockToolsInfoService.selectToolsInfoList(any(ToolsDto.class)))
                .thenReturn("checkParams tools The application system cannot be empty!");

        // Run the test
        final String result = toolsOperateBaseServiceImplUnderTest.checkParamsUpdate(toolsDto);

        // Verify the results
        assertThat(result).isEqualTo("checkParams tools Type is null!");
    }

    @Test
    void testCheckParamsUpdate_WithToolsDtoNameNULL() {
        // Setup
        ToolsDto toolsDto = getToolsDto();
        toolsDto.setName(null);
        when(mockToolsInfoService.selectToolsInfoList(any(ToolsDto.class)))
                .thenReturn("checkParams tools The application system cannot be empty!");

        // Run the test
        final String result = toolsOperateBaseServiceImplUnderTest.checkParamsUpdate(toolsDto);

        // Verify the results
        assertThat(result).isEqualTo("checkParams tools Name is null!");
    }

    @Test
    void testCheckParamsUpdate_WithToolsDtoHighRiskNULL() {
        // Setup
        ToolsDto toolsDto = getToolsDto();
        toolsDto.setBusinessSystemId(1L);
        toolsDto.setBusinessSystemName("111");
        toolsDto.setHighRisk(null);
        when(mockToolsInfoService.selectToolsInfoList(any(ToolsDto.class)))
                .thenReturn("checkParams tools The application system cannot be empty!");

        // Run the test
        final String result = toolsOperateBaseServiceImplUnderTest.checkParamsUpdate(toolsDto);

        // Verify the results
        assertThat(result).isEqualTo("checkParams tools High Risk cannot be empty!");
    }

    @Test
    void testCheckParamsUpdate_WithToolsDtoAuditEverybodyDtoListNULL() {
        // Setup
        ToolsDto toolsDto = getToolsDto();
        toolsDto.setBusinessSystemId(1L);
        toolsDto.setBusinessSystemName("111");
        toolsDto.setAuditEverybodyDtoList(null);
        when(mockToolsInfoService.selectToolsInfoList(any(ToolsDto.class)))
                .thenReturn("checkParams tools The application system cannot be empty!");

        // Run the test
        final String result = toolsOperateBaseServiceImplUnderTest.checkParamsUpdate(toolsDto);

        // Verify the results
        assertThat(result).isEqualTo("checkParams tools approval is empty!");
    }

    @Test
    void testSaveToolsBaseInfo() throws Exception {
        // Setup
        ToolsDto toolsDto = getToolsDto();

        UserDto userDto = getUserDto();

        // Run the test
        final Long result = toolsOperateBaseServiceImplUnderTest.saveToolsBaseInfo(toolsDto, userDto);

        // Verify the results
        assertThat(result).isEqualTo(0L);
        verify(mockToolsInfoMapper).insertToolsInfo(any(ToolsInfoEntity.class));
    }

    @Test
    void testUpdateToolsBaseInfo() throws Exception {
        // Setup
        ToolsDto toolsDto = getToolsDto();

        UserDto userDto = getUserDto();

        // Run the test
        final Long result = toolsOperateBaseServiceImplUnderTest.updateToolsBaseInfo(toolsDto, userDto);

        // Verify the results
        assertThat(result).isEqualTo(0L);
        verify(mockToolsInfoMapper).updateToolsInfo(any(ToolsInfoEntity.class));
    }

    @Test
    void testSaveToolsFilesBaseInfo() throws Exception {
        // Setup
        ToolsDto toolsDto = getToolsDto();

        UserDto userDto = getUserDto();

        // Run the test
        final Long result = toolsOperateBaseServiceImplUnderTest.saveToolsFilesBaseInfo(0L, toolsDto, userDto);

        ArgumentCaptor<List<ToolsFilesDto>> filesCaptor = ArgumentCaptor.forClass(List.class);
        verify(mockToolsFilesService).batchUpdateToolsFiles(eq(0L), filesCaptor.capture());
    }

    @Test
    void testSaveToolsFilesBaseInfo_IToolsFilesServiceThrowsToolsException() throws Exception {
        // Setup
        ToolsDto toolsDto = getToolsDto();

        UserDto userDto = getUserDto();

        doThrow(ToolsException.class).when(mockToolsFilesService).batchUpdateToolsFiles(anyLong(), anyList());

        // Run the test
        assertThatThrownBy(
                () -> toolsOperateBaseServiceImplUnderTest.saveToolsFilesBaseInfo(0L, toolsDto, userDto))
                .isInstanceOf(ToolsException.class);
    }

    @Test
    void testSaveToolsParamBaseInfo() throws Exception {
        // Setup
        List<ToolsParamResultDto> toolsParamResultList = getToolsParamResultDtoList();

        UserDto userDto = getUserDto();

        // Run the test
        toolsOperateBaseServiceImplUnderTest.saveToolsParamBaseInfo(0L, toolsParamResultList, userDto);

        verify(mockToolsParamService).batchInsertToolsParam(eq(0L), anyList(), any(UserDto.class));
    }

    @Test
    void testSaveToolsParamBaseInfo_IToolsParamServiceThrowsToolsException() throws Exception {
        // Setup
        List<ToolsParamResultDto> toolsParamResultList = getToolsParamResultDtoList();

        UserDto userDto = getUserDto();

        doThrow(ToolsException.class).when(mockToolsParamService).batchInsertToolsParam(eq(0L),
                anyList(), any(UserDto.class));

        // Run the test
        assertThatThrownBy(() -> toolsOperateBaseServiceImplUnderTest.saveToolsParamBaseInfo(0L, toolsParamResultList,
                userDto)).isInstanceOf(ToolsException.class);
    }

    @Test
    void testSaveToolsAgentBaseInfo() throws Exception {
        // Setup
        List<ToolsAgentResultDto> toolsParamResultList = getToolsAgentResultDtoList();

        UserDto userDto = getUserDto();

        // Run the test
        toolsOperateBaseServiceImplUnderTest.saveToolsAgentBaseInfo(0L, toolsParamResultList, userDto);

        // Verify the results

        verify(mockToolsAgentInfoService).batchInsertToolsAgent(eq(0L), anyList());
    }

    @Test
    void testSaveToolsAgentBaseInfo_IToolsAgentInfoServiceThrowsToolsException() throws Exception {
        // Setup
        List<ToolsAgentResultDto> toolsParamResultList = getToolsAgentResultDtoList();

        UserDto userDto = getUserDto();

        doThrow(ToolsException.class).when(mockToolsAgentInfoService).batchInsertToolsAgent(eq(0L),
                anyList());

        // Run the test
        assertThatThrownBy(() -> toolsOperateBaseServiceImplUnderTest.saveToolsAgentBaseInfo(0L, toolsParamResultList,
                userDto)).isInstanceOf(ToolsException.class);
    }

    @Test
    void testDeleteToolsScriptAnnexedTable() throws Exception {
        // Setup
        when(mockBusinessConfig.isToolEvnMainSwitch()).thenReturn(false);

        // Run the test
        toolsOperateBaseServiceImplUnderTest.deleteToolsScriptAnnexedTable(0L);

        // Verify the results
        verify(mockToolsParamService).deleteParamToolId(0L);
        verify(mockToolsAgentInfoService).deleteAgentToolId(0L);
    }

    @Test
    void testDeleteToolsScriptAnnexedTable_BusinessConfigReturnsTrue() throws Exception {
        // Setup
        when(mockBusinessConfig.isToolEvnMainSwitch()).thenReturn(true);

        // Run the test
        toolsOperateBaseServiceImplUnderTest.deleteToolsScriptAnnexedTable(0L);

        // Verify the results
        verify(mockToolsParamService).deleteParamToolId(0L);
    }

    @Test
    void testDeleteToolsScriptAnnexedTable_IToolsParamServiceThrowsToolsException() throws Exception {
        // Setup
        when(mockBusinessConfig.isToolEvnMainSwitch()).thenReturn(true);
        when(mockToolsParamService.deleteParamToolId(0L)).thenThrow(ToolsException.class);

        // Run the test
        assertThatThrownBy(() -> toolsOperateBaseServiceImplUnderTest.deleteToolsScriptAnnexedTable(0L))
                .isInstanceOf(ToolsException.class);
    }

    @Test
    void testDeleteToolsScriptAnnexedTable_IToolsAgentInfoServiceThrowsToolsException() throws Exception {
        // Setup
        when(mockBusinessConfig.isToolEvnMainSwitch()).thenReturn(false);
        when(mockToolsAgentInfoService.deleteAgentToolId(0L)).thenThrow(ToolsException.class);

        // Run the test
        assertThatThrownBy(() -> toolsOperateBaseServiceImplUnderTest.deleteToolsScriptAnnexedTable(0L))
                .isInstanceOf(ToolsException.class);
        verify(mockToolsParamService).deleteParamToolId(0L);
    }

    @Test
    void testDeleteToolsFilesTable() throws Exception {
        // Setup
        ToolsDto toolsDto = getToolsDto();
        toolsDto.getScriptFilesList().get(0).setId(0L);
        toolsDto.getDescribeFilesList().get(0).setId(0L);
        List<ToolsFilesDto> toolsFilesDtos = getToolsFilesDtoList();
        toolsFilesDtos.get(0).setId(1001L);
        when(mockToolsFilesService.selectToolsFilesList(any(ToolsFilesQueryDto.class))).thenReturn(toolsFilesDtos);

        // Run the test
        toolsOperateBaseServiceImplUnderTest.deleteToolsFilesTable(toolsDto);

        // Verify the results
        verify(mockToolsFilesService).deleteToolsFilesByIds(any(Long[].class));
    }

    @Test
    void testDeleteToolsFilesTable_IToolsFilesServiceSelectToolsFilesListReturnsNoItems() throws Exception {
        // Setup
        ToolsDto toolsDto = getToolsDto();
        toolsDto.getScriptFilesList().get(0).setId(0L);
        toolsDto.getDescribeFilesList().get(0).setId(0L);
        List<ToolsFilesDto> toolsFilesDtos = getToolsFilesDtoList();
        toolsFilesDtos.get(0).setId(1001L);
        when(mockToolsFilesService.selectToolsFilesList(any(ToolsFilesQueryDto.class)))
                .thenReturn(toolsFilesDtos);

        // Run the test
        toolsOperateBaseServiceImplUnderTest.deleteToolsFilesTable(toolsDto);

        // Verify the results
        verify(mockToolsFilesService).deleteToolsFilesByIds(any(Long[].class));
    }

    @Test
    void testDeleteToolsFilesTable_IToolsFilesServiceDeleteToolsFilesByIdsThrowsToolsException() throws Exception {
        // Setup
        ToolsDto toolsDto = getToolsDto();

        toolsDto.getScriptFilesList().get(0).setId(0L);
        toolsDto.getDescribeFilesList().get(0).setId(0L);
        List<ToolsFilesDto> toolsFilesDtos = getToolsFilesDtoList();
        toolsFilesDtos.get(0).setId(1001L);

        when(mockToolsFilesService.selectToolsFilesList(any(ToolsFilesQueryDto.class))).thenReturn(toolsFilesDtos);

        when(mockToolsFilesService.deleteToolsFilesByIds(any(Long[].class))).thenThrow(ToolsException.class);

        // Run the test
        assertThatThrownBy(() -> toolsOperateBaseServiceImplUnderTest.deleteToolsFilesTable(toolsDto))
                .isInstanceOf(ToolsException.class);
    }

    @Test
    void testGetToolsFilesQueryDtoList() {
        // Setup
        List<ToolsFilesQueryDto> scriptFilesList = getToolsFilesQueryDtoList();
        scriptFilesList.get(0).setId(0L);
        List<ToolsFilesQueryDto> describeFilesList = getToolsFilesQueryDtoList();
        describeFilesList.get(0).setId(0L);
        List<ToolsFilesDto> toolsFilesDtoList = getToolsFilesDtoList();
        describeFilesList.get(0).setId(1001L);
        // Run the test
        final Long[] result = toolsOperateBaseServiceImplUnderTest.getToolsFilesQueryDtoList(scriptFilesList,
                describeFilesList, toolsFilesDtoList);

        // Verify the results
        assertThat(result).isEqualTo(new Long[]{0L});
    }

    @Test
    void testVerificationScript() {
        // Setup
        UserDto userDto = getUserDto();

        // Configure IToolsInfoService.selectScriptContentInfo(...).
        ScriptContentDto scriptContentDto = getScriptContentDto();

        when(mockToolsInfoService.selectScriptContentInfo(any(UserDto.class), eq("scriptId")))
                .thenReturn(scriptContentDto);

        // Run the test
        final ScriptContentDto result = toolsOperateBaseServiceImplUnderTest.verificationScript("scriptId", userDto);

        // Verify the results
    }

    @Test
    void testUpdateToolsSatus() throws Exception {
        // Setup
        ToolsInfoEntity toolsInfo = getToolsInfoEntity();

        UserDto userDto = getUserDto();

        // Run the test
        final Long result = toolsOperateBaseServiceImplUnderTest.updateToolsSatus(toolsInfo, userDto);

        // Verify the results
        assertThat(result).isEqualTo(0L);
        verify(mockToolsInfoMapper).updateToolsInfo(any(ToolsInfoEntity.class));
    }

    @Test
    void testInsertUpdateToolsProject() throws Exception {
        // Setup
        ToolsProjectInfoDto toolsProjectInfoDto = getToolsProjectInfoDto();

        UserDto userDto = getUserDto();

        when(mockToolsProjectMapper.insertToolsProject(any())).thenAnswer(invocation -> {
            ToolsProjectEntity project = invocation.getArgument(0);
            project.setId(2001L);
            return 1;
        });

        // Run the test
        final Long result = toolsOperateBaseServiceImplUnderTest.insertUpdateToolsProject(0L, toolsProjectInfoDto,
                userDto);

        // Verify the results
        assertThat(result).isNotNull();
        verify(mockToolsProjectMapper).insertToolsProject(any(ToolsProjectEntity.class));
    }

    @Test
    void testDeleteToolsProject() throws Exception {
        // Setup
        // Run the test
        toolsOperateBaseServiceImplUnderTest.deleteToolsProject(0L);

        // Verify the results
        verify(mockToolsProjectMapper).deleteToolsProjectByToolsId(0L);
    }

    @Test
    void testCheckToolsAgentType2() {
        // Setup
        // Configure IToolsInfoService.selectToolsInfoById(...).
        ToolsInfoDto toolsInfoDto = getToolsInfoDto();
        toolsInfoDto.setChildIds(",1012168867683618816_644a8db4-486b-4cb6-a82f-de07cf8274d6");
        when(mockToolsInfoService.selectToolsInfoById(0L)).thenReturn(toolsInfoDto);

        when(mockCommonToolsAgentInfoService.selectToolsAgentCountByCommonToolsIdAndScriptToolsId(0L, 0L))
                .thenReturn(0);
        when(mockToolsAgentInfoService.selectAgentCountByToolId(0L)).thenReturn(0);

        // Run the test
        final boolean result = toolsOperateBaseServiceImplUnderTest.checkToolsAgent(0L, 2);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    void testCheckToolsAgentType2_IToolsInfoServiceReturnsNull() {
        // Setup
        when(mockToolsInfoService.selectToolsInfoById(0L)).thenReturn(null);

        // Run the test
        final boolean result = toolsOperateBaseServiceImplUnderTest.checkToolsAgent(0L, 2);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    void testCheckToolsAgentType2_IToolsAgentInfoServiceReturnsNull() {
        // Setup
        when(mockToolsAgentInfoService.selectAgentCountByToolId(0L)).thenReturn(null);

        // Run the test
        final boolean result = toolsOperateBaseServiceImplUnderTest.checkToolsAgent(0L, 2);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    void testCheckToolsAgentType3() {
        // Setup
        // Configure IToolsInfoService.selectToolsInfoById(...).
        ToolsInfoDto toolsInfoDto = getToolsInfoDto();

        when(mockToolsInfoService.selectToolsInfoById(0L)).thenReturn(toolsInfoDto);

        when(mockCommonToolsAgentInfoService.selectToolsAgentCountByCommonToolsIdAndScriptToolsId(0L, 0L))
                .thenReturn(0);
        when(mockToolsAgentInfoService.selectAgentCountByToolId(0L)).thenReturn(0);

        // Run the test
        final boolean result = toolsOperateBaseServiceImplUnderTest.checkToolsAgent(0L, 3);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    void testCheckToolsAgentType3_IToolsInfoServiceReturnsNull() {
        // Setup
        when(mockToolsInfoService.selectToolsInfoById(0L)).thenReturn(null);

        // Run the test
        final boolean result = toolsOperateBaseServiceImplUnderTest.checkToolsAgent(0L, 3);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    void testCheckToolsAgentType3_IToolsAgentInfoServiceReturnsNull() {
        // Setup
        when(mockToolsAgentInfoService.selectAgentCountByToolId(0L)).thenReturn(null);

        // Run the test
        final boolean result = toolsOperateBaseServiceImplUnderTest.checkToolsAgent(0L, 3);

        // Verify the results
        assertThat(result).isFalse();
    }

    private ToolsDto getToolsDto() {
        ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setName("sdfsd");
        toolsDto.setType(2);
        toolsDto.setHighRisk(0);
        toolsDto.setOperateStatus(1);
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        toolsDto.setOsType(0);
        toolsDto.setEstimateOperationalRisk(new Long[]{0L});
        toolsDto.setScriptType(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setEstimateOperationalRisk(new Long[]{0L});
        final ToolsFilesQueryDto toolsFilesQueryDto2 = new ToolsFilesQueryDto();
        toolsDto.setScriptFilesList(Arrays.asList(toolsFilesQueryDto2));
        final ToolsFilesQueryDto toolsFilesQueryDto3 = new ToolsFilesQueryDto();
        toolsDto.setDescribeFilesList(Arrays.asList(toolsFilesQueryDto3));
        toolsDto.setScriptEditing("scriptEditing");
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setProjectId(0L);
        toolsProjectInfoDto.setProjectVersionId(0L);
        toolsProjectInfoDto.setProjectName("sdfsd");
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowName("workflowName");
        toolsProjectInfoDto.setWorkflowContent("{\"mxGrpah\":\"<mxGraphModel dx=\\\"1190\\\" dy=\\\"369\\\" grid=\\\"1\\\" gridSize=\\\"10\\\" guides=\\\"1\\\" tooltips=\\\"1\\\" connect=\\\"1\\\" arrows=\\\"1\\\" fold=\\\"1\\\" page=\\\"1\\\" pageScale=\\\"1\\\" pageWidth=\\\"1920\\\" pageHeight=\\\"1080\\\"><root><mxCell id=\\\"0\\\"/><mxCell id=\\\"1\\\" parent=\\\"0\\\"/><mxCell id=\\\"5\\\" style=\\\"edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;\\\" edge=\\\"1\\\" parent=\\\"1\\\" source=\\\"2\\\" target=\\\"4\\\"><mxGeometry relative=\\\"1\\\" as=\\\"geometry\\\"/></mxCell><mxCell id=\\\"2\\\" value=\\\"��ʼ�\\\" style=\\\"adapter=start;shape=image;html=1;verticalLabelPosition=bottom;labelBackgroundColor=#ffffff;verticalAlign=top;imageAspect=1;aspect=fixed;image=/editor/images/adaptor/01.png\\\" vertex=\\\"1\\\" parent=\\\"1\\\"><mxGeometry x=\\\"200\\\" y=\\\"80\\\" width=\\\"42\\\" height=\\\"42\\\" as=\\\"geometry\\\"/></mxCell><mxCell id=\\\"3\\\" value=\\\"�����\\\" style=\\\"adapter=end;shape=image;html=1;verticalLabelPosition=bottom;labelBackgroundColor=#ffffff;verticalAlign=top;imageAspect=1;aspect=fixed;image=/editor/images/adaptor/02.png\\\" vertex=\\\"1\\\" parent=\\\"1\\\"><mxGeometry x=\\\"970\\\" y=\\\"220\\\" width=\\\"42\\\" height=\\\"42\\\" as=\\\"geometry\\\"/></mxCell><mxCell id=\\\"6\\\" style=\\\"edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;\\\" edge=\\\"1\\\" parent=\\\"1\\\" source=\\\"4\\\" target=\\\"3\\\"><mxGeometry relative=\\\"1\\\" as=\\\"geometry\\\"/></mxCell><mxCell id=\\\"4\\\" value=\\\"���������_0\\\" style=\\\"adapter=callflowTool;shape=image;html=1;verticalLabelPosition=bottom;labelBackgroundColor=#ffffff;verticalAlign=top;imageAspect=1;aspect=fixed;image=/editor/images/adaptor/28.png\\\" vertex=\\\"1\\\" parent=\\\"1\\\"><mxGeometry x=\\\"580\\\" y=\\\"100\\\" width=\\\"42\\\" height=\\\"42\\\" as=\\\"geometry\\\"/></mxCell></root></mxGraphModel>\",\"activitiNodeData\":{\"2\":{\"id\":\"2\",\"name\":\"��ʼ�\",\"actId\":\"��ʼ�\",\"timeout\":0,\"actType\":\"start\",\"des\":\"\",\"exceptionHandle\":\"AM\",\"succeedingBranchCondition\":[],\"succeedingActs\":[\"4\"],\"postConditions\":[],\"inputExprs\":{},\"levelOfPri\":\"5\",\"levelOfWeight\":\"1\",\"actElementType\":\"activityElementVo\",\"defaultConfig\":\"\",\"remark\":\"\",\"activityExeTimeConfigDto\":null,\"retryTime\":0,\"retryInterval\":0,\"adaptorName\":\"start\",\"remoteExec\":false,\"noRemoteExec\":false,\"remoteGroupExec\":false,\"remoteAgent\":null,\"remoteAgentGroup\":\"\",\"remoteHostName\":\"\",\"remoteHostNameBak\":null,\"remotePort\":0,\"remotePortBak\":0,\"remoteSwitch\":false,\"connectType\":0,\"isInputRemote\":false,\"isGroup\":false,\"isRemoteGroup\":false},\"3\":{\"id\":\"3\",\"name\":\"�����\",\"timeout\":0,\"des\":\"\",\"actType\":\"end\",\"actId\":\"�����\",\"exceptionHandle\":\"AM\",\"succeedingBranchCondition\":[],\"succeedingActs\":[],\"preAct\":[\"4\"],\"postConditions\":[],\"inputExprs\":{},\"levelOfPri\":\"5\",\"levelOfWeight\":\"1\",\"actElementType\":\"endActElementVo\",\"defaultConfig\":\"\",\"remark\":\"\",\"activityExeTimeConfigDto\":null,\"retryTime\":0,\"retryInterval\":0,\"adaptorName\":\"end\",\"remoteExec\":false,\"noRemoteExec\":false,\"remoteGroupExec\":false,\"remoteAgent\":null,\"remoteAgentGroup\":\"\",\"remoteHostName\":\"\",\"remoteHostNameBak\":null,\"remotePort\":0,\"remotePortBak\":0,\"remoteSwitch\":false,\"connectType\":0,\"isInputRemote\":false,\"isGroup\":false,\"isRemoteGroup\":false},\"4\":{\"id\":\"4\",\"timeout\":0,\"des\":\"\",\"name\":\"���������_0\",\"actId\":\"���������_0\",\"exceptionHandle\":\"AM\",\"succeedingBranchCondition\":[{\"3\":\"\"}],\"succeedingActs\":[\"3\"],\"preAct\":[\"2\"],\"postConditions\":[],\"inputExprs\":{},\"levelOfPri\":\"5\",\"levelOfWeight\":\"1\",\"actElementType\":\"activityElementVo\",\"defaultConfig\":\"{\\\"itoolName\\\":\\\"jiangyujian_202411011122\\\",\\\"itoolCode\\\":\\\"TOOLS_1730431473254\\\",\\\"isystem\\\":\\\"�����ʾ(��)\\\",\\\"nOsType\\\":\\\"Linux\\\",\\\"ionetype\\\":\\\"һ������\\\",\\\"itwotype\\\":\\\"2\\\",\\\"execuser\\\":\\\"root\\\",\\\"itoolType\\\":\\\"�ű�����\\\",\\\"scriptNameText\\\":\\\"jiangyujian_202411011122\\\",\\\"itoolAuditorName\\\":\\\"����,dhjtest,sxx,wyTest,��ΰ��,lhy,liushuai\\\",\\\"itoolId\\\":\\\"1012168867683618816\\\",\\\"isystemId\\\":\\\"943030362109706240\\\",\\\"uuid\\\":\\\"644a8db4-486b-4cb6-a82f-de07cf8274d6\\\",\\\"project\\\":\\\"ExecActModelTool\\\",\\\"flow\\\":\\\"sciptServiceMain\\\",\\\"bussCbText\\\":\\\"\\\",\\\"bussTypeCbText\\\":\\\"\\\",\\\"scriptTypeText\\\":\\\"\\\",\\\"taskName\\\":\\\"\\\",\\\"userName\\\":\\\"\\\",\\\"agentIpandPort\\\":\\\"*********:15000\\\",\\\"scriptParams\\\":[\\\"1\\\"],\\\"ids\\\":[{\\\"ip\\\":\\\"*********\\\",\\\"port\\\":15000}],\\\"allParams\\\":[{\\\"sort\\\":1,\\\"name\\\":\\\"1\\\",\\\"type\\\":\\\"1\\\",\\\"value\\\":\\\"1\\\",\\\"description\\\":\\\"1\\\"}],\\\"serviceIp\\\":\\\"\\\",\\\"scriptId\\\":\\\"\\\",\\\"port\\\":\\\"\\\",\\\"eachNum\\\":\\\"\\\",\\\"isOrder\\\":\\\"\\\"}\",\"remark\":\"\",\"activityExeTimeConfigDto\":{\"relFlag\":0,\"year\":-1,\"month\":-1,\"day\":-1,\"hour\":-1,\"minute\":-1,\"actId\":4,\"actState\":null,\"validateTimes\":[]},\"retryTime\":0,\"retryInterval\":0,\"adaptorName\":\"scriptservice\",\"actType\":\"callflowTool\",\"actDefDto\":{\"version\":\"1.0.0\",\"iConfigClassName\":\"\",\"adaptorName\":\"scriptservice\",\"className\":\"com.ideal.ieai.scriptserviceadaptor.scriptcall.ScriptCallAct\",\"configViewClassName\":\"com.ideal.ieai.scriptserviceadaptor.scriptcall.ScriptCallActView\",\"name\":\"ScriptCall\",\"iconPath\":\"icon6.gif\",\"tip\":\"\",\"desc\":\"\"},\"remoteExec\":false,\"noRemoteExec\":false,\"remoteGroupExec\":false,\"remoteAgent\":null,\"remoteAgentGroup\":null,\"remoteHostName\":null,\"remoteHostNameBak\":null,\"remotePort\":0,\"remotePortBak\":0,\"remoteSwitch\":false,\"connectType\":0,\"isInputRemote\":false,\"isGroup\":false,\"isRemoteGroup\":false}},\"activitiRelationData\":[{\"id\":\"5\",\"source\":\"2\",\"target\":\"4\"},{\"id\":\"6\",\"source\":\"4\",\"target\":\"3\"}],\"workflow\":{\"0\":{\"id\":0,\"priority\":3,\"projectFlag\":1,\"upperId\":0,\"lastId\":0,\"name\":\"toolbox\",\"riskLevel\":1,\"selectDeviceEnable\":0,\"useCalendar\":0,\"calendarName\":\"\",\"inputParams\":\"[]\",\"outputParams\":\"[]\",\"activities\":{\"2\":\"��ʼ�\",\"3\":\"�����\",\"4\":\"���������_0\"}}}}");
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setDeliveryStatus(0);
        return toolsDto;
    }

    private UserDto getUserDto() {
        UserDto userDto = new UserDto();
        userDto.setUserName("creatorName");
        userDto.setUserId(0L);
        userDto.setUserCode("userCode");
        userDto.setLoginName("loginName");
        return userDto;
    }

    private List<ToolsFilesDto> getToolsFilesDtoList() {
        ToolsFilesDto toolsFilesDto = new ToolsFilesDto();
        toolsFilesDto.setId(0L);
        toolsFilesDto.setTdToolsId(0L);
        toolsFilesDto.setName("name");
        toolsFilesDto.setFiles("content".getBytes());
        toolsFilesDto.setCreatorName("creatorName");
        List<ToolsFilesDto> toolsFilesList = new ArrayList<>();
        toolsFilesList.add(toolsFilesDto);
        return toolsFilesList;
    }

    private List<ToolsParamResultDto> getToolsParamResultDtoList() {
        ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        toolsParamResultDto.setSort(0L);
        toolsParamResultDto.setName("name");
        toolsParamResultDto.setType("type");
        toolsParamResultDto.setValue("value");
        toolsParamResultDto.setDescription("description");
        List<ToolsParamResultDto> toolsParamResultList = new ArrayList<>();
        toolsParamResultList.add(toolsParamResultDto);
        return toolsParamResultList;
    }

    private List<ToolsParamDto> getToolsParamDtoList() {
        ToolsParamDto toolsParamDto = new ToolsParamDto();
        toolsParamDto.setId(0L);
        toolsParamDto.setSort(0L);
        toolsParamDto.setName("name");
        toolsParamDto.setType("type");
        toolsParamDto.setValue("value");
        List<ToolsParamDto> toolsParamDtoList = new ArrayList<>();
        toolsParamDtoList.add(toolsParamDto);
        return toolsParamDtoList;
    }

    private List<ToolsAgentResultDto> getToolsAgentResultDtoList() {
        ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsAgentResultDto.setDeviceName("deviceName");
        toolsAgentResultDto.setOsName("osName");
        toolsAgentResultDto.setAgentName("agentName");
        toolsAgentResultDto.setAgentIp("agentIp");
        toolsAgentResultDto.setAgentPort(0);
        List<ToolsAgentResultDto> toolsParamResultList = new ArrayList<>();
        toolsParamResultList.add(toolsAgentResultDto);
        return toolsParamResultList;
    }

    private List<ToolsAgentInfoDto> getToolsAgentInfoDtoList() {
        ToolsAgentInfoDto toolsAgentInfoDto = new ToolsAgentInfoDto();
        toolsAgentInfoDto.setId(0L);
        toolsAgentInfoDto.setDeviceName("deviceName");
        toolsAgentInfoDto.setOsName("osName");
        toolsAgentInfoDto.setAgentName("agentName");
        toolsAgentInfoDto.setAgentIp("agentIp");
        List<ToolsAgentInfoDto> toolsAgentInfoList = new ArrayList<>();
        toolsAgentInfoList.add(toolsAgentInfoDto);
        return toolsAgentInfoList;
    }

    private List<ToolsFilesQueryDto> getToolsFilesQueryDtoList(){
        ToolsFilesQueryDto toolsFilesQueryDto = new ToolsFilesQueryDto();
        toolsFilesQueryDto.setId(0L);
        toolsFilesQueryDto.setTdToolsId(0L);
        toolsFilesQueryDto.setName("name");
        toolsFilesQueryDto.setFiles("content".getBytes());
        toolsFilesQueryDto.setCreatorName("creatorName");
        List<ToolsFilesQueryDto> scriptFilesList = new ArrayList<>();
        scriptFilesList.add(toolsFilesQueryDto);
        return scriptFilesList;
    }

    private ScriptContentDto getScriptContentDto() {
        ScriptContentDto scriptContentDto = new ScriptContentDto();
        scriptContentDto.setScriptIds("scriptIds");
        scriptContentDto.setScriptName("scriptName");
        scriptContentDto.setScriptType(0);
        scriptContentDto.setScriptEditing("scriptEditing");
        ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        List<ToolsParamResultDto> toolsParamResultDtoList = new ArrayList<>();
        toolsParamResultDtoList.add(toolsParamResultDto);
        scriptContentDto.setToolsParamResultList(toolsParamResultDtoList);
        return scriptContentDto;
    }

    private ToolsInfoEntity getToolsInfoEntity() {
        ToolsInfoEntity toolsInfo = new ToolsInfoEntity();
        toolsInfo.setId(0L);
        toolsInfo.setCode("code");
        toolsInfo.setEstimateOperationalRisk("estimateOperationalRisk");
        toolsInfo.setStatus(0);
        toolsInfo.setDeliveryStatus(0);
        toolsInfo.setUpdatorId(0L);
        toolsInfo.setUpdatorName("creatorName");
        toolsInfo.setCreatorId(0L);
        toolsInfo.setCreatorName("creatorName");
        return toolsInfo;
    }

    private ToolsProjectInfoDto getToolsProjectInfoDto() {
        ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setId(0L);
        toolsProjectInfoDto.setProjectId(0L);
        toolsProjectInfoDto.setProjectVersionId(0L);
        toolsProjectInfoDto.setProjectName("��Ԫ��������");
        toolsProjectInfoDto.setWorkflowId(0L);
        return toolsProjectInfoDto;
    }

    private ToolsInfoDto getToolsInfoDto() {
        ToolsInfoDto toolsInfoDto = new ToolsInfoDto();
        toolsInfoDto.setId(0L);
        toolsInfoDto.setCode("code");
        toolsInfoDto.setName("name");
        toolsInfoDto.setType(3);
        toolsInfoDto.setChildIds("childIds");
        return toolsInfoDto;
    }
}

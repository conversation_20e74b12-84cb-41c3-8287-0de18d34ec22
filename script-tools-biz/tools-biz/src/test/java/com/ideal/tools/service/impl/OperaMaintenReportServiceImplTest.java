package com.ideal.tools.service.impl;

import com.github.pagehelper.PageInfo;
import com.ideal.common.util.PageDataUtil;
import com.ideal.tools.mapper.OperaMaintenReportMapper;
import com.ideal.tools.model.bean.OperaMaintenReportExcle;
import com.ideal.tools.model.dto.OperaAndMaintenResultDto;
import com.ideal.tools.model.dto.OperaMaintenReportQueryDto;
import com.ideal.tools.model.dto.SystemPullDto;
import com.ideal.tools.model.dto.UserDto;
import com.ideal.tools.service.IToolsInfoService;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class OperaMaintenReportServiceImplTest {

    @Mock
    private OperaMaintenReportMapper mockOperaMaintenReportMapper;
    @Mock
    private IToolsInfoService mockToolsInfoService;

    private OperaMaintenReportServiceImpl operaMaintenReportServiceImplUnderTest;

    @BeforeEach
    void setUp() throws Exception {
        operaMaintenReportServiceImplUnderTest = new OperaMaintenReportServiceImpl(mockOperaMaintenReportMapper,
                mockToolsInfoService);
    }
    @Mock
    private MockedStatic<PageDataUtil> mockedStaticPageDataUtil;

    @AfterEach
    void tearDown() {
        mockedStaticPageDataUtil.close();
    }

    @Test
    void testSelectOperaMaintenReportList() {
        // Setup
        final OperaMaintenReportQueryDto query = new OperaMaintenReportQueryDto();
        query.setBusinessSystemId("businessSystemId");
        query.setMonth(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setPreviousMonth(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        final UserDto user = new UserDto();
        user.setUserName("userName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        // Configure OperaMaintenReportMapper.selectOperaMaintenReportList(...).
        final OperaAndMaintenResultDto operaAndMaintenResultDto = new OperaAndMaintenResultDto();
        operaAndMaintenResultDto.setAlarmBigClass("alarmBigClass");
        operaAndMaintenResultDto.setBusinessSystemCode("businessSystemCode");
        operaAndMaintenResultDto.setBusinessSystemId("businessSystemId");
        operaAndMaintenResultDto.setBusinessSystemName("businessSystemName");
        operaAndMaintenResultDto.setImplRate("implRate");
        final List<OperaAndMaintenResultDto> operaAndMaintenResultDtos = Arrays.asList(operaAndMaintenResultDto);
        when(mockOperaMaintenReportMapper.selectOperaMaintenReportList(
                any(OperaMaintenReportQueryDto.class))).thenReturn(operaAndMaintenResultDtos);

        // Configure IToolsInfoService.selectToolsInfoCategoryList(...).
        final SystemPullDto systemPullDto = new SystemPullDto();
        systemPullDto.setBusinessSystemId(0L);
        systemPullDto.setBusinessSystemCode("businessSystemCode");
        systemPullDto.setBusinessSystemName("businessSystemName");
        final List<SystemPullDto> systemPullDtos = Arrays.asList(systemPullDto);
        when(mockToolsInfoService.selectToolsInfoCategoryList(any(UserDto.class))).thenReturn(systemPullDtos);

        // Run the test
        final PageInfo<OperaAndMaintenResultDto> result = operaMaintenReportServiceImplUnderTest.selectOperaMaintenReportList(
                query, 0, 0, user);

        // Verify the results
    }

    @Test
    void testSelectOperaMaintenReportList_OperaMaintenReportMapperReturnsNoItems() {
        // Setup
        final OperaMaintenReportQueryDto query = new OperaMaintenReportQueryDto();
        query.setBusinessSystemId("businessSystemId");
        query.setMonth(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setPreviousMonth(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        final UserDto user = new UserDto();
        user.setUserName("userName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        when(mockOperaMaintenReportMapper.selectOperaMaintenReportList(
                any(OperaMaintenReportQueryDto.class))).thenReturn(Collections.emptyList());

        // Configure IToolsInfoService.selectToolsInfoCategoryList(...).
        final SystemPullDto systemPullDto = new SystemPullDto();
        systemPullDto.setBusinessSystemId(0L);
        systemPullDto.setBusinessSystemCode("businessSystemCode");
        systemPullDto.setBusinessSystemName("businessSystemName");
        final List<SystemPullDto> systemPullDtos = Arrays.asList(systemPullDto);
        when(mockToolsInfoService.selectToolsInfoCategoryList(any(UserDto.class))).thenReturn(systemPullDtos);

        // Run the test
        final PageInfo<OperaAndMaintenResultDto> result = operaMaintenReportServiceImplUnderTest.selectOperaMaintenReportList(
                query, 0, 0, user);

        // Verify the results
    }

    @Test
    void testSelectOperaMaintenReportList_IToolsInfoServiceReturnsNoItems() {
        // Setup
        final OperaMaintenReportQueryDto query = new OperaMaintenReportQueryDto();
        query.setBusinessSystemId("businessSystemId");
        query.setMonth(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        query.setPreviousMonth(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        final UserDto user = new UserDto();
        user.setUserName("userName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        // Configure OperaMaintenReportMapper.selectOperaMaintenReportList(...).
        final OperaAndMaintenResultDto operaAndMaintenResultDto = new OperaAndMaintenResultDto();
        operaAndMaintenResultDto.setAlarmBigClass("alarmBigClass");
        operaAndMaintenResultDto.setBusinessSystemCode("businessSystemCode");
        operaAndMaintenResultDto.setBusinessSystemId("businessSystemId");
        operaAndMaintenResultDto.setBusinessSystemName("businessSystemName");
        operaAndMaintenResultDto.setImplRate("implRate");
        final List<OperaAndMaintenResultDto> operaAndMaintenResultDtos = Arrays.asList(operaAndMaintenResultDto);
        when(mockOperaMaintenReportMapper.selectOperaMaintenReportList(
                any(OperaMaintenReportQueryDto.class))).thenReturn(operaAndMaintenResultDtos);

        when(mockToolsInfoService.selectToolsInfoCategoryList(any(UserDto.class))).thenReturn(Collections.emptyList());

        // Run the test
        final PageInfo<OperaAndMaintenResultDto> result = operaMaintenReportServiceImplUnderTest.selectOperaMaintenReportList(
                query, 0, 0, user);

        // Verify the results
    }

    @Test
    void testExportOperaMaintenReportData() {
        // Setup
        final OperaMaintenReportQueryDto queryDto = new OperaMaintenReportQueryDto();
        queryDto.setBusinessSystemId("businessSystemId");
        queryDto.setMonth(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        queryDto.setPreviousMonth(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        final UserDto user = new UserDto();
        user.setUserName("userName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        // Configure OperaMaintenReportMapper.selectOperaMaintenReportList(...).
        final OperaAndMaintenResultDto operaAndMaintenResultDto = new OperaAndMaintenResultDto();
        operaAndMaintenResultDto.setAlarmBigClass("alarmBigClass");
        operaAndMaintenResultDto.setBusinessSystemCode("businessSystemCode");
        operaAndMaintenResultDto.setBusinessSystemId("businessSystemId");
        operaAndMaintenResultDto.setBusinessSystemName("businessSystemName");
        operaAndMaintenResultDto.setImplRate("implRate");
        final List<OperaAndMaintenResultDto> operaAndMaintenResultDtos = Arrays.asList(operaAndMaintenResultDto);
        when(mockOperaMaintenReportMapper.selectOperaMaintenReportList(
                any(OperaMaintenReportQueryDto.class))).thenReturn(operaAndMaintenResultDtos);

        // Configure IToolsInfoService.selectToolsInfoCategoryList(...).
        final SystemPullDto systemPullDto = new SystemPullDto();
        systemPullDto.setBusinessSystemId(0L);
        systemPullDto.setBusinessSystemCode("businessSystemCode");
        systemPullDto.setBusinessSystemName("businessSystemName");
        final List<SystemPullDto> systemPullDtos = Arrays.asList(systemPullDto);
        when(mockToolsInfoService.selectToolsInfoCategoryList(any(UserDto.class))).thenReturn(systemPullDtos);

        // Run the test
        final List<OperaMaintenReportExcle> result = operaMaintenReportServiceImplUnderTest.exportOperaMaintenReportData(
                queryDto, user);

        // Verify the results
    }

    @Test
    void testExportOperaMaintenReportData_OperaMaintenReportMapperReturnsNoItems() {
        // Setup
        final OperaMaintenReportQueryDto queryDto = new OperaMaintenReportQueryDto();
        queryDto.setBusinessSystemId("businessSystemId");
        queryDto.setMonth(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        queryDto.setPreviousMonth(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        final UserDto user = new UserDto();
        user.setUserName("userName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        when(mockOperaMaintenReportMapper.selectOperaMaintenReportList(
                any(OperaMaintenReportQueryDto.class))).thenReturn(Collections.emptyList());

        // Configure IToolsInfoService.selectToolsInfoCategoryList(...).
        final SystemPullDto systemPullDto = new SystemPullDto();
        systemPullDto.setBusinessSystemId(0L);
        systemPullDto.setBusinessSystemCode("businessSystemCode");
        systemPullDto.setBusinessSystemName("businessSystemName");
        final List<SystemPullDto> systemPullDtos = Arrays.asList(systemPullDto);
        when(mockToolsInfoService.selectToolsInfoCategoryList(any(UserDto.class))).thenReturn(systemPullDtos);

        // Run the test
        final List<OperaMaintenReportExcle> result = operaMaintenReportServiceImplUnderTest.exportOperaMaintenReportData(
                queryDto, user);

        // Verify the results
    }

    @Test
    void testExportOperaMaintenReportData_IToolsInfoServiceReturnsNoItems() {
        // Setup
        final OperaMaintenReportQueryDto queryDto = new OperaMaintenReportQueryDto();
        queryDto.setBusinessSystemId("businessSystemId");
        queryDto.setMonth(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        queryDto.setPreviousMonth(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        final UserDto user = new UserDto();
        user.setUserName("userName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        // Configure OperaMaintenReportMapper.selectOperaMaintenReportList(...).
        final OperaAndMaintenResultDto operaAndMaintenResultDto = new OperaAndMaintenResultDto();
        operaAndMaintenResultDto.setAlarmBigClass("alarmBigClass");
        operaAndMaintenResultDto.setBusinessSystemCode("businessSystemCode");
        operaAndMaintenResultDto.setBusinessSystemId("businessSystemId");
        operaAndMaintenResultDto.setBusinessSystemName("businessSystemName");
        operaAndMaintenResultDto.setImplRate("implRate");
        final List<OperaAndMaintenResultDto> operaAndMaintenResultDtos = Arrays.asList(operaAndMaintenResultDto);
        when(mockOperaMaintenReportMapper.selectOperaMaintenReportList(
                any(OperaMaintenReportQueryDto.class))).thenReturn(operaAndMaintenResultDtos);

        when(mockToolsInfoService.selectToolsInfoCategoryList(any(UserDto.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<OperaMaintenReportExcle> result = operaMaintenReportServiceImplUnderTest.exportOperaMaintenReportData(
                queryDto, user);

        // Verify the results
    }
}

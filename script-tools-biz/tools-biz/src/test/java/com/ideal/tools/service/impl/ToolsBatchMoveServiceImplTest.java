package com.ideal.tools.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.ideal.common.dto.R;
import com.ideal.script.exception.ScriptException;
import com.ideal.tools.common.SFTPUtil;
import com.ideal.tools.config.BusinessConfig;
import com.ideal.tools.exception.StudioException;
import com.ideal.tools.exception.ToolsException;
import com.ideal.tools.ftp.FtpListBean;
import com.ideal.tools.mapper.*;
import com.ideal.tools.model.dto.*;
import com.ideal.tools.model.entity.*;
import com.ideal.tools.model.interaction.*;
import com.ideal.tools.service.ITbCategoryService;
import com.ideal.tools.service.IToolsInfoService;
import com.ideal.tools.service.interaction.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.SetOperations;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.mock.web.MockHttpServletResponse;

import java.io.File;
import java.io.IOException;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ToolsBatchMoveServiceImplTest {

    @Mock
    private IToolsInfoService mockToolsInfoService;
    @Mock
    private BusinessConfig mockBusinessConfig;
    @Mock
    private ScriptInteract mockScriptInteract;
    @Mock
    private StudioInteract mockStudioInteract;
    @Mock
    private ToolsInfoMapper mockToolsInfoMapper;
    @Mock
    private RedisTemplate<String, String> mockRedisTemplate;
    @Mock
    private SystemDataInteract mockSystemDataInteract;
    @Mock
    private ITbCategoryService mockCategoryService;
    @Mock
    private MqInteract mockMqInteract;
    @Mock
    private ToolsParamMapper mockToolsParamMapper;
    @Mock
    private ToolsProjectMapper mockToolsProjectMapper;
    @Mock
    private DevelopmentReportMapper mockDevelopmentReportMapper;
    @Mock
    private ToolsFilesMapper mockToolsFilesMapper;
    @Mock
    private SystemComputerInteract mockSystemComputerInteract;
    @Mock
    private ToolsAgentInfoMapper mockToolsAgentInfoMapper;
    @Mock
    private AuditMapper mockauditMapper;
    @Mock
    private ValueOperations valueOperations;
    @Mock
    private SFTPUtil sftputil;
    private ToolsBatchMoveServiceImpl toolsBatchMoveServiceImplUnderTest;

    @BeforeEach
    void setUp() throws Exception {
        toolsBatchMoveServiceImplUnderTest = new ToolsBatchMoveServiceImpl(mockToolsInfoService, mockBusinessConfig,
                mockScriptInteract, mockStudioInteract, mockToolsInfoMapper, mockRedisTemplate, mockSystemDataInteract,
                mockCategoryService, mockMqInteract, mockToolsParamMapper, mockToolsProjectMapper,
                mockDevelopmentReportMapper, mockToolsFilesMapper, mockSystemComputerInteract,
                mockToolsAgentInfoMapper,mockauditMapper);
        toolsBatchMoveServiceImplUnderTest.setSftp(sftputil);
    }
    @Test
    void testPublishBatchExportToolsType3() throws Exception {
        // Setup
        final MockHttpServletResponse response = new MockHttpServletResponse();
        when(mockBusinessConfig.getToolsEvn()).thenReturn("生产环境");
        when(mockBusinessConfig.getToolsbatchPath()).thenReturn("result");
        when(mockBusinessConfig.getToolsbatchErrorPath()).thenReturn("result");
        when(mockBusinessConfig.getFtpType()).thenReturn("sftp");
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Configure IToolsInfoService.getToolsCombinedInfo(...).
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setCode("code");
        toolsDto.setName("name");
        toolsDto.setType(3);
        toolsDto.setBusinessSystemId(0L);
        toolsDto.setBusinessSystemName("businessSystemName");
        toolsDto.setOneTypeId(0L);
        toolsDto.setTwoTypeId(0L);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setOperateStatus(0);
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        toolsDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsAgentResultDto.setAgentName("agentName");
        toolsAgentResultDto.setAgentIp("agentIp");
        toolsAgentResultDto.setAgentPort(0);
        toolsAgentResultDto.setSysAgentInfoId(0L);
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        final ToolsFilesQueryDto toolsFilesQueryDto = new ToolsFilesQueryDto();
        toolsDto.setDescribeFilesList(Arrays.asList(toolsFilesQueryDto));
        toolsDto.setStatus(0);
        toolsDto.setOneTypeName("OneTypeName");
        toolsDto.setTwoTypeName("OneTypeName");
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setProjectId(0L);
        toolsProjectInfoDto.setProjectVersionId(0L);
        toolsProjectInfoDto.setProjectName("projectName");
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowName("workflowName");
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto.setCreatorId(0L);
        toolsDto.setCreatorName("creatorName");
        toolsDto.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto.setDesc("desc");
        when(mockToolsInfoService.getToolsCombinedInfo(0L)).thenReturn(toolsDto);

        when(mockBusinessConfig.getFtpType()).thenReturn("sftp");
        when(mockBusinessConfig.getFtpUser()).thenReturn("ftpusr");
        when(mockBusinessConfig.getFtpPw()).thenReturn("ideal");
        when(mockBusinessConfig.getFtpIp()).thenReturn("*************");
        when(mockBusinessConfig.getFtpPort()).thenReturn("22");

        when(mockRedisTemplate.opsForValue()).thenReturn(valueOperations);
        ScriptFileInfo info = new ScriptFileInfo();
        info.setFileContentByte("fileContentByte".getBytes());
        when(mockScriptInteract.exportScriptProduction(any())).thenReturn(info);
        // Configure ScriptInteract.exportScriptProduction(...).
        final ScriptFileInfo scriptFileInfo = new ScriptFileInfo();
        scriptFileInfo.setFileName("fileName");
        scriptFileInfo.setFileSuffix("fileSuffix");
        scriptFileInfo.setFileContentByte("content".getBytes());

        // Configure StudioInteract.exportStudioData(...).
        final StudioFileProjectInteractDto studioFileProjectInteractDto = new StudioFileProjectInteractDto("fileName",
                "fileSuffix", "content".getBytes());


        // Configure IToolsInfoService.getToolsBatchCombinedInfo(...).
        final ToolsDto toolsDto1 = new ToolsDto();
        toolsDto1.setId(0L);
        toolsDto1.setCode("code");
        toolsDto1.setName("name");
        toolsDto1.setType(3);
        toolsDto1.setBusinessSystemId(0L);
        toolsDto1.setBusinessSystemName("businessSystemName");
        toolsDto1.setOneTypeId(0L);
        toolsDto1.setTwoTypeId(0L);
        final AuditEverybodyQueryDto auditEverybodyQueryDto1 = new AuditEverybodyQueryDto();
        toolsDto1.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto1));
        toolsDto1.setOperateStatus(0);
        toolsDto1.setScriptName("scriptName");
        toolsDto1.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto1 = new ToolsParamResultDto();
        toolsDto1.setToolsParamResultList(Arrays.asList(toolsParamResultDto1));
        final ToolsAgentResultDto toolsAgentResultDto1 = new ToolsAgentResultDto();
        toolsAgentResultDto1.setAgentName("agentName");
        toolsAgentResultDto1.setAgentIp("agentIp");
        toolsAgentResultDto1.setAgentPort(0);
        toolsAgentResultDto1.setSysAgentInfoId(0L);
        toolsDto1.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto1));
        final ToolsFilesQueryDto toolsFilesQueryDto1 = new ToolsFilesQueryDto();
        toolsDto1.setDescribeFilesList(Arrays.asList(toolsFilesQueryDto1));
        toolsDto1.setStatus(0);
        toolsDto1.setOneTypeName("OneTypeName");
        toolsDto1.setTwoTypeName("OneTypeName");
        final ToolsProjectInfoDto toolsProjectInfoDto1 = new ToolsProjectInfoDto();
        toolsProjectInfoDto1.setProjectId(0L);
        toolsProjectInfoDto1.setProjectVersionId(0L);
        toolsProjectInfoDto1.setProjectName("projectName");
        toolsProjectInfoDto1.setWorkflowId(0L);
        toolsProjectInfoDto1.setWorkflowName("workflowName");
        toolsDto1.setToolsProjectInfoDto(toolsProjectInfoDto1);
        toolsDto1.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto1.setCreatorId(0L);
        toolsDto1.setCreatorName("creatorName");
        toolsDto1.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto1.setDesc("desc");
        final List<ToolsDto> errorScriptList = Arrays.asList(toolsDto1);
        final ToolsDto toolsDto2 = new ToolsDto();
        toolsDto2.setId(0L);
        toolsDto2.setCode("code");
        toolsDto2.setName("name");
        toolsDto2.setType(3);
        toolsDto2.setBusinessSystemId(0L);
        toolsDto2.setBusinessSystemName("businessSystemName");
        toolsDto2.setOneTypeId(0L);
        toolsDto2.setTwoTypeId(0L);
        final AuditEverybodyQueryDto auditEverybodyQueryDto2 = new AuditEverybodyQueryDto();
        toolsDto2.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto2));
        toolsDto2.setOperateStatus(0);
        toolsDto2.setScriptName("scriptName");
        toolsDto2.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto2 = new ToolsParamResultDto();
        toolsDto2.setToolsParamResultList(Arrays.asList(toolsParamResultDto2));
        final ToolsAgentResultDto toolsAgentResultDto2 = new ToolsAgentResultDto();
        toolsAgentResultDto2.setAgentName("agentName");
        toolsAgentResultDto2.setAgentIp("agentIp");
        toolsAgentResultDto2.setAgentPort(0);
        toolsAgentResultDto2.setSysAgentInfoId(0L);
        toolsDto2.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto2));
        final ToolsFilesQueryDto toolsFilesQueryDto2 = new ToolsFilesQueryDto();
        toolsDto2.setDescribeFilesList(Arrays.asList(toolsFilesQueryDto2));
        toolsDto2.setStatus(0);
        toolsDto2.setOneTypeName("OneTypeName");
        toolsDto2.setTwoTypeName("OneTypeName");
        final ToolsProjectInfoDto toolsProjectInfoDto2 = new ToolsProjectInfoDto();
        toolsProjectInfoDto2.setProjectId(0L);
        toolsProjectInfoDto2.setProjectVersionId(0L);
        toolsProjectInfoDto2.setProjectName("projectName");
        toolsProjectInfoDto2.setWorkflowId(0L);
        toolsProjectInfoDto2.setWorkflowName("workflowName");
        toolsDto2.setToolsProjectInfoDto(toolsProjectInfoDto2);
        toolsDto2.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto2.setCreatorId(0L);
        toolsDto2.setCreatorName("creatorName");
        toolsDto2.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto2.setDesc("desc");
        final List<ToolsDto> errorComList = Arrays.asList(toolsDto2);
        final ToolsDto toolsDto3 = new ToolsDto();
        toolsDto3.setId(0L);
        toolsDto3.setCode("code");
        toolsDto3.setName("name");
        toolsDto3.setType(3);
        toolsDto3.setBusinessSystemId(0L);
        toolsDto3.setBusinessSystemName("businessSystemName");
        toolsDto3.setOneTypeId(0L);
        toolsDto3.setTwoTypeId(0L);
        final AuditEverybodyQueryDto auditEverybodyQueryDto3 = new AuditEverybodyQueryDto();
        toolsDto3.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto3));
        toolsDto3.setOperateStatus(0);
        toolsDto3.setScriptName("scriptName");
        toolsDto3.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto3 = new ToolsParamResultDto();
        toolsDto3.setToolsParamResultList(Arrays.asList(toolsParamResultDto3));
        final ToolsAgentResultDto toolsAgentResultDto3 = new ToolsAgentResultDto();
        toolsAgentResultDto3.setAgentName("agentName");
        toolsAgentResultDto3.setAgentIp("agentIp");
        toolsAgentResultDto3.setAgentPort(0);
        toolsAgentResultDto3.setSysAgentInfoId(0L);
        toolsDto3.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto3));
        final ToolsFilesQueryDto toolsFilesQueryDto3 = new ToolsFilesQueryDto();
        toolsDto3.setDescribeFilesList(Arrays.asList(toolsFilesQueryDto3));
        toolsDto3.setStatus(0);
        toolsDto3.setOneTypeName("OneTypeName");
        toolsDto3.setTwoTypeName("OneTypeName");
        final ToolsProjectInfoDto toolsProjectInfoDto3 = new ToolsProjectInfoDto();
        toolsProjectInfoDto3.setProjectId(0L);
        toolsProjectInfoDto3.setProjectVersionId(0L);
        toolsProjectInfoDto3.setProjectName("projectName");
        toolsProjectInfoDto3.setWorkflowId(0L);
        toolsProjectInfoDto3.setWorkflowName("workflowName");
        toolsDto3.setToolsProjectInfoDto(toolsProjectInfoDto3);
        toolsDto3.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto3.setCreatorId(0L);
        toolsDto3.setCreatorName("creatorName");
        toolsDto3.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto3.setDesc("desc");
        final List<ToolsDto> errorDescList = Arrays.asList(toolsDto3);
        // Run the test
        final R<Object> result = toolsBatchMoveServiceImplUnderTest.publishBatchExportTools(response, new Long[]{0L},
                0);

        // Verify the results
        verify(mockRedisTemplate).delete("exOrInporting");
    }
    @Test
    void testPublishBatchExportToolssftpType3() throws Exception {
        // Setup
        final MockHttpServletResponse response = new MockHttpServletResponse();
        when(mockBusinessConfig.getToolsEvn()).thenReturn("DEV环境");
        when(mockBusinessConfig.getToolsbatchPath()).thenReturn("result");
        when(mockBusinessConfig.getToolsbatchErrorPath()).thenReturn("result");
        when(mockBusinessConfig.getFtpType()).thenReturn("sftp");
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Configure IToolsInfoService.getToolsCombinedInfo(...).
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setCode("code");
        toolsDto.setName("name");
        toolsDto.setType(3);
        toolsDto.setBusinessSystemId(0L);
        toolsDto.setBusinessSystemName("businessSystemName");
        toolsDto.setOneTypeId(0L);
        toolsDto.setTwoTypeId(0L);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setOperateStatus(0);
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        toolsDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsAgentResultDto.setAgentName("agentName");
        toolsAgentResultDto.setAgentIp("agentIp");
        toolsAgentResultDto.setAgentPort(0);
        toolsAgentResultDto.setSysAgentInfoId(0L);
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        final ToolsFilesQueryDto toolsFilesQueryDto = new ToolsFilesQueryDto();
        toolsDto.setDescribeFilesList(Arrays.asList(toolsFilesQueryDto));
        toolsDto.setStatus(0);
        toolsDto.setOneTypeName("OneTypeName");
        toolsDto.setTwoTypeName("OneTypeName");
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setProjectId(0L);
        toolsProjectInfoDto.setProjectVersionId(0L);
        toolsProjectInfoDto.setProjectName("projectName");
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowName("workflowName");
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto.setCreatorId(0L);
        toolsDto.setCreatorName("creatorName");
        toolsDto.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto.setDesc("desc");

        when(mockBusinessConfig.getFtpType()).thenReturn("sftp");
        when(mockBusinessConfig.getFtpUser()).thenReturn("ftpusr");
        when(mockBusinessConfig.getFtpPw()).thenReturn("ideal");
        when(mockBusinessConfig.getFtpIp()).thenReturn("*************");
        when(mockBusinessConfig.getFtpPort()).thenReturn("22");

        when(mockRedisTemplate.opsForValue()).thenReturn(valueOperations);


        // Configure ScriptInteract.exportScriptProduction(...).
        final ScriptFileInfo scriptFileInfo = new ScriptFileInfo();
        scriptFileInfo.setFileName("fileName");
        scriptFileInfo.setFileSuffix("fileSuffix");
        scriptFileInfo.setFileContentByte("content".getBytes());

        // Configure StudioInteract.exportStudioData(...).
        final StudioFileProjectInteractDto studioFileProjectInteractDto = new StudioFileProjectInteractDto("fileName",
                "fileSuffix", "content".getBytes());


        // Configure IToolsInfoService.getToolsBatchCombinedInfo(...).
        final ToolsDto toolsDto1 = new ToolsDto();
        toolsDto1.setId(0L);
        toolsDto1.setCode("code");
        toolsDto1.setName("name");
        toolsDto1.setType(3);
        toolsDto1.setBusinessSystemId(0L);
        toolsDto1.setBusinessSystemName("businessSystemName");
        toolsDto1.setOneTypeId(0L);
        toolsDto1.setTwoTypeId(0L);
        final AuditEverybodyQueryDto auditEverybodyQueryDto1 = new AuditEverybodyQueryDto();
        toolsDto1.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto1));
        toolsDto1.setOperateStatus(0);
        toolsDto1.setScriptName("scriptName");
        toolsDto1.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto1 = new ToolsParamResultDto();
        toolsDto1.setToolsParamResultList(Arrays.asList(toolsParamResultDto1));
        final ToolsAgentResultDto toolsAgentResultDto1 = new ToolsAgentResultDto();
        toolsAgentResultDto1.setAgentName("agentName");
        toolsAgentResultDto1.setAgentIp("agentIp");
        toolsAgentResultDto1.setAgentPort(0);
        toolsAgentResultDto1.setSysAgentInfoId(0L);
        toolsDto1.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto1));
        final ToolsFilesQueryDto toolsFilesQueryDto1 = new ToolsFilesQueryDto();
        toolsDto1.setDescribeFilesList(Arrays.asList(toolsFilesQueryDto1));
        toolsDto1.setStatus(0);
        toolsDto1.setOneTypeName("OneTypeName");
        toolsDto1.setTwoTypeName("OneTypeName");
        final ToolsProjectInfoDto toolsProjectInfoDto1 = new ToolsProjectInfoDto();
        toolsProjectInfoDto1.setProjectId(0L);
        toolsProjectInfoDto1.setProjectVersionId(0L);
        toolsProjectInfoDto1.setProjectName("projectName");
        toolsProjectInfoDto1.setWorkflowId(0L);
        toolsProjectInfoDto1.setWorkflowName("workflowName");
        toolsDto1.setToolsProjectInfoDto(toolsProjectInfoDto1);
        toolsDto1.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto1.setCreatorId(0L);
        toolsDto1.setCreatorName("creatorName");
        toolsDto1.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto1.setDesc("desc");
        final List<ToolsDto> errorScriptList = Arrays.asList(toolsDto1);
        final ToolsDto toolsDto2 = new ToolsDto();
        toolsDto2.setId(0L);
        toolsDto2.setCode("code");
        toolsDto2.setName("name");
        toolsDto2.setType(3);
        toolsDto2.setBusinessSystemId(0L);
        toolsDto2.setBusinessSystemName("businessSystemName");
        toolsDto2.setOneTypeId(0L);
        toolsDto2.setTwoTypeId(0L);
        final AuditEverybodyQueryDto auditEverybodyQueryDto2 = new AuditEverybodyQueryDto();
        toolsDto2.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto2));
        toolsDto2.setOperateStatus(0);
        toolsDto2.setScriptName("scriptName");
        toolsDto2.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto2 = new ToolsParamResultDto();
        toolsDto2.setToolsParamResultList(Arrays.asList(toolsParamResultDto2));
        final ToolsAgentResultDto toolsAgentResultDto2 = new ToolsAgentResultDto();
        toolsAgentResultDto2.setAgentName("agentName");
        toolsAgentResultDto2.setAgentIp("agentIp");
        toolsAgentResultDto2.setAgentPort(0);
        toolsAgentResultDto2.setSysAgentInfoId(0L);
        toolsDto2.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto2));
        final ToolsFilesQueryDto toolsFilesQueryDto2 = new ToolsFilesQueryDto();
        toolsDto2.setDescribeFilesList(Arrays.asList(toolsFilesQueryDto2));
        toolsDto2.setStatus(0);
        toolsDto2.setOneTypeName("OneTypeName");
        toolsDto2.setTwoTypeName("OneTypeName");
        final ToolsProjectInfoDto toolsProjectInfoDto2 = new ToolsProjectInfoDto();
        toolsProjectInfoDto2.setProjectId(0L);
        toolsProjectInfoDto2.setProjectVersionId(0L);
        toolsProjectInfoDto2.setProjectName("projectName");
        toolsProjectInfoDto2.setWorkflowId(0L);
        toolsProjectInfoDto2.setWorkflowName("workflowName");
        toolsDto2.setToolsProjectInfoDto(toolsProjectInfoDto2);
        toolsDto2.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto2.setCreatorId(0L);
        toolsDto2.setCreatorName("creatorName");
        toolsDto2.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto2.setDesc("desc");
        final List<ToolsDto> errorComList = Arrays.asList(toolsDto2);
        final ToolsDto toolsDto3 = new ToolsDto();
        toolsDto3.setId(0L);
        toolsDto3.setCode("code");
        toolsDto3.setName("name");
        toolsDto3.setType(3);
        toolsDto3.setBusinessSystemId(0L);
        toolsDto3.setBusinessSystemName("businessSystemName");
        toolsDto3.setOneTypeId(0L);
        toolsDto3.setTwoTypeId(0L);
        final AuditEverybodyQueryDto auditEverybodyQueryDto3 = new AuditEverybodyQueryDto();
        toolsDto3.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto3));
        toolsDto3.setOperateStatus(0);
        toolsDto3.setScriptName("scriptName");
        toolsDto3.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto3 = new ToolsParamResultDto();
        toolsDto3.setToolsParamResultList(Arrays.asList(toolsParamResultDto3));
        final ToolsAgentResultDto toolsAgentResultDto3 = new ToolsAgentResultDto();
        toolsAgentResultDto3.setAgentName("agentName");
        toolsAgentResultDto3.setAgentIp("agentIp");
        toolsAgentResultDto3.setAgentPort(0);
        toolsAgentResultDto3.setSysAgentInfoId(0L);
        toolsDto3.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto3));
        final ToolsFilesQueryDto toolsFilesQueryDto3 = new ToolsFilesQueryDto();
        toolsDto3.setDescribeFilesList(Arrays.asList(toolsFilesQueryDto3));
        toolsDto3.setStatus(0);
        toolsDto3.setOneTypeName("OneTypeName");
        toolsDto3.setTwoTypeName("OneTypeName");
        final ToolsProjectInfoDto toolsProjectInfoDto3 = new ToolsProjectInfoDto();
        toolsProjectInfoDto3.setProjectId(0L);
        toolsProjectInfoDto3.setProjectVersionId(0L);
        toolsProjectInfoDto3.setProjectName("projectName");
        toolsProjectInfoDto3.setWorkflowId(0L);
        toolsProjectInfoDto3.setWorkflowName("workflowName");
        toolsDto3.setToolsProjectInfoDto(toolsProjectInfoDto3);
        toolsDto3.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto3.setCreatorId(0L);
        toolsDto3.setCreatorName("creatorName");
        toolsDto3.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto3.setDesc("desc");
        final List<ToolsDto> errorDescList = Arrays.asList(toolsDto3);
        HashMap<String, List<ToolsDto>> map = new HashMap<>();
        ArrayList<ToolsDto> value = new ArrayList<>();
        value.add(toolsDto1);
        value.add(toolsDto2);
        map.put("toolsDtos", value);
        map.put("errorScriptList",errorScriptList);
        map.put("errorComList",errorComList);
        map.put("errorDescList",errorDescList);
        map.put("errorScriptListImport",errorScriptList);
        map.put("errorComListImport",errorComList);
        map.put("errorDescListImport",errorDescList);
        when(mockToolsInfoService.getToolsBatchCombinedInfo(any(List.class),any(List.class),any(List.class),any(List.class))).thenReturn(map);

        when(mockScriptInteract.exportScriptProduction(any())).thenReturn(scriptFileInfo);
        // Run the test
        final R<Object> result = toolsBatchMoveServiceImplUnderTest.publishBatchExportTools(response, new Long[]{0L,1L,2L,3L},
                0);

        // Verify the results
        verify(mockRedisTemplate).delete("exOrInporting");
    }
    @Test
    void testPublishBatchImportTools_toolsSFtpErrorType3() throws Exception {
        // Setup
        final MigrationImportToolsDto migrationImportToolsDto = new MigrationImportToolsDto();
        migrationImportToolsDto.setFileNames(Arrays.asList("value.zip"));
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto.setId(0L);
        auditEverybodyQueryDto.setAuditorId(0L);
        auditEverybodyQueryDto.setAuditorName("auditorName");
        migrationImportToolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));

        final UserDto user = new UserDto();
        user.setUserName("creatorName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        // Configure ToolsInfoMapper.selectToolsInfoByCodes(...).
        final ToolsInfoEntity toolsInfoEntity = new ToolsInfoEntity();
        toolsInfoEntity.setId(0L);
        toolsInfoEntity.setCode("code");
        toolsInfoEntity.setName("name");
        toolsInfoEntity.setType(3);
        toolsInfoEntity.setClassification(0);
        final List<ToolsInfoEntity> toolsInfoEntities = Arrays.asList(toolsInfoEntity);

        // Configure SystemDataInteract.getBusinessSystemList(...).
        final SystemPullDto systemPullDto = new SystemPullDto();
        systemPullDto.setBusinessSystemId(0L);
        systemPullDto.setBusinessSystemCode("businessSystemCode");
        systemPullDto.setBusinessSystemName("businessSystemName");
        final List<SystemPullDto> systemPullDtos = Arrays.asList(systemPullDto);

        // Configure ITbCategoryService.selectCategorySuperiorList(...).
        final CategorySuperiorDto categorySuperiorDto = new CategorySuperiorDto();
        categorySuperiorDto.setId(0L);
        categorySuperiorDto.setParentId(0L);
        categorySuperiorDto.setClassname("classname");
        categorySuperiorDto.setLevel(0);
        categorySuperiorDto.setChildren(Arrays.asList(new CategorySuperiorDto()));
        final List<CategorySuperiorDto> categorySuperiorDtos = Arrays.asList(categorySuperiorDto);

        when(mockBusinessConfig.getFtpType()).thenReturn("SFP");

        // Configure SystemComputerInteract.getAgentAllList(...).
        final AgentListResultDto agentListResultDto = new AgentListResultDto();
        agentListResultDto.setAgentId(0L);
        agentListResultDto.setAgentIp("agentIp");
        agentListResultDto.setName("name");
        agentListResultDto.setPort(0);
        agentListResultDto.setSysmComputerListId(0L);
        final List<AgentListResultDto> agentListResultDtos = Arrays.asList(agentListResultDto);

        // Configure ITbCategoryService.selectCategoryExist(...).
        final CategoryResultDto categoryResultDto = new CategoryResultDto();
        categoryResultDto.setId(0L);
        categoryResultDto.setParentId(0L);
        categoryResultDto.setClassname("classname");
        categoryResultDto.setDescription("description");
        categoryResultDto.setLevel(0);
        final List<CategoryResultDto> categoryResultDtos = Arrays.asList(categoryResultDto);

        // Configure ScriptInteract.importScriptProduction(...).
        final ScriptFileInfo scriptFileInfo = new ScriptFileInfo();
        scriptFileInfo.setFileName("fileName");
        scriptFileInfo.setFileSuffix("fileSuffix");
        scriptFileInfo.setFileContentByte("content".getBytes());

        // Configure StudioInteract.importStudioData(...).
        final ToolBoxDto toolBoxDto = new ToolBoxDto();
        toolBoxDto.setProjectId(0L);
        toolBoxDto.setProjectVerId(0L);
        toolBoxDto.setWorkflowId(0L);
        toolBoxDto.setProjectName("projectName");
        toolBoxDto.setWorkflowName("workflowName");


        // Run the test
        final R result = toolsBatchMoveServiceImplUnderTest.publishBatchImportTools(migrationImportToolsDto, user,
                null);

    }

    @Test
    void testPublishBatchExportToolsType2() throws Exception {
        // Setup
        final MockHttpServletResponse response = new MockHttpServletResponse();
        when(mockBusinessConfig.getToolsEvn()).thenReturn("DEV环境");
        when(mockBusinessConfig.getToolsbatchPath()).thenReturn("result");
        when(mockBusinessConfig.getToolsbatchErrorPath()).thenReturn("result");
        when(mockBusinessConfig.getFtpType()).thenReturn("sftp");
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Configure IToolsInfoService.getToolsCombinedInfo(...).
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setCode("code");
        toolsDto.setName("name");
        toolsDto.setType(2);
        toolsDto.setBusinessSystemId(0L);
        toolsDto.setBusinessSystemName("businessSystemName");
        toolsDto.setOneTypeId(0L);
        toolsDto.setTwoTypeId(0L);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setOperateStatus(0);
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        toolsDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsAgentResultDto.setAgentName("agentName");
        toolsAgentResultDto.setAgentIp("agentIp");
        toolsAgentResultDto.setAgentPort(0);
        toolsAgentResultDto.setSysAgentInfoId(0L);
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        final ToolsFilesQueryDto toolsFilesQueryDto = new ToolsFilesQueryDto();
        toolsDto.setDescribeFilesList(Arrays.asList(toolsFilesQueryDto));
        toolsDto.setStatus(0);
        toolsDto.setOneTypeName("OneTypeName");
        toolsDto.setTwoTypeName("OneTypeName");
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setProjectId(0L);
        toolsProjectInfoDto.setProjectVersionId(0L);
        toolsProjectInfoDto.setProjectName("projectName");
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowName("workflowName");
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto.setCreatorId(0L);
        toolsDto.setCreatorName("creatorName");
        toolsDto.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto.setDesc("desc");
        when(mockToolsInfoService.getToolsCombinedInfo(0L)).thenReturn(toolsDto);

        when(mockBusinessConfig.getFtpType()).thenReturn("sftp");
        when(mockBusinessConfig.getFtpUser()).thenReturn("ftpusr");
        when(mockBusinessConfig.getFtpPw()).thenReturn("ideal");
        when(mockBusinessConfig.getFtpIp()).thenReturn("*************");
        when(mockBusinessConfig.getFtpPort()).thenReturn("22");
        StudioFileProjectInteractDto dto = new StudioFileProjectInteractDto();
        when(mockRedisTemplate.opsForValue()).thenReturn(valueOperations);
        when(mockStudioInteract.exportStudioData(any(Long.class))).thenReturn(dto);

        // Configure ScriptInteract.exportScriptProduction(...).
        final ScriptFileInfo scriptFileInfo = new ScriptFileInfo();
        scriptFileInfo.setFileName("fileName");
        scriptFileInfo.setFileSuffix("fileSuffix");
        scriptFileInfo.setFileContentByte("content".getBytes());

        // Configure StudioInteract.exportStudioData(...).
        final StudioFileProjectInteractDto studioFileProjectInteractDto = new StudioFileProjectInteractDto("fileName",
                "fileSuffix", "content".getBytes());


        // Configure IToolsInfoService.getToolsBatchCombinedInfo(...).
        final ToolsDto toolsDto1 = new ToolsDto();
        toolsDto1.setId(0L);
        toolsDto1.setCode("code");
        toolsDto1.setName("name");
        toolsDto1.setType(2);
        toolsDto1.setBusinessSystemId(0L);
        toolsDto1.setBusinessSystemName("businessSystemName");
        toolsDto1.setOneTypeId(0L);
        toolsDto1.setTwoTypeId(0L);
        final AuditEverybodyQueryDto auditEverybodyQueryDto1 = new AuditEverybodyQueryDto();
        toolsDto1.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto1));
        toolsDto1.setOperateStatus(0);
        toolsDto1.setScriptName("scriptName");
        toolsDto1.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto1 = new ToolsParamResultDto();
        toolsDto1.setToolsParamResultList(Arrays.asList(toolsParamResultDto1));
        final ToolsAgentResultDto toolsAgentResultDto1 = new ToolsAgentResultDto();
        toolsAgentResultDto1.setAgentName("agentName");
        toolsAgentResultDto1.setAgentIp("agentIp");
        toolsAgentResultDto1.setAgentPort(0);
        toolsAgentResultDto1.setSysAgentInfoId(0L);
        toolsDto1.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto1));
        final ToolsFilesQueryDto toolsFilesQueryDto1 = new ToolsFilesQueryDto();
        toolsDto1.setDescribeFilesList(Arrays.asList(toolsFilesQueryDto1));
        toolsDto1.setStatus(0);
        toolsDto1.setOneTypeName("OneTypeName");
        toolsDto1.setTwoTypeName("OneTypeName");
        final ToolsProjectInfoDto toolsProjectInfoDto1 = new ToolsProjectInfoDto();
        toolsProjectInfoDto1.setProjectId(0L);
        toolsProjectInfoDto1.setProjectVersionId(0L);
        toolsProjectInfoDto1.setProjectName("projectName");
        toolsProjectInfoDto1.setWorkflowId(0L);
        toolsProjectInfoDto1.setWorkflowName("workflowName");
        toolsDto1.setToolsProjectInfoDto(toolsProjectInfoDto1);
        toolsDto1.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto1.setCreatorId(0L);
        toolsDto1.setCreatorName("creatorName");
        toolsDto1.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto1.setDesc("desc");
        final List<ToolsDto> errorScriptList = Arrays.asList(toolsDto1);
        final ToolsDto toolsDto2 = new ToolsDto();
        toolsDto2.setId(0L);
        toolsDto2.setCode("code");
        toolsDto2.setName("name");
        toolsDto2.setType(2);
        toolsDto2.setBusinessSystemId(0L);
        toolsDto2.setBusinessSystemName("businessSystemName");
        toolsDto2.setOneTypeId(0L);
        toolsDto2.setTwoTypeId(0L);
        final AuditEverybodyQueryDto auditEverybodyQueryDto2 = new AuditEverybodyQueryDto();
        toolsDto2.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto2));
        toolsDto2.setOperateStatus(0);
        toolsDto2.setScriptName("scriptName");
        toolsDto2.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto2 = new ToolsParamResultDto();
        toolsDto2.setToolsParamResultList(Arrays.asList(toolsParamResultDto2));
        final ToolsAgentResultDto toolsAgentResultDto2 = new ToolsAgentResultDto();
        toolsAgentResultDto2.setAgentName("agentName");
        toolsAgentResultDto2.setAgentIp("agentIp");
        toolsAgentResultDto2.setAgentPort(0);
        toolsAgentResultDto2.setSysAgentInfoId(0L);
        toolsDto2.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto2));
        final ToolsFilesQueryDto toolsFilesQueryDto2 = new ToolsFilesQueryDto();
        toolsDto2.setDescribeFilesList(Arrays.asList(toolsFilesQueryDto2));
        toolsDto2.setStatus(0);
        toolsDto2.setOneTypeName("OneTypeName");
        toolsDto2.setTwoTypeName("OneTypeName");
        final ToolsProjectInfoDto toolsProjectInfoDto2 = new ToolsProjectInfoDto();
        toolsProjectInfoDto2.setProjectId(0L);
        toolsProjectInfoDto2.setProjectVersionId(0L);
        toolsProjectInfoDto2.setProjectName("projectName");
        toolsProjectInfoDto2.setWorkflowId(0L);
        toolsProjectInfoDto2.setWorkflowName("workflowName");
        toolsDto2.setToolsProjectInfoDto(toolsProjectInfoDto2);
        toolsDto2.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto2.setCreatorId(0L);
        toolsDto2.setCreatorName("creatorName");
        toolsDto2.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto2.setDesc("desc");
        final List<ToolsDto> errorComList = Arrays.asList(toolsDto2);
        final ToolsDto toolsDto3 = new ToolsDto();
        toolsDto3.setId(0L);
        toolsDto3.setCode("code");
        toolsDto3.setName("name");
        toolsDto3.setType(2);
        toolsDto3.setBusinessSystemId(0L);
        toolsDto3.setBusinessSystemName("businessSystemName");
        toolsDto3.setOneTypeId(0L);
        toolsDto3.setTwoTypeId(0L);
        final AuditEverybodyQueryDto auditEverybodyQueryDto3 = new AuditEverybodyQueryDto();
        toolsDto3.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto3));
        toolsDto3.setOperateStatus(0);
        toolsDto3.setScriptName("scriptName");
        toolsDto3.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto3 = new ToolsParamResultDto();
        toolsDto3.setToolsParamResultList(Arrays.asList(toolsParamResultDto3));
        final ToolsAgentResultDto toolsAgentResultDto3 = new ToolsAgentResultDto();
        toolsAgentResultDto3.setAgentName("agentName");
        toolsAgentResultDto3.setAgentIp("agentIp");
        toolsAgentResultDto3.setAgentPort(0);
        toolsAgentResultDto3.setSysAgentInfoId(0L);
        toolsDto3.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto3));
        final ToolsFilesQueryDto toolsFilesQueryDto3 = new ToolsFilesQueryDto();
        toolsDto3.setDescribeFilesList(Arrays.asList(toolsFilesQueryDto3));
        toolsDto3.setStatus(0);
        toolsDto3.setOneTypeName("OneTypeName");
        toolsDto3.setTwoTypeName("OneTypeName");
        final ToolsProjectInfoDto toolsProjectInfoDto3 = new ToolsProjectInfoDto();
        toolsProjectInfoDto3.setProjectId(0L);
        toolsProjectInfoDto3.setProjectVersionId(0L);
        toolsProjectInfoDto3.setProjectName("projectName");
        toolsProjectInfoDto3.setWorkflowId(0L);
        toolsProjectInfoDto3.setWorkflowName("workflowName");
        toolsDto3.setToolsProjectInfoDto(toolsProjectInfoDto3);
        toolsDto3.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto3.setCreatorId(0L);
        toolsDto3.setCreatorName("creatorName");
        toolsDto3.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto3.setDesc("desc");
        final List<ToolsDto> errorDescList = Arrays.asList(toolsDto3);

        // Run the test
        final R<Object> result = toolsBatchMoveServiceImplUnderTest.publishBatchExportTools(response, new Long[]{0L},
                0);
        // Verify the results
        verify(mockRedisTemplate).delete("exOrInporting");
    }
    @Test
    void testPublishBatchExportToolssftpType2() throws Exception {
        // Setup
        final MockHttpServletResponse response = new MockHttpServletResponse();
        when(mockBusinessConfig.getToolsEvn()).thenReturn("DEV环境");
        when(mockBusinessConfig.getToolsbatchPath()).thenReturn("result");
        when(mockBusinessConfig.getToolsbatchErrorPath()).thenReturn("result");
        when(mockBusinessConfig.getFtpType()).thenReturn("sftp");
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Configure IToolsInfoService.getToolsCombinedInfo(...).
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setCode("code");
        toolsDto.setName("name");
        toolsDto.setType(2);
        toolsDto.setBusinessSystemId(0L);
        toolsDto.setBusinessSystemName("businessSystemName");
        toolsDto.setOneTypeId(0L);
        toolsDto.setTwoTypeId(0L);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setOperateStatus(0);
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        toolsDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsAgentResultDto.setAgentName("agentName");
        toolsAgentResultDto.setAgentIp("agentIp");
        toolsAgentResultDto.setAgentPort(0);
        toolsAgentResultDto.setSysAgentInfoId(0L);
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        final ToolsFilesQueryDto toolsFilesQueryDto = new ToolsFilesQueryDto();
        toolsDto.setDescribeFilesList(Arrays.asList(toolsFilesQueryDto));
        toolsDto.setStatus(0);
        toolsDto.setOneTypeName("OneTypeName");
        toolsDto.setTwoTypeName("OneTypeName");
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setProjectId(0L);
        toolsProjectInfoDto.setProjectVersionId(0L);
        toolsProjectInfoDto.setProjectName("projectName");
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowName("workflowName");
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto.setCreatorId(0L);
        toolsDto.setCreatorName("creatorName");
        toolsDto.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto.setDesc("desc");

        when(mockBusinessConfig.getFtpType()).thenReturn("sftp");
        when(mockBusinessConfig.getFtpUser()).thenReturn("ftpusr");
        when(mockBusinessConfig.getFtpPw()).thenReturn("ideal");
        when(mockBusinessConfig.getFtpIp()).thenReturn("*************");
        when(mockBusinessConfig.getFtpPort()).thenReturn("22");

        when(mockRedisTemplate.opsForValue()).thenReturn(valueOperations);
        HashMap<String, List<ToolsDto>> map = new HashMap<>();
        map.put("toolsDtos",new ArrayList<>());
        map.put("errorScriptList",new ArrayList<>());
        map.put("errorComList",new ArrayList<>());
        map.put("errorDescList",new ArrayList<>());
        when(mockToolsInfoService.getToolsBatchCombinedInfo(any(List.class),any(List.class),any(List.class),any(List.class))).thenReturn(map);

        // Configure ScriptInteract.exportScriptProduction(...).
        final ScriptFileInfo scriptFileInfo = new ScriptFileInfo();
        scriptFileInfo.setFileName("fileName");
        scriptFileInfo.setFileSuffix("fileSuffix");
        scriptFileInfo.setFileContentByte("content".getBytes());

        // Configure StudioInteract.exportStudioData(...).
        final StudioFileProjectInteractDto studioFileProjectInteractDto = new StudioFileProjectInteractDto("fileName",
                "fileSuffix", "content".getBytes());


        // Configure IToolsInfoService.getToolsBatchCombinedInfo(...).
        final ToolsDto toolsDto1 = new ToolsDto();
        toolsDto1.setId(0L);
        toolsDto1.setCode("code");
        toolsDto1.setName("name");
        toolsDto1.setType(2);
        toolsDto1.setBusinessSystemId(0L);
        toolsDto1.setBusinessSystemName("businessSystemName");
        toolsDto1.setOneTypeId(0L);
        toolsDto1.setTwoTypeId(0L);
        final AuditEverybodyQueryDto auditEverybodyQueryDto1 = new AuditEverybodyQueryDto();
        toolsDto1.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto1));
        toolsDto1.setOperateStatus(0);
        toolsDto1.setScriptName("scriptName");
        toolsDto1.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto1 = new ToolsParamResultDto();
        toolsDto1.setToolsParamResultList(Arrays.asList(toolsParamResultDto1));
        final ToolsAgentResultDto toolsAgentResultDto1 = new ToolsAgentResultDto();
        toolsAgentResultDto1.setAgentName("agentName");
        toolsAgentResultDto1.setAgentIp("agentIp");
        toolsAgentResultDto1.setAgentPort(0);
        toolsAgentResultDto1.setSysAgentInfoId(0L);
        toolsDto1.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto1));
        final ToolsFilesQueryDto toolsFilesQueryDto1 = new ToolsFilesQueryDto();
        toolsDto1.setDescribeFilesList(Arrays.asList(toolsFilesQueryDto1));
        toolsDto1.setStatus(0);
        toolsDto1.setOneTypeName("OneTypeName");
        toolsDto1.setTwoTypeName("OneTypeName");
        final ToolsProjectInfoDto toolsProjectInfoDto1 = new ToolsProjectInfoDto();
        toolsProjectInfoDto1.setProjectId(0L);
        toolsProjectInfoDto1.setProjectVersionId(0L);
        toolsProjectInfoDto1.setProjectName("projectName");
        toolsProjectInfoDto1.setWorkflowId(0L);
        toolsProjectInfoDto1.setWorkflowName("workflowName");
        toolsDto1.setToolsProjectInfoDto(toolsProjectInfoDto1);
        toolsDto1.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto1.setCreatorId(0L);
        toolsDto1.setCreatorName("creatorName");
        toolsDto1.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto1.setDesc("desc");
        final List<ToolsDto> errorScriptList = Arrays.asList(toolsDto1);
        final ToolsDto toolsDto2 = new ToolsDto();
        toolsDto2.setId(0L);
        toolsDto2.setCode("code");
        toolsDto2.setName("name");
        toolsDto2.setType(2);
        toolsDto2.setBusinessSystemId(0L);
        toolsDto2.setBusinessSystemName("businessSystemName");
        toolsDto2.setOneTypeId(0L);
        toolsDto2.setTwoTypeId(0L);
        final AuditEverybodyQueryDto auditEverybodyQueryDto2 = new AuditEverybodyQueryDto();
        toolsDto2.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto2));
        toolsDto2.setOperateStatus(0);
        toolsDto2.setScriptName("scriptName");
        toolsDto2.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto2 = new ToolsParamResultDto();
        toolsDto2.setToolsParamResultList(Arrays.asList(toolsParamResultDto2));
        final ToolsAgentResultDto toolsAgentResultDto2 = new ToolsAgentResultDto();
        toolsAgentResultDto2.setAgentName("agentName");
        toolsAgentResultDto2.setAgentIp("agentIp");
        toolsAgentResultDto2.setAgentPort(0);
        toolsAgentResultDto2.setSysAgentInfoId(0L);
        toolsDto2.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto2));
        final ToolsFilesQueryDto toolsFilesQueryDto2 = new ToolsFilesQueryDto();
        toolsDto2.setDescribeFilesList(Arrays.asList(toolsFilesQueryDto2));
        toolsDto2.setStatus(0);
        toolsDto2.setOneTypeName("OneTypeName");
        toolsDto2.setTwoTypeName("OneTypeName");
        final ToolsProjectInfoDto toolsProjectInfoDto2 = new ToolsProjectInfoDto();
        toolsProjectInfoDto2.setProjectId(0L);
        toolsProjectInfoDto2.setProjectVersionId(0L);
        toolsProjectInfoDto2.setProjectName("projectName");
        toolsProjectInfoDto2.setWorkflowId(0L);
        toolsProjectInfoDto2.setWorkflowName("workflowName");
        toolsDto2.setToolsProjectInfoDto(toolsProjectInfoDto2);
        toolsDto2.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto2.setCreatorId(0L);
        toolsDto2.setCreatorName("creatorName");
        toolsDto2.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto2.setDesc("desc");
        final List<ToolsDto> errorComList = Arrays.asList(toolsDto2);
        final ToolsDto toolsDto3 = new ToolsDto();
        toolsDto3.setId(0L);
        toolsDto3.setCode("code");
        toolsDto3.setName("name");
        toolsDto3.setType(2);
        toolsDto3.setBusinessSystemId(0L);
        toolsDto3.setBusinessSystemName("businessSystemName");
        toolsDto3.setOneTypeId(0L);
        toolsDto3.setTwoTypeId(0L);
        final AuditEverybodyQueryDto auditEverybodyQueryDto3 = new AuditEverybodyQueryDto();
        toolsDto3.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto3));
        toolsDto3.setOperateStatus(0);
        toolsDto3.setScriptName("scriptName");
        toolsDto3.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto3 = new ToolsParamResultDto();
        toolsDto3.setToolsParamResultList(Arrays.asList(toolsParamResultDto3));
        final ToolsAgentResultDto toolsAgentResultDto3 = new ToolsAgentResultDto();
        toolsAgentResultDto3.setAgentName("agentName");
        toolsAgentResultDto3.setAgentIp("agentIp");
        toolsAgentResultDto3.setAgentPort(0);
        toolsAgentResultDto3.setSysAgentInfoId(0L);
        toolsDto3.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto3));
        final ToolsFilesQueryDto toolsFilesQueryDto3 = new ToolsFilesQueryDto();
        toolsDto3.setDescribeFilesList(Arrays.asList(toolsFilesQueryDto3));
        toolsDto3.setStatus(0);
        toolsDto3.setOneTypeName("OneTypeName");
        toolsDto3.setTwoTypeName("OneTypeName");
        final ToolsProjectInfoDto toolsProjectInfoDto3 = new ToolsProjectInfoDto();
        toolsProjectInfoDto3.setProjectId(0L);
        toolsProjectInfoDto3.setProjectVersionId(0L);
        toolsProjectInfoDto3.setProjectName("projectName");
        toolsProjectInfoDto3.setWorkflowId(0L);
        toolsProjectInfoDto3.setWorkflowName("workflowName");
        toolsDto3.setToolsProjectInfoDto(toolsProjectInfoDto3);
        toolsDto3.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto3.setCreatorId(0L);
        toolsDto3.setCreatorName("creatorName");
        toolsDto3.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto3.setDesc("desc");
        final List<ToolsDto> errorDescList = Arrays.asList(toolsDto3);
        // Run the test
        ArrayList<Long> longs = new ArrayList<>();
        for (int i = 0; i < 503; i++) {
            longs.add(Long.valueOf(i));
        }
        Long[] toolIds = longs.toArray(new Long[0]);
        final R<Object> result = toolsBatchMoveServiceImplUnderTest.publishBatchExportTools(response, toolIds,
                0);

        // Verify the results
        verify(mockRedisTemplate).delete("exOrInporting");
    }
    @Test
    void testPublishBatchImportTools_toolsSFtpErrorType2() throws Exception {
        // Setup
        final MigrationImportToolsDto migrationImportToolsDto = new MigrationImportToolsDto();
        migrationImportToolsDto.setFileNames(Arrays.asList("value.zip"));
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto.setId(0L);
        auditEverybodyQueryDto.setAuditorId(0L);
        auditEverybodyQueryDto.setAuditorName("auditorName");
        migrationImportToolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));

        final UserDto user = new UserDto();
        user.setUserName("creatorName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        // Configure ToolsInfoMapper.selectToolsInfoByCodes(...).
        final ToolsInfoEntity toolsInfoEntity = new ToolsInfoEntity();
        toolsInfoEntity.setId(0L);
        toolsInfoEntity.setCode("code");
        toolsInfoEntity.setName("name");
        toolsInfoEntity.setType(2);
        toolsInfoEntity.setClassification(0);
        final List<ToolsInfoEntity> toolsInfoEntities = Arrays.asList(toolsInfoEntity);

        // Configure SystemDataInteract.getBusinessSystemList(...).
        final SystemPullDto systemPullDto = new SystemPullDto();
        systemPullDto.setBusinessSystemId(0L);
        systemPullDto.setBusinessSystemCode("businessSystemCode");
        systemPullDto.setBusinessSystemName("businessSystemName");
        final List<SystemPullDto> systemPullDtos = Arrays.asList(systemPullDto);

        // Configure ITbCategoryService.selectCategorySuperiorList(...).
        final CategorySuperiorDto categorySuperiorDto = new CategorySuperiorDto();
        categorySuperiorDto.setId(0L);
        categorySuperiorDto.setParentId(0L);
        categorySuperiorDto.setClassname("classname");
        categorySuperiorDto.setLevel(0);
        categorySuperiorDto.setChildren(Arrays.asList(new CategorySuperiorDto()));
        final List<CategorySuperiorDto> categorySuperiorDtos = Arrays.asList(categorySuperiorDto);

        when(mockBusinessConfig.getFtpType()).thenReturn("SFP");

        // Configure SystemComputerInteract.getAgentAllList(...).
        final AgentListResultDto agentListResultDto = new AgentListResultDto();
        agentListResultDto.setAgentId(0L);
        agentListResultDto.setAgentIp("agentIp");
        agentListResultDto.setName("name");
        agentListResultDto.setPort(0);
        agentListResultDto.setSysmComputerListId(0L);
        final List<AgentListResultDto> agentListResultDtos = Arrays.asList(agentListResultDto);

        // Configure ITbCategoryService.selectCategoryExist(...).
        final CategoryResultDto categoryResultDto = new CategoryResultDto();
        categoryResultDto.setId(0L);
        categoryResultDto.setParentId(0L);
        categoryResultDto.setClassname("classname");
        categoryResultDto.setDescription("description");
        categoryResultDto.setLevel(0);
        final List<CategoryResultDto> categoryResultDtos = Arrays.asList(categoryResultDto);

        // Configure ScriptInteract.importScriptProduction(...).
        final ScriptFileInfo scriptFileInfo = new ScriptFileInfo();
        scriptFileInfo.setFileName("fileName");
        scriptFileInfo.setFileSuffix("fileSuffix");
        scriptFileInfo.setFileContentByte("content".getBytes());

        // Configure StudioInteract.importStudioData(...).
        final ToolBoxDto toolBoxDto = new ToolBoxDto();
        toolBoxDto.setProjectId(0L);
        toolBoxDto.setProjectVerId(0L);
        toolBoxDto.setWorkflowId(0L);
        toolBoxDto.setProjectName("projectName");
        toolBoxDto.setWorkflowName("workflowName");


        // Run the test
        final R result = toolsBatchMoveServiceImplUnderTest.publishBatchImportTools(migrationImportToolsDto, user,
                null);

    }

    @Test
    void testPublishBatchExportToolsType1() throws Exception {
        // Setup
        final MockHttpServletResponse response = new MockHttpServletResponse();
        when(mockBusinessConfig.getToolsEvn()).thenReturn("DEV环境");
        when(mockBusinessConfig.getToolsbatchPath()).thenReturn("result");
        when(mockBusinessConfig.getToolsbatchErrorPath()).thenReturn("result");
        when(mockBusinessConfig.getFtpType()).thenReturn("sftp");
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Configure IToolsInfoService.getToolsCombinedInfo(...).
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setCode("code");
        toolsDto.setName("name");
        toolsDto.setType(1);
        toolsDto.setBusinessSystemId(0L);
        toolsDto.setBusinessSystemName("businessSystemName");
        toolsDto.setOneTypeId(0L);
        toolsDto.setTwoTypeId(0L);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setOperateStatus(0);
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        toolsDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsAgentResultDto.setAgentName("agentName");
        toolsAgentResultDto.setAgentIp("agentIp");
        toolsAgentResultDto.setAgentPort(0);
        toolsAgentResultDto.setSysAgentInfoId(0L);
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        final ToolsFilesQueryDto toolsFilesQueryDto = new ToolsFilesQueryDto();
        toolsDto.setDescribeFilesList(Arrays.asList(toolsFilesQueryDto));
        toolsDto.setStatus(0);
        toolsDto.setOneTypeName("OneTypeName");
        toolsDto.setTwoTypeName("OneTypeName");
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setProjectId(0L);
        toolsProjectInfoDto.setProjectVersionId(0L);
        toolsProjectInfoDto.setProjectName("projectName");
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowName("workflowName");
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto.setCreatorId(0L);
        toolsDto.setCreatorName("creatorName");
        toolsDto.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto.setDesc("desc");
        when(mockToolsInfoService.getToolsCombinedInfo(0L)).thenReturn(toolsDto);

        when(mockBusinessConfig.getFtpType()).thenReturn("sftp");
        when(mockBusinessConfig.getFtpUser()).thenReturn("ftpusr");
        when(mockBusinessConfig.getFtpPw()).thenReturn("ideal");
        when(mockBusinessConfig.getFtpIp()).thenReturn("*************");
        when(mockBusinessConfig.getFtpPort()).thenReturn("22");

        when(mockRedisTemplate.opsForValue()).thenReturn(valueOperations);

        // Configure ScriptInteract.exportScriptProduction(...).
        final ScriptFileInfo scriptFileInfo = new ScriptFileInfo();
        scriptFileInfo.setFileName("fileName");
        scriptFileInfo.setFileSuffix("fileSuffix");
        scriptFileInfo.setFileContentByte("content".getBytes());

        // Configure StudioInteract.exportStudioData(...).
        final StudioFileProjectInteractDto studioFileProjectInteractDto = new StudioFileProjectInteractDto("fileName",
                "fileSuffix", "content".getBytes());


        // Configure IToolsInfoService.getToolsBatchCombinedInfo(...).
        final ToolsDto toolsDto1 = new ToolsDto();
        toolsDto1.setId(0L);
        toolsDto1.setCode("code");
        toolsDto1.setName("name");
        toolsDto1.setType(1);
        toolsDto1.setBusinessSystemId(0L);
        toolsDto1.setBusinessSystemName("businessSystemName");
        toolsDto1.setOneTypeId(0L);
        toolsDto1.setTwoTypeId(0L);
        final AuditEverybodyQueryDto auditEverybodyQueryDto1 = new AuditEverybodyQueryDto();
        toolsDto1.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto1));
        toolsDto1.setOperateStatus(0);
        toolsDto1.setScriptName("scriptName");
        toolsDto1.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto1 = new ToolsParamResultDto();
        toolsDto1.setToolsParamResultList(Arrays.asList(toolsParamResultDto1));
        final ToolsAgentResultDto toolsAgentResultDto1 = new ToolsAgentResultDto();
        toolsAgentResultDto1.setAgentName("agentName");
        toolsAgentResultDto1.setAgentIp("agentIp");
        toolsAgentResultDto1.setAgentPort(0);
        toolsAgentResultDto1.setSysAgentInfoId(0L);
        toolsDto1.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto1));
        final ToolsFilesQueryDto toolsFilesQueryDto1 = new ToolsFilesQueryDto();
        toolsDto1.setDescribeFilesList(Arrays.asList(toolsFilesQueryDto1));
        toolsDto1.setStatus(0);
        toolsDto1.setOneTypeName("OneTypeName");
        toolsDto1.setTwoTypeName("OneTypeName");
        final ToolsProjectInfoDto toolsProjectInfoDto1 = new ToolsProjectInfoDto();
        toolsProjectInfoDto1.setProjectId(0L);
        toolsProjectInfoDto1.setProjectVersionId(0L);
        toolsProjectInfoDto1.setProjectName("projectName");
        toolsProjectInfoDto1.setWorkflowId(0L);
        toolsProjectInfoDto1.setWorkflowName("workflowName");
        toolsDto1.setToolsProjectInfoDto(toolsProjectInfoDto1);
        toolsDto1.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto1.setCreatorId(0L);
        toolsDto1.setCreatorName("creatorName");
        toolsDto1.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto1.setDesc("desc");
        final List<ToolsDto> errorScriptList = Arrays.asList(toolsDto1);
        final ToolsDto toolsDto2 = new ToolsDto();
        toolsDto2.setId(0L);
        toolsDto2.setCode("code");
        toolsDto2.setName("name");
        toolsDto2.setType(1);
        toolsDto2.setBusinessSystemId(0L);
        toolsDto2.setBusinessSystemName("businessSystemName");
        toolsDto2.setOneTypeId(0L);
        toolsDto2.setTwoTypeId(0L);
        final AuditEverybodyQueryDto auditEverybodyQueryDto2 = new AuditEverybodyQueryDto();
        toolsDto2.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto2));
        toolsDto2.setOperateStatus(0);
        toolsDto2.setScriptName("scriptName");
        toolsDto2.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto2 = new ToolsParamResultDto();
        toolsDto2.setToolsParamResultList(Arrays.asList(toolsParamResultDto2));
        final ToolsAgentResultDto toolsAgentResultDto2 = new ToolsAgentResultDto();
        toolsAgentResultDto2.setAgentName("agentName");
        toolsAgentResultDto2.setAgentIp("agentIp");
        toolsAgentResultDto2.setAgentPort(0);
        toolsAgentResultDto2.setSysAgentInfoId(0L);
        toolsDto2.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto2));
        final ToolsFilesQueryDto toolsFilesQueryDto2 = new ToolsFilesQueryDto();
        toolsDto2.setDescribeFilesList(Arrays.asList(toolsFilesQueryDto2));
        toolsDto2.setStatus(0);
        toolsDto2.setOneTypeName("OneTypeName");
        toolsDto2.setTwoTypeName("OneTypeName");
        final ToolsProjectInfoDto toolsProjectInfoDto2 = new ToolsProjectInfoDto();
        toolsProjectInfoDto2.setProjectId(0L);
        toolsProjectInfoDto2.setProjectVersionId(0L);
        toolsProjectInfoDto2.setProjectName("projectName");
        toolsProjectInfoDto2.setWorkflowId(0L);
        toolsProjectInfoDto2.setWorkflowName("workflowName");
        toolsDto2.setToolsProjectInfoDto(toolsProjectInfoDto2);
        toolsDto2.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto2.setCreatorId(0L);
        toolsDto2.setCreatorName("creatorName");
        toolsDto2.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto2.setDesc("desc");
        final List<ToolsDto> errorComList = Arrays.asList(toolsDto2);
        final ToolsDto toolsDto3 = new ToolsDto();
        toolsDto3.setId(0L);
        toolsDto3.setCode("code");
        toolsDto3.setName("name");
        toolsDto3.setType(1);
        toolsDto3.setBusinessSystemId(0L);
        toolsDto3.setBusinessSystemName("businessSystemName");
        toolsDto3.setOneTypeId(0L);
        toolsDto3.setTwoTypeId(0L);
        final AuditEverybodyQueryDto auditEverybodyQueryDto3 = new AuditEverybodyQueryDto();
        toolsDto3.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto3));
        toolsDto3.setOperateStatus(0);
        toolsDto3.setScriptName("scriptName");
        toolsDto3.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto3 = new ToolsParamResultDto();
        toolsDto3.setToolsParamResultList(Arrays.asList(toolsParamResultDto3));
        final ToolsAgentResultDto toolsAgentResultDto3 = new ToolsAgentResultDto();
        toolsAgentResultDto3.setAgentName("agentName");
        toolsAgentResultDto3.setAgentIp("agentIp");
        toolsAgentResultDto3.setAgentPort(0);
        toolsAgentResultDto3.setSysAgentInfoId(0L);
        toolsDto3.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto3));
        final ToolsFilesQueryDto toolsFilesQueryDto3 = new ToolsFilesQueryDto();
        toolsDto3.setDescribeFilesList(Arrays.asList(toolsFilesQueryDto3));
        toolsDto3.setStatus(0);
        toolsDto3.setOneTypeName("OneTypeName");
        toolsDto3.setTwoTypeName("OneTypeName");
        final ToolsProjectInfoDto toolsProjectInfoDto3 = new ToolsProjectInfoDto();
        toolsProjectInfoDto3.setProjectId(0L);
        toolsProjectInfoDto3.setProjectVersionId(0L);
        toolsProjectInfoDto3.setProjectName("projectName");
        toolsProjectInfoDto3.setWorkflowId(0L);
        toolsProjectInfoDto3.setWorkflowName("workflowName");
        toolsDto3.setToolsProjectInfoDto(toolsProjectInfoDto3);
        toolsDto3.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto3.setCreatorId(0L);
        toolsDto3.setCreatorName("creatorName");
        toolsDto3.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto3.setDesc("desc");
        final List<ToolsDto> errorDescList = Arrays.asList(toolsDto3);
        // Run the test
        final R<Object> result = toolsBatchMoveServiceImplUnderTest.publishBatchExportTools(response, new Long[]{0L},
                0);

        // Verify the results
        verify(mockRedisTemplate).delete("exOrInporting");
    }
    @Test
    void testPublishBatchExportToolsCount4() throws Exception {
        // Setup
        final MockHttpServletResponse response = new MockHttpServletResponse();
        when(mockBusinessConfig.getToolsEvn()).thenReturn("生产环境");
        when(mockBusinessConfig.getToolsbatchPath()).thenReturn("result");
        when(mockBusinessConfig.getToolsbatchErrorPath()).thenReturn("result");
        when(mockBusinessConfig.getFtpType()).thenReturn("sftp");
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Configure IToolsInfoService.getToolsCombinedInfo(...).
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setCode("code");
        toolsDto.setName("name");
        toolsDto.setType(1);
        toolsDto.setBusinessSystemId(0L);
        toolsDto.setBusinessSystemName("businessSystemName");
        toolsDto.setOneTypeId(0L);
        toolsDto.setTwoTypeId(0L);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setOperateStatus(0);
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        toolsDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsAgentResultDto.setAgentName("agentName");
        toolsAgentResultDto.setAgentIp("agentIp");
        toolsAgentResultDto.setAgentPort(0);
        toolsAgentResultDto.setSysAgentInfoId(0L);
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        final ToolsFilesQueryDto toolsFilesQueryDto = new ToolsFilesQueryDto();
        toolsDto.setDescribeFilesList(Arrays.asList(toolsFilesQueryDto));
        toolsDto.setStatus(0);
        toolsDto.setOneTypeName("OneTypeName");
        toolsDto.setTwoTypeName("OneTypeName");
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setProjectId(0L);
        toolsProjectInfoDto.setProjectVersionId(0L);
        toolsProjectInfoDto.setProjectName("projectName");
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowName("workflowName");
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto.setCreatorId(0L);
        toolsDto.setCreatorName("creatorName");
        toolsDto.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto.setDesc("desc");

        when(mockBusinessConfig.getFtpType()).thenReturn("sftp");
        when(mockBusinessConfig.getFtpUser()).thenReturn("ftpusr");
        when(mockBusinessConfig.getFtpPw()).thenReturn("ideal");
        when(mockBusinessConfig.getFtpIp()).thenReturn("*************");
        when(mockBusinessConfig.getFtpPort()).thenReturn("22");

        when(mockRedisTemplate.opsForValue()).thenReturn(valueOperations);
        HashMap<String, List<ToolsDto>> map = new HashMap<>();
        map.put("toolsDtos",new ArrayList<>());
        map.put("errorScriptList",new ArrayList<>());
        map.put("errorComList",new ArrayList<>());
        map.put("errorDescList",new ArrayList<>());
        when(mockToolsInfoService.getToolsBatchCombinedInfo(any(List.class),any(List.class),any(List.class),any(List.class))).thenReturn(map);

        // Configure ScriptInteract.exportScriptProduction(...).
        final ScriptFileInfo scriptFileInfo = new ScriptFileInfo();
        scriptFileInfo.setFileName("fileName");
        scriptFileInfo.setFileSuffix("fileSuffix");
        scriptFileInfo.setFileContentByte("content".getBytes());

        // Configure StudioInteract.exportStudioData(...).
        final StudioFileProjectInteractDto studioFileProjectInteractDto = new StudioFileProjectInteractDto("fileName",
                "fileSuffix", "content".getBytes());


        // Configure IToolsInfoService.getToolsBatchCombinedInfo(...).
        final ToolsDto toolsDto1 = new ToolsDto();
        toolsDto1.setId(0L);
        toolsDto1.setCode("code");
        toolsDto1.setName("name");
        toolsDto1.setType(1);
        toolsDto1.setBusinessSystemId(0L);
        toolsDto1.setBusinessSystemName("businessSystemName");
        toolsDto1.setOneTypeId(0L);
        toolsDto1.setTwoTypeId(0L);
        final AuditEverybodyQueryDto auditEverybodyQueryDto1 = new AuditEverybodyQueryDto();
        toolsDto1.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto1));
        toolsDto1.setOperateStatus(0);
        toolsDto1.setScriptName("scriptName");
        toolsDto1.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto1 = new ToolsParamResultDto();
        toolsDto1.setToolsParamResultList(Arrays.asList(toolsParamResultDto1));
        final ToolsAgentResultDto toolsAgentResultDto1 = new ToolsAgentResultDto();
        toolsAgentResultDto1.setAgentName("agentName");
        toolsAgentResultDto1.setAgentIp("agentIp");
        toolsAgentResultDto1.setAgentPort(0);
        toolsAgentResultDto1.setSysAgentInfoId(0L);
        toolsDto1.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto1));
        final ToolsFilesQueryDto toolsFilesQueryDto1 = new ToolsFilesQueryDto();
        toolsDto1.setDescribeFilesList(Arrays.asList(toolsFilesQueryDto1));
        toolsDto1.setStatus(0);
        toolsDto1.setOneTypeName("OneTypeName");
        toolsDto1.setTwoTypeName("OneTypeName");
        final ToolsProjectInfoDto toolsProjectInfoDto1 = new ToolsProjectInfoDto();
        toolsProjectInfoDto1.setProjectId(0L);
        toolsProjectInfoDto1.setProjectVersionId(0L);
        toolsProjectInfoDto1.setProjectName("projectName");
        toolsProjectInfoDto1.setWorkflowId(0L);
        toolsProjectInfoDto1.setWorkflowName("workflowName");
        toolsDto1.setToolsProjectInfoDto(toolsProjectInfoDto1);
        toolsDto1.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto1.setCreatorId(0L);
        toolsDto1.setCreatorName("creatorName");
        toolsDto1.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto1.setDesc("desc");
        final List<ToolsDto> errorScriptList = Arrays.asList(toolsDto1);
        final ToolsDto toolsDto2 = new ToolsDto();
        toolsDto2.setId(0L);
        toolsDto2.setCode("code");
        toolsDto2.setName("name");
        toolsDto2.setType(1);
        toolsDto2.setBusinessSystemId(0L);
        toolsDto2.setBusinessSystemName("businessSystemName");
        toolsDto2.setOneTypeId(0L);
        toolsDto2.setTwoTypeId(0L);
        final AuditEverybodyQueryDto auditEverybodyQueryDto2 = new AuditEverybodyQueryDto();
        toolsDto2.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto2));
        toolsDto2.setOperateStatus(0);
        toolsDto2.setScriptName("scriptName");
        toolsDto2.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto2 = new ToolsParamResultDto();
        toolsDto2.setToolsParamResultList(Arrays.asList(toolsParamResultDto2));
        final ToolsAgentResultDto toolsAgentResultDto2 = new ToolsAgentResultDto();
        toolsAgentResultDto2.setAgentName("agentName");
        toolsAgentResultDto2.setAgentIp("agentIp");
        toolsAgentResultDto2.setAgentPort(0);
        toolsAgentResultDto2.setSysAgentInfoId(0L);
        toolsDto2.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto2));
        final ToolsFilesQueryDto toolsFilesQueryDto2 = new ToolsFilesQueryDto();
        toolsDto2.setDescribeFilesList(Arrays.asList(toolsFilesQueryDto2));
        toolsDto2.setStatus(0);
        toolsDto2.setOneTypeName("OneTypeName");
        toolsDto2.setTwoTypeName("OneTypeName");
        final ToolsProjectInfoDto toolsProjectInfoDto2 = new ToolsProjectInfoDto();
        toolsProjectInfoDto2.setProjectId(0L);
        toolsProjectInfoDto2.setProjectVersionId(0L);
        toolsProjectInfoDto2.setProjectName("projectName");
        toolsProjectInfoDto2.setWorkflowId(0L);
        toolsProjectInfoDto2.setWorkflowName("workflowName");
        toolsDto2.setToolsProjectInfoDto(toolsProjectInfoDto2);
        toolsDto2.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto2.setCreatorId(0L);
        toolsDto2.setCreatorName("creatorName");
        toolsDto2.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto2.setDesc("desc");
        final List<ToolsDto> errorComList = Arrays.asList(toolsDto2);
        final ToolsDto toolsDto3 = new ToolsDto();
        toolsDto3.setId(0L);
        toolsDto3.setCode("code");
        toolsDto3.setName("name");
        toolsDto3.setType(1);
        toolsDto3.setBusinessSystemId(0L);
        toolsDto3.setBusinessSystemName("businessSystemName");
        toolsDto3.setOneTypeId(0L);
        toolsDto3.setTwoTypeId(0L);
        final AuditEverybodyQueryDto auditEverybodyQueryDto3 = new AuditEverybodyQueryDto();
        toolsDto3.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto3));
        toolsDto3.setOperateStatus(0);
        toolsDto3.setScriptName("scriptName");
        toolsDto3.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto3 = new ToolsParamResultDto();
        toolsDto3.setToolsParamResultList(Arrays.asList(toolsParamResultDto3));
        final ToolsAgentResultDto toolsAgentResultDto3 = new ToolsAgentResultDto();
        toolsAgentResultDto3.setAgentName("agentName");
        toolsAgentResultDto3.setAgentIp("agentIp");
        toolsAgentResultDto3.setAgentPort(0);
        toolsAgentResultDto3.setSysAgentInfoId(0L);
        toolsDto3.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto3));
        final ToolsFilesQueryDto toolsFilesQueryDto3 = new ToolsFilesQueryDto();
        toolsDto3.setDescribeFilesList(Arrays.asList(toolsFilesQueryDto3));
        toolsDto3.setStatus(0);
        toolsDto3.setOneTypeName("OneTypeName");
        toolsDto3.setTwoTypeName("OneTypeName");
        final ToolsProjectInfoDto toolsProjectInfoDto3 = new ToolsProjectInfoDto();
        toolsProjectInfoDto3.setProjectId(0L);
        toolsProjectInfoDto3.setProjectVersionId(0L);
        toolsProjectInfoDto3.setProjectName("projectName");
        toolsProjectInfoDto3.setWorkflowId(0L);
        toolsProjectInfoDto3.setWorkflowName("workflowName");
        toolsDto3.setToolsProjectInfoDto(toolsProjectInfoDto3);
        toolsDto3.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto3.setCreatorId(0L);
        toolsDto3.setCreatorName("creatorName");
        toolsDto3.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto3.setDesc("desc");
        final List<ToolsDto> errorDescList = Arrays.asList(toolsDto3);
        // Run the test
        final R<Object> result = toolsBatchMoveServiceImplUnderTest.publishBatchExportTools(response, new Long[]{0L,1L,2L,3L},
                0);

        // Verify the results
        verify(mockRedisTemplate).delete("exOrInporting");
    }

    @Test
    void testPublishBatchExportToolssftpType1() throws Exception {
        // Setup
        final MockHttpServletResponse response = new MockHttpServletResponse();
        when(mockBusinessConfig.getToolsEvn()).thenReturn("DEV环境");
        when(mockBusinessConfig.getToolsbatchPath()).thenReturn("result");
        when(mockBusinessConfig.getToolsbatchErrorPath()).thenReturn("result");
        when(mockBusinessConfig.getFtpType()).thenReturn("sftp");
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Configure IToolsInfoService.getToolsCombinedInfo(...).
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setCode("code");
        toolsDto.setName("name");
        toolsDto.setType(1);
        toolsDto.setBusinessSystemId(0L);
        toolsDto.setBusinessSystemName("businessSystemName");
        toolsDto.setOneTypeId(0L);
        toolsDto.setTwoTypeId(0L);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setOperateStatus(0);
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        toolsDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsAgentResultDto.setAgentName("agentName");
        toolsAgentResultDto.setAgentIp("agentIp");
        toolsAgentResultDto.setAgentPort(0);
        toolsAgentResultDto.setSysAgentInfoId(0L);
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        final ToolsFilesQueryDto toolsFilesQueryDto = new ToolsFilesQueryDto();
        toolsDto.setDescribeFilesList(Arrays.asList(toolsFilesQueryDto));
        toolsDto.setStatus(0);
        toolsDto.setOneTypeName("OneTypeName");
        toolsDto.setTwoTypeName("OneTypeName");
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setProjectId(0L);
        toolsProjectInfoDto.setProjectVersionId(0L);
        toolsProjectInfoDto.setProjectName("projectName");
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowName("workflowName");
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto.setCreatorId(0L);
        toolsDto.setCreatorName("creatorName");
        toolsDto.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto.setDesc("desc");

        when(mockBusinessConfig.getFtpType()).thenReturn("sftp");
        when(mockBusinessConfig.getFtpUser()).thenReturn("ftpusr");
        when(mockBusinessConfig.getFtpPw()).thenReturn("ideal");
        when(mockBusinessConfig.getFtpIp()).thenReturn("*************");
        when(mockBusinessConfig.getFtpPort()).thenReturn("22");

        when(mockRedisTemplate.opsForValue()).thenReturn(valueOperations);
        HashMap<String, List<ToolsDto>> map = new HashMap<>();
        map.put("toolsDtos",new ArrayList<>());
        map.put("errorScriptList",new ArrayList<>());
        map.put("errorComList",new ArrayList<>());
        map.put("errorDescList",new ArrayList<>());
        when(mockToolsInfoService.getToolsBatchCombinedInfo(any(List.class),any(List.class),any(List.class),any(List.class))).thenReturn(map);

        // Configure ScriptInteract.exportScriptProduction(...).
        final ScriptFileInfo scriptFileInfo = new ScriptFileInfo();
        scriptFileInfo.setFileName("fileName");
        scriptFileInfo.setFileSuffix("fileSuffix");
        scriptFileInfo.setFileContentByte("content".getBytes());

        // Configure StudioInteract.exportStudioData(...).
        final StudioFileProjectInteractDto studioFileProjectInteractDto = new StudioFileProjectInteractDto("fileName",
                "fileSuffix", "content".getBytes());


        // Configure IToolsInfoService.getToolsBatchCombinedInfo(...).
        final ToolsDto toolsDto1 = new ToolsDto();
        toolsDto1.setId(0L);
        toolsDto1.setCode("code");
        toolsDto1.setName("name");
        toolsDto1.setType(1);
        toolsDto1.setBusinessSystemId(0L);
        toolsDto1.setBusinessSystemName("businessSystemName");
        toolsDto1.setOneTypeId(0L);
        toolsDto1.setTwoTypeId(0L);
        final AuditEverybodyQueryDto auditEverybodyQueryDto1 = new AuditEverybodyQueryDto();
        toolsDto1.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto1));
        toolsDto1.setOperateStatus(0);
        toolsDto1.setScriptName("scriptName");
        toolsDto1.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto1 = new ToolsParamResultDto();
        toolsDto1.setToolsParamResultList(Arrays.asList(toolsParamResultDto1));
        final ToolsAgentResultDto toolsAgentResultDto1 = new ToolsAgentResultDto();
        toolsAgentResultDto1.setAgentName("agentName");
        toolsAgentResultDto1.setAgentIp("agentIp");
        toolsAgentResultDto1.setAgentPort(0);
        toolsAgentResultDto1.setSysAgentInfoId(0L);
        toolsDto1.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto1));
        final ToolsFilesQueryDto toolsFilesQueryDto1 = new ToolsFilesQueryDto();
        toolsDto1.setDescribeFilesList(Arrays.asList(toolsFilesQueryDto1));
        toolsDto1.setStatus(0);
        toolsDto1.setOneTypeName("OneTypeName");
        toolsDto1.setTwoTypeName("OneTypeName");
        final ToolsProjectInfoDto toolsProjectInfoDto1 = new ToolsProjectInfoDto();
        toolsProjectInfoDto1.setProjectId(0L);
        toolsProjectInfoDto1.setProjectVersionId(0L);
        toolsProjectInfoDto1.setProjectName("projectName");
        toolsProjectInfoDto1.setWorkflowId(0L);
        toolsProjectInfoDto1.setWorkflowName("workflowName");
        toolsDto1.setToolsProjectInfoDto(toolsProjectInfoDto1);
        toolsDto1.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto1.setCreatorId(0L);
        toolsDto1.setCreatorName("creatorName");
        toolsDto1.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto1.setDesc("desc");
        final List<ToolsDto> errorScriptList = Arrays.asList(toolsDto1);
        final ToolsDto toolsDto2 = new ToolsDto();
        toolsDto2.setId(0L);
        toolsDto2.setCode("code");
        toolsDto2.setName("name");
        toolsDto2.setType(1);
        toolsDto2.setBusinessSystemId(0L);
        toolsDto2.setBusinessSystemName("businessSystemName");
        toolsDto2.setOneTypeId(0L);
        toolsDto2.setTwoTypeId(0L);
        final AuditEverybodyQueryDto auditEverybodyQueryDto2 = new AuditEverybodyQueryDto();
        toolsDto2.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto2));
        toolsDto2.setOperateStatus(0);
        toolsDto2.setScriptName("scriptName");
        toolsDto2.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto2 = new ToolsParamResultDto();
        toolsDto2.setToolsParamResultList(Arrays.asList(toolsParamResultDto2));
        final ToolsAgentResultDto toolsAgentResultDto2 = new ToolsAgentResultDto();
        toolsAgentResultDto2.setAgentName("agentName");
        toolsAgentResultDto2.setAgentIp("agentIp");
        toolsAgentResultDto2.setAgentPort(0);
        toolsAgentResultDto2.setSysAgentInfoId(0L);
        toolsDto2.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto2));
        final ToolsFilesQueryDto toolsFilesQueryDto2 = new ToolsFilesQueryDto();
        toolsDto2.setDescribeFilesList(Arrays.asList(toolsFilesQueryDto2));
        toolsDto2.setStatus(0);
        toolsDto2.setOneTypeName("OneTypeName");
        toolsDto2.setTwoTypeName("OneTypeName");
        final ToolsProjectInfoDto toolsProjectInfoDto2 = new ToolsProjectInfoDto();
        toolsProjectInfoDto2.setProjectId(0L);
        toolsProjectInfoDto2.setProjectVersionId(0L);
        toolsProjectInfoDto2.setProjectName("projectName");
        toolsProjectInfoDto2.setWorkflowId(0L);
        toolsProjectInfoDto2.setWorkflowName("workflowName");
        toolsDto2.setToolsProjectInfoDto(toolsProjectInfoDto2);
        toolsDto2.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto2.setCreatorId(0L);
        toolsDto2.setCreatorName("creatorName");
        toolsDto2.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto2.setDesc("desc");
        final List<ToolsDto> errorComList = Arrays.asList(toolsDto2);
        final ToolsDto toolsDto3 = new ToolsDto();
        toolsDto3.setId(0L);
        toolsDto3.setCode("code");
        toolsDto3.setName("name");
        toolsDto3.setType(1);
        toolsDto3.setBusinessSystemId(0L);
        toolsDto3.setBusinessSystemName("businessSystemName");
        toolsDto3.setOneTypeId(0L);
        toolsDto3.setTwoTypeId(0L);
        final AuditEverybodyQueryDto auditEverybodyQueryDto3 = new AuditEverybodyQueryDto();
        toolsDto3.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto3));
        toolsDto3.setOperateStatus(0);
        toolsDto3.setScriptName("scriptName");
        toolsDto3.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto3 = new ToolsParamResultDto();
        toolsDto3.setToolsParamResultList(Arrays.asList(toolsParamResultDto3));
        final ToolsAgentResultDto toolsAgentResultDto3 = new ToolsAgentResultDto();
        toolsAgentResultDto3.setAgentName("agentName");
        toolsAgentResultDto3.setAgentIp("agentIp");
        toolsAgentResultDto3.setAgentPort(0);
        toolsAgentResultDto3.setSysAgentInfoId(0L);
        toolsDto3.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto3));
        final ToolsFilesQueryDto toolsFilesQueryDto3 = new ToolsFilesQueryDto();
        toolsDto3.setDescribeFilesList(Arrays.asList(toolsFilesQueryDto3));
        toolsDto3.setStatus(0);
        toolsDto3.setOneTypeName("OneTypeName");
        toolsDto3.setTwoTypeName("OneTypeName");
        final ToolsProjectInfoDto toolsProjectInfoDto3 = new ToolsProjectInfoDto();
        toolsProjectInfoDto3.setProjectId(0L);
        toolsProjectInfoDto3.setProjectVersionId(0L);
        toolsProjectInfoDto3.setProjectName("projectName");
        toolsProjectInfoDto3.setWorkflowId(0L);
        toolsProjectInfoDto3.setWorkflowName("workflowName");
        toolsDto3.setToolsProjectInfoDto(toolsProjectInfoDto3);
        toolsDto3.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto3.setCreatorId(0L);
        toolsDto3.setCreatorName("creatorName");
        toolsDto3.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto3.setDesc("desc");
        final List<ToolsDto> errorDescList = Arrays.asList(toolsDto3);
        // Run the test
        final R<Object> result = toolsBatchMoveServiceImplUnderTest.publishBatchExportTools(response, new Long[]{0L,1L,2L,3L},
                0);

        // Verify the results
        verify(mockRedisTemplate).delete("exOrInporting");
    }


    @Test
    void testPublishBatchExportTools_ToolsInfoMapperReturnsNoItems() throws Exception {
        // Setup
        final MockHttpServletResponse response = new MockHttpServletResponse();
        when(mockBusinessConfig.getToolsEvn()).thenReturn("DEV环境");
        when(mockBusinessConfig.getToolsbatchPath()).thenReturn("result");
        when(mockBusinessConfig.getToolsbatchErrorPath()).thenReturn("result");
        when(mockBusinessConfig.getFtpType()).thenReturn("sftp");
        when(mockRedisTemplate.opsForValue()).thenReturn(null);
        when(mockBusinessConfig.getFtpType()).thenReturn("sftp");
        when(mockBusinessConfig.getFtpUser()).thenReturn("ftpusr");
        when(mockBusinessConfig.getFtpPw()).thenReturn("ideal");
        when(mockBusinessConfig.getFtpIp()).thenReturn("*************");
        when(mockBusinessConfig.getFtpPort()).thenReturn("22");

        // Configure ScriptInteract.exportScriptProduction(...).
        final ScriptFileInfo scriptFileInfo = new ScriptFileInfo();
        scriptFileInfo.setFileName("fileName");
        scriptFileInfo.setFileSuffix("fileSuffix");
        scriptFileInfo.setFileContentByte("content".getBytes());

        // Configure StudioInteract.exportStudioData(...).
        final StudioFileProjectInteractDto studioFileProjectInteractDto = new StudioFileProjectInteractDto("fileName",
                "fileSuffix", "content".getBytes());

        // Configure IToolsInfoService.getToolsBatchCombinedInfo(...).
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setCode("code");
        toolsDto.setName("name");
        toolsDto.setType(1);
        toolsDto.setBusinessSystemId(0L);
        toolsDto.setBusinessSystemName("businessSystemName");
        toolsDto.setOneTypeId(0L);
        toolsDto.setTwoTypeId(0L);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setOperateStatus(0);
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        toolsDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsAgentResultDto.setAgentName("agentName");
        toolsAgentResultDto.setAgentIp("agentIp");
        toolsAgentResultDto.setAgentPort(0);
        toolsAgentResultDto.setSysAgentInfoId(0L);
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        final ToolsFilesQueryDto toolsFilesQueryDto = new ToolsFilesQueryDto();
        toolsDto.setDescribeFilesList(Arrays.asList(toolsFilesQueryDto));
        toolsDto.setStatus(0);
        toolsDto.setOneTypeName("OneTypeName");
        toolsDto.setTwoTypeName("OneTypeName");
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setProjectId(0L);
        toolsProjectInfoDto.setProjectVersionId(0L);
        toolsProjectInfoDto.setProjectName("projectName");
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowName("workflowName");
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto.setCreatorId(0L);
        toolsDto.setCreatorName("creatorName");
        toolsDto.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto.setDesc("desc");
        final List<ToolsDto> errorScriptList = Arrays.asList(toolsDto);
        final ToolsDto toolsDto1 = new ToolsDto();
        toolsDto1.setId(0L);
        toolsDto1.setCode("code");
        toolsDto1.setName("name");
        toolsDto1.setType(1);
        toolsDto1.setBusinessSystemId(0L);
        toolsDto1.setBusinessSystemName("businessSystemName");
        toolsDto1.setOneTypeId(0L);
        toolsDto1.setTwoTypeId(0L);
        final AuditEverybodyQueryDto auditEverybodyQueryDto1 = new AuditEverybodyQueryDto();
        toolsDto1.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto1));
        toolsDto1.setOperateStatus(0);
        toolsDto1.setScriptName("scriptName");
        toolsDto1.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto1 = new ToolsParamResultDto();
        toolsDto1.setToolsParamResultList(Arrays.asList(toolsParamResultDto1));
        final ToolsAgentResultDto toolsAgentResultDto1 = new ToolsAgentResultDto();
        toolsAgentResultDto1.setAgentName("agentName");
        toolsAgentResultDto1.setAgentIp("agentIp");
        toolsAgentResultDto1.setAgentPort(0);
        toolsAgentResultDto1.setSysAgentInfoId(0L);
        toolsDto1.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto1));
        final ToolsFilesQueryDto toolsFilesQueryDto1 = new ToolsFilesQueryDto();
        toolsDto1.setDescribeFilesList(Arrays.asList(toolsFilesQueryDto1));
        toolsDto1.setStatus(0);
        toolsDto1.setOneTypeName("OneTypeName");
        toolsDto1.setTwoTypeName("OneTypeName");
        final ToolsProjectInfoDto toolsProjectInfoDto1 = new ToolsProjectInfoDto();
        toolsProjectInfoDto1.setProjectId(0L);
        toolsProjectInfoDto1.setProjectVersionId(0L);
        toolsProjectInfoDto1.setProjectName("projectName");
        toolsProjectInfoDto1.setWorkflowId(0L);
        toolsProjectInfoDto1.setWorkflowName("workflowName");
        toolsDto1.setToolsProjectInfoDto(toolsProjectInfoDto1);
        toolsDto1.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto1.setCreatorId(0L);
        toolsDto1.setCreatorName("creatorName");
        toolsDto1.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto1.setDesc("desc");
        final List<ToolsDto> errorComList = Arrays.asList(toolsDto1);
        final ToolsDto toolsDto2 = new ToolsDto();
        toolsDto2.setId(0L);
        toolsDto2.setCode("code");
        toolsDto2.setName("name");
        toolsDto2.setType(1);
        toolsDto2.setBusinessSystemId(0L);
        toolsDto2.setBusinessSystemName("businessSystemName");
        toolsDto2.setOneTypeId(0L);
        toolsDto2.setTwoTypeId(0L);
        final AuditEverybodyQueryDto auditEverybodyQueryDto2 = new AuditEverybodyQueryDto();
        toolsDto2.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto2));
        toolsDto2.setOperateStatus(0);
        toolsDto2.setScriptName("scriptName");
        toolsDto2.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto2 = new ToolsParamResultDto();
        toolsDto2.setToolsParamResultList(Arrays.asList(toolsParamResultDto2));
        final ToolsAgentResultDto toolsAgentResultDto2 = new ToolsAgentResultDto();
        toolsAgentResultDto2.setAgentName("agentName");
        toolsAgentResultDto2.setAgentIp("agentIp");
        toolsAgentResultDto2.setAgentPort(0);
        toolsAgentResultDto2.setSysAgentInfoId(0L);
        toolsDto2.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto2));
        final ToolsFilesQueryDto toolsFilesQueryDto2 = new ToolsFilesQueryDto();
        toolsDto2.setDescribeFilesList(Arrays.asList(toolsFilesQueryDto2));
        toolsDto2.setStatus(0);
        toolsDto2.setOneTypeName("OneTypeName");
        toolsDto2.setTwoTypeName("OneTypeName");
        final ToolsProjectInfoDto toolsProjectInfoDto2 = new ToolsProjectInfoDto();
        toolsProjectInfoDto2.setProjectId(0L);
        toolsProjectInfoDto2.setProjectVersionId(0L);
        toolsProjectInfoDto2.setProjectName("projectName");
        toolsProjectInfoDto2.setWorkflowId(0L);
        toolsProjectInfoDto2.setWorkflowName("workflowName");
        toolsDto2.setToolsProjectInfoDto(toolsProjectInfoDto2);
        toolsDto2.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto2.setCreatorId(0L);
        toolsDto2.setCreatorName("creatorName");
        toolsDto2.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto2.setDesc("desc");
        final List<ToolsDto> errorDescList = Arrays.asList(toolsDto2);
        when(mockRedisTemplate.opsForValue()).thenReturn(valueOperations);
        when(mockToolsInfoService.getToolsCombinedInfo(any(Long.class))).thenReturn(toolsDto1);
        // Run the test
        final R<Object> result = toolsBatchMoveServiceImplUnderTest.publishBatchExportTools(response, new Long[]{1L},
                1);

        // Verify the results
        verify(mockRedisTemplate).delete("exOrInporting");
    }

    @Test
    void testConvertToolJsonFile() throws Exception {
        Map aaMap=new HashMap<>();
        // Setup
        final JSONObject jSONObject = new JSONObject(aaMap);
        final File expectedResult = new File("filename.txt");

        // Run the test
        final File result = toolsBatchMoveServiceImplUnderTest.convertToolJsonFile("fileName", jSONObject);

        // Verify the results
        assertThat(result).isNotNull();
    }

    @Test
    void testConvertToolJsonFile_ThrowsIOException() {
        // Setup
        final JSONObject jSONObject = null;

        // Run the test
        assertThatThrownBy(() -> toolsBatchMoveServiceImplUnderTest.convertToolJsonFile("fileName", jSONObject))
                .isInstanceOf(Exception.class);
    }

    @Test
    void testExportTools_ToolsEvn1() throws ToolsException {
        final MockHttpServletResponse response = new MockHttpServletResponse();
        when(mockBusinessConfig.getToolsEvn()).thenReturn("DEV环境");
        assertThat(toolsBatchMoveServiceImplUnderTest.exportTools(response, new Long[] {0L}, true, "SFTP")).isNotNull();
    }
    @Test
    void testExportTools_ToolsEvn2() throws ToolsException {
        final MockHttpServletResponse response = new MockHttpServletResponse();
        when(mockBusinessConfig.getToolsEvn()).thenReturn("QA环境");
        assertThat(toolsBatchMoveServiceImplUnderTest.exportTools(response, new Long[] {0L}, true, "SFTP")).isNotNull();
    }
    @Test
    void testExportTools_ToolsEvn3() throws ToolsException {
        final MockHttpServletResponse response = new MockHttpServletResponse();
        when(mockBusinessConfig.getToolsEvn()).thenReturn("验证环境");
        assertThat(toolsBatchMoveServiceImplUnderTest.exportTools(response, new Long[] {0L}, true, "SFTP")).isNotNull();
    }
    @Test
    void testExportTools_ToolsEvn4() throws ToolsException {
        final MockHttpServletResponse response = new MockHttpServletResponse();
        when(mockBusinessConfig.getToolsEvn()).thenReturn("生产环境");
        assertThat(toolsBatchMoveServiceImplUnderTest.exportTools(response, new Long[] {0L}, true, "SFTP")).isNotNull();
    }
    @Test
    void testExportTools_ToolsEvn5() throws ToolsException {
        final MockHttpServletResponse response = new MockHttpServletResponse();
        when(mockBusinessConfig.getToolsEvn()).thenReturn("");
        assertThatThrownBy(
                () -> toolsBatchMoveServiceImplUnderTest.exportTools(response, new Long[] {0L}, true, ""))
                .isInstanceOf(ToolsException.class);
    }


    @Test
    void testConvertToolJsonFileDesc() throws Exception {
        // Setup
        final File expectedResult = new File("filename.txt");

        // Run the test
        final File result = toolsBatchMoveServiceImplUnderTest.convertToolJsonFileDesc("fileName", "jSONObject");

        // Verify the results
//        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testGetZipNameByPath() throws Exception {
        // Setup
        final MockHttpServletResponse response = new MockHttpServletResponse();
        when(mockBusinessConfig.getFtpType()).thenReturn("sftp");
        when(mockBusinessConfig.getFtpUser()).thenReturn("ftpusr");
        when(mockBusinessConfig.getFtpPw()).thenReturn("ideal");
        when(mockBusinessConfig.getFtpIp()).thenReturn("*************");
        when(mockBusinessConfig.getFtpPort()).thenReturn("22");
        Map<String, Object> rsMap =new HashMap<>();
        FtpListBean ftpListBean = new FtpListBean();
        List<FtpListBean> list = new ArrayList<FtpListBean>();
        ftpListBean.setGenFTPFile("basePath");
        ftpListBean.setCurFtpFileName("filename");
        list.add(ftpListBean);
        rsMap.put("dataList", list);
        when(sftputil.getSftpFileList("path")).thenReturn(rsMap);

        // Run the test
        final List<String> result = toolsBatchMoveServiceImplUnderTest.getZipNameByPath(response, "path");

    }

    @Test
    void testGetZipNameByPathNull() throws Exception {
        // Setup
        final MockHttpServletResponse response = new MockHttpServletResponse();
        when(mockBusinessConfig.getFtpType()).thenReturn("sftp");
        when(mockBusinessConfig.getFtpUser()).thenReturn("ftpusr");
        when(mockBusinessConfig.getFtpPw()).thenReturn("ideal");
        when(mockBusinessConfig.getFtpIp()).thenReturn("*************");
        when(mockBusinessConfig.getFtpPort()).thenReturn("22");
        Map<String, Object> rsMap =new HashMap<>();
        FtpListBean ftpListBean = new FtpListBean();
        List<FtpListBean> list = new ArrayList<FtpListBean>();
        ftpListBean.setGenFTPFile("basePath");
        ftpListBean.setCurFtpFileName("filename");
        rsMap.put("dataList", list);
        when(sftputil.getSftpFileList("path")).thenReturn(rsMap);

        // Run the test
        final List<String> result = toolsBatchMoveServiceImplUnderTest.getZipNameByPath(response, "path");

    }

    @Test
    void testPublishBatchImportTools_migrationImportToolsDtoNull() throws Exception {
        // Setup
        final MigrationImportToolsDto migrationImportToolsDto = new MigrationImportToolsDto();
        migrationImportToolsDto.setFileNames(Arrays.asList("script.zip"));
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto.setId(0L);
        auditEverybodyQueryDto.setAuditorId(0L);
        auditEverybodyQueryDto.setAuditorName("auditorName");
        migrationImportToolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));

        final UserDto user = new UserDto();
        user.setUserName("creatorName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        // Configure ToolsInfoMapper.selectToolsInfoByCodes(...).
        final ToolsInfoEntity toolsInfoEntity = new ToolsInfoEntity();
        toolsInfoEntity.setId(0L);
        toolsInfoEntity.setCode("code");
        toolsInfoEntity.setName("name");
        toolsInfoEntity.setType(1);
        toolsInfoEntity.setClassification(0);
        final List<ToolsInfoEntity> toolsInfoEntities = Arrays.asList(toolsInfoEntity);

        // Configure ITbCategoryService.selectCategorySuperiorList(...).
        final CategorySuperiorDto categorySuperiorDto = new CategorySuperiorDto();
        categorySuperiorDto.setId(0L);
        categorySuperiorDto.setParentId(0L);
        categorySuperiorDto.setClassname("classname");
        categorySuperiorDto.setLevel(0);
        categorySuperiorDto.setChildren(Arrays.asList(new CategorySuperiorDto()));
        final List<CategorySuperiorDto> categorySuperiorDtos = Arrays.asList(categorySuperiorDto);

        // Configure SystemDataInteract.getUserList(...).
        final UserPullDto userPullDto = new UserPullDto();
        userPullDto.setUserId(0L);
        userPullDto.setUserName("userName");
        final List<UserPullDto> userPullDtos = Arrays.asList(userPullDto);

        // Configure SystemComputerInteract.getAgentAllList(...).
        final AgentListResultDto agentListResultDto = new AgentListResultDto();
        agentListResultDto.setAgentId(0L);
        agentListResultDto.setAgentIp("agentIp");
        agentListResultDto.setName("name");
        agentListResultDto.setPort(0);
        agentListResultDto.setSysmComputerListId(0L);
        final List<AgentListResultDto> agentListResultDtos = Arrays.asList(agentListResultDto);

        // Configure ITbCategoryService.selectCategoryExist(...).
        final CategoryResultDto categoryResultDto = new CategoryResultDto();
        categoryResultDto.setId(0L);
        categoryResultDto.setParentId(0L);
        categoryResultDto.setClassname("classname");
        categoryResultDto.setDescription("description");
        categoryResultDto.setLevel(0);
        final List<CategoryResultDto> categoryResultDtos = Arrays.asList(categoryResultDto);

        // Configure ScriptInteract.importScriptProduction(...).
        final ScriptFileInfo scriptFileInfo = new ScriptFileInfo();
        scriptFileInfo.setFileName("fileName");
        scriptFileInfo.setFileSuffix("fileSuffix");
        scriptFileInfo.setFileContentByte("content".getBytes());

        // Configure StudioInteract.importStudioData(...).
        final ToolBoxDto toolBoxDto = new ToolBoxDto();
        toolBoxDto.setProjectId(0L);
        toolBoxDto.setProjectVerId(0L);
        toolBoxDto.setWorkflowId(0L);
        toolBoxDto.setProjectName("projectName");
        toolBoxDto.setWorkflowName("workflowName");
        // Run the test
        final R result = toolsBatchMoveServiceImplUnderTest.publishBatchImportTools(null, user,
                "path");

        // Verify the results
    }

    @Test
    void testPublishBatchImportTools_SystemDataInteractGetBusinessSystemListReturnsNoItems() throws Exception {
        // Setup
        final MigrationImportToolsDto migrationImportToolsDto = new MigrationImportToolsDto();
        migrationImportToolsDto.setFileNames(Arrays.asList("batch_tools~count2~20241206102728.zip"));
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto.setId(0L);
        auditEverybodyQueryDto.setAuditorId(0L);
        auditEverybodyQueryDto.setAuditorName("auditorName");
        migrationImportToolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));

        final UserDto user = new UserDto();
        user.setUserName("creatorName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        // Configure ToolsInfoMapper.selectToolsInfoByCodes(...).
        final ToolsInfoEntity toolsInfoEntity = new ToolsInfoEntity();
        toolsInfoEntity.setId(0L);
        toolsInfoEntity.setCode("code");
        toolsInfoEntity.setName("name");
        toolsInfoEntity.setType(1);
        toolsInfoEntity.setClassification(0);
        final List<ToolsInfoEntity> toolsInfoEntities = Arrays.asList(toolsInfoEntity);

        when(mockBusinessConfig.getFtpType()).thenReturn("sftp");
        when(mockBusinessConfig.getFtpUser()).thenReturn("ftpusr");
        when(mockBusinessConfig.getFtpPw()).thenReturn("ideal");
        when(mockBusinessConfig.getFtpIp()).thenReturn("*************");
        when(mockBusinessConfig.getFtpPort()).thenReturn("22");

        // Configure ITbCategoryService.selectCategorySuperiorList(...).
        final CategorySuperiorDto categorySuperiorDto = new CategorySuperiorDto();
        categorySuperiorDto.setId(0L);
        categorySuperiorDto.setParentId(0L);
        categorySuperiorDto.setClassname("classname");
        categorySuperiorDto.setLevel(0);
        categorySuperiorDto.setChildren(Arrays.asList(new CategorySuperiorDto()));
        final List<CategorySuperiorDto> categorySuperiorDtos = Arrays.asList(categorySuperiorDto);

        // Configure SystemDataInteract.getUserList(...).
        final UserPullDto userPullDto = new UserPullDto();
        userPullDto.setUserId(0L);
        userPullDto.setUserName("userName");
        final List<UserPullDto> userPullDtos = Arrays.asList(userPullDto);

        // Configure SystemComputerInteract.getAgentAllList(...).
        final AgentListResultDto agentListResultDto = new AgentListResultDto();
        agentListResultDto.setAgentId(0L);
        agentListResultDto.setAgentIp("agentIp");
        agentListResultDto.setName("name");
        agentListResultDto.setPort(0);
        agentListResultDto.setSysmComputerListId(0L);
        final List<AgentListResultDto> agentListResultDtos = Arrays.asList(agentListResultDto);

        // Configure ITbCategoryService.selectCategoryExist(...).
        final CategoryResultDto categoryResultDto = new CategoryResultDto();
        categoryResultDto.setId(0L);
        categoryResultDto.setParentId(0L);
        categoryResultDto.setClassname("classname");
        categoryResultDto.setDescription("description");
        categoryResultDto.setLevel(0);
        final List<CategoryResultDto> categoryResultDtos = Arrays.asList(categoryResultDto);

        // Configure ScriptInteract.importScriptProduction(...).
        final ScriptFileInfo scriptFileInfo = new ScriptFileInfo();
        scriptFileInfo.setFileName("fileName");
        scriptFileInfo.setFileSuffix("fileSuffix");
        scriptFileInfo.setFileContentByte("content".getBytes());

        // Configure StudioInteract.importStudioData(...).
        final ToolBoxDto toolBoxDto = new ToolBoxDto();
        toolBoxDto.setProjectId(0L);
        toolBoxDto.setProjectVerId(0L);
        toolBoxDto.setWorkflowId(0L);
        toolBoxDto.setProjectName("projectName");
        toolBoxDto.setWorkflowName("workflowName");
        when(mockRedisTemplate.opsForValue()).thenReturn(valueOperations);
        when(sftputil.isExistDir(any(),any())).thenReturn(true);
        when(mockBusinessConfig.getToolsEvn()).thenReturn("DEV环境");
       // Run the test
        final R result = toolsBatchMoveServiceImplUnderTest.publishBatchImportTools(migrationImportToolsDto, user,
                "path");

        // Verify the results
    }

    @Test
    void testPublishBatchImportTools_toolsSFtpErrorType1() throws Exception {
        // Setup
        final MigrationImportToolsDto migrationImportToolsDto = new MigrationImportToolsDto();
        migrationImportToolsDto.setFileNames(Arrays.asList("value.zip"));
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto.setId(0L);
        auditEverybodyQueryDto.setAuditorId(0L);
        auditEverybodyQueryDto.setAuditorName("auditorName");
        migrationImportToolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));

        final UserDto user = new UserDto();
        user.setUserName("creatorName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        // Configure ToolsInfoMapper.selectToolsInfoByCodes(...).
        final ToolsInfoEntity toolsInfoEntity = new ToolsInfoEntity();
        toolsInfoEntity.setId(0L);
        toolsInfoEntity.setCode("code");
        toolsInfoEntity.setName("name");
        toolsInfoEntity.setType(1);
        toolsInfoEntity.setClassification(0);
        final List<ToolsInfoEntity> toolsInfoEntities = Arrays.asList(toolsInfoEntity);

        // Configure SystemDataInteract.getBusinessSystemList(...).
        final SystemPullDto systemPullDto = new SystemPullDto();
        systemPullDto.setBusinessSystemId(0L);
        systemPullDto.setBusinessSystemCode("businessSystemCode");
        systemPullDto.setBusinessSystemName("businessSystemName");
        final List<SystemPullDto> systemPullDtos = Arrays.asList(systemPullDto);

        // Configure ITbCategoryService.selectCategorySuperiorList(...).
        final CategorySuperiorDto categorySuperiorDto = new CategorySuperiorDto();
        categorySuperiorDto.setId(0L);
        categorySuperiorDto.setParentId(0L);
        categorySuperiorDto.setClassname("classname");
        categorySuperiorDto.setLevel(0);
        categorySuperiorDto.setChildren(Arrays.asList(new CategorySuperiorDto()));
        final List<CategorySuperiorDto> categorySuperiorDtos = Arrays.asList(categorySuperiorDto);

        when(mockBusinessConfig.getFtpType()).thenReturn("SFP");

        // Configure SystemComputerInteract.getAgentAllList(...).
        final AgentListResultDto agentListResultDto = new AgentListResultDto();
        agentListResultDto.setAgentId(0L);
        agentListResultDto.setAgentIp("agentIp");
        agentListResultDto.setName("name");
        agentListResultDto.setPort(0);
        agentListResultDto.setSysmComputerListId(0L);
        final List<AgentListResultDto> agentListResultDtos = Arrays.asList(agentListResultDto);

        // Configure ITbCategoryService.selectCategoryExist(...).
        final CategoryResultDto categoryResultDto = new CategoryResultDto();
        categoryResultDto.setId(0L);
        categoryResultDto.setParentId(0L);
        categoryResultDto.setClassname("classname");
        categoryResultDto.setDescription("description");
        categoryResultDto.setLevel(0);
        final List<CategoryResultDto> categoryResultDtos = Arrays.asList(categoryResultDto);

        // Configure ScriptInteract.importScriptProduction(...).
        final ScriptFileInfo scriptFileInfo = new ScriptFileInfo();
        scriptFileInfo.setFileName("fileName");
        scriptFileInfo.setFileSuffix("fileSuffix");
        scriptFileInfo.setFileContentByte("content".getBytes());

        // Configure StudioInteract.importStudioData(...).
        final ToolBoxDto toolBoxDto = new ToolBoxDto();
        toolBoxDto.setProjectId(0L);
        toolBoxDto.setProjectVerId(0L);
        toolBoxDto.setWorkflowId(0L);
        toolBoxDto.setProjectName("projectName");
        toolBoxDto.setWorkflowName("workflowName");


        // Run the test
        final R result = toolsBatchMoveServiceImplUnderTest.publishBatchImportTools(migrationImportToolsDto, user,
                null);

    }

    @Test
    void testPublishBatchImportTools_toolsMigrateTypeError() throws Exception {
        // Setup
        final MigrationImportToolsDto migrationImportToolsDto = new MigrationImportToolsDto();
        migrationImportToolsDto.setFileNames(Arrays.asList("value"));
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto.setId(0L);
        auditEverybodyQueryDto.setAuditorId(0L);
        auditEverybodyQueryDto.setAuditorName("auditorName");
        migrationImportToolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));

        final UserDto user = new UserDto();
        user.setUserName("creatorName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        // Configure ToolsInfoMapper.selectToolsInfoByCodes(...).
        final ToolsInfoEntity toolsInfoEntity = new ToolsInfoEntity();
        toolsInfoEntity.setId(0L);
        toolsInfoEntity.setCode("code");
        toolsInfoEntity.setName("name");
        toolsInfoEntity.setType(1);
        toolsInfoEntity.setClassification(0);
        final List<ToolsInfoEntity> toolsInfoEntities = Arrays.asList(toolsInfoEntity);

        // Configure SystemDataInteract.getBusinessSystemList(...).
        final SystemPullDto systemPullDto = new SystemPullDto();
        systemPullDto.setBusinessSystemId(0L);
        systemPullDto.setBusinessSystemCode("businessSystemCode");
        systemPullDto.setBusinessSystemName("businessSystemName");
        final List<SystemPullDto> systemPullDtos = Arrays.asList(systemPullDto);

        // Configure ITbCategoryService.selectCategorySuperiorList(...).
        final CategorySuperiorDto categorySuperiorDto = new CategorySuperiorDto();
        categorySuperiorDto.setId(0L);
        categorySuperiorDto.setParentId(0L);
        categorySuperiorDto.setClassname("classname");
        categorySuperiorDto.setLevel(0);
        categorySuperiorDto.setChildren(Arrays.asList(new CategorySuperiorDto()));
        final List<CategorySuperiorDto> categorySuperiorDtos = Arrays.asList(categorySuperiorDto);


        // Configure SystemComputerInteract.getAgentAllList(...).
        final AgentListResultDto agentListResultDto = new AgentListResultDto();
        agentListResultDto.setAgentId(0L);
        agentListResultDto.setAgentIp("agentIp");
        agentListResultDto.setName("name");
        agentListResultDto.setPort(0);
        agentListResultDto.setSysmComputerListId(0L);
        final List<AgentListResultDto> agentListResultDtos = Arrays.asList(agentListResultDto);

        // Configure ITbCategoryService.selectCategoryExist(...).
        final CategoryResultDto categoryResultDto = new CategoryResultDto();
        categoryResultDto.setId(0L);
        categoryResultDto.setParentId(0L);
        categoryResultDto.setClassname("classname");
        categoryResultDto.setDescription("description");
        categoryResultDto.setLevel(0);
        final List<CategoryResultDto> categoryResultDtos = Arrays.asList(categoryResultDto);

        // Configure ScriptInteract.importScriptProduction(...).
        final ScriptFileInfo scriptFileInfo = new ScriptFileInfo();
        scriptFileInfo.setFileName("fileName");
        scriptFileInfo.setFileSuffix("fileSuffix");
        scriptFileInfo.setFileContentByte("content".getBytes());

        // Configure StudioInteract.importStudioData(...).
        final ToolBoxDto toolBoxDto = new ToolBoxDto();
        toolBoxDto.setProjectId(0L);
        toolBoxDto.setProjectVerId(0L);
        toolBoxDto.setWorkflowId(0L);
        toolBoxDto.setProjectName("projectName");
        toolBoxDto.setWorkflowName("workflowName");


        // Run the test
        final R result = toolsBatchMoveServiceImplUnderTest.publishBatchImportTools(migrationImportToolsDto, user,
                null);

    }


    @Test
    void testPublishBatchImportTools_PathNull() throws Exception {
        // Setup
        final MigrationImportToolsDto migrationImportToolsDto = new MigrationImportToolsDto();
        migrationImportToolsDto.setFileNames(Arrays.asList("value.zip"));
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto.setId(0L);
        auditEverybodyQueryDto.setAuditorId(0L);
        auditEverybodyQueryDto.setAuditorName("auditorName");
        migrationImportToolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));

        final UserDto user = new UserDto();
        user.setUserName("creatorName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        // Configure ToolsInfoMapper.selectToolsInfoByCodes(...).
        final ToolsInfoEntity toolsInfoEntity = new ToolsInfoEntity();
        toolsInfoEntity.setId(0L);
        toolsInfoEntity.setCode("code");
        toolsInfoEntity.setName("name");
        toolsInfoEntity.setType(1);
        toolsInfoEntity.setClassification(0);
        final List<ToolsInfoEntity> toolsInfoEntities = Arrays.asList(toolsInfoEntity);

        // Configure SystemDataInteract.getBusinessSystemList(...).
        final SystemPullDto systemPullDto = new SystemPullDto();
        systemPullDto.setBusinessSystemId(0L);
        systemPullDto.setBusinessSystemCode("businessSystemCode");
        systemPullDto.setBusinessSystemName("businessSystemName");
        final List<SystemPullDto> systemPullDtos = Arrays.asList(systemPullDto);

        // Configure ITbCategoryService.selectCategorySuperiorList(...).
        final CategorySuperiorDto categorySuperiorDto = new CategorySuperiorDto();
        categorySuperiorDto.setId(0L);
        categorySuperiorDto.setParentId(0L);
        categorySuperiorDto.setClassname("classname");
        categorySuperiorDto.setLevel(0);
        categorySuperiorDto.setChildren(Arrays.asList(new CategorySuperiorDto()));
        final List<CategorySuperiorDto> categorySuperiorDtos = Arrays.asList(categorySuperiorDto);

        when(mockBusinessConfig.getFtpType()).thenReturn("sftp");
        when(mockBusinessConfig.getFtpUser()).thenReturn("ftpusr");
        when(mockBusinessConfig.getFtpPw()).thenReturn("ideal");
        when(mockBusinessConfig.getFtpIp()).thenReturn("*************");
        when(mockBusinessConfig.getFtpPort()).thenReturn("22");

        // Configure SystemComputerInteract.getAgentAllList(...).
        final AgentListResultDto agentListResultDto = new AgentListResultDto();
        agentListResultDto.setAgentId(0L);
        agentListResultDto.setAgentIp("agentIp");
        agentListResultDto.setName("name");
        agentListResultDto.setPort(0);
        agentListResultDto.setSysmComputerListId(0L);
        final List<AgentListResultDto> agentListResultDtos = Arrays.asList(agentListResultDto);

        // Configure ITbCategoryService.selectCategoryExist(...).
        final CategoryResultDto categoryResultDto = new CategoryResultDto();
        categoryResultDto.setId(0L);
        categoryResultDto.setParentId(0L);
        categoryResultDto.setClassname("classname");
        categoryResultDto.setDescription("description");
        categoryResultDto.setLevel(0);
        final List<CategoryResultDto> categoryResultDtos = Arrays.asList(categoryResultDto);

        // Configure ScriptInteract.importScriptProduction(...).
        final ScriptFileInfo scriptFileInfo = new ScriptFileInfo();
        scriptFileInfo.setFileName("fileName");
        scriptFileInfo.setFileSuffix("fileSuffix");
        scriptFileInfo.setFileContentByte("content".getBytes());

        // Configure StudioInteract.importStudioData(...).
        final ToolBoxDto toolBoxDto = new ToolBoxDto();
        toolBoxDto.setProjectId(0L);
        toolBoxDto.setProjectVerId(0L);
        toolBoxDto.setWorkflowId(0L);
        toolBoxDto.setProjectName("projectName");
        toolBoxDto.setWorkflowName("workflowName");

        when(mockBusinessConfig.getFtpType()).thenReturn("sftp");

        // Run the test
        final R result = toolsBatchMoveServiceImplUnderTest.publishBatchImportTools(migrationImportToolsDto, user,
                null);

    }

    @Test
    void testSaveMoveCommitToolsInfo_StudioInteractThrowsStudioException() throws Exception {
        // Setup
        final ScriptFileInfo scriptFile = new ScriptFileInfo();
        scriptFile.setFileName("fileName");
        scriptFile.setFileSuffix("fileSuffix");
        scriptFile.setFileContentByte("content".getBytes());

        final ToolsSftpImportDto sftpImportDto = new ToolsSftpImportDto();
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setCode("code");
        toolsDto.setName("name");
        toolsDto.setType(1);
        toolsDto.setBusinessSystemId(0L);
        toolsDto.setBusinessSystemName("businessSystemName");
        toolsDto.setOneTypeId(0L);
        toolsDto.setTwoTypeId(0L);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setOperateStatus(0);
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        toolsDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsAgentResultDto.setAgentName("agentName");
        toolsAgentResultDto.setAgentIp("agentIp");
        toolsAgentResultDto.setAgentPort(0);
        toolsAgentResultDto.setSysAgentInfoId(0L);
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        final ToolsFilesQueryDto toolsFilesQueryDto = new ToolsFilesQueryDto();
        toolsDto.setDescribeFilesList(Arrays.asList(toolsFilesQueryDto));
        toolsDto.setStatus(0);
        toolsDto.setOneTypeName("OneTypeName");
        toolsDto.setTwoTypeName("OneTypeName");
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setProjectId(0L);
        toolsProjectInfoDto.setProjectVersionId(0L);
        toolsProjectInfoDto.setProjectName("projectName");
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowName("workflowName");
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto.setCreatorId(0L);
        toolsDto.setCreatorName("creatorName");
        toolsDto.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto.setDesc("desc");
        sftpImportDto.setToolsDto(toolsDto);
        sftpImportDto.setCombinedFileInfo(new CombinedFileInfo("fileName", "fileSuffix", "content".getBytes()));

        final UserDto userDto = new UserDto();
        userDto.setUserName("creatorName");
        userDto.setUserId(0L);
        userDto.setUserCode("userCode");
        userDto.setLoginName("loginName");

        // Run the test
    }

    @Test
    void testCleanupFiles() throws Exception {
        // Setup
        final File zipFile = new File("filename.txt");
        final File sourceFolder = new File("filename.txt");

        // Run the test
        toolsBatchMoveServiceImplUnderTest.cleanupFiles(zipFile, sourceFolder);

        // Verify the results
    }

    @Test
    void testCleanupFiles_ThrowsToolsException() {
        // Setup
        final File zipFile = new File("filename.txt");
        final File sourceFolder = new File("filename.txt");

        // Run the test
    }
}


package com.ideal.tools.service.interaction;

import com.ideal.studio.api.IProject;
import com.ideal.studio.api.IToolBox;
import com.ideal.studio.dto.*;
import com.ideal.tools.exception.StudioException;
import com.ideal.tools.mapper.ToolsProjectMapper;
import com.ideal.tools.model.dto.ToolsDto;
import com.ideal.tools.model.dto.ToolsProjectInfoDto;
import com.ideal.tools.model.dto.UserDto;
import com.ideal.tools.model.entity.ToolsProjectEntity;
import com.ideal.tools.model.interaction.StudioFileProjectInteractDto;
import com.ideal.tools.model.interaction.ToolBoxDto;
import com.ideal.tools.model.interaction.WorkflowActivitiesDto;
import com.ideal.tools.model.interaction.WorkflowQueryDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class StudioInteractTest {

    @Mock(lenient=true)
    private IProject mockProjectService;
    @Mock(lenient=true)
    private IToolBox mockToolBox;
    @Mock(lenient=true)
    private ToolsProjectMapper mockToolsProjectMapper;

    private StudioInteract studioInteractUnderTest;

    @BeforeEach
    void setUp() {
        studioInteractUnderTest = new StudioInteract(mockProjectService, mockToolBox, mockToolsProjectMapper);
    }

    @Test
    void testGetWorkflowJson() throws Exception {
        // Setup
        // Configure IProject.getWorkFlowAttr(...).
        final WorkflowAttrResultApiDto workflowAttrResultApiDto = new WorkflowAttrResultApiDto();
        workflowAttrResultApiDto.setStudioWorkflowId(0L);
        workflowAttrResultApiDto.setAttributType(0);
        workflowAttrResultApiDto.setAttributeValue("workflowContent");
        workflowAttrResultApiDto.setStudioAttrValueId(0L);
        final List<WorkflowAttrResultApiDto> workflowAttrResultApiDtos = Arrays.asList(workflowAttrResultApiDto);
        when(mockProjectService.getWorkFlowAttr(0L, 1)).thenReturn(workflowAttrResultApiDtos);

        // Run the test
        final String result = studioInteractUnderTest.getWorkflowJson(0L);

        // Verify the results
        assertThat(result).isEqualTo("workflowContent");
    }

    @Test
    void testGetWorkflowJson_WithStudioException() throws Exception {
        // Setup
        // Configure IProject.getWorkFlowAttr(...).
        final WorkflowAttrResultApiDto workflowAttrResultApiDto = new WorkflowAttrResultApiDto();
        workflowAttrResultApiDto.setStudioWorkflowId(0L);
        workflowAttrResultApiDto.setAttributType(0);
        workflowAttrResultApiDto.setAttributeValue("workflowContent");
        workflowAttrResultApiDto.setStudioAttrValueId(0L);
        final List<WorkflowAttrResultApiDto> workflowAttrResultApiDtos = Arrays.asList(workflowAttrResultApiDto);
        when(mockProjectService.getWorkFlowAttr(0L, 1)).thenReturn(workflowAttrResultApiDtos);

        assertThatThrownBy(() -> studioInteractUnderTest.getWorkflowJson(null))
                .isInstanceOf(StudioException.class);
    }

    @Test
    void testGetWorkflowJson_IProjectReturnsNoItems() throws Exception {
        // Setup
        when(mockProjectService.getWorkFlowAttr(0L, 1)).thenReturn(Collections.emptyList());

        // Run the test
        final String result = studioInteractUnderTest.getWorkflowJson(0L);

        // Verify the results
        assertThat(result).isNull();
    }

    @Test
    void testDetailBaseData() throws Exception {
        // Setup
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setName("name");
        toolsDto.setBusinessSystemId(0L);
        toolsDto.setBusinessSystemName("businessSystemName");
        toolsDto.setBusinessSystemCode("businessSystemCode");

        final UserDto userDto = new UserDto();
        userDto.setUserName("userName");
        userDto.setUserId(0L);
        userDto.setUserCode("userCode");
        userDto.setLoginName("loginName");

        // Configure IToolBox.detailBaseData(...).
        final ToolBoxApiDto toolBoxApiDto = new ToolBoxApiDto();
        toolBoxApiDto.setProjectId(0L);
        toolBoxApiDto.setProjectVerId(0L);
        toolBoxApiDto.setWorkflowId(0L);
        toolBoxApiDto.setProjectName("projectName");
        toolBoxApiDto.setWorkflowName("workflowName");
        when(mockToolBox.detailBaseData(any(ProjectToolDto.class))).thenReturn(toolBoxApiDto);

        // Run the test
        final ToolsProjectInfoDto result = studioInteractUnderTest.detailBaseData(toolsDto, userDto);

        // Verify the results
    }

    @Test
    void testDetailBaseData_WithStudioException() throws Exception {
        // Configure IToolBox.detailBaseData(...).
        final ToolBoxApiDto toolBoxApiDto = new ToolBoxApiDto();
        toolBoxApiDto.setProjectId(0L);
        toolBoxApiDto.setProjectVerId(0L);
        toolBoxApiDto.setWorkflowId(0L);
        toolBoxApiDto.setProjectName("projectName");
        toolBoxApiDto.setWorkflowName("workflowName");
        when(mockToolBox.detailBaseData(any(ProjectToolDto.class))).thenReturn(toolBoxApiDto);

        assertThatThrownBy(() -> studioInteractUnderTest.detailBaseData(null, null))
                .isInstanceOf(StudioException.class);
        // Verify the results
    }

    @Test
    void testDetailBaseData_IToolBoxReturnsNull() throws Exception {
        // Setup
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setName("name");
        toolsDto.setBusinessSystemId(0L);
        toolsDto.setBusinessSystemName("businessSystemName");
        toolsDto.setBusinessSystemCode("businessSystemCode");

        final UserDto userDto = new UserDto();
        userDto.setUserName("userName");
        userDto.setUserId(0L);
        userDto.setUserCode("userCode");
        userDto.setLoginName("loginName");

        when(mockToolBox.detailBaseData(any(ProjectToolDto.class))).thenReturn(null);

        // Run the test
        final ToolsProjectInfoDto result = studioInteractUnderTest.detailBaseData(toolsDto, userDto);

        // Verify the results
        assertThat(result).isNull();
    }

    @Test
    void testProjectPublish() throws Exception {
        // Setup
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setProjectId(0L);
        toolsProjectInfoDto.setProjectVersionId(0L);
        toolsProjectInfoDto.setProjectName("projectName");
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowName("desc");
        toolsProjectInfoDto.setWorkflowContent("workflowContent");
        toolsProjectInfoDto.setTdToolsId(0L);

        // Configure ToolsProjectMapper.selectToolsProjectByTdToolsId(...).
        final ToolsProjectEntity toolsProjectEntity = new ToolsProjectEntity();
        toolsProjectEntity.setId(0L);
        toolsProjectEntity.setProjectId(0L);
        toolsProjectEntity.setProjectVersionId(0L);
        toolsProjectEntity.setProjectName("projectName");
        toolsProjectEntity.setWorkflowId(0L);
        when(mockToolsProjectMapper.selectToolsProjectByTdToolsId(0L)).thenReturn(toolsProjectEntity);

        // Configure IToolBox.getProjectVersionAttr(...).
        final ProjectVerApiDto projectVerApiDto = new ProjectVerApiDto();
        projectVerApiDto.setLabel("label");
        projectVerApiDto.setId(0L);
        projectVerApiDto.setUniqueCode("uniqueCode");
        projectVerApiDto.setStudioProjectId(0L);
        projectVerApiDto.setReleaseState(0);
        when(mockToolBox.getProjectVersionAttr(any(ProjectPublishApiDto.class))).thenReturn(projectVerApiDto);

        when(mockToolBox.projectPublish(any(ProjectPublishApiDto.class))).thenReturn(new HashMap<>());

        // Run the test
        final ToolsProjectInfoDto result = studioInteractUnderTest.projectPublish(toolsProjectInfoDto);

        // Verify the results
    }

    @Test
    void testProjectPublish_WithStudioException() throws Exception {
        // Configure ToolsProjectMapper.selectToolsProjectByTdToolsId(...).
        final ToolsProjectEntity toolsProjectEntity = new ToolsProjectEntity();
        toolsProjectEntity.setId(0L);
        toolsProjectEntity.setProjectId(0L);
        toolsProjectEntity.setProjectVersionId(0L);
        toolsProjectEntity.setProjectName("projectName");
        toolsProjectEntity.setWorkflowId(0L);
        when(mockToolsProjectMapper.selectToolsProjectByTdToolsId(0L)).thenReturn(toolsProjectEntity);

        // Configure IToolBox.getProjectVersionAttr(...).
        final ProjectVerApiDto projectVerApiDto = new ProjectVerApiDto();
        projectVerApiDto.setLabel("label");
        projectVerApiDto.setId(0L);
        projectVerApiDto.setUniqueCode("uniqueCode");
        projectVerApiDto.setStudioProjectId(0L);
        projectVerApiDto.setReleaseState(0);
        when(mockToolBox.getProjectVersionAttr(any(ProjectPublishApiDto.class))).thenReturn(projectVerApiDto);

        when(mockToolBox.projectPublish(any(ProjectPublishApiDto.class))).thenReturn(new HashMap<>());

        // Verify the results
        assertThatThrownBy(() -> studioInteractUnderTest.projectPublish(null))
                .isInstanceOf(StudioException.class);
    }

    @Test
    void testProjectPublish_WithStudioException2() throws Exception {
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setProjectId(null);
        toolsProjectInfoDto.setProjectVersionId(0L);
        toolsProjectInfoDto.setProjectName("projectName");
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowName("desc");
        toolsProjectInfoDto.setWorkflowContent("workflowContent");
        toolsProjectInfoDto.setTdToolsId(null);
        // Configure ToolsProjectMapper.selectToolsProjectByTdToolsId(...).
        final ToolsProjectEntity toolsProjectEntity = new ToolsProjectEntity();
        toolsProjectEntity.setId(0L);
        toolsProjectEntity.setProjectId(0L);
        toolsProjectEntity.setProjectVersionId(0L);
        toolsProjectEntity.setProjectName("projectName");
        toolsProjectEntity.setWorkflowId(0L);
        when(mockToolsProjectMapper.selectToolsProjectByTdToolsId(0L)).thenReturn(toolsProjectEntity);

        // Configure IToolBox.getProjectVersionAttr(...).
        final ProjectVerApiDto projectVerApiDto = new ProjectVerApiDto();
        projectVerApiDto.setLabel("label");
        projectVerApiDto.setId(0L);
        projectVerApiDto.setUniqueCode("uniqueCode");
        projectVerApiDto.setStudioProjectId(0L);
        projectVerApiDto.setReleaseState(0);
        when(mockToolBox.getProjectVersionAttr(any(ProjectPublishApiDto.class))).thenReturn(projectVerApiDto);

        when(mockToolBox.projectPublish(any(ProjectPublishApiDto.class))).thenReturn(new HashMap<>());

        // Verify the results
        assertThatThrownBy(() -> studioInteractUnderTest.projectPublish(toolsProjectInfoDto))
                .isInstanceOf(StudioException.class);
    }

    @Test
    void testProjectPublish_IToolBoxGetProjectVersionAttrReturnsNull() {
        // Setup
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setProjectId(0L);
        toolsProjectInfoDto.setProjectVersionId(0L);
        toolsProjectInfoDto.setProjectName("projectName");
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowName("desc");
        toolsProjectInfoDto.setWorkflowContent("workflowContent");
        toolsProjectInfoDto.setTdToolsId(0L);

        // Configure ToolsProjectMapper.selectToolsProjectByTdToolsId(...).
        final ToolsProjectEntity toolsProjectEntity = new ToolsProjectEntity();
        toolsProjectEntity.setId(0L);
        toolsProjectEntity.setProjectId(0L);
        toolsProjectEntity.setProjectVersionId(0L);
        toolsProjectEntity.setProjectName("projectName");
        toolsProjectEntity.setWorkflowId(0L);
        when(mockToolsProjectMapper.selectToolsProjectByTdToolsId(0L)).thenReturn(toolsProjectEntity);

        when(mockToolBox.getProjectVersionAttr(any(ProjectPublishApiDto.class))).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> studioInteractUnderTest.projectPublish(toolsProjectInfoDto))
                .isInstanceOf(StudioException.class);
    }

    @Test
    void testExternalReleaseInterface() throws Exception {
        // Setup
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setProjectId(0L);
        toolsProjectInfoDto.setProjectVersionId(0L);
        toolsProjectInfoDto.setProjectName("projectName");
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowName("desc");
        toolsProjectInfoDto.setWorkflowContent("workflowContent");
        toolsProjectInfoDto.setTdToolsId(0L);

        final UserDto user = new UserDto();
        user.setUserName("userName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        when(mockToolBox.externalReleaseInterface(any(ProjectReleaseApiDto.class))).thenReturn(new HashMap<>());

        // Run the test
        final ToolsProjectInfoDto result = studioInteractUnderTest.externalReleaseInterface(toolsProjectInfoDto, user);

        // Verify the results
    }

    @Test
    void testExternalReleaseInterface_WithStudioException() throws Exception {
        final UserDto user = new UserDto();
        user.setUserName("userName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        when(mockToolBox.externalReleaseInterface(any(ProjectReleaseApiDto.class))).thenReturn(new HashMap<>());

        assertThatThrownBy(() -> studioInteractUnderTest.externalReleaseInterface(null, user))
                .isInstanceOf(StudioException.class);
    }

    @Test
    void testExternalReleaseInterface_WithStudioException2() throws Exception {
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setProjectId(null);
        toolsProjectInfoDto.setProjectVersionId(0L);
        toolsProjectInfoDto.setProjectName("projectName");
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowName("desc");
        toolsProjectInfoDto.setWorkflowContent("workflowContent");
        toolsProjectInfoDto.setTdToolsId(null);

        final UserDto user = new UserDto();
        user.setUserName("userName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        when(mockToolBox.externalReleaseInterface(any(ProjectReleaseApiDto.class))).thenReturn(new HashMap<>());

        assertThatThrownBy(() -> studioInteractUnderTest.externalReleaseInterface(null, user))
                .isInstanceOf(StudioException.class);
    }

    @Test
    void testGetStudioProjectList() {
        // Setup
        final UserDto user = new UserDto();
        user.setUserName("userName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        // Configure IProject.listWorkflowRelates(...).
        final WorkflowResultApiDto workflowResultApiDto = new WorkflowResultApiDto();
        workflowResultApiDto.setWorkflowId(0L);
        workflowResultApiDto.setWorkflowUniqueCode("workflowUniqueCode");
        workflowResultApiDto.setWorkflowName("desc");
        workflowResultApiDto.setStudioProjectId(0L);
        workflowResultApiDto.setProjectName("projectName");
        final List<WorkflowResultApiDto> workflowResultApiDtos = Arrays.asList(workflowResultApiDto);
        when(mockProjectService.listWorkflowRelates(any(WorkflowQueryApiDto.class))).thenReturn(workflowResultApiDtos);

        // Run the test
        final List<ToolsProjectInfoDto> result = studioInteractUnderTest.getStudioProjectList(0L, user);

        // Verify the results
    }

    @Test
    void testGetStudioProjectList_IProjectReturnsNull() {
        // Setup
        final UserDto user = new UserDto();
        user.setUserName("userName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        when(mockProjectService.listWorkflowRelates(any(WorkflowQueryApiDto.class))).thenReturn(null);

        // Run the test
        final List<ToolsProjectInfoDto> result = studioInteractUnderTest.getStudioProjectList(0L, user);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetStudioProjectList_IProjectReturnsNoItems() {
        // Setup
        final UserDto user = new UserDto();
        user.setUserName("userName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        when(mockProjectService.listWorkflowRelates(any(WorkflowQueryApiDto.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<ToolsProjectInfoDto> result = studioInteractUnderTest.getStudioProjectList(0L, user);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetStudioProject() {
        // Setup
        final WorkflowQueryDto workflowQueryDto = new WorkflowQueryDto();
        workflowQueryDto.setWorkflowId(0L);
        workflowQueryDto.setUserId(0L);
        workflowQueryDto.setProjectCreateName("projectCreateName");
        workflowQueryDto.setStudioProjectId(0L);
        workflowQueryDto.setProjectName("projectName");

        // Configure IProject.listWorkflowRelates(...).
        final WorkflowResultApiDto workflowResultApiDto = new WorkflowResultApiDto();
        workflowResultApiDto.setWorkflowId(0L);
        workflowResultApiDto.setWorkflowUniqueCode("workflowUniqueCode");
        workflowResultApiDto.setWorkflowName("desc");
        workflowResultApiDto.setStudioProjectId(0L);
        workflowResultApiDto.setProjectName("projectName");
        final List<WorkflowResultApiDto> workflowResultApiDtos = Arrays.asList(workflowResultApiDto);
        when(mockProjectService.listWorkflowRelates(any(WorkflowQueryApiDto.class))).thenReturn(workflowResultApiDtos);

        // Configure IProject.getWorkFlowAttr(...).
        final WorkflowAttrResultApiDto workflowAttrResultApiDto = new WorkflowAttrResultApiDto();
        workflowAttrResultApiDto.setStudioWorkflowId(0L);
        workflowAttrResultApiDto.setAttributType(0);
        workflowAttrResultApiDto.setAttributeValue("workflowContent");
        workflowAttrResultApiDto.setStudioAttrValueId(0L);
        final List<WorkflowAttrResultApiDto> workflowAttrResultApiDtos = Arrays.asList(workflowAttrResultApiDto);
        when(mockProjectService.getWorkFlowAttr(0L, 1)).thenReturn(workflowAttrResultApiDtos);

        // Run the test
        final List<ToolsProjectInfoDto> result = studioInteractUnderTest.getStudioProject(workflowQueryDto);

        // Verify the results
    }

    @Test
    void testGetStudioProject_IProjectListWorkflowRelatesReturnsNull() {
        // Setup
        final WorkflowQueryDto workflowQueryDto = new WorkflowQueryDto();
        workflowQueryDto.setWorkflowId(0L);
        workflowQueryDto.setUserId(0L);
        workflowQueryDto.setProjectCreateName("projectCreateName");
        workflowQueryDto.setStudioProjectId(0L);
        workflowQueryDto.setProjectName("projectName");

        when(mockProjectService.listWorkflowRelates(any(WorkflowQueryApiDto.class))).thenReturn(null);

        // Run the test
        final List<ToolsProjectInfoDto> result = studioInteractUnderTest.getStudioProject(workflowQueryDto);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetStudioProject_IProjectListWorkflowRelatesReturnsNoItems() {
        // Setup
        final WorkflowQueryDto workflowQueryDto = new WorkflowQueryDto();
        workflowQueryDto.setWorkflowId(0L);
        workflowQueryDto.setUserId(0L);
        workflowQueryDto.setProjectCreateName("projectCreateName");
        workflowQueryDto.setStudioProjectId(0L);
        workflowQueryDto.setProjectName("projectName");

        when(mockProjectService.listWorkflowRelates(any(WorkflowQueryApiDto.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<ToolsProjectInfoDto> result = studioInteractUnderTest.getStudioProject(workflowQueryDto);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testGetStudioProject_IProjectGetWorkFlowAttrReturnsNoItems() {
        // Setup
        final WorkflowQueryDto workflowQueryDto = new WorkflowQueryDto();
        workflowQueryDto.setWorkflowId(0L);
        workflowQueryDto.setUserId(0L);
        workflowQueryDto.setProjectCreateName("projectCreateName");
        workflowQueryDto.setStudioProjectId(0L);
        workflowQueryDto.setProjectName("projectName");

        // Configure IProject.listWorkflowRelates(...).
        final WorkflowResultApiDto workflowResultApiDto = new WorkflowResultApiDto();
        workflowResultApiDto.setWorkflowId(0L);
        workflowResultApiDto.setWorkflowUniqueCode("workflowUniqueCode");
        workflowResultApiDto.setWorkflowName("desc");
        workflowResultApiDto.setStudioProjectId(0L);
        workflowResultApiDto.setProjectName("projectName");
        final List<WorkflowResultApiDto> workflowResultApiDtos = Arrays.asList(workflowResultApiDto);
        when(mockProjectService.listWorkflowRelates(any(WorkflowQueryApiDto.class))).thenReturn(workflowResultApiDtos);

        when(mockProjectService.getWorkFlowAttr(0L, 1)).thenReturn(Collections.emptyList());

        // Run the test
        final List<ToolsProjectInfoDto> result = studioInteractUnderTest.getStudioProject(workflowQueryDto);

        // Verify the results
    }

    @Test
    void testExportStudioData() throws Exception {
        // Setup
        // Configure IToolBox.exportStudioData(...).
        final StudioFileProjectDto studioFileProjectDto = new StudioFileProjectDto("fileName", "fileSuffix",
                "content".getBytes());
        when(mockToolBox.exportStudioData(0L)).thenReturn(studioFileProjectDto);

        // Run the test
        final StudioFileProjectInteractDto result = studioInteractUnderTest.exportStudioData(0L);

        // Verify the results
    }

    @Test
    void testExportStudioData_WithStudioException() throws Exception {
        // Setup
        // Configure IToolBox.exportStudioData(...).
        final StudioFileProjectDto studioFileProjectDto = new StudioFileProjectDto("fileName", "fileSuffix",
                "content".getBytes());
        when(mockToolBox.exportStudioData(0L)).thenReturn(studioFileProjectDto);

        assertThatThrownBy(() -> studioInteractUnderTest.exportStudioData(null))
                .isInstanceOf(StudioException.class);
    }

    @Test
    void testExportStudioData_WithStudioException2() throws Exception {
        // Setup
        // Configure IToolBox.exportStudioData(...).
        final StudioFileProjectDto studioFileProjectDto = new StudioFileProjectDto("fileName", "fileSuffix",
                "content".getBytes());
        when(mockToolBox.exportStudioData(any())).thenThrow(RuntimeException.class);

        assertThatThrownBy(() -> studioInteractUnderTest.exportStudioData(0L))
                .isInstanceOf(StudioException.class);
    }

    @Test
    void testImportStudioData1() throws Exception {
        // Setup
        final StudioFileProjectInteractDto studioFileProjectInteractDto = new StudioFileProjectInteractDto("fileName",
                "fileSuffix", "content".getBytes());
        final UserDto user = new UserDto();
        user.setUserName("userName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        // Configure IToolBox.importStudioData(...).
        final StudioResultApiDto<ToolBoxApiDto> toolBoxApiDtoStudioResultApiDto = new StudioResultApiDto<>();
        toolBoxApiDtoStudioResultApiDto.setSuccess(true);
        toolBoxApiDtoStudioResultApiDto.setMessage("message");
        final ToolBoxApiDto toolBoxApiDto = new ToolBoxApiDto();
        toolBoxApiDto.setProjectId(0L);
        toolBoxApiDto.setProjectVerId(0L);
        toolBoxApiDtoStudioResultApiDto.setData(toolBoxApiDto);
        when(mockToolBox.importStudioData(any(StudioFileProjectDto.class), eq(0L), eq("loginName")))
                .thenReturn(toolBoxApiDtoStudioResultApiDto);

        // Run the test
        final ToolBoxDto result = studioInteractUnderTest.importStudioData(studioFileProjectInteractDto, user);

        // Verify the results
    }

    @Test
    void testImportStudioData2() throws Exception {
        // Setup
        final StudioFileProjectInteractDto studioFileProjectInteractDto = new StudioFileProjectInteractDto("fileName",
                "fileSuffix", "content".getBytes());
        final UserDto user = new UserDto();
        user.setUserName("userName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        final Map<String, List<String>> commonToolsAgentMapList = new HashMap<>();

        // Configure IToolBox.importStudioData(...).
        final StudioResultApiDto<ToolBoxApiDto> toolBoxApiDtoStudioResultApiDto = new StudioResultApiDto<>();
        toolBoxApiDtoStudioResultApiDto.setSuccess(true);
        toolBoxApiDtoStudioResultApiDto.setMessage("message");
        final ToolBoxApiDto toolBoxApiDto = new ToolBoxApiDto();
        toolBoxApiDto.setProjectId(0L);
        toolBoxApiDto.setProjectVerId(0L);
        toolBoxApiDtoStudioResultApiDto.setData(toolBoxApiDto);
        when(mockToolBox.importStudioData(any(StudioFileProjectDto.class), eq(0L), eq("loginName"),
                eq(new HashMap<>()))).thenReturn(toolBoxApiDtoStudioResultApiDto);

        // Run the test
        final ToolBoxDto result = studioInteractUnderTest.importStudioData(studioFileProjectInteractDto, user,
                commonToolsAgentMapList);

        // Verify the results
    }

    @Test
    void testGetWorkflowActivities() {
        // Setup
        // Configure IProject.getWorkflowActivities(...).
        final WorkflowActivitiesApiDto workflowActivitiesApiDto = new WorkflowActivitiesApiDto();
        workflowActivitiesApiDto.setPreAct("preAct");
        workflowActivitiesApiDto.setSucceedingActs("succeedingActs");
        workflowActivitiesApiDto.setId(0L);
        workflowActivitiesApiDto.setStudioProjectId(0L);
        workflowActivitiesApiDto.setStudioProjectUniqueCode("studioProjectUniqueCode");
        final List<WorkflowActivitiesApiDto> workflowActivitiesApiDtos = Arrays.asList(workflowActivitiesApiDto);
        when(mockProjectService.getWorkflowActivities(any(WorkflowActivitiesQueryApiDto.class)))
                .thenReturn(workflowActivitiesApiDtos);

        // Run the test
        final List<WorkflowActivitiesDto> result = studioInteractUnderTest.getWorkflowActivities(0L);

        // Verify the results
    }

    @Test
    void testGetWorkflowActivities_IProjectReturnsNoItems() {
        // Setup
        when(mockProjectService.getWorkflowActivities(any(WorkflowActivitiesQueryApiDto.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<WorkflowActivitiesDto> result = studioInteractUnderTest.getWorkflowActivities(0L);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }
}

package com.ideal.tools.service.impl;

import com.ideal.common.dto.R;
import com.ideal.tools.common.ControlsTimeUtil;
import com.ideal.tools.config.BusinessConfig;
import com.ideal.tools.exception.AuditException;
import com.ideal.tools.exception.ExecuteMonitorException;
import com.ideal.tools.exception.ToolsException;
import com.ideal.tools.mapper.AuditExecutAgentMapper;
import com.ideal.tools.mapper.CommonToolsAgentInfoMapper;
import com.ideal.tools.mapper.ToolsExecuteMapper;
import com.ideal.tools.mapper.ToolsInfoMapper;
import com.ideal.tools.model.bean.ExecuteAuditBean;
import com.ideal.tools.model.dto.*;
import com.ideal.tools.model.entity.AuditExecutAgentEntity;
import com.ideal.tools.model.entity.ExecuteAuditEntity;
import com.ideal.tools.model.entity.ToolsInfoCategoryEntity;
import com.ideal.tools.model.enums.AuditorPermissionEnum;
import com.ideal.tools.model.enums.SwitchConfigEnum;
import com.ideal.tools.model.enums.SwitchConfigValueEnum;
import com.ideal.tools.model.interaction.ScriptExecuteDto;
import com.ideal.tools.service.*;
import com.ideal.tools.service.interaction.StudioInteract;
import com.ideal.tools.service.interaction.SystemDataInteract;
import com.ideal.tools.service.interaction.UserInfoInteract;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ToolsExecuteServiceImplTest {

    @Mock
    private ToolsExecuteMapper mockToolsExecuteMapper;
    @Mock
    private IToolsInfoService mockToolsInfoService;
    @Mock
    private ITopTimeService mockTopTimeService;
    @Mock
    private IEccWhiteService mockEccWhiteService;
    @Mock
    private UserInfoInteract mockUserInfoInteract;
    @Mock
    private SystemDataInteract mockSystemDataInteract;
    @Mock
    private IAuditService mockAuditService;
    @Mock
    private IAuditExecutAgentService mockAuditExecutAgentService;
    @Mock
    private IAuditExecutParamService mockAuditExecutParamService;
    @Mock
    private ISwitchConfigService mockSwitchConfigService;
    @Mock
    private IExecuteMonitorService mockExecuteMonitorService;
    @Mock
    private IToolsOperateService mockToolsOperateService;
    @Mock
    private BusinessConfig mockBusinessConfig;

    private ToolsExecuteServiceImpl toolsExecuteServiceImplUnderTest;
    @Mock
    private ILogAuditService mockLogAuditService;
    @Mock
    private AuditExecutAgentMapper auditExecutAgentMapper;
    @Mock
    private IUsageReportService usageReportService;
    @Mock
    private ToolsInfoMapper toolsInfoMapper;
    @Mock
    private StudioInteract studioInteract;
    @Mock
    private IToolsOperateBaseService toolsOperateBaseService;
    @Mock
    private IToolsAgentInfoService toolsAgentInfoService;
    @Mock
    private CommonToolsAgentInfoMapper commonToolsAgentInfoMapper;


    @BeforeEach
    void setUp() throws Exception {
        toolsExecuteServiceImplUnderTest = new ToolsExecuteServiceImpl(mockToolsExecuteMapper, mockToolsInfoService,
                mockTopTimeService, mockEccWhiteService, mockUserInfoInteract, mockSystemDataInteract, mockAuditService,
                mockAuditExecutAgentService, mockAuditExecutParamService, mockSwitchConfigService,
                mockExecuteMonitorService, mockToolsOperateService, mockBusinessConfig, mockLogAuditService, auditExecutAgentMapper,
                usageReportService, toolsInfoMapper, studioInteract,toolsOperateBaseService,toolsAgentInfoService,commonToolsAgentInfoMapper);
    }

    /**
     * 未获取到工具 返回禁止执行
     */
    @Test
    void testGetExecutButtonStatus_IToolsInfoServiceReturnsNull() {
        // Setup
        final ExecutAuditQueryDto executAuditQueryDto = new ExecutAuditQueryDto();
        executAuditQueryDto.setCurrentIp("currentIp");
        executAuditQueryDto.setToolsId(0L);
        executAuditQueryDto.setExecFrom(0);
        executAuditQueryDto.setUserId(0L);
        executAuditQueryDto.setAuditId(0L);
        executAuditQueryDto.setAuditState(0);

        when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(null);

        // Run the test
        final R<ExecuteAuditResultDto> result = toolsExecuteServiceImplUnderTest.getExecutButtonStatus(
                executAuditQueryDto);

        // Verify the results
        assertThat(result.getData().getButtenType()).isEqualTo(0);
    }

    /**
     * 未开启执行校验   返回直接执行
     */
    @Test
    void testGetExecutButtonStatus_FavoritesToolsExecuteCheckReturnsFalse() {
        // Setup
        final ExecutAuditQueryDto executAuditQueryDto = new ExecutAuditQueryDto();
        executAuditQueryDto.setCurrentIp("currentIp");
        executAuditQueryDto.setToolsId(0L);
        executAuditQueryDto.setExecFrom(0);
        executAuditQueryDto.setUserId(0L);
        executAuditQueryDto.setAuditId(0L);
        executAuditQueryDto.setAuditState(0);

        // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
        final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setType(0);
        toolsInfoCategoryEntity.setClassification(0);
        toolsInfoCategoryEntity.setScriptOperatingUser("scriptOperatingUser");
        toolsInfoCategoryEntity.setStatus(0);
        toolsInfoCategoryEntity.setBusinessSystemId(0L);
        toolsInfoCategoryEntity.setDeliveryStatus(0);
        toolsInfoCategoryEntity.setApprovalState(0);
        when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);

        when(mockBusinessConfig.isToolsExecuteCheck()).thenReturn(false);

        // Run the test
        final R<ExecuteAuditResultDto> result = toolsExecuteServiceImplUnderTest.getExecutButtonStatus(
                executAuditQueryDto);

        // Verify the results
        assertThat(result.getData().getButtenType()).isEqualTo(1);
    }

    /**
     * 告警诊断 执行校验 审批状态，工具状态 ，交付状态 返回禁止执行
     */
   /* @Test
    void testGetExecutButtonStatus_AlarmToolStatusCheckReturnsTrue() {
        // Setup
        final ExecutAuditQueryDto executAuditQueryDto = new ExecutAuditQueryDto();
        executAuditQueryDto.setCurrentIp("currentIp");
        executAuditQueryDto.setToolsId(0L);
        executAuditQueryDto.setExecFrom(5);
        executAuditQueryDto.setUserId(0L);
        executAuditQueryDto.setAuditId(0L);
        executAuditQueryDto.setAuditState(0);

        // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
        final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setType(0);
        toolsInfoCategoryEntity.setClassification(0);
        toolsInfoCategoryEntity.setScriptOperatingUser("scriptOperatingUser");
        toolsInfoCategoryEntity.setStatus(3);
        toolsInfoCategoryEntity.setBusinessSystemId(0L);
        toolsInfoCategoryEntity.setDeliveryStatus(1);
        toolsInfoCategoryEntity.setApprovalState(1);
        when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);

//        when(mockBusinessConfig.isToolsExecuteCheck()).thenReturn(true);


        // Run the test
        final R<ExecuteAuditResultDto> result = toolsExecuteServiceImplUnderTest.getExecutButtonStatus(
                executAuditQueryDto);

        // Verify the results
        assertThat(result.getData().getButtenType()).isEqualTo(0);
    }

    *//**
     * 场景工具 执行校验 审批状态，工具状态  返回禁止执行
     *//*
    @Test
    void testGetExecutButtonStatus_ToolStatusCheckReturnsTrue() {
        // Setup
        final ExecutAuditQueryDto executAuditQueryDto = new ExecutAuditQueryDto();
        executAuditQueryDto.setCurrentIp("currentIp");
        executAuditQueryDto.setToolsId(0L);
        executAuditQueryDto.setExecFrom(4);
        executAuditQueryDto.setUserId(0L);
        executAuditQueryDto.setAuditId(0L);
        executAuditQueryDto.setAuditState(0);

        // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
        final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setType(0);
        toolsInfoCategoryEntity.setClassification(0);
        toolsInfoCategoryEntity.setScriptOperatingUser("scriptOperatingUser");
        toolsInfoCategoryEntity.setStatus(2);
        toolsInfoCategoryEntity.setBusinessSystemId(0L);
        toolsInfoCategoryEntity.setDeliveryStatus(0);
        toolsInfoCategoryEntity.setApprovalState(2);
        when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);

//        when(mockBusinessConfig.isToolsExecuteCheck()).thenReturn(true);


        // Run the test
        final R<ExecuteAuditResultDto> result = toolsExecuteServiceImplUnderTest.getExecutButtonStatus(
                executAuditQueryDto);

        // Verify the results
        assertThat(result.getData().getButtenType()).isEqualTo(0);
    }*/

    /**
     * 未获取到请求ip   返回不可执行
     */
    @Test
    void testGetExecutButtonStatus_CurrentIpCheckReturnsTrue() {
        // Setup
        final ExecutAuditQueryDto executAuditQueryDto = new ExecutAuditQueryDto();
        executAuditQueryDto.setCurrentIp("");
        executAuditQueryDto.setToolsId(0L);
        executAuditQueryDto.setExecFrom(0);
        executAuditQueryDto.setUserId(0L);
        executAuditQueryDto.setAuditId(0L);
        executAuditQueryDto.setAuditState(0);

        // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
        final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setType(0);
        toolsInfoCategoryEntity.setClassification(0);
        toolsInfoCategoryEntity.setScriptOperatingUser("scriptOperatingUser");
        toolsInfoCategoryEntity.setStatus(0);
        toolsInfoCategoryEntity.setBusinessSystemId(0L);
        toolsInfoCategoryEntity.setDeliveryStatus(0);
        toolsInfoCategoryEntity.setApprovalState(0);
        when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);

        when(mockBusinessConfig.isToolsExecuteCheck()).thenReturn(true);


        // Run the test
        final R<ExecuteAuditResultDto> result = toolsExecuteServiceImplUnderTest.getExecutButtonStatus(
                executAuditQueryDto);

        // Verify the results
        assertThat(result.getData().getButtenType()).isEqualTo(0);
    }

    /**
     * 存在审批通过未执行 返回直接执行
     */
    @Test
    void testGetExecutButtonStatus_ToolsExecuteMapperReturnsNotNullForExecuteMonitorServiceReturnsFalse() {
        // Setup
        final ExecutAuditQueryDto executAuditQueryDto = new ExecutAuditQueryDto();
        executAuditQueryDto.setCurrentIp("currentIp");
        executAuditQueryDto.setToolsId(0L);
        executAuditQueryDto.setExecFrom(0);
        executAuditQueryDto.setUserId(0L);
        executAuditQueryDto.setAuditId(0L);
        executAuditQueryDto.setAuditState(0);

        // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
        final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setType(0);
        toolsInfoCategoryEntity.setClassification(0);
        toolsInfoCategoryEntity.setScriptOperatingUser("scriptOperatingUser");
        toolsInfoCategoryEntity.setStatus(0);
        toolsInfoCategoryEntity.setBusinessSystemId(0L);
        toolsInfoCategoryEntity.setDeliveryStatus(0);
        toolsInfoCategoryEntity.setApprovalState(0);
        when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);

        when(mockBusinessConfig.isToolsExecuteCheck()).thenReturn(true);

        // Configure ToolsExecuteMapper.getExecuteAudit(...).
        final ExecuteAuditBean executeAuditBean = new ExecuteAuditBean();
        final AuditDto auditDto = new AuditDto();
        auditDto.setId(0L);
        auditDto.setApplyId(0L);
        auditDto.setType(0);
        auditDto.setApprovalState(1);
        executeAuditBean.setAuditDto(auditDto);
        final AuditExecutAgentDto agentDto = new AuditExecutAgentDto();
        agentDto.setAgentIp("agentIp");
        agentDto.setAgentPort(0);
        agentDto.setSysAgentInfoId(0L);
        executeAuditBean.setAgentIps(Arrays.asList(agentDto));
        final AuditExecutParamDto params = new AuditExecutParamDto();
        params.setParamJson("[{\"id\":1,\"sort\":2,\"name\":'12312',\"type\":2,\"value\":'2',\"description\":'123123'}]");
        params.setScriptContent("scriptContent");
        params.setAuditId(1L);
        params.setScriptIds("scriptIds");
        executeAuditBean.setParams(params);
        when(mockToolsExecuteMapper.getExecuteAudit(any(ExecuteAuditEntity.class))).thenReturn(executeAuditBean);

        when(mockExecuteMonitorService.matchAuditId(0L)).thenReturn(false);

        // Run the test
        final R<ExecuteAuditResultDto> result = toolsExecuteServiceImplUnderTest.getExecutButtonStatus(
                executAuditQueryDto);

        // Verify the results
        assertThat(result.getData().getButtenType()).isEqualTo(1);
    }

    /**
     * 存在未审批 返回审批中
     */
    @Test
    void testGetExecutButtonStatus_ToolsExecuteMapperReturnsNotNull() {
        // Setup
        final ExecutAuditQueryDto executAuditQueryDto = new ExecutAuditQueryDto();
        executAuditQueryDto.setCurrentIp("currentIp");
        executAuditQueryDto.setToolsId(0L);
        executAuditQueryDto.setExecFrom(0);
        executAuditQueryDto.setUserId(0L);
        executAuditQueryDto.setAuditId(0L);
        executAuditQueryDto.setAuditState(0);

        // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
        final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setType(0);
        toolsInfoCategoryEntity.setClassification(0);
        toolsInfoCategoryEntity.setScriptOperatingUser("scriptOperatingUser");
        toolsInfoCategoryEntity.setStatus(0);
        toolsInfoCategoryEntity.setBusinessSystemId(0L);
        toolsInfoCategoryEntity.setDeliveryStatus(0);
        toolsInfoCategoryEntity.setApprovalState(0);
        when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);

        when(mockBusinessConfig.isToolsExecuteCheck()).thenReturn(true);

        // Configure ToolsExecuteMapper.getExecuteAudit(...).
        final ExecuteAuditBean executeAuditBean = new ExecuteAuditBean();
        final AuditDto auditDto = new AuditDto();
        auditDto.setId(0L);
        auditDto.setApplyId(0L);
        auditDto.setType(0);
        auditDto.setApprovalState(10);
        executeAuditBean.setAuditDto(auditDto);
        final AuditExecutAgentDto agentDto = new AuditExecutAgentDto();
        agentDto.setAgentIp("agentIp");
        agentDto.setAgentPort(0);
        agentDto.setSysAgentInfoId(0L);
        executeAuditBean.setAgentIps(Arrays.asList(agentDto));
        final AuditExecutParamDto params = new AuditExecutParamDto();
        params.setParamJson("[{\"id\":1,\"sort\":2,\"name\":'12312',\"type\":2,\"value\":'2',\"description\":'123123'}]");
        params.setScriptContent("scriptContent");
        params.setAuditId(0L);
        params.setScriptIds("scriptIds");
        executeAuditBean.setParams(params);
        when(mockToolsExecuteMapper.getExecuteAudit(any(ExecuteAuditEntity.class))).thenReturn(executeAuditBean);


        // Run the test
        final R<ExecuteAuditResultDto> result = toolsExecuteServiceImplUnderTest.getExecutButtonStatus(
                executAuditQueryDto);

        // Verify the results
        assertThat(result.getData().getButtenType()).isEqualTo(5);
    }

    /**
     * 高峰时期，ecc，预案类   返回直接执行
     */
    @Test
    void testGetExecutButtonStatus_TopTime_ECC_PLAN() {
        // Setup
        final ExecutAuditQueryDto executAuditQueryDto = new ExecutAuditQueryDto();
        executAuditQueryDto.setCurrentIp("currentIp");
        executAuditQueryDto.setToolsId(0L);
        executAuditQueryDto.setExecFrom(0);
        executAuditQueryDto.setUserId(0L);
        executAuditQueryDto.setAuditId(0L);
        executAuditQueryDto.setAuditState(0);

        // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
        final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setType(0);
        toolsInfoCategoryEntity.setClassification(1);
        toolsInfoCategoryEntity.setScriptOperatingUser("scriptOperatingUser");
        toolsInfoCategoryEntity.setStatus(0);
        toolsInfoCategoryEntity.setBusinessSystemId(0L);
        toolsInfoCategoryEntity.setDeliveryStatus(0);
        toolsInfoCategoryEntity.setApprovalState(0);
        when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);

        when(mockBusinessConfig.isToolsExecuteCheck()).thenReturn(true);
        when(mockToolsExecuteMapper.getExecuteAudit(any(ExecuteAuditEntity.class))).thenReturn(null);
        when(mockTopTimeService.checkHighTime(ControlsTimeUtil.getCurrentDayOfWeek()
                , ControlsTimeUtil.getStringNowDate("HH:mm"))).thenReturn(true);
        when(mockEccWhiteService.selectLoginUrlByIp("currentIp")).thenReturn(1);

        // Run the test
        final R<ExecuteAuditResultDto> result = toolsExecuteServiceImplUnderTest.getExecutButtonStatus(
                executAuditQueryDto);

        // Verify the results

        assertThat(result.getData().getButtenType()).isEqualTo(1);
    }

    /**
     * 高峰时期，生产变更，预案类   返回直接执行
     */
    @Test
    void testGetExecutButtonStatus_TopTime_Change_PLAN() {
        // Setup
        final ExecutAuditQueryDto executAuditQueryDto = new ExecutAuditQueryDto();
        executAuditQueryDto.setCurrentIp("currentIp");
        executAuditQueryDto.setToolsId(0L);
        executAuditQueryDto.setExecFrom(0);
        executAuditQueryDto.setUserId(0L);
        executAuditQueryDto.setAuditId(0L);
        executAuditQueryDto.setAuditState(0);

        // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
        final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setType(0);
        toolsInfoCategoryEntity.setClassification(1);
        toolsInfoCategoryEntity.setScriptOperatingUser("scriptOperatingUser");
        toolsInfoCategoryEntity.setStatus(0);
        toolsInfoCategoryEntity.setBusinessSystemId(0L);
        toolsInfoCategoryEntity.setDeliveryStatus(0);
        toolsInfoCategoryEntity.setApprovalState(0);
        when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);

        when(mockBusinessConfig.isToolsExecuteCheck()).thenReturn(true);
        when(mockToolsExecuteMapper.getExecuteAudit(any(ExecuteAuditEntity.class))).thenReturn(null);
        when(mockTopTimeService.checkHighTime(ControlsTimeUtil.getCurrentDayOfWeek()
                , ControlsTimeUtil.getStringNowDate("HH:mm"))).thenReturn(true);
        when(mockEccWhiteService.selectLoginUrlByIp("currentIp")).thenReturn(2);

        // Run the test
        final R<ExecuteAuditResultDto> result = toolsExecuteServiceImplUnderTest.getExecutButtonStatus(
                executAuditQueryDto);

        // Verify the results

        assertThat(result.getData().getButtenType()).isEqualTo(1);
    }

    /**
     * 高峰时期，查询诊断，预案类   返回直接执行
     */
    @Test
    void testGetExecutButtonStatus_TopTime_QUERY_PLAN() {
        // Setup
        final ExecutAuditQueryDto executAuditQueryDto = new ExecutAuditQueryDto();
        executAuditQueryDto.setCurrentIp("currentIp");
        executAuditQueryDto.setToolsId(0L);
        executAuditQueryDto.setExecFrom(0);
        executAuditQueryDto.setUserId(0L);
        executAuditQueryDto.setAuditId(0L);
        executAuditQueryDto.setAuditState(0);

        // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
        final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setType(0);
        toolsInfoCategoryEntity.setClassification(1);
        toolsInfoCategoryEntity.setScriptOperatingUser("scriptOperatingUser");
        toolsInfoCategoryEntity.setStatus(0);
        toolsInfoCategoryEntity.setBusinessSystemId(0L);
        toolsInfoCategoryEntity.setDeliveryStatus(0);
        toolsInfoCategoryEntity.setApprovalState(0);
        when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);

        when(mockBusinessConfig.isToolsExecuteCheck()).thenReturn(true);
        when(mockToolsExecuteMapper.getExecuteAudit(any(ExecuteAuditEntity.class))).thenReturn(null);
        when(mockTopTimeService.checkHighTime(ControlsTimeUtil.getCurrentDayOfWeek()
                , ControlsTimeUtil.getStringNowDate("HH:mm"))).thenReturn(true);
        when(mockEccWhiteService.selectLoginUrlByIp("currentIp")).thenReturn(3);

        // Run the test
        final R<ExecuteAuditResultDto> result = toolsExecuteServiceImplUnderTest.getExecutButtonStatus(
                executAuditQueryDto);

        // Verify the results

        assertThat(result.getData().getButtenType()).isEqualTo(1);
    }

    /**
     * 非高峰时期，ecc，预案类   返回直接执行
     */
    @Test
    void testGetExecutButtonStatus_NotTopTime_ECC_PLAN() {
        // Setup
        final ExecutAuditQueryDto executAuditQueryDto = new ExecutAuditQueryDto();
        executAuditQueryDto.setCurrentIp("currentIp");
        executAuditQueryDto.setToolsId(0L);
        executAuditQueryDto.setExecFrom(0);
        executAuditQueryDto.setUserId(0L);
        executAuditQueryDto.setAuditId(0L);
        executAuditQueryDto.setAuditState(0);

        // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
        final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setType(0);
        toolsInfoCategoryEntity.setClassification(1);
        toolsInfoCategoryEntity.setScriptOperatingUser("scriptOperatingUser");
        toolsInfoCategoryEntity.setStatus(0);
        toolsInfoCategoryEntity.setBusinessSystemId(0L);
        toolsInfoCategoryEntity.setDeliveryStatus(0);
        toolsInfoCategoryEntity.setApprovalState(0);
        when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);

        when(mockBusinessConfig.isToolsExecuteCheck()).thenReturn(true);
        when(mockToolsExecuteMapper.getExecuteAudit(any(ExecuteAuditEntity.class))).thenReturn(null);
        when(mockTopTimeService.checkHighTime(ControlsTimeUtil.getCurrentDayOfWeek()
                , ControlsTimeUtil.getStringNowDate("HH:mm"))).thenReturn(false);
        when(mockEccWhiteService.selectLoginUrlByIp("currentIp")).thenReturn(1);

        // Run the test
        final R<ExecuteAuditResultDto> result = toolsExecuteServiceImplUnderTest.getExecutButtonStatus(
                executAuditQueryDto);

        // Verify the results

        assertThat(result.getData().getButtenType()).isEqualTo(1);
    }

    /**
     * 非高峰时期，生产变更，预案类   返回直接执行
     */
    @Test
    void testGetExecutButtonStatus_NotTopTime_Change_PLAN() {
        // Setup
        final ExecutAuditQueryDto executAuditQueryDto = new ExecutAuditQueryDto();
        executAuditQueryDto.setCurrentIp("currentIp");
        executAuditQueryDto.setToolsId(0L);
        executAuditQueryDto.setExecFrom(0);
        executAuditQueryDto.setUserId(0L);
        executAuditQueryDto.setAuditId(0L);
        executAuditQueryDto.setAuditState(0);

        // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
        final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setType(0);
        toolsInfoCategoryEntity.setClassification(1);
        toolsInfoCategoryEntity.setScriptOperatingUser("scriptOperatingUser");
        toolsInfoCategoryEntity.setStatus(0);
        toolsInfoCategoryEntity.setBusinessSystemId(0L);
        toolsInfoCategoryEntity.setDeliveryStatus(0);
        toolsInfoCategoryEntity.setApprovalState(0);
        when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);

        when(mockBusinessConfig.isToolsExecuteCheck()).thenReturn(true);
        when(mockToolsExecuteMapper.getExecuteAudit(any(ExecuteAuditEntity.class))).thenReturn(null);
        when(mockTopTimeService.checkHighTime(ControlsTimeUtil.getCurrentDayOfWeek()
                , ControlsTimeUtil.getStringNowDate("HH:mm"))).thenReturn(false);
        when(mockEccWhiteService.selectLoginUrlByIp("currentIp")).thenReturn(2);

        // Run the test
        final R<ExecuteAuditResultDto> result = toolsExecuteServiceImplUnderTest.getExecutButtonStatus(
                executAuditQueryDto);

        // Verify the results

        assertThat(result.getData().getButtenType()).isEqualTo(1);
    }

    /**
     * 非高峰时期，查询诊断，预案类   返回直接执行
     */
    @Test
    void testGetExecutButtonStatus_NotTopTime_QUERY_PLAN() {
        // Setup
        final ExecutAuditQueryDto executAuditQueryDto = new ExecutAuditQueryDto();
        executAuditQueryDto.setCurrentIp("currentIp");
        executAuditQueryDto.setToolsId(0L);
        executAuditQueryDto.setExecFrom(0);
        executAuditQueryDto.setUserId(0L);
        executAuditQueryDto.setAuditId(0L);
        executAuditQueryDto.setAuditState(0);

        // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
        final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setType(0);
        toolsInfoCategoryEntity.setClassification(1);
        toolsInfoCategoryEntity.setScriptOperatingUser("scriptOperatingUser");
        toolsInfoCategoryEntity.setStatus(0);
        toolsInfoCategoryEntity.setBusinessSystemId(0L);
        toolsInfoCategoryEntity.setDeliveryStatus(0);
        toolsInfoCategoryEntity.setApprovalState(0);
        when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);

        when(mockBusinessConfig.isToolsExecuteCheck()).thenReturn(true);
        when(mockToolsExecuteMapper.getExecuteAudit(any(ExecuteAuditEntity.class))).thenReturn(null);
        when(mockTopTimeService.checkHighTime(ControlsTimeUtil.getCurrentDayOfWeek()
                , ControlsTimeUtil.getStringNowDate("HH:mm"))).thenReturn(false);
        when(mockEccWhiteService.selectLoginUrlByIp("currentIp")).thenReturn(3);

        // Run the test
        final R<ExecuteAuditResultDto> result = toolsExecuteServiceImplUnderTest.getExecutButtonStatus(
                executAuditQueryDto);

        // Verify the results

        assertThat(result.getData().getButtenType()).isEqualTo(1);
    }

    /**
     * 高峰时期，ecc，查询诊断类  *view用户 返回直接执行
     */
    @Test
    void testGetExecutButtonStatus_TopTime_ECC_QUERY_View() {
        // Setup
        final ExecutAuditQueryDto executAuditQueryDto = new ExecutAuditQueryDto();
        executAuditQueryDto.setCurrentIp("currentIp");
        executAuditQueryDto.setToolsId(0L);
        executAuditQueryDto.setExecFrom(0);
        executAuditQueryDto.setUserId(0L);
        executAuditQueryDto.setAuditId(0L);
        executAuditQueryDto.setAuditState(0);

        // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
        final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setType(0);
        toolsInfoCategoryEntity.setClassification(2);
        toolsInfoCategoryEntity.setScriptOperatingUser("1view");
        toolsInfoCategoryEntity.setStatus(0);
        toolsInfoCategoryEntity.setBusinessSystemId(0L);
        toolsInfoCategoryEntity.setDeliveryStatus(0);
        toolsInfoCategoryEntity.setApprovalState(0);
        when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);

        when(mockBusinessConfig.isToolsExecuteCheck()).thenReturn(true);
        when(mockToolsExecuteMapper.getExecuteAudit(any(ExecuteAuditEntity.class))).thenReturn(null);
        when(mockTopTimeService.checkHighTime(ControlsTimeUtil.getCurrentDayOfWeek()
                , ControlsTimeUtil.getStringNowDate("HH:mm"))).thenReturn(true);
        when(mockEccWhiteService.selectLoginUrlByIp("currentIp")).thenReturn(1);
        List key = new ArrayList();
        key.add("*view");
        when(mockSwitchConfigService.selectSwitchConfigValueByKey(SwitchConfigEnum.SWITCH_VIEW.getName())).thenReturn(key);
        // Run the test
        final R<ExecuteAuditResultDto> result = toolsExecuteServiceImplUnderTest.getExecutButtonStatus(
                executAuditQueryDto);

        // Verify the results

        assertThat(result.getData().getButtenType()).isEqualTo(1);
    }

    /**
     * 高峰时期，ecc，查询诊断类  非*view用户 全部系统开关  返回双人复核
     */
    @Test
    void testGetExecutButtonStatus_TopTime_ECC_QUERY_NotView_AllSys() {
        // Setup
        final ExecutAuditQueryDto executAuditQueryDto = new ExecutAuditQueryDto();
        executAuditQueryDto.setCurrentIp("currentIp");
        executAuditQueryDto.setToolsId(0L);
        executAuditQueryDto.setExecFrom(0);
        executAuditQueryDto.setUserId(0L);
        executAuditQueryDto.setAuditId(0L);
        executAuditQueryDto.setAuditState(0);

        // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
        final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setType(0);
        toolsInfoCategoryEntity.setClassification(2);
        toolsInfoCategoryEntity.setScriptOperatingUser("root1");
        toolsInfoCategoryEntity.setStatus(0);
        toolsInfoCategoryEntity.setBusinessSystemId(0L);
        toolsInfoCategoryEntity.setDeliveryStatus(0);
        toolsInfoCategoryEntity.setApprovalState(0);
        when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);

        when(mockBusinessConfig.isToolsExecuteCheck()).thenReturn(true);
        when(mockToolsExecuteMapper.getExecuteAudit(any(ExecuteAuditEntity.class))).thenReturn(null);
        when(mockTopTimeService.checkHighTime(ControlsTimeUtil.getCurrentDayOfWeek()
                , ControlsTimeUtil.getStringNowDate("HH:mm"))).thenReturn(true);
        when(mockEccWhiteService.selectLoginUrlByIp("currentIp")).thenReturn(1);
        List key = new ArrayList();
        key.add("root");
        when(mockSwitchConfigService.selectSwitchConfigValueByKey(SwitchConfigEnum.SWITCH_VIEW.getName())).thenReturn(key);
        when(mockSwitchConfigService.selectSwitchConfigValueByKey(SwitchConfigEnum.SWITCH_IMPORTANT.getName()))
                .thenReturn(SwitchConfigValueEnum.SWITCH_IMPORTANT2.getCode());
        // Run the test
        final R<ExecuteAuditResultDto> result = toolsExecuteServiceImplUnderTest.getExecutButtonStatus(
                executAuditQueryDto);

        // Verify the results

        assertThat(result.getData().getButtenType()).isEqualTo(3);
    }

    /**
     * 高峰时期，ecc，查询诊断类  非*view用户 全部系统开关  返回双人复核
     */
    @Test
    void testGetExecutButtonStatus_TopTime_ECC_QUERY_NoView_AllSys() {
        // Setup
        final ExecutAuditQueryDto executAuditQueryDto = new ExecutAuditQueryDto();
        executAuditQueryDto.setCurrentIp("currentIp");
        executAuditQueryDto.setToolsId(0L);
        executAuditQueryDto.setExecFrom(0);
        executAuditQueryDto.setUserId(0L);
        executAuditQueryDto.setAuditId(0L);
        executAuditQueryDto.setAuditState(0);

        // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
        final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setType(0);
        toolsInfoCategoryEntity.setClassification(2);
        toolsInfoCategoryEntity.setScriptOperatingUser("root1");
        toolsInfoCategoryEntity.setStatus(0);
        toolsInfoCategoryEntity.setBusinessSystemId(0L);
        toolsInfoCategoryEntity.setDeliveryStatus(0);
        toolsInfoCategoryEntity.setApprovalState(0);
        when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);

        when(mockBusinessConfig.isToolsExecuteCheck()).thenReturn(true);
        when(mockToolsExecuteMapper.getExecuteAudit(any(ExecuteAuditEntity.class))).thenReturn(null);
        when(mockTopTimeService.checkHighTime(ControlsTimeUtil.getCurrentDayOfWeek()
                , ControlsTimeUtil.getStringNowDate("HH:mm"))).thenReturn(true);
        when(mockEccWhiteService.selectLoginUrlByIp("currentIp")).thenReturn(1);
        List key = new ArrayList();
        when(mockSwitchConfigService.selectSwitchConfigValueByKey(SwitchConfigEnum.SWITCH_VIEW.getName())).thenReturn(key);
        // Run the test
        final R<ExecuteAuditResultDto> result = toolsExecuteServiceImplUnderTest.getExecutButtonStatus(
                executAuditQueryDto);

        // Verify the results

        assertThat(result.getData().getButtenType()).isEqualTo(3);
    }

    /**
     * 高峰时期，ecc，查询诊断类  非*view用户 重要系统开关  重要系统 返回双人复核
     */
    @Test
    void testGetExecutButtonStatus_TopTime_ECC_QUERY_NotView_ImportantSys_Important() {
        // Setup
        final ExecutAuditQueryDto executAuditQueryDto = new ExecutAuditQueryDto();
        executAuditQueryDto.setCurrentIp("currentIp");
        executAuditQueryDto.setToolsId(0L);
        executAuditQueryDto.setExecFrom(0);
        executAuditQueryDto.setUserId(0L);
        executAuditQueryDto.setAuditId(0L);
        executAuditQueryDto.setAuditState(0);

        // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
        final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setType(0);
        toolsInfoCategoryEntity.setClassification(2);
        toolsInfoCategoryEntity.setScriptOperatingUser("root1");
        toolsInfoCategoryEntity.setStatus(0);
        toolsInfoCategoryEntity.setBusinessSystemId(0L);
        toolsInfoCategoryEntity.setDeliveryStatus(0);
        toolsInfoCategoryEntity.setApprovalState(0);
        when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);

        when(mockBusinessConfig.isToolsExecuteCheck()).thenReturn(true);
        when(mockToolsExecuteMapper.getExecuteAudit(any(ExecuteAuditEntity.class))).thenReturn(null);
        when(mockTopTimeService.checkHighTime(ControlsTimeUtil.getCurrentDayOfWeek()
                , ControlsTimeUtil.getStringNowDate("HH:mm"))).thenReturn(true);
        when(mockEccWhiteService.selectLoginUrlByIp("currentIp")).thenReturn(1);
        List key = new ArrayList();
        key.add("root");
        when(mockSwitchConfigService.selectSwitchConfigValueByKey(SwitchConfigEnum.SWITCH_VIEW.getName())).thenReturn(key);
        when(mockSwitchConfigService.selectSwitchConfigValueByKey(SwitchConfigEnum.SWITCH_IMPORTANT.getName()))
                .thenReturn(SwitchConfigValueEnum.SWITCH_IMPORTANT1.getCode());

        // Configure SystemDataInteract.getBusinessSystemInfo(...).
        when(mockSystemDataInteract.getBusinessSystemInfoForType(0L)).thenReturn(true);

        // Run the test
        final R<ExecuteAuditResultDto> result = toolsExecuteServiceImplUnderTest.getExecutButtonStatus(
                executAuditQueryDto);

        // Verify the results

        assertThat(result.getData().getButtenType()).isEqualTo(3);
    }

    /**
     * 高峰时期，ecc，查询诊断类  非*view用户 重要系统开关  非重要系统 返回双人复核
     */
    @Test
    void testGetExecutButtonStatus_TopTime_ECC_QUERY_NotView_ImportantSys_NotImportant() {
        // Setup
        final ExecutAuditQueryDto executAuditQueryDto = new ExecutAuditQueryDto();
        executAuditQueryDto.setCurrentIp("currentIp");
        executAuditQueryDto.setToolsId(0L);
        executAuditQueryDto.setExecFrom(0);
        executAuditQueryDto.setUserId(0L);
        executAuditQueryDto.setAuditId(0L);
        executAuditQueryDto.setAuditState(0);

        // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
        final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setType(0);
        toolsInfoCategoryEntity.setClassification(2);
        toolsInfoCategoryEntity.setScriptOperatingUser("root1");
        toolsInfoCategoryEntity.setStatus(0);
        toolsInfoCategoryEntity.setBusinessSystemId(0L);
        toolsInfoCategoryEntity.setDeliveryStatus(0);
        toolsInfoCategoryEntity.setApprovalState(0);
        when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);

        when(mockBusinessConfig.isToolsExecuteCheck()).thenReturn(true);
        when(mockToolsExecuteMapper.getExecuteAudit(any(ExecuteAuditEntity.class))).thenReturn(null);
        when(mockTopTimeService.checkHighTime(ControlsTimeUtil.getCurrentDayOfWeek()
                , ControlsTimeUtil.getStringNowDate("HH:mm"))).thenReturn(true);
        when(mockEccWhiteService.selectLoginUrlByIp("currentIp")).thenReturn(1);
        List key = new ArrayList();
        key.add("root");
        when(mockSwitchConfigService.selectSwitchConfigValueByKey(SwitchConfigEnum.SWITCH_VIEW.getName())).thenReturn(key);
        when(mockSwitchConfigService.selectSwitchConfigValueByKey(SwitchConfigEnum.SWITCH_IMPORTANT.getName()))
                .thenReturn(SwitchConfigValueEnum.SWITCH_IMPORTANT1.getCode());

        // Configure SystemDataInteract.getBusinessSystemInfo(...).
        when(mockSystemDataInteract.getBusinessSystemInfoForType(0L)).thenReturn(false);

        // Run the test
        final R<ExecuteAuditResultDto> result = toolsExecuteServiceImplUnderTest.getExecutButtonStatus(
                executAuditQueryDto);

        // Verify the results

        assertThat(result.getData().getButtenType()).isEqualTo(1);
    }

    /**
     * 高峰时期，ecc，查询诊断类  非*view用户 未开启开关  返回直接执行
     */
    @Test
    void testGetExecutButtonStatus_TopTime_ECC_QUERY_NotView_CloseSwitch() {
        // Setup
        final ExecutAuditQueryDto executAuditQueryDto = new ExecutAuditQueryDto();
        executAuditQueryDto.setCurrentIp("currentIp");
        executAuditQueryDto.setToolsId(0L);
        executAuditQueryDto.setExecFrom(0);
        executAuditQueryDto.setUserId(0L);
        executAuditQueryDto.setAuditId(0L);
        executAuditQueryDto.setAuditState(0);

        // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
        final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setType(0);
        toolsInfoCategoryEntity.setClassification(2);
        toolsInfoCategoryEntity.setScriptOperatingUser("root1");
        toolsInfoCategoryEntity.setStatus(0);
        toolsInfoCategoryEntity.setBusinessSystemId(0L);
        toolsInfoCategoryEntity.setDeliveryStatus(0);
        toolsInfoCategoryEntity.setApprovalState(0);
        when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);

        when(mockBusinessConfig.isToolsExecuteCheck()).thenReturn(true);
        when(mockToolsExecuteMapper.getExecuteAudit(any(ExecuteAuditEntity.class))).thenReturn(null);
        when(mockTopTimeService.checkHighTime(ControlsTimeUtil.getCurrentDayOfWeek()
                , ControlsTimeUtil.getStringNowDate("HH:mm"))).thenReturn(true);
        when(mockEccWhiteService.selectLoginUrlByIp("currentIp")).thenReturn(1);
        List key = new ArrayList();
        key.add("root");
        when(mockSwitchConfigService.selectSwitchConfigValueByKey(SwitchConfigEnum.SWITCH_VIEW.getName())).thenReturn(key);
        when(mockSwitchConfigService.selectSwitchConfigValueByKey(SwitchConfigEnum.SWITCH_IMPORTANT.getName()))
                .thenReturn(SwitchConfigValueEnum.SWITCH_IMPORTANT3.getCode());

        // Run the test
        final R<ExecuteAuditResultDto> result = toolsExecuteServiceImplUnderTest.getExecutButtonStatus(
                executAuditQueryDto);

        // Verify the results

        assertThat(result.getData().getButtenType()).isEqualTo(1);
    }

    /**
     * 非高峰时期，ecc，查询诊断类  *view用户 返回直接执行
     */
    @Test
    void testGetExecutButtonStatus_NotTopTime_ECC_QUERY_View() {
        // Setup
        final ExecutAuditQueryDto executAuditQueryDto = new ExecutAuditQueryDto();
        executAuditQueryDto.setCurrentIp("currentIp");
        executAuditQueryDto.setToolsId(0L);
        executAuditQueryDto.setExecFrom(0);
        executAuditQueryDto.setUserId(0L);
        executAuditQueryDto.setAuditId(0L);
        executAuditQueryDto.setAuditState(0);

        // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
        final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setType(0);
        toolsInfoCategoryEntity.setClassification(2);
        toolsInfoCategoryEntity.setScriptOperatingUser("root");
        toolsInfoCategoryEntity.setStatus(0);
        toolsInfoCategoryEntity.setBusinessSystemId(0L);
        toolsInfoCategoryEntity.setDeliveryStatus(0);
        toolsInfoCategoryEntity.setApprovalState(0);
        when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);

        when(mockBusinessConfig.isToolsExecuteCheck()).thenReturn(true);
        when(mockToolsExecuteMapper.getExecuteAudit(any(ExecuteAuditEntity.class))).thenReturn(null);
        when(mockTopTimeService.checkHighTime(ControlsTimeUtil.getCurrentDayOfWeek()
                , ControlsTimeUtil.getStringNowDate("HH:mm"))).thenReturn(false);
        when(mockEccWhiteService.selectLoginUrlByIp("currentIp")).thenReturn(1);
        List key = new ArrayList();
        key.add("root");
        when(mockSwitchConfigService.selectSwitchConfigValueByKey(SwitchConfigEnum.SWITCH_VIEW.getName())).thenReturn(key);
        // Run the test
        final R<ExecuteAuditResultDto> result = toolsExecuteServiceImplUnderTest.getExecutButtonStatus(
                executAuditQueryDto);

        // Verify the results

        assertThat(result.getData().getButtenType()).isEqualTo(1);
    }

    /**
     * 高峰时期，ecc，查询诊断类  非*view用户 全部系统开关  返回双人复核
     */
    @Test
    void testGetExecutButtonStatus_NotTopTime_ECC_QUERY_NotView_AllSys() {
        // Setup
        final ExecutAuditQueryDto executAuditQueryDto = new ExecutAuditQueryDto();
        executAuditQueryDto.setCurrentIp("currentIp");
        executAuditQueryDto.setToolsId(0L);
        executAuditQueryDto.setExecFrom(0);
        executAuditQueryDto.setUserId(0L);
        executAuditQueryDto.setAuditId(0L);
        executAuditQueryDto.setAuditState(0);

        // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
        final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setType(0);
        toolsInfoCategoryEntity.setClassification(2);
        toolsInfoCategoryEntity.setScriptOperatingUser("root1");
        toolsInfoCategoryEntity.setStatus(0);
        toolsInfoCategoryEntity.setBusinessSystemId(0L);
        toolsInfoCategoryEntity.setDeliveryStatus(0);
        toolsInfoCategoryEntity.setApprovalState(0);
        when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);

        when(mockBusinessConfig.isToolsExecuteCheck()).thenReturn(true);
        when(mockToolsExecuteMapper.getExecuteAudit(any(ExecuteAuditEntity.class))).thenReturn(null);
        when(mockTopTimeService.checkHighTime(ControlsTimeUtil.getCurrentDayOfWeek()
                , ControlsTimeUtil.getStringNowDate("HH:mm"))).thenReturn(false);
        when(mockEccWhiteService.selectLoginUrlByIp("currentIp")).thenReturn(1);
        List key = new ArrayList();
        key.add("root");
        when(mockSwitchConfigService.selectSwitchConfigValueByKey(SwitchConfigEnum.SWITCH_VIEW.getName())).thenReturn(key);
        when(mockSwitchConfigService.selectSwitchConfigValueByKey(SwitchConfigEnum.SWITCH_IMPORTANT.getName()))
                .thenReturn(SwitchConfigValueEnum.SWITCH_IMPORTANT2.getCode());
        // Run the test
        final R<ExecuteAuditResultDto> result = toolsExecuteServiceImplUnderTest.getExecutButtonStatus(
                executAuditQueryDto);

        // Verify the results

        assertThat(result.getData().getButtenType()).isEqualTo(3);
    }

    /**
     * 非高峰时期，ecc，查询诊断类  非*view用户 重要系统开关  重要系统 返回双人复核
     */
    @Test
    void testGetExecutButtonStatus_NotTopTime_ECC_QUERY_NotView_ImportantSys_Important() {
        // Setup
        final ExecutAuditQueryDto executAuditQueryDto = new ExecutAuditQueryDto();
        executAuditQueryDto.setCurrentIp("currentIp");
        executAuditQueryDto.setToolsId(0L);
        executAuditQueryDto.setExecFrom(0);
        executAuditQueryDto.setUserId(0L);
        executAuditQueryDto.setAuditId(0L);
        executAuditQueryDto.setAuditState(0);

        // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
        final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setType(0);
        toolsInfoCategoryEntity.setClassification(2);
        toolsInfoCategoryEntity.setScriptOperatingUser("root1");
        toolsInfoCategoryEntity.setStatus(0);
        toolsInfoCategoryEntity.setBusinessSystemId(0L);
        toolsInfoCategoryEntity.setDeliveryStatus(0);
        toolsInfoCategoryEntity.setApprovalState(0);
        when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);

        when(mockBusinessConfig.isToolsExecuteCheck()).thenReturn(true);
        when(mockToolsExecuteMapper.getExecuteAudit(any(ExecuteAuditEntity.class))).thenReturn(null);
        when(mockTopTimeService.checkHighTime(ControlsTimeUtil.getCurrentDayOfWeek()
                , ControlsTimeUtil.getStringNowDate("HH:mm"))).thenReturn(false);
        when(mockEccWhiteService.selectLoginUrlByIp("currentIp")).thenReturn(1);
        List key = new ArrayList();
        key.add("root");
        when(mockSwitchConfigService.selectSwitchConfigValueByKey(SwitchConfigEnum.SWITCH_VIEW.getName())).thenReturn(key);
        when(mockSwitchConfigService.selectSwitchConfigValueByKey(SwitchConfigEnum.SWITCH_IMPORTANT.getName()))
                .thenReturn(SwitchConfigValueEnum.SWITCH_IMPORTANT1.getCode());

        // Configure SystemDataInteract.getBusinessSystemInfo(...).
        when(mockSystemDataInteract.getBusinessSystemInfoForType(0L)).thenReturn(true);

        // Run the test
        final R<ExecuteAuditResultDto> result = toolsExecuteServiceImplUnderTest.getExecutButtonStatus(
                executAuditQueryDto);

        // Verify the results

        assertThat(result.getData().getButtenType()).isEqualTo(3);
    }

    /**
     * 非高峰时期，ecc，查询诊断类  非*view用户 重要系统开关  非重要系统 返回双人复核
     */
    @Test
    void testGetExecutButtonStatus_NotTopTime_ECC_QUERY_NotView_ImportantSys_NotImportant() {
        // Setup
        final ExecutAuditQueryDto executAuditQueryDto = new ExecutAuditQueryDto();
        executAuditQueryDto.setCurrentIp("currentIp");
        executAuditQueryDto.setToolsId(0L);
        executAuditQueryDto.setExecFrom(0);
        executAuditQueryDto.setUserId(0L);
        executAuditQueryDto.setAuditId(0L);
        executAuditQueryDto.setAuditState(0);

        // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
        final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setType(0);
        toolsInfoCategoryEntity.setClassification(2);
        toolsInfoCategoryEntity.setScriptOperatingUser("root1");
        toolsInfoCategoryEntity.setStatus(0);
        toolsInfoCategoryEntity.setBusinessSystemId(0L);
        toolsInfoCategoryEntity.setDeliveryStatus(0);
        toolsInfoCategoryEntity.setApprovalState(0);
        when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);

        when(mockBusinessConfig.isToolsExecuteCheck()).thenReturn(true);
        when(mockToolsExecuteMapper.getExecuteAudit(any(ExecuteAuditEntity.class))).thenReturn(null);
        when(mockTopTimeService.checkHighTime(ControlsTimeUtil.getCurrentDayOfWeek()
                , ControlsTimeUtil.getStringNowDate("HH:mm"))).thenReturn(false);
        when(mockEccWhiteService.selectLoginUrlByIp("currentIp")).thenReturn(1);
        List key = new ArrayList();
        key.add("root");
        when(mockSwitchConfigService.selectSwitchConfigValueByKey(SwitchConfigEnum.SWITCH_VIEW.getName())).thenReturn(key);
        when(mockSwitchConfigService.selectSwitchConfigValueByKey(SwitchConfigEnum.SWITCH_IMPORTANT.getName()))
                .thenReturn(SwitchConfigValueEnum.SWITCH_IMPORTANT1.getCode());

        // Configure SystemDataInteract.getBusinessSystemInfo(...).
        when(mockSystemDataInteract.getBusinessSystemInfoForType(0L)).thenReturn(false);

        // Run the test
        final R<ExecuteAuditResultDto> result = toolsExecuteServiceImplUnderTest.getExecutButtonStatus(
                executAuditQueryDto);

        // Verify the results

        assertThat(result.getData().getButtenType()).isEqualTo(1);
    }

    /**
     * 非高峰时期，ecc，查询诊断类  非*view用户 未开启开关  返回直接执行
     */
    @Test
    void testGetExecutButtonStatus_NotTopTime_ECC_QUERY_NotView_CloseSwitch() {
        // Setup
        final ExecutAuditQueryDto executAuditQueryDto = new ExecutAuditQueryDto();
        executAuditQueryDto.setCurrentIp("currentIp");
        executAuditQueryDto.setToolsId(0L);
        executAuditQueryDto.setExecFrom(0);
        executAuditQueryDto.setUserId(0L);
        executAuditQueryDto.setAuditId(0L);
        executAuditQueryDto.setAuditState(0);

        // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
        final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setType(0);
        toolsInfoCategoryEntity.setClassification(2);
        toolsInfoCategoryEntity.setScriptOperatingUser("root1");
        toolsInfoCategoryEntity.setStatus(0);
        toolsInfoCategoryEntity.setBusinessSystemId(0L);
        toolsInfoCategoryEntity.setDeliveryStatus(0);
        toolsInfoCategoryEntity.setApprovalState(0);
        when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);

        when(mockBusinessConfig.isToolsExecuteCheck()).thenReturn(true);
        when(mockToolsExecuteMapper.getExecuteAudit(any(ExecuteAuditEntity.class))).thenReturn(null);
        when(mockTopTimeService.checkHighTime(ControlsTimeUtil.getCurrentDayOfWeek()
                , ControlsTimeUtil.getStringNowDate("HH:mm"))).thenReturn(false);
        when(mockEccWhiteService.selectLoginUrlByIp("currentIp")).thenReturn(1);
        List key = new ArrayList();
        key.add("root");
        when(mockSwitchConfigService.selectSwitchConfigValueByKey(SwitchConfigEnum.SWITCH_VIEW.getName())).thenReturn(key);
        when(mockSwitchConfigService.selectSwitchConfigValueByKey(SwitchConfigEnum.SWITCH_IMPORTANT.getName()))
                .thenReturn(SwitchConfigValueEnum.SWITCH_IMPORTANT3.getCode());

        // Run the test
        final R<ExecuteAuditResultDto> result = toolsExecuteServiceImplUnderTest.getExecutButtonStatus(
                executAuditQueryDto);

        // Verify the results

        assertThat(result.getData().getButtenType()).isEqualTo(1);
    }


    /**
     * 高峰时期，生产变更，查询诊断类  *view用户 返回冷静期10秒
     */
    @Test
    void testGetExecutButtonStatus_TopTime_Change_QUERY_View() {
        // Setup
        final ExecutAuditQueryDto executAuditQueryDto = new ExecutAuditQueryDto();
        executAuditQueryDto.setCurrentIp("currentIp");
        executAuditQueryDto.setToolsId(0L);
        executAuditQueryDto.setExecFrom(0);
        executAuditQueryDto.setUserId(0L);
        executAuditQueryDto.setAuditId(0L);
        executAuditQueryDto.setAuditState(0);

        // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
        final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setType(0);
        toolsInfoCategoryEntity.setClassification(2);
        toolsInfoCategoryEntity.setScriptOperatingUser("root");
        toolsInfoCategoryEntity.setStatus(0);
        toolsInfoCategoryEntity.setBusinessSystemId(0L);
        toolsInfoCategoryEntity.setDeliveryStatus(0);
        toolsInfoCategoryEntity.setApprovalState(0);
        when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);

        when(mockBusinessConfig.isToolsExecuteCheck()).thenReturn(true);
        when(mockToolsExecuteMapper.getExecuteAudit(any(ExecuteAuditEntity.class))).thenReturn(null);
        when(mockTopTimeService.checkHighTime(ControlsTimeUtil.getCurrentDayOfWeek()
                , ControlsTimeUtil.getStringNowDate("HH:mm"))).thenReturn(true);
        when(mockEccWhiteService.selectLoginUrlByIp("currentIp")).thenReturn(2);
        List key = new ArrayList();
        key.add("root");
        when(mockSwitchConfigService.selectSwitchConfigValueByKey(SwitchConfigEnum.SWITCH_VIEW.getName())).thenReturn(key);
        // Run the test
        final R<ExecuteAuditResultDto> result = toolsExecuteServiceImplUnderTest.getExecutButtonStatus(
                executAuditQueryDto);

        // Verify the results

        assertThat(result.getData().getButtenType()).isEqualTo(2);
    }

    /**
     * 高峰时期，生产变更，查询诊断类  非*view用户 全部系统开关  返回双人复核
     */
    @Test
    void testGetExecutButtonStatus_TopTime_Change_QUERY_NotView_AllSys() {
        // Setup
        final ExecutAuditQueryDto executAuditQueryDto = new ExecutAuditQueryDto();
        executAuditQueryDto.setCurrentIp("currentIp");
        executAuditQueryDto.setToolsId(0L);
        executAuditQueryDto.setExecFrom(0);
        executAuditQueryDto.setUserId(0L);
        executAuditQueryDto.setAuditId(0L);
        executAuditQueryDto.setAuditState(0);

        // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
        final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setType(0);
        toolsInfoCategoryEntity.setClassification(2);
        toolsInfoCategoryEntity.setScriptOperatingUser("root1");
        toolsInfoCategoryEntity.setStatus(0);
        toolsInfoCategoryEntity.setBusinessSystemId(0L);
        toolsInfoCategoryEntity.setDeliveryStatus(0);
        toolsInfoCategoryEntity.setApprovalState(0);
        when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);

        when(mockBusinessConfig.isToolsExecuteCheck()).thenReturn(true);
        when(mockToolsExecuteMapper.getExecuteAudit(any(ExecuteAuditEntity.class))).thenReturn(null);
        when(mockTopTimeService.checkHighTime(ControlsTimeUtil.getCurrentDayOfWeek()
                , ControlsTimeUtil.getStringNowDate("HH:mm"))).thenReturn(true);
        when(mockEccWhiteService.selectLoginUrlByIp("currentIp")).thenReturn(2);
        List key = new ArrayList();
        key.add("root");
        when(mockSwitchConfigService.selectSwitchConfigValueByKey(SwitchConfigEnum.SWITCH_VIEW.getName())).thenReturn(key);
        when(mockSwitchConfigService.selectSwitchConfigValueByKey(SwitchConfigEnum.SWITCH_IMPORTANT.getName()))
                .thenReturn(SwitchConfigValueEnum.SWITCH_IMPORTANT2.getCode());
        // Run the test
        final R<ExecuteAuditResultDto> result = toolsExecuteServiceImplUnderTest.getExecutButtonStatus(
                executAuditQueryDto);

        // Verify the results

        assertThat(result.getData().getButtenType()).isEqualTo(3);
    }

    /**
     * 高峰时期，生产变更，查询诊断类  非*view用户 重要系统开关  重要系统 返回双人复核
     */
    @Test
    void testGetExecutButtonStatus_TopTime_Change_QUERY_NotView_ImportantSys_Important() {
        // Setup
        final ExecutAuditQueryDto executAuditQueryDto = new ExecutAuditQueryDto();
        executAuditQueryDto.setCurrentIp("currentIp");
        executAuditQueryDto.setToolsId(0L);
        executAuditQueryDto.setExecFrom(0);
        executAuditQueryDto.setUserId(0L);
        executAuditQueryDto.setAuditId(0L);
        executAuditQueryDto.setAuditState(0);

        // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
        final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setType(0);
        toolsInfoCategoryEntity.setClassification(2);
        toolsInfoCategoryEntity.setScriptOperatingUser("root1");
        toolsInfoCategoryEntity.setStatus(0);
        toolsInfoCategoryEntity.setBusinessSystemId(0L);
        toolsInfoCategoryEntity.setDeliveryStatus(0);
        toolsInfoCategoryEntity.setApprovalState(0);
        when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);

        when(mockBusinessConfig.isToolsExecuteCheck()).thenReturn(true);
        when(mockToolsExecuteMapper.getExecuteAudit(any(ExecuteAuditEntity.class))).thenReturn(null);
        when(mockTopTimeService.checkHighTime(ControlsTimeUtil.getCurrentDayOfWeek()
                , ControlsTimeUtil.getStringNowDate("HH:mm"))).thenReturn(true);
        when(mockEccWhiteService.selectLoginUrlByIp("currentIp")).thenReturn(2);
        List key = new ArrayList();
        key.add("root");
        when(mockSwitchConfigService.selectSwitchConfigValueByKey(SwitchConfigEnum.SWITCH_VIEW.getName())).thenReturn(key);
        when(mockSwitchConfigService.selectSwitchConfigValueByKey(SwitchConfigEnum.SWITCH_IMPORTANT.getName()))
                .thenReturn(SwitchConfigValueEnum.SWITCH_IMPORTANT1.getCode());

        // Configure SystemDataInteract.getBusinessSystemInfo(...).
        when(mockSystemDataInteract.getBusinessSystemInfoForType(0L)).thenReturn(true);

        // Run the test
        final R<ExecuteAuditResultDto> result = toolsExecuteServiceImplUnderTest.getExecutButtonStatus(
                executAuditQueryDto);

        // Verify the results

        assertThat(result.getData().getButtenType()).isEqualTo(3);
    }

    /**
     * 高峰时期，生产变更，查询诊断类  非*view用户 重要系统开关  非重要系统 返回冷静期10秒
     */
    @Test
    void testGetExecutButtonStatus_TopTime_Change_QUERY_NotView_ImportantSys_NotImportant() {
        // Setup
        final ExecutAuditQueryDto executAuditQueryDto = new ExecutAuditQueryDto();
        executAuditQueryDto.setCurrentIp("currentIp");
        executAuditQueryDto.setToolsId(0L);
        executAuditQueryDto.setExecFrom(0);
        executAuditQueryDto.setUserId(0L);
        executAuditQueryDto.setAuditId(0L);
        executAuditQueryDto.setAuditState(0);

        // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
        final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setType(0);
        toolsInfoCategoryEntity.setClassification(2);
        toolsInfoCategoryEntity.setScriptOperatingUser("root1");
        toolsInfoCategoryEntity.setStatus(0);
        toolsInfoCategoryEntity.setBusinessSystemId(0L);
        toolsInfoCategoryEntity.setDeliveryStatus(0);
        toolsInfoCategoryEntity.setApprovalState(0);
        when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);

        when(mockBusinessConfig.isToolsExecuteCheck()).thenReturn(true);
        when(mockToolsExecuteMapper.getExecuteAudit(any(ExecuteAuditEntity.class))).thenReturn(null);
        when(mockTopTimeService.checkHighTime(ControlsTimeUtil.getCurrentDayOfWeek()
                , ControlsTimeUtil.getStringNowDate("HH:mm"))).thenReturn(true);
        when(mockEccWhiteService.selectLoginUrlByIp("currentIp")).thenReturn(2);
        List key = new ArrayList();
        key.add("root");
        when(mockSwitchConfigService.selectSwitchConfigValueByKey(SwitchConfigEnum.SWITCH_VIEW.getName())).thenReturn(key);
        when(mockSwitchConfigService.selectSwitchConfigValueByKey(SwitchConfigEnum.SWITCH_IMPORTANT.getName()))
                .thenReturn(SwitchConfigValueEnum.SWITCH_IMPORTANT1.getCode());

        // Configure SystemDataInteract.getBusinessSystemInfo(...).
        when(mockSystemDataInteract.getBusinessSystemInfoForType(0L)).thenReturn(false);

        // Run the test
        final R<ExecuteAuditResultDto> result = toolsExecuteServiceImplUnderTest.getExecutButtonStatus(
                executAuditQueryDto);

        // Verify the results

        assertThat(result.getData().getButtenType()).isEqualTo(2);
    }

    /**
     * 高峰时期，生产变更，查询诊断类  非*view用户 未开启开关  返回冷静期10秒
     */
    @Test
    void testGetExecutButtonStatus_TopTime_Change_QUERY_NotView_CloseSwitch() {
        // Setup
        final ExecutAuditQueryDto executAuditQueryDto = new ExecutAuditQueryDto();
        executAuditQueryDto.setCurrentIp("currentIp");
        executAuditQueryDto.setToolsId(0L);
        executAuditQueryDto.setExecFrom(0);
        executAuditQueryDto.setUserId(0L);
        executAuditQueryDto.setAuditId(0L);
        executAuditQueryDto.setAuditState(0);

        // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
        final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setType(0);
        toolsInfoCategoryEntity.setClassification(2);
        toolsInfoCategoryEntity.setScriptOperatingUser("root1");
        toolsInfoCategoryEntity.setStatus(0);
        toolsInfoCategoryEntity.setBusinessSystemId(0L);
        toolsInfoCategoryEntity.setDeliveryStatus(0);
        toolsInfoCategoryEntity.setApprovalState(0);
        when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);

        when(mockBusinessConfig.isToolsExecuteCheck()).thenReturn(true);
        when(mockToolsExecuteMapper.getExecuteAudit(any(ExecuteAuditEntity.class))).thenReturn(null);
        when(mockTopTimeService.checkHighTime(ControlsTimeUtil.getCurrentDayOfWeek()
                , ControlsTimeUtil.getStringNowDate("HH:mm"))).thenReturn(true);
        when(mockEccWhiteService.selectLoginUrlByIp("currentIp")).thenReturn(2);
        List key = new ArrayList();
        key.add("root");
        when(mockSwitchConfigService.selectSwitchConfigValueByKey(SwitchConfigEnum.SWITCH_VIEW.getName())).thenReturn(key);
        when(mockSwitchConfigService.selectSwitchConfigValueByKey(SwitchConfigEnum.SWITCH_IMPORTANT.getName()))
                .thenReturn(SwitchConfigValueEnum.SWITCH_IMPORTANT3.getCode());

        // Run the test
        final R<ExecuteAuditResultDto> result = toolsExecuteServiceImplUnderTest.getExecutButtonStatus(
                executAuditQueryDto);

        // Verify the results

        assertThat(result.getData().getButtenType()).isEqualTo(2);
    }

    /**
     * 非高峰时期，生产变更，查询诊断类  *view用户 返回直接执行
     */
    @Test
    void testGetExecutButtonStatus_NotTopTime_Change_QUERY_View() {
        // Setup
        final ExecutAuditQueryDto executAuditQueryDto = new ExecutAuditQueryDto();
        executAuditQueryDto.setCurrentIp("currentIp");
        executAuditQueryDto.setToolsId(0L);
        executAuditQueryDto.setExecFrom(0);
        executAuditQueryDto.setUserId(0L);
        executAuditQueryDto.setAuditId(0L);
        executAuditQueryDto.setAuditState(0);

        // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
        final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setType(0);
        toolsInfoCategoryEntity.setClassification(2);
        toolsInfoCategoryEntity.setScriptOperatingUser("root");
        toolsInfoCategoryEntity.setStatus(0);
        toolsInfoCategoryEntity.setBusinessSystemId(0L);
        toolsInfoCategoryEntity.setDeliveryStatus(0);
        toolsInfoCategoryEntity.setApprovalState(0);
        when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);

        when(mockBusinessConfig.isToolsExecuteCheck()).thenReturn(true);
        when(mockToolsExecuteMapper.getExecuteAudit(any(ExecuteAuditEntity.class))).thenReturn(null);
        when(mockTopTimeService.checkHighTime(ControlsTimeUtil.getCurrentDayOfWeek()
                , ControlsTimeUtil.getStringNowDate("HH:mm"))).thenReturn(false);
        when(mockEccWhiteService.selectLoginUrlByIp("currentIp")).thenReturn(2);
        List key = new ArrayList();
        key.add("root");
        when(mockSwitchConfigService.selectSwitchConfigValueByKey(SwitchConfigEnum.SWITCH_VIEW.getName())).thenReturn(key);
        // Run the test
        final R<ExecuteAuditResultDto> result = toolsExecuteServiceImplUnderTest.getExecutButtonStatus(
                executAuditQueryDto);

        // Verify the results

        assertThat(result.getData().getButtenType()).isEqualTo(1);
    }

    /**
     * 高峰时期，生产变更，查询诊断类  非*view用户 全部系统开关  返回双人复核
     */
    @Test
    void testGetExecutButtonStatus_NotTopTime_Change_QUERY_NotView_AllSys() {
        // Setup
        final ExecutAuditQueryDto executAuditQueryDto = new ExecutAuditQueryDto();
        executAuditQueryDto.setCurrentIp("currentIp");
        executAuditQueryDto.setToolsId(0L);
        executAuditQueryDto.setExecFrom(0);
        executAuditQueryDto.setUserId(0L);
        executAuditQueryDto.setAuditId(0L);
        executAuditQueryDto.setAuditState(0);

        // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
        final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setType(0);
        toolsInfoCategoryEntity.setClassification(2);
        toolsInfoCategoryEntity.setScriptOperatingUser("root1");
        toolsInfoCategoryEntity.setStatus(0);
        toolsInfoCategoryEntity.setBusinessSystemId(0L);
        toolsInfoCategoryEntity.setDeliveryStatus(0);
        toolsInfoCategoryEntity.setApprovalState(0);
        when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);

        when(mockBusinessConfig.isToolsExecuteCheck()).thenReturn(true);
        when(mockToolsExecuteMapper.getExecuteAudit(any(ExecuteAuditEntity.class))).thenReturn(null);
        when(mockTopTimeService.checkHighTime(ControlsTimeUtil.getCurrentDayOfWeek()
                , ControlsTimeUtil.getStringNowDate("HH:mm"))).thenReturn(false);
        when(mockEccWhiteService.selectLoginUrlByIp("currentIp")).thenReturn(2);
        List key = new ArrayList();
        key.add("root");
        when(mockSwitchConfigService.selectSwitchConfigValueByKey(SwitchConfigEnum.SWITCH_VIEW.getName())).thenReturn(key);
        when(mockSwitchConfigService.selectSwitchConfigValueByKey(SwitchConfigEnum.SWITCH_IMPORTANT.getName()))
                .thenReturn(SwitchConfigValueEnum.SWITCH_IMPORTANT2.getCode());
        // Run the test
        final R<ExecuteAuditResultDto> result = toolsExecuteServiceImplUnderTest.getExecutButtonStatus(
                executAuditQueryDto);

        // Verify the results

        assertThat(result.getData().getButtenType()).isEqualTo(3);
    }

    /**
     * 非高峰时期，生产变更，查询诊断类  非*view用户 重要系统开关  重要系统 返回双人复核
     */
    @Test
    void testGetExecutButtonStatus_NotTopTime_Change_QUERY_NotView_ImportantSys_Important() {
        // Setup
        final ExecutAuditQueryDto executAuditQueryDto = new ExecutAuditQueryDto();
        executAuditQueryDto.setCurrentIp("currentIp");
        executAuditQueryDto.setToolsId(0L);
        executAuditQueryDto.setExecFrom(0);
        executAuditQueryDto.setUserId(0L);
        executAuditQueryDto.setAuditId(0L);
        executAuditQueryDto.setAuditState(0);

        // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
        final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setType(0);
        toolsInfoCategoryEntity.setClassification(2);
        toolsInfoCategoryEntity.setScriptOperatingUser("root1");
        toolsInfoCategoryEntity.setStatus(0);
        toolsInfoCategoryEntity.setBusinessSystemId(0L);
        toolsInfoCategoryEntity.setDeliveryStatus(0);
        toolsInfoCategoryEntity.setApprovalState(0);
        when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);

        when(mockBusinessConfig.isToolsExecuteCheck()).thenReturn(true);
        when(mockToolsExecuteMapper.getExecuteAudit(any(ExecuteAuditEntity.class))).thenReturn(null);
        when(mockTopTimeService.checkHighTime(ControlsTimeUtil.getCurrentDayOfWeek()
                , ControlsTimeUtil.getStringNowDate("HH:mm"))).thenReturn(false);
        when(mockEccWhiteService.selectLoginUrlByIp("currentIp")).thenReturn(2);
        List key = new ArrayList();
        key.add("root");
        when(mockSwitchConfigService.selectSwitchConfigValueByKey(SwitchConfigEnum.SWITCH_VIEW.getName())).thenReturn(key);
        when(mockSwitchConfigService.selectSwitchConfigValueByKey(SwitchConfigEnum.SWITCH_IMPORTANT.getName()))
                .thenReturn(SwitchConfigValueEnum.SWITCH_IMPORTANT1.getCode());

        // Configure SystemDataInteract.getBusinessSystemInfo(...).
        when(mockSystemDataInteract.getBusinessSystemInfoForType(0L)).thenReturn(true);

        // Run the test
        final R<ExecuteAuditResultDto> result = toolsExecuteServiceImplUnderTest.getExecutButtonStatus(
                executAuditQueryDto);

        // Verify the results

        assertThat(result.getData().getButtenType()).isEqualTo(3);
    }

    /**
     * 非高峰时期，生产变更，查询诊断类  非*view用户 重要系统开关  非重要系统 返回双人复核
     */
    @Test
    void testGetExecutButtonStatus_NotTopTime_Change_QUERY_NotView_ImportantSys_NotImportant() {
        // Setup
        final ExecutAuditQueryDto executAuditQueryDto = new ExecutAuditQueryDto();
        executAuditQueryDto.setCurrentIp("currentIp");
        executAuditQueryDto.setToolsId(0L);
        executAuditQueryDto.setExecFrom(0);
        executAuditQueryDto.setUserId(0L);
        executAuditQueryDto.setAuditId(0L);
        executAuditQueryDto.setAuditState(0);

        // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
        final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setType(0);
        toolsInfoCategoryEntity.setClassification(2);
        toolsInfoCategoryEntity.setScriptOperatingUser("root1");
        toolsInfoCategoryEntity.setStatus(0);
        toolsInfoCategoryEntity.setBusinessSystemId(0L);
        toolsInfoCategoryEntity.setDeliveryStatus(0);
        toolsInfoCategoryEntity.setApprovalState(0);
        when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);

        when(mockBusinessConfig.isToolsExecuteCheck()).thenReturn(true);
        when(mockToolsExecuteMapper.getExecuteAudit(any(ExecuteAuditEntity.class))).thenReturn(null);
        when(mockTopTimeService.checkHighTime(ControlsTimeUtil.getCurrentDayOfWeek()
                , ControlsTimeUtil.getStringNowDate("HH:mm"))).thenReturn(false);
        when(mockEccWhiteService.selectLoginUrlByIp("currentIp")).thenReturn(2);
        List key = new ArrayList();
        key.add("root");
        when(mockSwitchConfigService.selectSwitchConfigValueByKey(SwitchConfigEnum.SWITCH_VIEW.getName())).thenReturn(key);
        when(mockSwitchConfigService.selectSwitchConfigValueByKey(SwitchConfigEnum.SWITCH_IMPORTANT.getName()))
                .thenReturn(SwitchConfigValueEnum.SWITCH_IMPORTANT1.getCode());

        // Configure SystemDataInteract.getBusinessSystemInfo(...).
        when(mockSystemDataInteract.getBusinessSystemInfoForType(0L)).thenReturn(false);

        // Run the test
        final R<ExecuteAuditResultDto> result = toolsExecuteServiceImplUnderTest.getExecutButtonStatus(
                executAuditQueryDto);

        // Verify the results

        assertThat(result.getData().getButtenType()).isEqualTo(1);
    }

    /**
     * 非高峰时期，生产变更，查询诊断类  非*view用户 未开启开关  返回直接执行
     */
    @Test
    void testGetExecutButtonStatus_NotTopTime_Change_QUERY_NotView_CloseSwitch() {
        // Setup
        final ExecutAuditQueryDto executAuditQueryDto = new ExecutAuditQueryDto();
        executAuditQueryDto.setCurrentIp("currentIp");
        executAuditQueryDto.setToolsId(0L);
        executAuditQueryDto.setExecFrom(0);
        executAuditQueryDto.setUserId(0L);
        executAuditQueryDto.setAuditId(0L);
        executAuditQueryDto.setAuditState(0);

        // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
        final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setType(0);
        toolsInfoCategoryEntity.setClassification(2);
        toolsInfoCategoryEntity.setScriptOperatingUser("root1");
        toolsInfoCategoryEntity.setStatus(0);
        toolsInfoCategoryEntity.setBusinessSystemId(0L);
        toolsInfoCategoryEntity.setDeliveryStatus(0);
        toolsInfoCategoryEntity.setApprovalState(0);
        when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);

        when(mockBusinessConfig.isToolsExecuteCheck()).thenReturn(true);
        when(mockToolsExecuteMapper.getExecuteAudit(any(ExecuteAuditEntity.class))).thenReturn(null);
        when(mockTopTimeService.checkHighTime(ControlsTimeUtil.getCurrentDayOfWeek()
                , ControlsTimeUtil.getStringNowDate("HH:mm"))).thenReturn(false);
        when(mockEccWhiteService.selectLoginUrlByIp("currentIp")).thenReturn(2);
        List key = new ArrayList();
        key.add("root");
        when(mockSwitchConfigService.selectSwitchConfigValueByKey(SwitchConfigEnum.SWITCH_VIEW.getName())).thenReturn(key);
        when(mockSwitchConfigService.selectSwitchConfigValueByKey(SwitchConfigEnum.SWITCH_IMPORTANT.getName()))
                .thenReturn(SwitchConfigValueEnum.SWITCH_IMPORTANT3.getCode());

        // Run the test
        final R<ExecuteAuditResultDto> result = toolsExecuteServiceImplUnderTest.getExecutButtonStatus(
                executAuditQueryDto);

        // Verify the results

        assertThat(result.getData().getButtenType()).isEqualTo(1);
    }


    /**
     * 高峰时期，生产查询，查询诊断类  *view用户 返回双人复核
     */
    @Test
    void testGetExecutButtonStatus_TopTime_Query_QUERY_View() {
        // Setup
        final ExecutAuditQueryDto executAuditQueryDto = new ExecutAuditQueryDto();
        executAuditQueryDto.setCurrentIp("currentIp");
        executAuditQueryDto.setToolsId(0L);
        executAuditQueryDto.setExecFrom(0);
        executAuditQueryDto.setUserId(0L);
        executAuditQueryDto.setAuditId(0L);
        executAuditQueryDto.setAuditState(0);

        // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
        final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setType(0);
        toolsInfoCategoryEntity.setClassification(2);
        toolsInfoCategoryEntity.setScriptOperatingUser("root");
        toolsInfoCategoryEntity.setStatus(0);
        toolsInfoCategoryEntity.setBusinessSystemId(0L);
        toolsInfoCategoryEntity.setDeliveryStatus(0);
        toolsInfoCategoryEntity.setApprovalState(0);
        when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);

        when(mockBusinessConfig.isToolsExecuteCheck()).thenReturn(true);
        when(mockToolsExecuteMapper.getExecuteAudit(any(ExecuteAuditEntity.class))).thenReturn(null);
        when(mockTopTimeService.checkHighTime(ControlsTimeUtil.getCurrentDayOfWeek()
                , ControlsTimeUtil.getStringNowDate("HH:mm"))).thenReturn(true);
        when(mockEccWhiteService.selectLoginUrlByIp("currentIp")).thenReturn(3);
        // Run the test
        final R<ExecuteAuditResultDto> result = toolsExecuteServiceImplUnderTest.getExecutButtonStatus(
                executAuditQueryDto);

        // Verify the results

        assertThat(result.getData().getButtenType()).isEqualTo(3);
    }

    /**
     * 高峰时期，生产查询，查询诊断类  非*view用户 全部系统开关  返回双人复核
     */
    @Test
    void testGetExecutButtonStatus_TopTime_Query_QUERY_NotView_AllSys() {
        // Setup
        final ExecutAuditQueryDto executAuditQueryDto = new ExecutAuditQueryDto();
        executAuditQueryDto.setCurrentIp("currentIp");
        executAuditQueryDto.setToolsId(0L);
        executAuditQueryDto.setExecFrom(0);
        executAuditQueryDto.setUserId(0L);
        executAuditQueryDto.setAuditId(0L);
        executAuditQueryDto.setAuditState(0);

        // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
        final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setType(0);
        toolsInfoCategoryEntity.setClassification(2);
        toolsInfoCategoryEntity.setScriptOperatingUser("root1");
        toolsInfoCategoryEntity.setStatus(0);
        toolsInfoCategoryEntity.setBusinessSystemId(0L);
        toolsInfoCategoryEntity.setDeliveryStatus(0);
        toolsInfoCategoryEntity.setApprovalState(0);
        when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);

        when(mockBusinessConfig.isToolsExecuteCheck()).thenReturn(true);
        when(mockToolsExecuteMapper.getExecuteAudit(any(ExecuteAuditEntity.class))).thenReturn(null);
        when(mockTopTimeService.checkHighTime(ControlsTimeUtil.getCurrentDayOfWeek()
                , ControlsTimeUtil.getStringNowDate("HH:mm"))).thenReturn(true);
        when(mockEccWhiteService.selectLoginUrlByIp("currentIp")).thenReturn(3);
        // Run the test
        final R<ExecuteAuditResultDto> result = toolsExecuteServiceImplUnderTest.getExecutButtonStatus(
                executAuditQueryDto);

        // Verify the results

        assertThat(result.getData().getButtenType()).isEqualTo(3);
    }

    /**
     * 高峰时期，生产查询，查询诊断类  非*view用户 重要系统开关  重要系统 返回双人复核
     */
    @Test
    void testGetExecutButtonStatus_TopTime_Query_QUERY_NotView_ImportantSys_Important() {
        // Setup
        final ExecutAuditQueryDto executAuditQueryDto = new ExecutAuditQueryDto();
        executAuditQueryDto.setCurrentIp("currentIp");
        executAuditQueryDto.setToolsId(0L);
        executAuditQueryDto.setExecFrom(0);
        executAuditQueryDto.setUserId(0L);
        executAuditQueryDto.setAuditId(0L);
        executAuditQueryDto.setAuditState(0);

        // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
        final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setType(0);
        toolsInfoCategoryEntity.setClassification(2);
        toolsInfoCategoryEntity.setScriptOperatingUser("root1");
        toolsInfoCategoryEntity.setStatus(0);
        toolsInfoCategoryEntity.setBusinessSystemId(0L);
        toolsInfoCategoryEntity.setDeliveryStatus(0);
        toolsInfoCategoryEntity.setApprovalState(0);
        when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);

        when(mockBusinessConfig.isToolsExecuteCheck()).thenReturn(true);
        when(mockToolsExecuteMapper.getExecuteAudit(any(ExecuteAuditEntity.class))).thenReturn(null);
        when(mockTopTimeService.checkHighTime(ControlsTimeUtil.getCurrentDayOfWeek()
                , ControlsTimeUtil.getStringNowDate("HH:mm"))).thenReturn(true);
        when(mockEccWhiteService.selectLoginUrlByIp("currentIp")).thenReturn(3);

        // Run the test
        final R<ExecuteAuditResultDto> result = toolsExecuteServiceImplUnderTest.getExecutButtonStatus(
                executAuditQueryDto);

        // Verify the results

        assertThat(result.getData().getButtenType()).isEqualTo(3);
    }

    /**
     * 高峰时期，生产查询，查询诊断类  非*view用户 重要系统开关  非重要系统 返回双人复核
     */
    @Test
    void testGetExecutButtonStatus_TopTime_Query_QUERY_NotView_ImportantSys_NotImportant() {
        // Setup
        final ExecutAuditQueryDto executAuditQueryDto = new ExecutAuditQueryDto();
        executAuditQueryDto.setCurrentIp("currentIp");
        executAuditQueryDto.setToolsId(0L);
        executAuditQueryDto.setExecFrom(0);
        executAuditQueryDto.setUserId(0L);
        executAuditQueryDto.setAuditId(0L);
        executAuditQueryDto.setAuditState(0);

        // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
        final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setType(0);
        toolsInfoCategoryEntity.setClassification(2);
        toolsInfoCategoryEntity.setScriptOperatingUser("root1");
        toolsInfoCategoryEntity.setStatus(0);
        toolsInfoCategoryEntity.setBusinessSystemId(0L);
        toolsInfoCategoryEntity.setDeliveryStatus(0);
        toolsInfoCategoryEntity.setApprovalState(0);
        when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);

        when(mockBusinessConfig.isToolsExecuteCheck()).thenReturn(true);
        when(mockToolsExecuteMapper.getExecuteAudit(any(ExecuteAuditEntity.class))).thenReturn(null);
        when(mockTopTimeService.checkHighTime(ControlsTimeUtil.getCurrentDayOfWeek()
                , ControlsTimeUtil.getStringNowDate("HH:mm"))).thenReturn(true);
        when(mockEccWhiteService.selectLoginUrlByIp("currentIp")).thenReturn(3);

        // Run the test
        final R<ExecuteAuditResultDto> result = toolsExecuteServiceImplUnderTest.getExecutButtonStatus(
                executAuditQueryDto);

        // Verify the results

        assertThat(result.getData().getButtenType()).isEqualTo(3);
    }

    /**
     * 高峰时期，生产查询，查询诊断类  非*view用户 未开启开关  返回双人复核
     */
    @Test
    void testGetExecutButtonStatus_TopTime_Query_QUERY_NotView_CloseSwitch() {
        // Setup
        final ExecutAuditQueryDto executAuditQueryDto = new ExecutAuditQueryDto();
        executAuditQueryDto.setCurrentIp("currentIp");
        executAuditQueryDto.setToolsId(0L);
        executAuditQueryDto.setExecFrom(0);
        executAuditQueryDto.setUserId(0L);
        executAuditQueryDto.setAuditId(0L);
        executAuditQueryDto.setAuditState(0);

        // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
        final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setType(0);
        toolsInfoCategoryEntity.setClassification(2);
        toolsInfoCategoryEntity.setScriptOperatingUser("root1");
        toolsInfoCategoryEntity.setStatus(0);
        toolsInfoCategoryEntity.setBusinessSystemId(0L);
        toolsInfoCategoryEntity.setDeliveryStatus(0);
        toolsInfoCategoryEntity.setApprovalState(0);
        when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);

        when(mockBusinessConfig.isToolsExecuteCheck()).thenReturn(true);
        when(mockToolsExecuteMapper.getExecuteAudit(any(ExecuteAuditEntity.class))).thenReturn(null);
        when(mockTopTimeService.checkHighTime(ControlsTimeUtil.getCurrentDayOfWeek()
                , ControlsTimeUtil.getStringNowDate("HH:mm"))).thenReturn(true);
        when(mockEccWhiteService.selectLoginUrlByIp("currentIp")).thenReturn(3);

        // Run the test
        final R<ExecuteAuditResultDto> result = toolsExecuteServiceImplUnderTest.getExecutButtonStatus(
                executAuditQueryDto);

        // Verify the results

        assertThat(result.getData().getButtenType()).isEqualTo(3);
    }

    /**
     * 非高峰时期，生产查询，查询诊断类  *view用户 返回双人复核
     */
    @Test
    void testGetExecutButtonStatus_NotTopTime_Query_QUERY_View() {
        // Setup
        final ExecutAuditQueryDto executAuditQueryDto = new ExecutAuditQueryDto();
        executAuditQueryDto.setCurrentIp("currentIp");
        executAuditQueryDto.setToolsId(0L);
        executAuditQueryDto.setExecFrom(0);
        executAuditQueryDto.setUserId(0L);
        executAuditQueryDto.setAuditId(0L);
        executAuditQueryDto.setAuditState(0);

        // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
        final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setType(0);
        toolsInfoCategoryEntity.setClassification(2);
        toolsInfoCategoryEntity.setScriptOperatingUser("root");
        toolsInfoCategoryEntity.setStatus(0);
        toolsInfoCategoryEntity.setBusinessSystemId(0L);
        toolsInfoCategoryEntity.setDeliveryStatus(0);
        toolsInfoCategoryEntity.setApprovalState(0);
        when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);

        when(mockBusinessConfig.isToolsExecuteCheck()).thenReturn(true);
        when(mockToolsExecuteMapper.getExecuteAudit(any(ExecuteAuditEntity.class))).thenReturn(null);
        when(mockTopTimeService.checkHighTime(ControlsTimeUtil.getCurrentDayOfWeek()
                , ControlsTimeUtil.getStringNowDate("HH:mm"))).thenReturn(false);
        when(mockEccWhiteService.selectLoginUrlByIp("currentIp")).thenReturn(3);
        List key = new ArrayList();
        key.add("root");
        when(mockSwitchConfigService.selectSwitchConfigValueByKey(SwitchConfigEnum.SWITCH_VIEW.getName())).thenReturn(key);

        // Run the test
        final R<ExecuteAuditResultDto> result = toolsExecuteServiceImplUnderTest.getExecutButtonStatus(
                executAuditQueryDto);

        // Verify the results

        assertThat(result.getData().getButtenType()).isEqualTo(1);
    }

    /**
     * 高峰时期，生产查询，查询诊断类  非*view用户 全部系统开关  返回双人复核
     */
    @Test
    void testGetExecutButtonStatus_NotTopTime_Query_QUERY_NotView_AllSys() {
        // Setup
        final ExecutAuditQueryDto executAuditQueryDto = new ExecutAuditQueryDto();
        executAuditQueryDto.setCurrentIp("currentIp");
        executAuditQueryDto.setToolsId(0L);
        executAuditQueryDto.setExecFrom(0);
        executAuditQueryDto.setUserId(0L);
        executAuditQueryDto.setAuditId(0L);
        executAuditQueryDto.setAuditState(0);

        // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
        final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setType(0);
        toolsInfoCategoryEntity.setClassification(2);
        toolsInfoCategoryEntity.setScriptOperatingUser("root1");
        toolsInfoCategoryEntity.setStatus(0);
        toolsInfoCategoryEntity.setBusinessSystemId(0L);
        toolsInfoCategoryEntity.setDeliveryStatus(0);
        toolsInfoCategoryEntity.setApprovalState(0);
        when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);

        when(mockBusinessConfig.isToolsExecuteCheck()).thenReturn(true);
        when(mockToolsExecuteMapper.getExecuteAudit(any(ExecuteAuditEntity.class))).thenReturn(null);
        when(mockTopTimeService.checkHighTime(ControlsTimeUtil.getCurrentDayOfWeek()
                , ControlsTimeUtil.getStringNowDate("HH:mm"))).thenReturn(false);
        when(mockEccWhiteService.selectLoginUrlByIp("currentIp")).thenReturn(3);
        List key = new ArrayList();
        key.add("root");
        when(mockSwitchConfigService.selectSwitchConfigValueByKey(SwitchConfigEnum.SWITCH_VIEW.getName())).thenReturn(key);
        when(mockSwitchConfigService.selectSwitchConfigValueByKey(SwitchConfigEnum.SWITCH_IMPORTANT.getName()))
                .thenReturn(SwitchConfigValueEnum.SWITCH_IMPORTANT2.getCode());

        // Run the test
        final R<ExecuteAuditResultDto> result = toolsExecuteServiceImplUnderTest.getExecutButtonStatus(
                executAuditQueryDto);

        // Verify the results

        assertThat(result.getData().getButtenType()).isEqualTo(3);
    }

    /**
     * 非高峰时期，生产查询，查询诊断类  非*view用户 重要系统开关  重要系统 返回双人复核
     */
    @Test
    void testGetExecutButtonStatus_NotTopTime_Query_QUERY_NotView_ImportantSys_Important() {
        // Setup
        final ExecutAuditQueryDto executAuditQueryDto = new ExecutAuditQueryDto();
        executAuditQueryDto.setCurrentIp("currentIp");
        executAuditQueryDto.setToolsId(0L);
        executAuditQueryDto.setExecFrom(0);
        executAuditQueryDto.setUserId(0L);
        executAuditQueryDto.setAuditId(0L);
        executAuditQueryDto.setAuditState(0);

        // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
        final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setType(0);
        toolsInfoCategoryEntity.setClassification(2);
        toolsInfoCategoryEntity.setScriptOperatingUser("root1");
        toolsInfoCategoryEntity.setStatus(0);
        toolsInfoCategoryEntity.setBusinessSystemId(0L);
        toolsInfoCategoryEntity.setDeliveryStatus(0);
        toolsInfoCategoryEntity.setApprovalState(0);
        when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);

        when(mockBusinessConfig.isToolsExecuteCheck()).thenReturn(true);
        when(mockToolsExecuteMapper.getExecuteAudit(any(ExecuteAuditEntity.class))).thenReturn(null);
        when(mockTopTimeService.checkHighTime(ControlsTimeUtil.getCurrentDayOfWeek()
                , ControlsTimeUtil.getStringNowDate("HH:mm"))).thenReturn(false);
        when(mockEccWhiteService.selectLoginUrlByIp("currentIp")).thenReturn(3);
        List key = new ArrayList();
        key.add("root");
        when(mockSwitchConfigService.selectSwitchConfigValueByKey(SwitchConfigEnum.SWITCH_VIEW.getName())).thenReturn(key);
        when(mockSwitchConfigService.selectSwitchConfigValueByKey(SwitchConfigEnum.SWITCH_IMPORTANT.getName()))
                .thenReturn(SwitchConfigValueEnum.SWITCH_IMPORTANT1.getCode());
        // Configure SystemDataInteract.getBusinessSystemInfo(...).
        when(mockSystemDataInteract.getBusinessSystemInfoForType(0L)).thenReturn(true);

        // Run the test
        final R<ExecuteAuditResultDto> result = toolsExecuteServiceImplUnderTest.getExecutButtonStatus(
                executAuditQueryDto);

        // Verify the results

        assertThat(result.getData().getButtenType()).isEqualTo(3);
    }

    /**
     * 非高峰时期，生产查询，查询诊断类  非*view用户 重要系统开关  非重要系统 返回双人复核
     */
    @Test
    void testGetExecutButtonStatus_NotTopTime_Query_QUERY_NotView_ImportantSys_NotImportant() {
        // Setup
        final ExecutAuditQueryDto executAuditQueryDto = new ExecutAuditQueryDto();
        executAuditQueryDto.setCurrentIp("currentIp");
        executAuditQueryDto.setToolsId(0L);
        executAuditQueryDto.setExecFrom(0);
        executAuditQueryDto.setUserId(0L);
        executAuditQueryDto.setAuditId(0L);
        executAuditQueryDto.setAuditState(0);

        // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
        final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setType(0);
        toolsInfoCategoryEntity.setClassification(2);
        toolsInfoCategoryEntity.setScriptOperatingUser("root1");
        toolsInfoCategoryEntity.setStatus(0);
        toolsInfoCategoryEntity.setBusinessSystemId(0L);
        toolsInfoCategoryEntity.setDeliveryStatus(0);
        toolsInfoCategoryEntity.setApprovalState(0);
        when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);

        when(mockBusinessConfig.isToolsExecuteCheck()).thenReturn(true);
        when(mockToolsExecuteMapper.getExecuteAudit(any(ExecuteAuditEntity.class))).thenReturn(null);
        when(mockTopTimeService.checkHighTime(ControlsTimeUtil.getCurrentDayOfWeek()
                , ControlsTimeUtil.getStringNowDate("HH:mm"))).thenReturn(false);
        when(mockEccWhiteService.selectLoginUrlByIp("currentIp")).thenReturn(3);
        List key = new ArrayList();
        key.add("root");
        when(mockSwitchConfigService.selectSwitchConfigValueByKey(SwitchConfigEnum.SWITCH_VIEW.getName())).thenReturn(key);
        when(mockSwitchConfigService.selectSwitchConfigValueByKey(SwitchConfigEnum.SWITCH_IMPORTANT.getName()))
                .thenReturn(SwitchConfigValueEnum.SWITCH_IMPORTANT1.getCode());
        // Configure SystemDataInteract.getBusinessSystemInfo(...).
        when(mockSystemDataInteract.getBusinessSystemInfoForType(0L)).thenReturn(false);
        // Run the test
        final R<ExecuteAuditResultDto> result = toolsExecuteServiceImplUnderTest.getExecutButtonStatus(
                executAuditQueryDto);

        // Verify the results

        assertThat(result.getData().getButtenType()).isEqualTo(1);
    }

    /**
     * 非高峰时期，生产查询，查询诊断类  非*view用户 未开启开关  返回双人复核
     */
    @Test
    void testGetExecutButtonStatus_NotTopTime_Query_QUERY_NotView_CloseSwitch() {
        // Setup
        final ExecutAuditQueryDto executAuditQueryDto = new ExecutAuditQueryDto();
        executAuditQueryDto.setCurrentIp("currentIp");
        executAuditQueryDto.setToolsId(0L);
        executAuditQueryDto.setExecFrom(0);
        executAuditQueryDto.setUserId(0L);
        executAuditQueryDto.setAuditId(0L);
        executAuditQueryDto.setAuditState(0);

        // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
        final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setType(0);
        toolsInfoCategoryEntity.setClassification(2);
        toolsInfoCategoryEntity.setScriptOperatingUser("root1");
        toolsInfoCategoryEntity.setStatus(0);
        toolsInfoCategoryEntity.setBusinessSystemId(0L);
        toolsInfoCategoryEntity.setDeliveryStatus(0);
        toolsInfoCategoryEntity.setApprovalState(0);
        when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);

        when(mockBusinessConfig.isToolsExecuteCheck()).thenReturn(true);
        when(mockToolsExecuteMapper.getExecuteAudit(any(ExecuteAuditEntity.class))).thenReturn(null);
        when(mockTopTimeService.checkHighTime(ControlsTimeUtil.getCurrentDayOfWeek()
                , ControlsTimeUtil.getStringNowDate("HH:mm"))).thenReturn(false);
        when(mockEccWhiteService.selectLoginUrlByIp("currentIp")).thenReturn(3);
        List key = new ArrayList();
        key.add("root");
        when(mockSwitchConfigService.selectSwitchConfigValueByKey(SwitchConfigEnum.SWITCH_VIEW.getName())).thenReturn(key);
        when(mockSwitchConfigService.selectSwitchConfigValueByKey(SwitchConfigEnum.SWITCH_IMPORTANT.getName()))
                .thenReturn(SwitchConfigValueEnum.SWITCH_IMPORTANT3.getCode());

        // Run the test
        final R<ExecuteAuditResultDto> result = toolsExecuteServiceImplUnderTest.getExecutButtonStatus(
                executAuditQueryDto);

        // Verify the results

        assertThat(result.getData().getButtenType()).isEqualTo(1);
    }


    /**
     * 高峰时期，ecc，告警自愈类 一线人员  返回双人复核
     */
    @Test
    void testGetExecutButtonStatus_TopTime_ECC_CHANGE_OnePerson() {
        // Setup
        final ExecutAuditQueryDto executAuditQueryDto = new ExecutAuditQueryDto();
        executAuditQueryDto.setCurrentIp("currentIp");
        executAuditQueryDto.setToolsId(0L);
        executAuditQueryDto.setExecFrom(0);
        executAuditQueryDto.setUserId(0L);
        executAuditQueryDto.setAuditId(0L);
        executAuditQueryDto.setAuditState(0);

        // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
        final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setType(0);
        toolsInfoCategoryEntity.setClassification(3);
        toolsInfoCategoryEntity.setScriptOperatingUser("root1");
        toolsInfoCategoryEntity.setStatus(0);
        toolsInfoCategoryEntity.setBusinessSystemId(0L);
        toolsInfoCategoryEntity.setDeliveryStatus(0);
        toolsInfoCategoryEntity.setApprovalState(0);
        when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);

        when(mockBusinessConfig.isToolsExecuteCheck()).thenReturn(true);
        when(mockToolsExecuteMapper.getExecuteAudit(any(ExecuteAuditEntity.class))).thenReturn(null);
        when(mockTopTimeService.checkHighTime(ControlsTimeUtil.getCurrentDayOfWeek()
                , ControlsTimeUtil.getStringNowDate("HH:mm"))).thenReturn(true);
        when(mockEccWhiteService.selectLoginUrlByIp("currentIp")).thenReturn(1);
        // Configure UserInfoInteract.queryUserInfoListByPermissionCode(...).
        final UserInfoDto userInfoDto = new UserInfoDto();
        userInfoDto.setId(0L);
        userInfoDto.setLoginName("loginName");
        userInfoDto.setEmail("email");
        userInfoDto.setPhone("phone");
        userInfoDto.setFullName("fullName");
        final List<UserInfoDto> userInfoDtos = Arrays.asList(userInfoDto);
        when(mockUserInfoInteract.queryUserInfoListByPermissionCode(AuditorPermissionEnum.AUDIT_PERMISSION_FRONTLINE.getValue()))
                .thenReturn(userInfoDtos);

        // Run the test
        final R<ExecuteAuditResultDto> result = toolsExecuteServiceImplUnderTest.getExecutButtonStatus(
                executAuditQueryDto);

        // Verify the results

        assertThat(result.getData().getButtenType()).isEqualTo(3);
    }

    /**
     * 高峰时期，ecc，告警自愈类 二线人员  返回冷静提示5秒
     */
    @Test
    void testGetExecutButtonStatus_TopTime_ECC_CHANGE_TwoPerson() {
        // Setup
        final ExecutAuditQueryDto executAuditQueryDto = new ExecutAuditQueryDto();
        executAuditQueryDto.setCurrentIp("currentIp");
        executAuditQueryDto.setToolsId(0L);
        executAuditQueryDto.setExecFrom(0);
        executAuditQueryDto.setUserId(0L);
        executAuditQueryDto.setAuditId(0L);
        executAuditQueryDto.setAuditState(0);

        // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
        final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setType(0);
        toolsInfoCategoryEntity.setClassification(3);
        toolsInfoCategoryEntity.setScriptOperatingUser("root1");
        toolsInfoCategoryEntity.setStatus(0);
        toolsInfoCategoryEntity.setBusinessSystemId(0L);
        toolsInfoCategoryEntity.setDeliveryStatus(0);
        toolsInfoCategoryEntity.setApprovalState(0);
        when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);

        when(mockBusinessConfig.isToolsExecuteCheck()).thenReturn(true);
        when(mockToolsExecuteMapper.getExecuteAudit(any(ExecuteAuditEntity.class))).thenReturn(null);
        when(mockTopTimeService.checkHighTime(ControlsTimeUtil.getCurrentDayOfWeek()
                , ControlsTimeUtil.getStringNowDate("HH:mm"))).thenReturn(true);
        when(mockEccWhiteService.selectLoginUrlByIp("currentIp")).thenReturn(1);
        // Configure UserInfoInteract.queryUserInfoListByPermissionCode(...).
        final UserInfoDto userInfoDto = new UserInfoDto();
        userInfoDto.setId(1L);
        userInfoDto.setLoginName("loginName");
        userInfoDto.setEmail("email");
        userInfoDto.setPhone("phone");
        userInfoDto.setFullName("fullName");
        final List<UserInfoDto> userInfoDtos = Arrays.asList(userInfoDto);
        when(mockUserInfoInteract.queryUserInfoListByPermissionCode(AuditorPermissionEnum.AUDIT_PERMISSION_FRONTLINE.getValue()))
                .thenReturn(userInfoDtos);

        // Run the test
        final R<ExecuteAuditResultDto> result = toolsExecuteServiceImplUnderTest.getExecutButtonStatus(
                executAuditQueryDto);

        // Verify the results

        assertThat(result.getData().getButtenType()).isEqualTo(4);
    }

    /**
     * 非高峰时期，ecc，告警自愈类 一线人员  返回双人复核
     */
    @Test
    void testGetExecutButtonStatus_NotTopTime_ECC_CHANGE_OnePerson() {
        // Setup
        final ExecutAuditQueryDto executAuditQueryDto = new ExecutAuditQueryDto();
        executAuditQueryDto.setCurrentIp("currentIp");
        executAuditQueryDto.setToolsId(0L);
        executAuditQueryDto.setExecFrom(0);
        executAuditQueryDto.setUserId(0L);
        executAuditQueryDto.setAuditId(0L);
        executAuditQueryDto.setAuditState(0);

        // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
        final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setType(0);
        toolsInfoCategoryEntity.setClassification(3);
        toolsInfoCategoryEntity.setScriptOperatingUser("root1");
        toolsInfoCategoryEntity.setStatus(0);
        toolsInfoCategoryEntity.setBusinessSystemId(0L);
        toolsInfoCategoryEntity.setDeliveryStatus(0);
        toolsInfoCategoryEntity.setApprovalState(0);
        when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);

        when(mockBusinessConfig.isToolsExecuteCheck()).thenReturn(true);
        when(mockToolsExecuteMapper.getExecuteAudit(any(ExecuteAuditEntity.class))).thenReturn(null);
        when(mockTopTimeService.checkHighTime(ControlsTimeUtil.getCurrentDayOfWeek()
                , ControlsTimeUtil.getStringNowDate("HH:mm"))).thenReturn(false);
        when(mockEccWhiteService.selectLoginUrlByIp("currentIp")).thenReturn(1);
        // Configure UserInfoInteract.queryUserInfoListByPermissionCode(...).
        final UserInfoDto userInfoDto = new UserInfoDto();
        userInfoDto.setId(0L);
        userInfoDto.setLoginName("loginName");
        userInfoDto.setEmail("email");
        userInfoDto.setPhone("phone");
        userInfoDto.setFullName("fullName");
        final List<UserInfoDto> userInfoDtos = Arrays.asList(userInfoDto);
        when(mockUserInfoInteract.queryUserInfoListByPermissionCode(AuditorPermissionEnum.AUDIT_PERMISSION_FRONTLINE.getValue()))
                .thenReturn(userInfoDtos);

        // Run the test
        final R<ExecuteAuditResultDto> result = toolsExecuteServiceImplUnderTest.getExecutButtonStatus(
                executAuditQueryDto);

        // Verify the results

        assertThat(result.getData().getButtenType()).isEqualTo(3);
    }

    /**
     * 非高峰时期，ecc，告警自愈类 二线人员  返回冷静提示5秒
     */
    @Test
    void testGetExecutButtonStatus_NotTopTime_ECC_CHANGE_TwoPerson() {
        // Setup
        final ExecutAuditQueryDto executAuditQueryDto = new ExecutAuditQueryDto();
        executAuditQueryDto.setCurrentIp("currentIp");
        executAuditQueryDto.setToolsId(0L);
        executAuditQueryDto.setExecFrom(0);
        executAuditQueryDto.setUserId(0L);
        executAuditQueryDto.setAuditId(0L);
        executAuditQueryDto.setAuditState(0);

        // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
        final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setType(0);
        toolsInfoCategoryEntity.setClassification(3);
        toolsInfoCategoryEntity.setScriptOperatingUser("root1");
        toolsInfoCategoryEntity.setStatus(0);
        toolsInfoCategoryEntity.setBusinessSystemId(0L);
        toolsInfoCategoryEntity.setDeliveryStatus(0);
        toolsInfoCategoryEntity.setApprovalState(0);
        when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);

        when(mockBusinessConfig.isToolsExecuteCheck()).thenReturn(true);
        when(mockToolsExecuteMapper.getExecuteAudit(any(ExecuteAuditEntity.class))).thenReturn(null);
        when(mockTopTimeService.checkHighTime(ControlsTimeUtil.getCurrentDayOfWeek()
                , ControlsTimeUtil.getStringNowDate("HH:mm"))).thenReturn(false);
        when(mockEccWhiteService.selectLoginUrlByIp("currentIp")).thenReturn(1);
        // Configure UserInfoInteract.queryUserInfoListByPermissionCode(...).
        final UserInfoDto userInfoDto = new UserInfoDto();
        userInfoDto.setId(1L);
        userInfoDto.setLoginName("loginName");
        userInfoDto.setEmail("email");
        userInfoDto.setPhone("phone");
        userInfoDto.setFullName("fullName");
        final List<UserInfoDto> userInfoDtos = Arrays.asList(userInfoDto);
        when(mockUserInfoInteract.queryUserInfoListByPermissionCode(AuditorPermissionEnum.AUDIT_PERMISSION_FRONTLINE.getValue()))
                .thenReturn(userInfoDtos);

        // Run the test
        final R<ExecuteAuditResultDto> result = toolsExecuteServiceImplUnderTest.getExecutButtonStatus(
                executAuditQueryDto);

        // Verify the results

        assertThat(result.getData().getButtenType()).isEqualTo(4);
    }


    /**
     * 高峰时期，生产变更，告警自愈类   返回双人复核
     */
    @Test
    void testGetExecutButtonStatus_TopTime_Change_CHANGE() {
        // Setup
        final ExecutAuditQueryDto executAuditQueryDto = new ExecutAuditQueryDto();
        executAuditQueryDto.setCurrentIp("currentIp");
        executAuditQueryDto.setToolsId(0L);
        executAuditQueryDto.setExecFrom(0);
        executAuditQueryDto.setUserId(0L);
        executAuditQueryDto.setAuditId(0L);
        executAuditQueryDto.setAuditState(0);

        // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
        final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setType(0);
        toolsInfoCategoryEntity.setClassification(3);
        toolsInfoCategoryEntity.setScriptOperatingUser("root1");
        toolsInfoCategoryEntity.setStatus(0);
        toolsInfoCategoryEntity.setBusinessSystemId(0L);
        toolsInfoCategoryEntity.setDeliveryStatus(0);
        toolsInfoCategoryEntity.setApprovalState(0);
        when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);

        when(mockBusinessConfig.isToolsExecuteCheck()).thenReturn(true);
        when(mockToolsExecuteMapper.getExecuteAudit(any(ExecuteAuditEntity.class))).thenReturn(null);
        when(mockTopTimeService.checkHighTime(ControlsTimeUtil.getCurrentDayOfWeek()
                , ControlsTimeUtil.getStringNowDate("HH:mm"))).thenReturn(true);
        when(mockEccWhiteService.selectLoginUrlByIp("currentIp")).thenReturn(2);

        // Run the test
        final R<ExecuteAuditResultDto> result = toolsExecuteServiceImplUnderTest.getExecutButtonStatus(
                executAuditQueryDto);

        // Verify the results

        assertThat(result.getData().getButtenType()).isEqualTo(3);
    }


    /**
     * 非高峰时期，生产变更，告警自愈类   返回双人复核
     */
    @Test
    void testGetExecutButtonStatus_NotTopTime_Change_CHANGE() {
        // Setup
        final ExecutAuditQueryDto executAuditQueryDto = new ExecutAuditQueryDto();
        executAuditQueryDto.setCurrentIp("currentIp");
        executAuditQueryDto.setToolsId(0L);
        executAuditQueryDto.setExecFrom(0);
        executAuditQueryDto.setUserId(0L);
        executAuditQueryDto.setAuditId(0L);
        executAuditQueryDto.setAuditState(0);

        // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
        final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setType(0);
        toolsInfoCategoryEntity.setClassification(3);
        toolsInfoCategoryEntity.setScriptOperatingUser("root1");
        toolsInfoCategoryEntity.setStatus(0);
        toolsInfoCategoryEntity.setBusinessSystemId(0L);
        toolsInfoCategoryEntity.setDeliveryStatus(0);
        toolsInfoCategoryEntity.setApprovalState(0);
        when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);

        when(mockBusinessConfig.isToolsExecuteCheck()).thenReturn(true);
        when(mockToolsExecuteMapper.getExecuteAudit(any(ExecuteAuditEntity.class))).thenReturn(null);
        when(mockTopTimeService.checkHighTime(ControlsTimeUtil.getCurrentDayOfWeek()
                , ControlsTimeUtil.getStringNowDate("HH:mm"))).thenReturn(false);
        when(mockEccWhiteService.selectLoginUrlByIp("currentIp")).thenReturn(2);


        // Run the test
        final R<ExecuteAuditResultDto> result = toolsExecuteServiceImplUnderTest.getExecutButtonStatus(
                executAuditQueryDto);

        // Verify the results

        assertThat(result.getData().getButtenType()).isEqualTo(3);
    }


    /**
     * 高峰时期，生产查询，告警自愈类   返回双人复核
     */
    @Test
    void testGetExecutButtonStatus_TopTime_Query_CHANGE() {
        // Setup
        final ExecutAuditQueryDto executAuditQueryDto = new ExecutAuditQueryDto();
        executAuditQueryDto.setCurrentIp("currentIp");
        executAuditQueryDto.setToolsId(0L);
        executAuditQueryDto.setExecFrom(0);
        executAuditQueryDto.setUserId(0L);
        executAuditQueryDto.setAuditId(0L);
        executAuditQueryDto.setAuditState(0);

        // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
        final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setType(0);
        toolsInfoCategoryEntity.setClassification(3);
        toolsInfoCategoryEntity.setScriptOperatingUser("root1");
        toolsInfoCategoryEntity.setStatus(0);
        toolsInfoCategoryEntity.setBusinessSystemId(0L);
        toolsInfoCategoryEntity.setDeliveryStatus(0);
        toolsInfoCategoryEntity.setApprovalState(0);
        when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);

        when(mockBusinessConfig.isToolsExecuteCheck()).thenReturn(true);
        when(mockToolsExecuteMapper.getExecuteAudit(any(ExecuteAuditEntity.class))).thenReturn(null);
        when(mockTopTimeService.checkHighTime(ControlsTimeUtil.getCurrentDayOfWeek()
                , ControlsTimeUtil.getStringNowDate("HH:mm"))).thenReturn(true);
        when(mockEccWhiteService.selectLoginUrlByIp("currentIp")).thenReturn(3);

        // Run the test
        final R<ExecuteAuditResultDto> result = toolsExecuteServiceImplUnderTest.getExecutButtonStatus(
                executAuditQueryDto);

        // Verify the results

        assertThat(result.getData().getButtenType()).isEqualTo(0);
    }


    /**
     * 非高峰时期，生产查询，告警自愈类   返回双人复核
     */
    @Test
    void testGetExecutButtonStatus_NotTopTime_Query_CHANGE() {
        // Setup
        final ExecutAuditQueryDto executAuditQueryDto = new ExecutAuditQueryDto();
        executAuditQueryDto.setCurrentIp("currentIp");
        executAuditQueryDto.setToolsId(0L);
        executAuditQueryDto.setExecFrom(0);
        executAuditQueryDto.setUserId(0L);
        executAuditQueryDto.setAuditId(0L);
        executAuditQueryDto.setAuditState(0);

        // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
        final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setType(0);
        toolsInfoCategoryEntity.setClassification(3);
        toolsInfoCategoryEntity.setScriptOperatingUser("root1");
        toolsInfoCategoryEntity.setStatus(0);
        toolsInfoCategoryEntity.setBusinessSystemId(0L);
        toolsInfoCategoryEntity.setDeliveryStatus(0);
        toolsInfoCategoryEntity.setApprovalState(0);
        when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);

        when(mockBusinessConfig.isToolsExecuteCheck()).thenReturn(true);
        when(mockToolsExecuteMapper.getExecuteAudit(any(ExecuteAuditEntity.class))).thenReturn(null);
        when(mockTopTimeService.checkHighTime(ControlsTimeUtil.getCurrentDayOfWeek()
                , ControlsTimeUtil.getStringNowDate("HH:mm"))).thenReturn(false);
        when(mockEccWhiteService.selectLoginUrlByIp("currentIp")).thenReturn(3);


        // Run the test
        final R<ExecuteAuditResultDto> result = toolsExecuteServiceImplUnderTest.getExecutButtonStatus(
                executAuditQueryDto);

        // Verify the results

        assertThat(result.getData().getButtenType()).isEqualTo(0);
    }

    /**
     * 插入执行审批 输入参数
     *
     * @throws Exception
     */
    @Test
    void testSaveExecutToolsAuditStatus_Params() throws Exception {
        // Setup
        final ExecuteAuditSaveDto executeAuditSaveDto = new ExecuteAuditSaveDto();
        final ToolsInfoDto toolsInfoDto = new ToolsInfoDto();
        toolsInfoDto.setId(0L);
        toolsInfoDto.setName("name");
        toolsInfoDto.setType(0);
        toolsInfoDto.setScriptIds("scriptIds");
        toolsInfoDto.setScriptEditing("scriptContent");
        executeAuditSaveDto.setToolsInfoDto(toolsInfoDto);
        final AuditExecutAgentDto agentDto = new AuditExecutAgentDto();
        agentDto.setAgentIp("agentIp");
        agentDto.setAgentPort(0);
        agentDto.setSysAgentInfoId(0L);
        executeAuditSaveDto.setAgentIps(Arrays.asList(agentDto));
        final AuditExecutParamDto params = new AuditExecutParamDto();
        params.setParamJson("paramJson");
        params.setScriptContent("scriptContent");
        params.setAuditId(0L);
        params.setScriptIds("scriptIds");
        executeAuditSaveDto.setParams(params);
        executeAuditSaveDto.setExecFrom(4);
        executeAuditSaveDto.setAuditorId(0L);
        executeAuditSaveDto.setAuditorName("auditorName");

        final UserDto userDto = new UserDto();
        userDto.setUserName("userName");
        userDto.setUserId(0L);
        userDto.setUserCode("userCode");
        userDto.setLoginName("loginName");

        // Configure IToolsInfoService.selectToolsInfoById(...).
        final ToolsInfoDto toolsInfoDto1 = new ToolsInfoDto();
        toolsInfoDto1.setId(0L);
        toolsInfoDto1.setName("name");
        toolsInfoDto1.setType(0);
        toolsInfoDto1.setScriptIds("scriptIds");
        toolsInfoDto1.setScriptEditing("scriptContent");
        when(mockToolsInfoService.selectToolsInfoById(0L)).thenReturn(toolsInfoDto1);

        // Configure IAuditService.saveAuditDoubleCheck(...).
        final AuditDto auditDto = new AuditDto();
        auditDto.setId(0L);
        auditDto.setBusinessId(0L);
        auditDto.setApplyId(0L);
        auditDto.setType(0);
        auditDto.setApprovalState(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto.setId(0L);
        auditEverybodyQueryDto.setAuditorId(0L);
        auditEverybodyQueryDto.setAuditorName("auditorName");
        final List<AuditEverybodyQueryDto> auditEverybodyDtoList = Arrays.asList(auditEverybodyQueryDto);
        when(mockAuditService.saveAuditDoubleCheck(eq(0L), any(List.class), eq(4),
                any(UserDto.class))).thenReturn(auditDto);

        // Configure IAuditService.sendDoubleCheck(...).
        final AuditEverybodyQueryDto auditEverybodyQueryDto1 = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto1.setId(0L);
        auditEverybodyQueryDto1.setAuditorId(0L);
        auditEverybodyQueryDto1.setAuditorName("auditorName");
        final List<AuditEverybodyQueryDto> auditEverybodyDtoList1 = Arrays.asList(auditEverybodyQueryDto1);
        when(mockAuditService.sendDoubleCheck(any(AuditDto.class), any(List.class), eq("name")))
                .thenReturn(0L);

        // Run the test
        final ExecuteAuditResultDto result = toolsExecuteServiceImplUnderTest.saveExecutToolsAuditStatus(
                executeAuditSaveDto, userDto);
        assertThat(result.getButtenType()).isEqualTo(5);
    }

    /**
     * 插入执行审批 未输入参数
     *
     * @throws Exception
     */
    @Test
    void testSaveExecutToolsAuditStatus_NotParams() throws Exception {
        // Setup
        final ExecuteAuditSaveDto executeAuditSaveDto = new ExecuteAuditSaveDto();
        final ToolsInfoDto toolsInfoDto = new ToolsInfoDto();
        toolsInfoDto.setId(0L);
        toolsInfoDto.setName("name");
        toolsInfoDto.setType(0);
        toolsInfoDto.setScriptIds("scriptIds");
        toolsInfoDto.setScriptEditing("scriptContent");
        executeAuditSaveDto.setToolsInfoDto(toolsInfoDto);
        final AuditExecutAgentDto agentDto = new AuditExecutAgentDto();
        agentDto.setAgentIp("agentIp");
        agentDto.setAgentPort(0);
        agentDto.setSysAgentInfoId(0L);
        executeAuditSaveDto.setAgentIps(Arrays.asList(agentDto));
        executeAuditSaveDto.setExecFrom(4);
        executeAuditSaveDto.setAuditorId(0L);
        executeAuditSaveDto.setAuditorName("auditorName");

        final UserDto userDto = new UserDto();
        userDto.setUserName("userName");
        userDto.setUserId(0L);
        userDto.setUserCode("userCode");
        userDto.setLoginName("loginName");

        // Configure IToolsInfoService.selectToolsInfoById(...).
        final ToolsInfoDto toolsInfoDto1 = new ToolsInfoDto();
        toolsInfoDto1.setId(0L);
        toolsInfoDto1.setName("name");
        toolsInfoDto1.setType(3);
        toolsInfoDto1.setScriptIds("scriptIds");
        toolsInfoDto1.setScriptEditing("scriptContent");
        when(mockToolsInfoService.selectToolsInfoById(0L)).thenReturn(toolsInfoDto1);

        // Configure IAuditService.saveAuditDoubleCheck(...).
        final AuditDto auditDto = new AuditDto();
        auditDto.setId(0L);
        auditDto.setBusinessId(0L);
        auditDto.setApplyId(0L);
        auditDto.setType(0);
        auditDto.setApprovalState(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto.setId(0L);
        auditEverybodyQueryDto.setAuditorId(0L);
        auditEverybodyQueryDto.setAuditorName("auditorName");
        final List<AuditEverybodyQueryDto> auditEverybodyDtoList = Arrays.asList(auditEverybodyQueryDto);
        when(mockAuditService.saveAuditDoubleCheck(eq(0L), any(List.class), eq(4),
                any(UserDto.class))).thenReturn(auditDto);

        // Configure IAuditService.sendDoubleCheck(...).
        final AuditEverybodyQueryDto auditEverybodyQueryDto1 = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto1.setId(0L);
        auditEverybodyQueryDto1.setAuditorId(0L);
        auditEverybodyQueryDto1.setAuditorName("auditorName");
        final List<AuditEverybodyQueryDto> auditEverybodyDtoList1 = Arrays.asList(auditEverybodyQueryDto1);
        when(mockAuditService.sendDoubleCheck(any(AuditDto.class), any(List.class), eq("name")))
                .thenReturn(0L);

        // Run the test
        final ExecuteAuditResultDto result = toolsExecuteServiceImplUnderTest.saveExecutToolsAuditStatus(
                executeAuditSaveDto, userDto);
        assertThat(result.getButtenType()).isEqualTo(5);
    }

    /**
     * 插入执行审批 返回null报错
     *
     * @throws Exception
     */
    @Test
    void testSaveExecutToolsAuditStatus_IAuditServiceSaveAuditDoubleCheckReturnsNull() throws Exception {
        // Setup
        final ExecuteAuditSaveDto executeAuditSaveDto = new ExecuteAuditSaveDto();
        final ToolsInfoDto toolsInfoDto = new ToolsInfoDto();
        toolsInfoDto.setId(0L);
        toolsInfoDto.setName("name");
        toolsInfoDto.setType(0);
        toolsInfoDto.setScriptIds("scriptIds");
        toolsInfoDto.setScriptEditing("scriptContent");
        executeAuditSaveDto.setToolsInfoDto(toolsInfoDto);
        final AuditExecutAgentDto agentDto = new AuditExecutAgentDto();
        agentDto.setAgentIp("agentIp");
        agentDto.setAgentPort(0);
        agentDto.setSysAgentInfoId(0L);
        executeAuditSaveDto.setAgentIps(Arrays.asList(agentDto));
        final AuditExecutParamDto params = new AuditExecutParamDto();
        params.setParamJson("paramJson");
        params.setScriptContent("scriptContent");
        params.setAuditId(0L);
        params.setScriptIds("scriptIds");
        executeAuditSaveDto.setParams(params);
        executeAuditSaveDto.setExecFrom(0);
        executeAuditSaveDto.setAuditorId(0L);
        executeAuditSaveDto.setAuditorName("auditorName");

        final UserDto userDto = new UserDto();
        userDto.setUserName("userName");
        userDto.setUserId(0L);
        userDto.setUserCode("userCode");
        userDto.setLoginName("loginName");

        // Configure IToolsInfoService.selectToolsInfoById(...).
        final ToolsInfoDto toolsInfoDto1 = new ToolsInfoDto();
        toolsInfoDto1.setId(0L);
        toolsInfoDto1.setName("name");
        toolsInfoDto1.setType(0);
        toolsInfoDto1.setScriptIds("scriptIds");
        toolsInfoDto1.setScriptEditing("scriptContent");
        when(mockToolsInfoService.selectToolsInfoById(0L)).thenReturn(toolsInfoDto1);

        // Configure IAuditService.saveAuditDoubleCheck(...).
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto.setId(0L);
        auditEverybodyQueryDto.setAuditorId(0L);
        auditEverybodyQueryDto.setAuditorName("auditorName");
        final List<AuditEverybodyQueryDto> auditEverybodyDtoList = Arrays.asList(auditEverybodyQueryDto);
        when(mockAuditService.saveAuditDoubleCheck(eq(0L), any(List.class), eq(0),
                any(UserDto.class))).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> toolsExecuteServiceImplUnderTest.saveExecutToolsAuditStatus(executeAuditSaveDto,
                userDto)).isInstanceOf(AuditException.class);
    }

    /**
     * 插入执行审批 插入审批表报错
     *
     * @throws Exception
     */
    @Test
    void testSaveExecutToolsAuditStatus_IAuditServiceSaveAuditDoubleCheckThrowsAuditException() throws Exception {
        // Setup
        final ExecuteAuditSaveDto executeAuditSaveDto = new ExecuteAuditSaveDto();
        final ToolsInfoDto toolsInfoDto = new ToolsInfoDto();
        toolsInfoDto.setId(0L);
        toolsInfoDto.setName("name");
        toolsInfoDto.setType(0);
        toolsInfoDto.setScriptIds("scriptIds");
        toolsInfoDto.setScriptEditing("scriptContent");
        executeAuditSaveDto.setToolsInfoDto(toolsInfoDto);
        final AuditExecutAgentDto agentDto = new AuditExecutAgentDto();
        agentDto.setAgentIp("agentIp");
        agentDto.setAgentPort(0);
        agentDto.setSysAgentInfoId(0L);
        executeAuditSaveDto.setAgentIps(Arrays.asList(agentDto));
        final AuditExecutParamDto params = new AuditExecutParamDto();
        params.setParamJson("paramJson");
        params.setScriptContent("scriptContent");
        params.setAuditId(0L);
        params.setScriptIds("scriptIds");
        executeAuditSaveDto.setParams(params);
        executeAuditSaveDto.setExecFrom(0);
        executeAuditSaveDto.setAuditorId(0L);
        executeAuditSaveDto.setAuditorName("auditorName");

        final UserDto userDto = new UserDto();
        userDto.setUserName("userName");
        userDto.setUserId(0L);
        userDto.setUserCode("userCode");
        userDto.setLoginName("loginName");

        // Configure IToolsInfoService.selectToolsInfoById(...).
        final ToolsInfoDto toolsInfoDto1 = new ToolsInfoDto();
        toolsInfoDto1.setId(0L);
        toolsInfoDto1.setName("name");
        toolsInfoDto1.setType(0);
        toolsInfoDto1.setScriptIds("scriptIds");
        toolsInfoDto1.setScriptEditing("scriptContent");
        when(mockToolsInfoService.selectToolsInfoById(0L)).thenReturn(toolsInfoDto1);

        // Configure IAuditService.saveAuditDoubleCheck(...).
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto.setId(0L);
        auditEverybodyQueryDto.setAuditorId(0L);
        auditEverybodyQueryDto.setAuditorName("auditorName");
        final List<AuditEverybodyQueryDto> auditEverybodyDtoList = Arrays.asList(auditEverybodyQueryDto);
        when(mockAuditService.saveAuditDoubleCheck(eq(0L), any(List.class), eq(0),
                any(UserDto.class))).thenThrow(AuditException.class);

        // Run the test
        assertThatThrownBy(() -> toolsExecuteServiceImplUnderTest.saveExecutToolsAuditStatus(executeAuditSaveDto,
                userDto)).isInstanceOf(AuditException.class);
    }

    /**
     * 插入执行审批 发送双人复核报错
     *
     * @throws Exception
     */
    @Test
    void testSaveExecutToolsAuditStatus_IAuditServiceSendDoubleCheckThrowsAuditException() throws Exception {
        // Setup
        final ExecuteAuditSaveDto executeAuditSaveDto = new ExecuteAuditSaveDto();
        final ToolsInfoDto toolsInfoDto = new ToolsInfoDto();
        toolsInfoDto.setId(0L);
        toolsInfoDto.setName("name");
        toolsInfoDto.setType(0);
        toolsInfoDto.setScriptIds("scriptIds");
        toolsInfoDto.setScriptEditing("scriptContent");
        executeAuditSaveDto.setToolsInfoDto(toolsInfoDto);
        final AuditExecutAgentDto agentDto = new AuditExecutAgentDto();
        agentDto.setAgentIp("agentIp");
        agentDto.setAgentPort(0);
        agentDto.setSysAgentInfoId(0L);
        executeAuditSaveDto.setAgentIps(Arrays.asList(agentDto));
        final AuditExecutParamDto params = new AuditExecutParamDto();
        params.setParamJson("paramJson");
        params.setScriptContent("scriptContent");
        params.setAuditId(0L);
        params.setScriptIds("scriptIds");
        executeAuditSaveDto.setParams(params);
        executeAuditSaveDto.setExecFrom(0);
        executeAuditSaveDto.setAuditorId(0L);
        executeAuditSaveDto.setAuditorName("auditorName");

        final UserDto userDto = new UserDto();
        userDto.setUserName("userName");
        userDto.setUserId(0L);
        userDto.setUserCode("userCode");
        userDto.setLoginName("loginName");

        // Configure IToolsInfoService.selectToolsInfoById(...).
        final ToolsInfoDto toolsInfoDto1 = new ToolsInfoDto();
        toolsInfoDto1.setId(0L);
        toolsInfoDto1.setName("name");
        toolsInfoDto1.setType(0);
        toolsInfoDto1.setScriptIds("scriptIds");
        toolsInfoDto1.setScriptEditing("scriptContent");
        when(mockToolsInfoService.selectToolsInfoById(0L)).thenReturn(toolsInfoDto1);

        // Configure IAuditService.saveAuditDoubleCheck(...).
        final AuditDto auditDto = new AuditDto();
        auditDto.setId(0L);
        auditDto.setBusinessId(0L);
        auditDto.setApplyId(0L);
        auditDto.setType(0);
        auditDto.setApprovalState(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto.setId(0L);
        auditEverybodyQueryDto.setAuditorId(0L);
        auditEverybodyQueryDto.setAuditorName("auditorName");
        final List<AuditEverybodyQueryDto> auditEverybodyDtoList = Arrays.asList(auditEverybodyQueryDto);
        when(mockAuditService.saveAuditDoubleCheck(eq(0L), any(List.class), eq(0),
                any(UserDto.class))).thenReturn(auditDto);

        // Configure IAuditService.sendDoubleCheck(...).
        final AuditEverybodyQueryDto auditEverybodyQueryDto1 = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto1.setId(0L);
        auditEverybodyQueryDto1.setAuditorId(0L);
        auditEverybodyQueryDto1.setAuditorName("auditorName");
        final List<AuditEverybodyQueryDto> auditEverybodyDtoList1 = Arrays.asList(auditEverybodyQueryDto1);
        when(mockAuditService.sendDoubleCheck(any(AuditDto.class), any(List.class), eq("name")))
                .thenThrow(AuditException.class);

        // Run the test
        assertThatThrownBy(() -> toolsExecuteServiceImplUnderTest.saveExecutToolsAuditStatus(executeAuditSaveDto,
                userDto)).isInstanceOf(AuditException.class);
    }

    /**
     * 插入执行审批 插入审批agent表报错
     *
     * @throws Exception
     */
    @Test
    void testSaveExecutToolsAuditStatus_IAuditExecutAgentServiceThrowsAuditException() throws Exception {
        // Setup
        final ExecuteAuditSaveDto executeAuditSaveDto = new ExecuteAuditSaveDto();
        final ToolsInfoDto toolsInfoDto = new ToolsInfoDto();
        toolsInfoDto.setId(0L);
        toolsInfoDto.setName("name");
        toolsInfoDto.setType(3);
        toolsInfoDto.setScriptIds("scriptIds");
        toolsInfoDto.setScriptEditing("scriptContent");
        executeAuditSaveDto.setToolsInfoDto(toolsInfoDto);
        final AuditExecutAgentDto agentDto = new AuditExecutAgentDto();
        agentDto.setAgentIp("agentIp");
        agentDto.setAgentPort(0);
        agentDto.setSysAgentInfoId(0L);
        executeAuditSaveDto.setAgentIps(Arrays.asList(agentDto));
        final AuditExecutParamDto params = new AuditExecutParamDto();
        params.setParamJson("paramJson");
        params.setScriptContent("scriptContent");
        params.setAuditId(0L);
        params.setScriptIds("scriptIds");
        executeAuditSaveDto.setParams(params);
        executeAuditSaveDto.setExecFrom(0);
        executeAuditSaveDto.setAuditorId(0L);
        executeAuditSaveDto.setAuditorName("auditorName");

        final UserDto userDto = new UserDto();
        userDto.setUserName("userName");
        userDto.setUserId(0L);
        userDto.setUserCode("userCode");
        userDto.setLoginName("loginName");

        // Configure IToolsInfoService.selectToolsInfoById(...).
        final ToolsInfoDto toolsInfoDto1 = new ToolsInfoDto();
        toolsInfoDto1.setId(0L);
        toolsInfoDto1.setName("name");
        toolsInfoDto1.setType(3);
        toolsInfoDto1.setScriptIds("scriptIds");
        toolsInfoDto1.setScriptEditing("scriptContent");
        when(mockToolsInfoService.selectToolsInfoById(0L)).thenReturn(toolsInfoDto1);

        // Configure IAuditService.saveAuditDoubleCheck(...).
        final AuditDto auditDto = new AuditDto();
        auditDto.setId(0L);
        auditDto.setBusinessId(0L);
        auditDto.setApplyId(0L);
        auditDto.setType(0);
        auditDto.setApprovalState(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto.setId(0L);
        auditEverybodyQueryDto.setAuditorId(0L);
        auditEverybodyQueryDto.setAuditorName("auditorName");
        final List<AuditEverybodyQueryDto> auditEverybodyDtoList = Arrays.asList(auditEverybodyQueryDto);
        when(mockAuditService.saveAuditDoubleCheck(eq(0L), any(List.class), eq(0),
                any(UserDto.class))).thenReturn(auditDto);

        // Configure IAuditService.sendDoubleCheck(...).
        final AuditEverybodyQueryDto auditEverybodyQueryDto1 = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto1.setId(0L);
        auditEverybodyQueryDto1.setAuditorId(0L);
        auditEverybodyQueryDto1.setAuditorName("auditorName");
        final List<AuditEverybodyQueryDto> auditEverybodyDtoList1 = Arrays.asList(auditEverybodyQueryDto1);
        when(mockAuditService.sendDoubleCheck(any(AuditDto.class), any(List.class), eq("name")))
                .thenReturn(0L);

        // Configure IAuditExecutAgentService.batchInsertAuditExecutAgent(...).
        final AuditExecutAgentDto agentDto1 = new AuditExecutAgentDto();
        agentDto1.setId(0L);
        agentDto1.setOsName("osName");
        agentDto1.setAgentIp("agentIp");
        agentDto1.setAgentPort(0);
        agentDto1.setSysAgentInfoId(0L);
        final List<AuditExecutAgentDto> agentIps = Arrays.asList(agentDto1);
        doThrow(AuditException.class).when(mockAuditExecutAgentService).batchInsertAuditExecutAgent(any(List.class), eq(0L));

        // Run the test
        assertThatThrownBy(() -> toolsExecuteServiceImplUnderTest.saveExecutToolsAuditStatus(executeAuditSaveDto,
                userDto)).isInstanceOf(AuditException.class);
    }

    /**
     * 插入执行审批 插入审批参数表表报错
     *
     * @throws Exception
     */
    @Test
    void testSaveExecutToolsAuditStatus_IAuditExecutParamServiceThrowsAuditException() throws Exception {
        // Setup
        final ExecuteAuditSaveDto executeAuditSaveDto = new ExecuteAuditSaveDto();
        final ToolsInfoDto toolsInfoDto = new ToolsInfoDto();
        toolsInfoDto.setId(0L);
        toolsInfoDto.setName("name");
        toolsInfoDto.setType(0);
        toolsInfoDto.setScriptIds("scriptIds");
        toolsInfoDto.setScriptEditing("scriptContent");
        executeAuditSaveDto.setToolsInfoDto(toolsInfoDto);
        final AuditExecutAgentDto agentDto = new AuditExecutAgentDto();
        agentDto.setAgentIp("agentIp");
        agentDto.setAgentPort(0);
        agentDto.setSysAgentInfoId(0L);
        executeAuditSaveDto.setAgentIps(Arrays.asList(agentDto));
        final AuditExecutParamDto params = new AuditExecutParamDto();
        params.setParamJson("paramJson");
        params.setScriptContent("scriptContent");
        params.setAuditId(0L);
        params.setScriptIds("scriptIds");
        executeAuditSaveDto.setParams(params);
        executeAuditSaveDto.setExecFrom(0);
        executeAuditSaveDto.setAuditorId(0L);
        executeAuditSaveDto.setAuditorName("auditorName");

        final UserDto userDto = new UserDto();
        userDto.setUserName("userName");
        userDto.setUserId(0L);
        userDto.setUserCode("userCode");
        userDto.setLoginName("loginName");

        // Configure IToolsInfoService.selectToolsInfoById(...).
        final ToolsInfoDto toolsInfoDto1 = new ToolsInfoDto();
        toolsInfoDto1.setId(0L);
        toolsInfoDto1.setName("name");
        toolsInfoDto1.setType(3);
        toolsInfoDto1.setScriptIds("scriptIds");
        toolsInfoDto1.setScriptEditing("scriptContent");
        when(mockToolsInfoService.selectToolsInfoById(0L)).thenReturn(toolsInfoDto1);

        // Configure IAuditService.saveAuditDoubleCheck(...).
        final AuditDto auditDto = new AuditDto();
        auditDto.setId(0L);
        auditDto.setBusinessId(0L);
        auditDto.setApplyId(0L);
        auditDto.setType(0);
        auditDto.setApprovalState(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto.setId(0L);
        auditEverybodyQueryDto.setAuditorId(0L);
        auditEverybodyQueryDto.setAuditorName("auditorName");
        final List<AuditEverybodyQueryDto> auditEverybodyDtoList = Arrays.asList(auditEverybodyQueryDto);
        when(mockAuditService.saveAuditDoubleCheck(eq(0L), any(List.class), eq(0),
                any(UserDto.class))).thenReturn(auditDto);

        // Configure IAuditService.sendDoubleCheck(...).
        final AuditEverybodyQueryDto auditEverybodyQueryDto1 = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto1.setId(0L);
        auditEverybodyQueryDto1.setAuditorId(0L);
        auditEverybodyQueryDto1.setAuditorName("auditorName");
        final List<AuditEverybodyQueryDto> auditEverybodyDtoList1 = Arrays.asList(auditEverybodyQueryDto1);
        when(mockAuditService.sendDoubleCheck(any(AuditDto.class), any(List.class), eq("name")))
                .thenReturn(0L);

        // Configure IAuditExecutAgentService.batchInsertAuditExecutAgent(...).
        doThrow(AuditException.class).when(mockAuditExecutParamService).insertAuditExecutParam(any(AuditExecutParamDto.class));

        // Run the test
        assertThatThrownBy(() -> toolsExecuteServiceImplUnderTest.saveExecutToolsAuditStatus(executeAuditSaveDto,
                userDto)).isInstanceOf(AuditException.class);
    }

    /**
     * 获取审批详情内容
     *
     * @throws Exception
     */
    @Test
    void testGetAuditDetails() {
        // Setup
        final ExecutAuditQueryDto executAuditQueryDto = new ExecutAuditQueryDto();
        executAuditQueryDto.setCurrentIp("currentIp");
        executAuditQueryDto.setToolsId(0L);
        executAuditQueryDto.setExecFrom(0);
        executAuditQueryDto.setUserId(0L);
        executAuditQueryDto.setAuditId(0L);
        executAuditQueryDto.setAuditState(0);

        // Configure ToolsExecuteMapper.getExecuteAudit(...).
        final ExecuteAuditBean executeAuditBean = new ExecuteAuditBean();
        final AuditDto auditDto = new AuditDto();
        auditDto.setId(0L);
        auditDto.setApplyId(0L);
        auditDto.setType(4);
        auditDto.setApprovalState(0);
        executeAuditBean.setAuditDto(auditDto);
        final AuditExecutAgentDto agentDto = new AuditExecutAgentDto();
        agentDto.setAgentIp("agentIp");
        agentDto.setAgentPort(0);
        agentDto.setSysAgentInfoId(0L);
        executeAuditBean.setAgentIps(Arrays.asList(agentDto));
        final AuditExecutParamDto params = new AuditExecutParamDto();
        params.setParamJson("[{\"id\":1,\"sort\":2,\"name\":'12312',\"type\":2,\"value\":'2',\"description\":'123123'}]");
        params.setScriptContent("scriptContent");
        params.setAuditId(0L);
        params.setScriptIds("scriptIds");
        executeAuditBean.setParams(params);
        when(mockToolsExecuteMapper.getExecuteAudit(any(ExecuteAuditEntity.class))).thenReturn(executeAuditBean);

        // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
       /* final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setType(0);
        toolsInfoCategoryEntity.setClassification(0);
        toolsInfoCategoryEntity.setScriptOperatingUser("scriptOperatingUser");
        toolsInfoCategoryEntity.setStatus(0);
        toolsInfoCategoryEntity.setBusinessSystemId(0L);
        toolsInfoCategoryEntity.setDeliveryStatus(0);
        toolsInfoCategoryEntity.setApprovalState(0);
        when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);*/

        // Run the test
        final ExecuteAuditResultDto result = toolsExecuteServiceImplUnderTest.getAuditDetails(executAuditQueryDto);

        // Verify the results
        Assertions.assertNotNull(result);
    }

    /**
     * 获取审批详情内容
     *
     * @throws Exception
     */
    @Test
    void testGetToolsCombinedInfoAll_DoubleCheck() {
        // Setup
        final ExecutAuditQueryDto executAuditQueryDto = new ExecutAuditQueryDto();
        executAuditQueryDto.setCurrentIp("currentIp");
        executAuditQueryDto.setToolsId(0L);
        executAuditQueryDto.setExecFrom(0);
        executAuditQueryDto.setUserId(0L);
        executAuditQueryDto.setAuditId(0L);
        executAuditQueryDto.setAuditState(0);

        // Configure ToolsExecuteMapper.getExecuteAudit(...).
        final ExecuteAuditBean executeAuditBean = new ExecuteAuditBean();
        final AuditDto auditDto = new AuditDto();
        auditDto.setId(0L);
        auditDto.setApplyId(0L);
        auditDto.setType(4);
        auditDto.setApprovalState(0);
        executeAuditBean.setAuditDto(auditDto);
        final AuditExecutAgentDto agentDto = new AuditExecutAgentDto();
        agentDto.setAgentIp("agentIp");
        agentDto.setAgentPort(0);
        agentDto.setSysAgentInfoId(0L);
        executeAuditBean.setAgentIps(Arrays.asList(agentDto));
        final AuditExecutParamDto params = new AuditExecutParamDto();
        params.setParamJson("[{\"id\":1,\"sort\":2,\"name\":'12312',\"type\":2,\"value\":'2',\"description\":'123123'}]");
        params.setScriptContent("scriptContent");
        params.setAuditId(0L);
        params.setScriptIds("scriptIds");
        executeAuditBean.setParams(params);
        when(mockToolsExecuteMapper.getExecuteAudit(any(ExecuteAuditEntity.class))).thenReturn(executeAuditBean);
        ToolsAuditDto toolsAuditDto = new ToolsAuditDto();
        toolsAuditDto.setAuditId(11L);
        final UserDto user = new UserDto();
        user.setUserName("userName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");
        // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
        final ToolsDto toolsInfoCategoryEntity = new ToolsDto();
        toolsInfoCategoryEntity.setType(1);
        toolsInfoCategoryEntity.setId(1L);
        toolsInfoCategoryEntity.setClassification(0);
        toolsInfoCategoryEntity.setScriptOperatingUser("scriptOperatingUser");
        toolsInfoCategoryEntity.setStatus(0);
        toolsInfoCategoryEntity.setBusinessSystemId(0L);
        when(mockToolsInfoService.getToolsCombinedInfo(toolsAuditDto)).thenReturn(toolsInfoCategoryEntity);
//        when(toolsExecuteServiceImplUnderTest.getAuditDetails(executAuditQueryDto));
        // Run the test
        ToolsDto toolsCombinedInfoAll = toolsExecuteServiceImplUnderTest.getToolsCombinedInfoAll(toolsAuditDto, user, "123123123");

        // Verify the results
        Assertions.assertNotNull(toolsCombinedInfoAll);
    }

    /**
     * 获取审批详情内容
     *
     * @throws Exception
     */
    @Test
    void testGetToolsCombinedInfoAll_Details() {
//        when(mockToolsExecuteMapper.getExecuteAudit(any(ExecuteAuditEntity.class))).thenReturn(executeAuditBean);
        ToolsAuditDto toolsAuditDto = new ToolsAuditDto();
        toolsAuditDto.setId(11L);
        toolsAuditDto.setExecFrom(3);
        final UserDto user = new UserDto();
        user.setUserName("userName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");
        // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
        final ToolsDto toolsInfoCategoryEntity = new ToolsDto();
        toolsInfoCategoryEntity.setType(1);
        toolsInfoCategoryEntity.setId(11L);
        toolsInfoCategoryEntity.setClassification(0);
        toolsInfoCategoryEntity.setScriptOperatingUser("scriptOperatingUser");
        toolsInfoCategoryEntity.setStatus(0);
        toolsInfoCategoryEntity.setBusinessSystemId(0L);
        ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        toolsParamResultDto.setName("11");
        toolsParamResultDto.setType("1");
        toolsParamResultDto.setSort(1L);
        AuditExecutAgentDto AuditExecutAgentDto = new AuditExecutAgentDto();
        AuditExecutAgentDto.setAgentIp("111111");
        AuditExecutAgentDto.setAgentName("1234123");
        ExecutAuditQueryDto executAuditQueryDto = new ExecutAuditQueryDto();
        executAuditQueryDto.setToolsId(11L);
        executAuditQueryDto.setCurrentIp("123123123");
        executAuditQueryDto.setExecFrom(toolsAuditDto.getExecFrom());
        R<ExecuteAuditResultDto> r = new R<>();
        ExecuteAuditResultDto ExecuteAuditResultDto = new ExecuteAuditResultDto();
        ExecuteAuditResultDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        ExecuteAuditResultDto.setAgentIps(Arrays.asList(AuditExecutAgentDto));
        r.setData(ExecuteAuditResultDto);
        when(mockToolsInfoService.getToolsCombinedInfo(toolsAuditDto)).thenReturn(toolsInfoCategoryEntity);
//        when(toolsExecuteServiceImplUnderTest.getExecutButtonStatus(executAuditQueryDto)).thenReturn(r);


//        when(toolsExecuteServiceImplUnderTest.getAuditDetails(executAuditQueryDto));
        // Run the test
        ToolsDto toolsCombinedInfoAll = toolsExecuteServiceImplUnderTest.getToolsCombinedInfoAll(toolsAuditDto, user, "123123123");

        // Verify the results
        Assertions.assertNotNull(toolsCombinedInfoAll);
    }

    /**
     * 工具撤回   未审批撤回
     */
    @Test
    void testRevokeExecutToolsAudit_Awaiting() {
        // Setup
        final ExecutAuditQueryDto executAuditQueryDto = new ExecutAuditQueryDto();
        executAuditQueryDto.setCurrentIp("currentIp");
        executAuditQueryDto.setToolsId(0L);
        executAuditQueryDto.setExecFrom(0);
        executAuditQueryDto.setUserId(0L);
        executAuditQueryDto.setAuditId(0L);
        executAuditQueryDto.setAuditState(0);

        // Configure IAuditService.selectAuditByLastOne(...).
        final AuditDto auditDto = new AuditDto();
        auditDto.setId(0L);
        auditDto.setBusinessId(0L);
        auditDto.setApplyId(0L);
        auditDto.setType(0);
        auditDto.setApprovalState(0);
        when(mockAuditService.selectAuditByLastOne(any(AuditQueryDto.class))).thenReturn(auditDto);

        when(mockAuditService.updateAudit(any(AuditDto.class))).thenReturn(1);

        // Run the test
        final R<Object> result = toolsExecuteServiceImplUnderTest.revokeExecutToolsAudit(executAuditQueryDto);

        // Verify the results
        assertThat(result.getCode()).isEqualTo("10000");
    }

    /**
     * 工具撤回 未审批撤回，审批状态已经审批通过
     */
    @Test
    void testRevokeExecutToolsAudit_AwaitingForNotAudit_Awaiting() {
        // Setup
        final ExecutAuditQueryDto executAuditQueryDto = new ExecutAuditQueryDto();
        executAuditQueryDto.setCurrentIp("currentIp");
        executAuditQueryDto.setToolsId(0L);
        executAuditQueryDto.setExecFrom(0);
        executAuditQueryDto.setUserId(0L);
        executAuditQueryDto.setAuditId(0L);
        executAuditQueryDto.setAuditState(0);

        // Configure IAuditService.selectAuditByLastOne(...).
        final AuditDto auditDto = new AuditDto();
        auditDto.setId(0L);
        auditDto.setBusinessId(0L);
        auditDto.setApplyId(0L);
        auditDto.setType(0);
        auditDto.setApprovalState(1);
        when(mockAuditService.selectAuditByLastOne(any(AuditQueryDto.class))).thenReturn(auditDto);
        // Run the test
        final R<Object> result = toolsExecuteServiceImplUnderTest.revokeExecutToolsAudit(executAuditQueryDto);

        // Verify the results
        assertThat(result.getCode()).isEqualTo("10000");
    }

    /**
     * 工具撤回 执行撤回，审批通过已经执行
     */
    @Test
    void testRevokeExecutToolsAudit_AwaitingForNotAudit_Approved_ExecutRecord() {
        // Setup
        final ExecutAuditQueryDto executAuditQueryDto = new ExecutAuditQueryDto();
        executAuditQueryDto.setCurrentIp("currentIp");
        executAuditQueryDto.setToolsId(0L);
        executAuditQueryDto.setExecFrom(0);
        executAuditQueryDto.setUserId(0L);
        executAuditQueryDto.setAuditId(0L);
        executAuditQueryDto.setAuditState(1);

        // Configure IAuditService.selectAuditByLastOne(...).
        final AuditDto auditDto = new AuditDto();
        auditDto.setId(0L);
        auditDto.setBusinessId(0L);
        auditDto.setApplyId(0L);
        auditDto.setType(0);
        auditDto.setApprovalState(1);
        when(mockAuditService.selectAuditByLastOne(any(AuditQueryDto.class))).thenReturn(auditDto);
        when(mockExecuteMonitorService.matchAuditId(0L)).thenReturn(true);
        // Run the test
        final R<Object> result = toolsExecuteServiceImplUnderTest.revokeExecutToolsAudit(executAuditQueryDto);

        // Verify the results
        assertThat(result.getCode()).isEqualTo("10602");
    }

    /**
     * 工具撤回 执行撤回，审批通过未执行
     */
    @Test
    void testRevokeExecutToolsAudit_AwaitingForNotAudit_Approved_NotExecutRecord() {
        // Setup
        final ExecutAuditQueryDto executAuditQueryDto = new ExecutAuditQueryDto();
        executAuditQueryDto.setCurrentIp("currentIp");
        executAuditQueryDto.setToolsId(0L);
        executAuditQueryDto.setExecFrom(0);
        executAuditQueryDto.setUserId(0L);
        executAuditQueryDto.setAuditId(0L);
        executAuditQueryDto.setAuditState(1);

        // Configure IAuditService.selectAuditByLastOne(...).
        final AuditDto auditDto = new AuditDto();
        auditDto.setId(0L);
        auditDto.setBusinessId(0L);
        auditDto.setApplyId(0L);
        auditDto.setType(0);
        auditDto.setApprovalState(1);
        when(mockAuditService.selectAuditByLastOne(any(AuditQueryDto.class))).thenReturn(auditDto);
        when(mockExecuteMonitorService.matchAuditId(0L)).thenReturn(false);
        when(mockAuditService.updateAudit(any(AuditDto.class))).thenReturn(1);
        // Run the test
        final R<Object> result = toolsExecuteServiceImplUnderTest.revokeExecutToolsAudit(executAuditQueryDto);

        // Verify the results
        assertThat(result.getCode()).isEqualTo("10000");
    }

    /**
     * 工具撤回 撤回失败
     */
    @Test
    void testRevokeExecutToolsAuditReturnFail() {
        // Setup
        final ExecutAuditQueryDto executAuditQueryDto = new ExecutAuditQueryDto();
        executAuditQueryDto.setCurrentIp("currentIp");
        executAuditQueryDto.setToolsId(0L);
        executAuditQueryDto.setExecFrom(0);
        executAuditQueryDto.setUserId(0L);
        executAuditQueryDto.setAuditId(0L);
        executAuditQueryDto.setAuditState(0);

        // Configure IAuditService.selectAuditByLastOne(...).
        final AuditDto auditDto = new AuditDto();
        auditDto.setId(0L);
        auditDto.setBusinessId(0L);
        auditDto.setApplyId(0L);
        auditDto.setType(0);
        auditDto.setApprovalState(0);
        when(mockAuditService.selectAuditByLastOne(any(AuditQueryDto.class))).thenReturn(auditDto);

        when(mockAuditService.updateAudit(any(AuditDto.class))).thenReturn(0);

        // Run the test
        final R<Object> result = toolsExecuteServiceImplUnderTest.revokeExecutToolsAudit(executAuditQueryDto);

        // Verify the results
        assertThat(result.getCode()).isEqualTo("10602");
    }

    /**
     * 执行脚本工具    直接执行
     *
     * @throws Exception
     */
    @Test
    void testExecuteToolsFavorites_ExecuteForSuccess() throws Exception {
        // Setup
        final ExecuteToolsDto executeToolsDto = new ExecuteToolsDto();
        final AuditExecutAgentDto agentDto = new AuditExecutAgentDto();
        agentDto.setAgentIp("agentIp");
        agentDto.setAgentPort(0);
        agentDto.setSysAgentInfoId(0L);
        executeToolsDto.setAgentIps(Arrays.asList(agentDto));
        final AuditExecutParamDto params = new AuditExecutParamDto();
        params.setParamJson("[{\"id\":1,\"sort\":2,\"name\":'12312',\"type\":2,\"value\":'2',\"description\":'123123'}]");
        params.setScriptContent("scriptContent");
        params.setAuditId(0L);
        params.setScriptIds("scriptIds");
        executeToolsDto.setParams(params);
        executeToolsDto.setExecFrom(4);
        executeToolsDto.setAuditId(0L);
        executeToolsDto.setToolsId(0L);

        final UserDto user = new UserDto();
        user.setUserName("userName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        // Configure IToolsInfoService.selectToolsInfoById(...).
        final ToolsInfoDto toolsInfoDto = new ToolsInfoDto();
        toolsInfoDto.setId(0L);
        toolsInfoDto.setName("name");
        toolsInfoDto.setType(0);
        toolsInfoDto.setScriptIds("scriptIds");
        toolsInfoDto.setScriptEditing("scriptContent");
//        when(mockToolsInfoService.selectToolsInfoById(0L)).thenReturn(toolsInfoDto);
        final ToolsDto toolsInfoCategoryEntity = new ToolsDto();
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setType(3);
        toolsInfoCategoryEntity.setClassification(0);
        toolsInfoCategoryEntity.setScriptOperatingUser("scriptOperatingUser");
        toolsInfoCategoryEntity.setStatus(0);
        toolsInfoCategoryEntity.setBusinessSystemId(0L);
        when(mockToolsInfoService.getToolsCombinedInfo(0L)).thenReturn(toolsInfoCategoryEntity);
        // Run the test
        final R<Object> result = toolsExecuteServiceImplUnderTest.executeTools(executeToolsDto, user);
        // Verify the results
        assertThat(result.getCode()).isEqualTo("10000");
    }

    /**
     * 执行脚本工具    直接执行
     *
     * @throws Exception
     */
    @Test
    void testExecuteToolsAlarm_ExecuteForSuccess() throws Exception {
        // Setup
        final ExecuteToolsDto executeToolsDto = new ExecuteToolsDto();
        final AuditExecutAgentDto agentDto = new AuditExecutAgentDto();
        agentDto.setAgentIp("agentIp");
        agentDto.setAgentPort(0);
        agentDto.setSysAgentInfoId(0L);
        executeToolsDto.setAgentIps(Arrays.asList(agentDto));
        final AuditExecutParamDto params = new AuditExecutParamDto();
        params.setParamJson("[{\"id\":1,\"sort\":2,\"name\":'12312',\"type\":2,\"value\":'2',\"description\":'123123'}]");
        params.setScriptContent("scriptContent");
        params.setAuditId(0L);
        params.setScriptIds("scriptIds");
        executeToolsDto.setParams(params);
        executeToolsDto.setExecFrom(5);
        executeToolsDto.setAuditId(0L);
        executeToolsDto.setToolsId(0L);

        final UserDto user = new UserDto();
        user.setUserName("userName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        // Configure IToolsInfoService.selectToolsInfoById(...).
        final ToolsInfoDto toolsInfoDto = new ToolsInfoDto();
        toolsInfoDto.setId(0L);
        toolsInfoDto.setName("name");
        toolsInfoDto.setType(0);
        toolsInfoDto.setScriptIds("scriptIds");
        toolsInfoDto.setScriptEditing("scriptContent");
//        when(mockToolsInfoService.selectToolsInfoById(0L)).thenReturn(toolsInfoDto);
        final ToolsDto toolsInfoCategoryEntity = new ToolsDto();
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setType(3);
        toolsInfoCategoryEntity.setClassification(0);
        toolsInfoCategoryEntity.setScriptOperatingUser("scriptOperatingUser");
        toolsInfoCategoryEntity.setStatus(0);
        toolsInfoCategoryEntity.setBusinessSystemId(0L);
        when(mockToolsInfoService.getToolsCombinedInfo(0L)).thenReturn(toolsInfoCategoryEntity);

        // Run the test
        final R<Object> result = toolsExecuteServiceImplUnderTest.executeTools(executeToolsDto, user);
        // Verify the results
        assertThat(result.getCode()).isEqualTo("10000");
    }

    /**
     * 执行描述工具    直接执行
     *
     * @throws Exception
     */
    @Test
    void testExecuteToolsMs_ExecuteForSuccess() throws Exception {
        // Setup
        final ExecuteToolsDto executeToolsDto = new ExecuteToolsDto();
        final AuditExecutAgentDto agentDto = new AuditExecutAgentDto();
        agentDto.setAgentIp("agentIp");
        agentDto.setAgentPort(0);
        agentDto.setSysAgentInfoId(0L);
        executeToolsDto.setAgentIps(Arrays.asList(agentDto));
        final AuditExecutParamDto params = new AuditExecutParamDto();
        params.setParamJson("[{\"id\":1,\"sort\":2,\"name\":'12312',\"type\":2,\"value\":'2',\"description\":'123123'}]");
        params.setScriptContent("scriptContent");
        params.setAuditId(0L);
        params.setScriptIds("scriptIds");
        executeToolsDto.setParams(params);
        executeToolsDto.setExecFrom(0);
        executeToolsDto.setAuditId(0L);
        executeToolsDto.setToolsId(0L);

        final UserDto user = new UserDto();
        user.setUserName("userName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        // Configure IToolsInfoService.selectToolsInfoById(...).
        final ToolsInfoDto toolsInfoDto = new ToolsInfoDto();
        toolsInfoDto.setId(0L);
        toolsInfoDto.setName("name");
        toolsInfoDto.setType(1);
        toolsInfoDto.setScriptIds("scriptIds");
        toolsInfoDto.setScriptEditing("scriptContent");
//        when(mockToolsInfoService.selectToolsInfoById(0L)).thenReturn(toolsInfoDto);
        final ToolsDto toolsInfoCategoryEntity = new ToolsDto();
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setType(1);
        toolsInfoCategoryEntity.setClassification(0);
        toolsInfoCategoryEntity.setScriptOperatingUser("scriptOperatingUser");
        toolsInfoCategoryEntity.setStatus(0);
        toolsInfoCategoryEntity.setBusinessSystemId(0L);
        when(mockToolsInfoService.getToolsCombinedInfo(0L)).thenReturn(toolsInfoCategoryEntity);
        // Run the test
        final R<Object> result = toolsExecuteServiceImplUnderTest.executeTools(executeToolsDto, user);
        // Verify the results
        assertThat(result.getCode()).isEqualTo("10000");
    }

    /**
     * 执行工具   不存在工具
     *
     * @throws Exception
     */
   /* @Test
    void testExecuteToolsMs_ExecuteForNotToolsInfo() throws Exception {
        // Setup
        final ExecuteToolsDto executeToolsDto = new ExecuteToolsDto();
        final AuditExecutAgentDto agentDto = new AuditExecutAgentDto();
        agentDto.setAgentIp("agentIp");
        agentDto.setAgentPort(0);
        agentDto.setSysAgentInfoId(0L);
        executeToolsDto.setAgentIps(Arrays.asList(agentDto));
        final AuditExecutParamDto params = new AuditExecutParamDto();
        params.setParamJson("[{\"id\":1,\"sort\":2,\"name\":'12312',\"type\":2,\"value\":'2',\"description\":'123123'}]");
        params.setScriptContent("scriptContent");
        params.setAuditId(0L);
        params.setScriptIds("scriptIds");
        executeToolsDto.setParams(params);
        executeToolsDto.setExecFrom(0);
        executeToolsDto.setAuditId(0L);
        executeToolsDto.setToolsId(0L);

        final UserDto user = new UserDto();
        user.setUserName("userName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        // Configure IToolsInfoService.selectToolsInfoById(...).
        final ToolsInfoDto toolsInfoDto = new ToolsInfoDto();
        toolsInfoDto.setId(null);
        toolsInfoDto.setName("name");
        toolsInfoDto.setType(1);
        toolsInfoDto.setScriptIds("scriptIds");
        toolsInfoDto.setScriptEditing("scriptContent");
        when(mockToolsInfoService.selectToolsInfoById(0L)).thenReturn(toolsInfoDto);

        // Run the test
        final R<Object> result = new R<>();
        // Verify the results
        assertThat(result.getCode()).isEqualTo("10602");
    }

    *//**
     * 执行描述工具  直接执行异常
     *
     * @throws Exception
     *//*
    @Test
    void testExecuteToolsMs_IToolsOperateServiceThrowsScriptToolsException() throws Exception {
        // Setup
        final ExecuteToolsDto executeToolsDto = new ExecuteToolsDto();
        final AuditExecutAgentDto agentDto = new AuditExecutAgentDto();
        agentDto.setAgentIp("agentIp");
        agentDto.setAgentPort(0);
        agentDto.setSysAgentInfoId(0L);
        executeToolsDto.setAgentIps(Arrays.asList(agentDto));
        final AuditExecutParamDto params = new AuditExecutParamDto();
        params.setParamJson("paramJson");
        params.setScriptContent("scriptContent");
        params.setAuditId(0L);
        params.setScriptIds("scriptIds");
        executeToolsDto.setParams(params);
        executeToolsDto.setExecFrom(0);
        executeToolsDto.setAuditId(0L);
        executeToolsDto.setToolsId(0L);

        final UserDto user = new UserDto();
        user.setUserName("userName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        // Configure IToolsInfoService.selectToolsInfoById(...).
        final ToolsInfoDto toolsInfoDto = new ToolsInfoDto();
        toolsInfoDto.setId(0L);
        toolsInfoDto.setName("name");
        toolsInfoDto.setType(1);
        toolsInfoDto.setScriptIds("scriptIds");
        toolsInfoDto.setScriptEditing("scriptContent");
        when(mockToolsInfoService.selectToolsInfoById(0L)).thenReturn(toolsInfoDto);

        // Configure IToolsOperateService.execute(...).
        final ToolsParamDto toolsParamDto = new ToolsParamDto();
        toolsParamDto.setId(0L);
        toolsParamDto.setSort(0L);
        toolsParamDto.setName("name");
        toolsParamDto.setType("type");
        toolsParamDto.setValue("value");
        toolsParamDto.setDescription("description");
        toolsParamDto.setTdToolsId(0L);
        final List<ToolsParamDto> toolsParamDtoList = Arrays.asList(toolsParamDto);


        // Run the test
        assertThatThrownBy(() -> toolsExecuteServiceImplUnderTest.executeTools(executeToolsDto, user))
                .isInstanceOf(ToolsException.class);
    }

    *//**
     * 执行脚本工具  直接执行异常
     *
     * @throws Exception
     *//*
    @Test
    void testExecuteToolsJb_IToolsOperateServiceThrowsScriptToolsException() throws Exception {
        // Setup
        final ExecuteToolsDto executeToolsDto = new ExecuteToolsDto();
        final AuditExecutAgentDto agentDto = new AuditExecutAgentDto();
        agentDto.setAgentIp("agentIp");
        agentDto.setAgentPort(0);
        agentDto.setSysAgentInfoId(0L);
        executeToolsDto.setAgentIps(Arrays.asList(agentDto));
        final AuditExecutParamDto params = new AuditExecutParamDto();
        params.setParamJson("[{\"id\":1,\"sort\":2,\"name\":'12312',\"type\":2,\"value\":'2',\"description\":'123123'}]");
        params.setScriptContent("scriptContent");
        params.setAuditId(0L);
        params.setScriptIds("scriptIds");
        executeToolsDto.setParams(params);
        executeToolsDto.setExecFrom(0);
        executeToolsDto.setAuditId(0L);
        executeToolsDto.setToolsId(0L);

        final UserDto user = new UserDto();
        user.setUserName("userName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        // Configure IToolsInfoService.selectToolsInfoById(...).
        final ToolsInfoDto toolsInfoDto = new ToolsInfoDto();
        toolsInfoDto.setId(0L);
        toolsInfoDto.setName("name");
        toolsInfoDto.setType(3);
        toolsInfoDto.setScriptIds("scriptIds");
        toolsInfoDto.setScriptEditing("scriptContent");
        when(mockToolsInfoService.selectToolsInfoById(0L)).thenReturn(toolsInfoDto);

        // Configure IToolsOperateService.execute(...).
        final ToolsParamDto toolsParamDto = new ToolsParamDto();
        toolsParamDto.setId(0L);
        toolsParamDto.setSort(0L);
        toolsParamDto.setName("name");
        toolsParamDto.setType("type");
        toolsParamDto.setValue("value");
        toolsParamDto.setDescription("description");
        toolsParamDto.setTdToolsId(0L);
        final List<ToolsParamDto> toolsParamDtoList = Arrays.asList(toolsParamDto);


        // Run the test
        assertThatThrownBy(() -> toolsExecuteServiceImplUnderTest.executeTools(executeToolsDto, user))
                .isInstanceOf(ToolsException.class);
    }*/

    /**
     * 执行描述工具  直接执行异常
     *
     * @throws Exception
     */
    @Test
    void testExecuteToolsMs_IToolsOperateServiceThrowsExecuteMonitorException() throws Exception {
        // Setup
        final ExecuteToolsDto executeToolsDto = new ExecuteToolsDto();
        final AuditExecutAgentDto agentDto = new AuditExecutAgentDto();
        agentDto.setAgentIp("agentIp");
        agentDto.setAgentPort(0);
        agentDto.setSysAgentInfoId(0L);
        executeToolsDto.setAgentIps(Arrays.asList(agentDto));
        final AuditExecutParamDto params = new AuditExecutParamDto();
        params.setParamJson("paramJson");
        params.setScriptContent("scriptContent");
        params.setAuditId(0L);
        params.setScriptIds("scriptIds");
        executeToolsDto.setParams(params);
        executeToolsDto.setExecFrom(0);
        executeToolsDto.setAuditId(0L);
        executeToolsDto.setToolsId(0L);

        final UserDto user = new UserDto();
        user.setUserName("userName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        // Configure IToolsInfoService.selectToolsInfoById(...).
        final ToolsInfoDto toolsInfoDto = new ToolsInfoDto();
        toolsInfoDto.setId(0L);
        toolsInfoDto.setName("name");
        toolsInfoDto.setType(1);
        toolsInfoDto.setScriptIds("scriptIds");
        toolsInfoDto.setScriptEditing("scriptContent");
//        when(mockToolsInfoService.selectToolsInfoById(0L)).thenReturn(toolsInfoDto);

        // Configure IToolsOperateService.execute(...).
        final ToolsParamDto toolsParamDto = new ToolsParamDto();
        toolsParamDto.setId(0L);
        toolsParamDto.setSort(0L);
        toolsParamDto.setName("name");
        toolsParamDto.setType("type");
        toolsParamDto.setValue("value");
        toolsParamDto.setDescription("description");
        toolsParamDto.setTdToolsId(0L);
        final List<ToolsParamDto> toolsParamDtoList = Arrays.asList(toolsParamDto);
//        doThrow(ExecuteMonitorException.class).when(mockToolsOperateService).execute(any(ScriptExecuteDto.class),
//                any(ToolsDto.class), eq(null), Mockito.anyInt(), any(List.class), any(UserDto.class), eq(null));


    }

    /**
     * 执行脚本工具  直接执行异常
     *
     * @throws Exception
     */
    @Test
    void testExecuteToolsJb_IToolsOperateServiceThrowsExecuteMonitorException() throws Exception {
        // Setup
        final ExecuteToolsDto executeToolsDto = new ExecuteToolsDto();
        final AuditExecutAgentDto agentDto = new AuditExecutAgentDto();
        agentDto.setAgentIp("agentIp");
        agentDto.setAgentPort(0);
        agentDto.setSysAgentInfoId(0L);
        executeToolsDto.setAgentIps(Arrays.asList(agentDto));
        final AuditExecutParamDto params = new AuditExecutParamDto();
        params.setParamJson("[{\"id\":1,\"sort\":2,\"name\":'12312',\"type\":2,\"value\":'2',\"description\":'123123'}]");
        params.setScriptContent("scriptContent");
        params.setAuditId(0L);
        params.setScriptIds("scriptIds");
        executeToolsDto.setParams(params);
        executeToolsDto.setExecFrom(0);
        executeToolsDto.setAuditId(0L);
        executeToolsDto.setToolsId(0L);

        final UserDto user = new UserDto();
        user.setUserName("userName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        // Configure IToolsInfoService.selectToolsInfoById(...).
        final ToolsInfoDto toolsInfoDto = new ToolsInfoDto();
        toolsInfoDto.setId(0L);
        toolsInfoDto.setName("name");
        toolsInfoDto.setType(3);
        toolsInfoDto.setScriptIds("scriptIds");
        toolsInfoDto.setScriptEditing("scriptContent");
//        when(mockToolsInfoService.selectToolsInfoById(0L)).thenReturn(toolsInfoDto);

        // Configure IToolsOperateService.execute(...).
        final ToolsParamDto toolsParamDto = new ToolsParamDto();
        toolsParamDto.setId(0L);
        toolsParamDto.setSort(0L);
        toolsParamDto.setName("name");
        toolsParamDto.setType("type");
        toolsParamDto.setValue("value");
        toolsParamDto.setDescription("description");
        toolsParamDto.setTdToolsId(0L);
        final List<ToolsParamDto> toolsParamDtoList = Arrays.asList(toolsParamDto);
//        doThrow(ExecuteMonitorException.class).when(mockToolsOperateService).execute(any(ScriptExecuteDto.class),
//                any(ToolsDto.class), eq(null), Mockito.anyInt(), any(List.class), any(UserDto.class), eq(null));


    }


    /**
     * 执行脚本诊断工具    审批执行
     *
     * @throws Exception
     */
    @Test
    void testExecuteToolsAlarm_AuditExecuteForSuccess() throws Exception {
        // Setup
        final ExecuteToolsDto executeToolsDto = new ExecuteToolsDto();
        final AuditExecutAgentDto agentDto = new AuditExecutAgentDto();
        agentDto.setAgentIp("agentIp");
        agentDto.setAgentPort(0);
        agentDto.setSysAgentInfoId(0L);
        executeToolsDto.setAgentIps(Arrays.asList(agentDto));
        final AuditExecutParamDto params = new AuditExecutParamDto();
        params.setParamJson("[{\"id\":1,\"sort\":2,\"name\":'12312',\"type\":2,\"value\":'2',\"description\":'123123'}]");
        params.setScriptContent("scriptContent");
        params.setAuditId(0L);
        params.setScriptIds("scriptIds");
        executeToolsDto.setParams(params);
        executeToolsDto.setExecFrom(5);
        executeToolsDto.setAuditId(1L);
        executeToolsDto.setToolsId(0L);

        final UserDto user = new UserDto();
        user.setUserName("userName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        // Configure IToolsInfoService.selectToolsInfoById(...).
        final ToolsInfoDto toolsInfoDto = new ToolsInfoDto();
        toolsInfoDto.setId(0L);
        toolsInfoDto.setName("name");
        toolsInfoDto.setType(0);
        toolsInfoDto.setScriptIds("scriptIds");
        toolsInfoDto.setScriptEditing("scriptContent");
//        when(mockToolsInfoService.selectToolsInfoById(0L)).thenReturn(toolsInfoDto);
        final ToolsDto toolsInfoCategoryEntity = new ToolsDto();
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setType(1);
        toolsInfoCategoryEntity.setClassification(0);
        toolsInfoCategoryEntity.setScriptOperatingUser("scriptOperatingUser");
        toolsInfoCategoryEntity.setStatus(0);
        toolsInfoCategoryEntity.setBusinessSystemId(0L);
        when(mockToolsInfoService.getToolsCombinedInfo(0L)).thenReturn(toolsInfoCategoryEntity);
        // Configure ToolsExecuteMapper.getExecuteAudit(...).
        final ExecuteAuditBean executeAuditBean = new ExecuteAuditBean();
        final AuditDto auditDto = new AuditDto();
        auditDto.setId(0L);
        auditDto.setApplyId(0L);
        auditDto.setType(0);
        auditDto.setApprovalState(1);
        executeAuditBean.setAuditDto(auditDto);
        executeAuditBean.setAgentIps(Arrays.asList(agentDto));
        executeAuditBean.setParams(params);
        when(mockToolsExecuteMapper.getExecuteAudit(any(ExecuteAuditEntity.class))).thenReturn(executeAuditBean);

        when(mockExecuteMonitorService.matchAuditId(0L)).thenReturn(false);
        // Run the test
        final R<Object> result = toolsExecuteServiceImplUnderTest.executeTools(executeToolsDto, user);
        // Verify the results
        assertThat(result.getCode()).isEqualTo("10000");
    }

    /**
     * 执行脚本场景工具    审批执行
     *
     * @throws Exception
     */
    @Test
    void testExecuteToolsFavorites_AuditExecuteForSuccess() throws Exception {
        // Setup
        final ExecuteToolsDto executeToolsDto = new ExecuteToolsDto();
        final AuditExecutAgentDto agentDto = new AuditExecutAgentDto();
        agentDto.setAgentIp("agentIp");
        agentDto.setAgentPort(0);
        agentDto.setSysAgentInfoId(0L);
        executeToolsDto.setAgentIps(Arrays.asList(agentDto));
        final AuditExecutParamDto params = new AuditExecutParamDto();
        params.setParamJson("[{\"id\":1,\"sort\":2,\"name\":'12312',\"type\":2,\"value\":'2',\"description\":'123123'}]");
        params.setScriptContent("scriptContent");
        params.setAuditId(0L);
        params.setScriptIds("scriptIds");
        executeToolsDto.setParams(params);
        executeToolsDto.setExecFrom(4);
        executeToolsDto.setAuditId(1L);
        executeToolsDto.setToolsId(0L);

        final UserDto user = new UserDto();
        user.setUserName("userName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        // Configure IToolsInfoService.selectToolsInfoById(...).
        final ToolsInfoDto toolsInfoDto = new ToolsInfoDto();
        toolsInfoDto.setId(0L);
        toolsInfoDto.setName("name");
        toolsInfoDto.setType(0);
        toolsInfoDto.setScriptIds("scriptIds");
        toolsInfoDto.setScriptEditing("scriptContent");
//        when(mockToolsInfoService.selectToolsInfoById(0L)).thenReturn(toolsInfoDto);
        final ToolsDto toolsInfoCategoryEntity = new ToolsDto();
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setType(1);
        toolsInfoCategoryEntity.setClassification(0);
        toolsInfoCategoryEntity.setScriptOperatingUser("scriptOperatingUser");
        toolsInfoCategoryEntity.setStatus(0);
        toolsInfoCategoryEntity.setBusinessSystemId(0L);
        when(mockToolsInfoService.getToolsCombinedInfo(0L)).thenReturn(toolsInfoCategoryEntity);
        // Configure ToolsExecuteMapper.getExecuteAudit(...).
        final ExecuteAuditBean executeAuditBean = new ExecuteAuditBean();
        final AuditDto auditDto = new AuditDto();
        auditDto.setId(0L);
        auditDto.setApplyId(0L);
        auditDto.setType(0);
        auditDto.setApprovalState(1);
        executeAuditBean.setAuditDto(auditDto);
        executeAuditBean.setAgentIps(Arrays.asList(agentDto));
        executeAuditBean.setParams(params);
        when(mockToolsExecuteMapper.getExecuteAudit(any(ExecuteAuditEntity.class))).thenReturn(executeAuditBean);
        final AuditExecutAgentEntity auditExecutAgent = new AuditExecutAgentEntity();
        auditExecutAgent.setAgentIp("agentIp");
        auditExecutAgent.setAgentPort(0);
        auditExecutAgent.setSysAgentInfoId(0L);
        AuditExecutAgentEntity auditExecutAgentEntity = new AuditExecutAgentEntity();
        auditExecutAgentEntity.setAuditId(0L);
        when(auditExecutAgentMapper.selectAuditExecutAgentList(any())).thenReturn(Arrays.asList(auditExecutAgent));

        when(mockExecuteMonitorService.matchAuditId(0L)).thenReturn(false);
        // Run the test
        final R<Object> result = toolsExecuteServiceImplUnderTest.executeTools(executeToolsDto, user);
        // Verify the results
        assertThat(result.getCode()).isEqualTo("10000");
    }

    /**
     * 执行工具    审批未通过
     *
     * @throws Exception
     */
   /* @Test
    void testExecuteTools_AuditExecuteForNotAuditSuccess() throws Exception {
        // Setup
        final ExecuteToolsDto executeToolsDto = new ExecuteToolsDto();
        final AuditExecutAgentDto agentDto = new AuditExecutAgentDto();
        agentDto.setAgentIp("agentIp");
        agentDto.setAgentPort(0);
        agentDto.setSysAgentInfoId(0L);
        executeToolsDto.setAgentIps(Arrays.asList(agentDto));
        final AuditExecutParamDto params = new AuditExecutParamDto();
        params.setParamJson("[{\"id\":1,\"sort\":2,\"name\":'12312',\"type\":2,\"value\":'2',\"description\":'123123'}]");
        params.setScriptContent("scriptContent");
        params.setAuditId(0L);
        params.setScriptIds("scriptIds");
        executeToolsDto.setParams(params);
        executeToolsDto.setExecFrom(0);
        executeToolsDto.setAuditId(1L);
        executeToolsDto.setToolsId(0L);

        final UserDto user = new UserDto();
        user.setUserName("userName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        // Configure IToolsInfoService.selectToolsInfoById(...).
        final ToolsInfoDto toolsInfoDto = new ToolsInfoDto();
        toolsInfoDto.setId(0L);
        toolsInfoDto.setName("name");
        toolsInfoDto.setType(1);
        toolsInfoDto.setScriptIds("scriptIds");
        toolsInfoDto.setScriptEditing("scriptContent");
        when(mockToolsInfoService.selectToolsInfoById(0L)).thenReturn(toolsInfoDto);
        // Configure ToolsExecuteMapper.getExecuteAudit(...).
        final ExecuteAuditBean executeAuditBean = new ExecuteAuditBean();
        final AuditDto auditDto = new AuditDto();
        auditDto.setId(0L);
        auditDto.setApplyId(0L);
        auditDto.setType(0);
        auditDto.setApprovalState(5);
        executeAuditBean.setAuditDto(auditDto);
        executeAuditBean.setAgentIps(Arrays.asList(agentDto));
        executeAuditBean.setParams(params);
        when(mockToolsExecuteMapper.getExecuteAudit(any(ExecuteAuditEntity.class))).thenReturn(executeAuditBean);

        // Run the test
        final R<Object> result = new R<>();
        // Verify the results
        assertThat(result.getCode()).isEqualTo("10602");
    }*/

    /**
     * 执行工具    审批通过已经执行
     *
     * @throws Exception
     */
  /*  @Test
    void testExecuteTools_AuditExecuteForAuditSuccessNotExecute() throws Exception {
        // Setup
        final ExecuteToolsDto executeToolsDto = new ExecuteToolsDto();
        final AuditExecutAgentDto agentDto = new AuditExecutAgentDto();
        agentDto.setAgentIp("agentIp");
        agentDto.setAgentPort(0);
        agentDto.setSysAgentInfoId(0L);
        executeToolsDto.setAgentIps(Arrays.asList(agentDto));
        final AuditExecutParamDto params = new AuditExecutParamDto();
        params.setParamJson("[{\"id\":1,\"sort\":2,\"name\":'12312',\"type\":2,\"value\":'2',\"description\":'123123'}]");
        params.setScriptContent("scriptContent");
        params.setAuditId(0L);
        params.setScriptIds("scriptIds");
        executeToolsDto.setParams(params);
        executeToolsDto.setExecFrom(0);
        executeToolsDto.setAuditId(1L);
        executeToolsDto.setToolsId(0L);

        final UserDto user = new UserDto();
        user.setUserName("userName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        // Configure IToolsInfoService.selectToolsInfoById(...).
        final ToolsInfoDto toolsInfoDto = new ToolsInfoDto();
        toolsInfoDto.setId(0L);
        toolsInfoDto.setName("name");
        toolsInfoDto.setType(1);
        toolsInfoDto.setScriptIds("scriptIds");
        toolsInfoDto.setScriptEditing("scriptContent");
        when(mockToolsInfoService.selectToolsInfoById(0L)).thenReturn(toolsInfoDto);
        // Configure ToolsExecuteMapper.getExecuteAudit(...).
        final ExecuteAuditBean executeAuditBean = new ExecuteAuditBean();
        final AuditDto auditDto = new AuditDto();
        auditDto.setId(0L);
        auditDto.setApplyId(0L);
        auditDto.setType(0);
        auditDto.setApprovalState(1);
        executeAuditBean.setAuditDto(auditDto);
        executeAuditBean.setAgentIps(Arrays.asList(agentDto));
        executeAuditBean.setParams(params);
        when(mockToolsExecuteMapper.getExecuteAudit(any(ExecuteAuditEntity.class))).thenReturn(executeAuditBean);
        when(mockExecuteMonitorService.matchAuditId(0L)).thenReturn(true);
        // Run the test
        final R<Object> result = new R<>();
        // Verify the results
        assertThat(result.getCode()).isEqualTo("10602");
    }*/

    /**
     * 执行脚本工具  审批执行异常
     *
     * @throws Exception
     */
    @Test
    void testExecuteToolsJb_AuditIToolsOperateServiceThrowsScriptToolsException() throws Exception {
        // Setup
        final ExecuteToolsDto executeToolsDto = new ExecuteToolsDto();
        final AuditExecutAgentDto agentDto = new AuditExecutAgentDto();
        agentDto.setAgentIp("agentIp");
        agentDto.setAgentPort(0);
        agentDto.setSysAgentInfoId(0L);
        executeToolsDto.setAgentIps(Arrays.asList(agentDto));
        final AuditExecutParamDto params = new AuditExecutParamDto();
        params.setParamJson("[{\"id\":1,\"sort\":2,\"name\":'12312',\"type\":2,\"value\":'2',\"description\":'123123'}]");
        params.setScriptContent("scriptContent");
        params.setAuditId(0L);
        params.setScriptIds("scriptIds");
        executeToolsDto.setParams(params);
        executeToolsDto.setExecFrom(0);
        executeToolsDto.setAuditId(1L);
        executeToolsDto.setToolsId(0L);

        final UserDto user = new UserDto();
        user.setUserName("userName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        // Configure IToolsInfoService.selectToolsInfoById(...).
        final ToolsInfoDto toolsInfoDto = new ToolsInfoDto();
        toolsInfoDto.setId(0L);
        toolsInfoDto.setName("name");
        toolsInfoDto.setType(3);
        toolsInfoDto.setScriptIds("scriptIds");
        toolsInfoDto.setScriptEditing("scriptContent");
//        when(mockToolsInfoService.selectToolsInfoById(0L)).thenReturn(toolsInfoDto);

        // Configure IToolsOperateService.execute(...).
        final ToolsParamDto toolsParamDto = new ToolsParamDto();
        toolsParamDto.setId(0L);
        toolsParamDto.setSort(0L);
        toolsParamDto.setName("name");
        toolsParamDto.setType("type");
        toolsParamDto.setValue("value");
        toolsParamDto.setDescription("description");
        toolsParamDto.setTdToolsId(0L);
        final List<ToolsParamDto> toolsParamDtoList = Arrays.asList(toolsParamDto);
        // Configure ToolsExecuteMapper.getExecuteAudit(...).
        final ExecuteAuditBean executeAuditBean = new ExecuteAuditBean();
        final AuditDto auditDto = new AuditDto();
        auditDto.setId(0L);
        auditDto.setApplyId(0L);
        auditDto.setType(0);
        auditDto.setApprovalState(1);
        executeAuditBean.setAuditDto(auditDto);
        executeAuditBean.setAgentIps(Arrays.asList(agentDto));
        executeAuditBean.setParams(params);
//        when(mockToolsExecuteMapper.getExecuteAudit(any(ExecuteAuditEntity.class))).thenReturn(executeAuditBean);
//        when(mockExecuteMonitorService.matchAuditId(0L)).thenReturn(false);


    }

    /**
     * 执行脚本工具  直接执行异常
     *
     * @throws Exception
     */
    @Test
    void testExecuteToolsJb_AuditIToolsOperateServiceThrowsExecuteMonitorException() throws Exception {
        // Setup
        final ExecuteToolsDto executeToolsDto = new ExecuteToolsDto();
        final AuditExecutAgentDto agentDto = new AuditExecutAgentDto();
        agentDto.setAgentIp("agentIp");
        agentDto.setAgentPort(0);
        agentDto.setSysAgentInfoId(0L);
        executeToolsDto.setAgentIps(Arrays.asList(agentDto));
        final AuditExecutParamDto params = new AuditExecutParamDto();
        params.setParamJson("[{\"id\":1,\"sort\":2,\"name\":'12312',\"type\":2,\"value\":'2',\"description\":'123123'}]");
        params.setScriptContent("scriptContent");
        params.setAuditId(0L);
        params.setScriptIds("scriptIds");
        executeToolsDto.setParams(params);
        executeToolsDto.setExecFrom(0);
        executeToolsDto.setAuditId(1L);
        executeToolsDto.setToolsId(0L);

        final UserDto user = new UserDto();
        user.setUserName("userName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        // Configure IToolsInfoService.selectToolsInfoById(...).
        final ToolsInfoDto toolsInfoDto = new ToolsInfoDto();
        toolsInfoDto.setId(0L);
        toolsInfoDto.setName("name");
        toolsInfoDto.setType(3);
        toolsInfoDto.setScriptIds("scriptIds");
        toolsInfoDto.setScriptEditing("scriptContent");
//        when(mockToolsInfoService.selectToolsInfoById(0L)).thenReturn(toolsInfoDto);

        // Configure IToolsOperateService.execute(...).
        final ToolsParamDto toolsParamDto = new ToolsParamDto();
        toolsParamDto.setId(0L);
        toolsParamDto.setSort(0L);
        toolsParamDto.setName("name");
        toolsParamDto.setType("type");
        toolsParamDto.setValue("value");
        toolsParamDto.setDescription("description");
        toolsParamDto.setTdToolsId(0L);
        final List<ToolsParamDto> toolsParamDtoList = Arrays.asList(toolsParamDto);
        // Configure ToolsExecuteMapper.getExecuteAudit(...).
        final ExecuteAuditBean executeAuditBean = new ExecuteAuditBean();
        final AuditDto auditDto = new AuditDto();
        auditDto.setId(0L);
        auditDto.setApplyId(0L);
        auditDto.setType(0);
        auditDto.setApprovalState(1);
        executeAuditBean.setAuditDto(auditDto);
        executeAuditBean.setAgentIps(Arrays.asList(agentDto));
        executeAuditBean.setParams(params);
//        when(mockToolsExecuteMapper.getExecuteAudit(any(ExecuteAuditEntity.class))).thenReturn(executeAuditBean);
//        when(mockExecuteMonitorService.matchAuditId(0L)).thenReturn(false);


    }


    @Test
    void testIsImportantSystem() {
        // Setup
        // Configure SystemDataInteract.getBusinessSystemInfo(...).
        final BusinessSystemDto businessSystemDto = new BusinessSystemDto();
        businessSystemDto.setWhiteList(0);
        businessSystemDto.setId(0L);
        businessSystemDto.setName("name");
        businessSystemDto.setCode("code");
        businessSystemDto.setTypeName("typeName");
        final List<BusinessSystemDto> businessSystemDtos = Arrays.asList(businessSystemDto);
        when(mockSystemDataInteract.getBusinessSystemInfoForType(0L)).thenReturn(true);

        // Run the test
        final boolean result = toolsExecuteServiceImplUnderTest.isImportantSystem(0L);

        // Verify the results
        assertThat(result).isTrue();
    }

    @Test
    void testIsImportantSystem_SystemDataInteractReturnsNoItems() {
        // Setup
        when(mockSystemDataInteract.getBusinessSystemInfoForType(0L)).thenReturn(true);

        // Run the test
        final boolean result = toolsExecuteServiceImplUnderTest.isImportantSystem(0L);

        // Verify the results
        assertThat(result).isTrue();
    }
}

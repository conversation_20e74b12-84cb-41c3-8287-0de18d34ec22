package com.ideal.tools.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.ideal.common.util.PageDataUtil;
import com.ideal.tools.mapper.DevelopmentReportMapper;
import com.ideal.tools.model.bean.DevelopmentReportExcle;
import com.ideal.tools.model.dto.DevelopmentReportDto;
import com.ideal.tools.model.dto.DevelopmentReportQueryDto;
import com.ideal.tools.model.dto.DevelopmentReportResultDto;
import com.ideal.tools.model.entity.DevelopmentReportEntity;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class DevelopmentReportServiceImplTest {

    @Mock
    private DevelopmentReportMapper mockDevelopmentReportMapper;

    private DevelopmentReportServiceImpl developmentReportServiceImplUnderTest;

    @BeforeEach
    void setUp() throws Exception {
        developmentReportServiceImplUnderTest = new DevelopmentReportServiceImpl(mockDevelopmentReportMapper);
    }
    @Mock
    private MockedStatic<PageDataUtil> mockedStaticPageDataUtil;

    @AfterEach
    void tearDown() {
        mockedStaticPageDataUtil.close();
    }

    @Test
    void testSelectDevelopmentReportList() {
        // Setup
        final DevelopmentReportQueryDto query = new DevelopmentReportQueryDto();
        query.setBusinessSystemId(0L);
        query.setCreatorId(0L);
        query.setCreateStartTime(new GregorianCalendar(2024, Calendar.JANUARY, 1).getTime());
//        query.setCreateEndTime("createEndTime");
        query.setUpdateStartTime(new GregorianCalendar(2024, Calendar.JANUARY, 1).getTime());

        // Configure DevelopmentReportMapper.selectDevelopmentReportList(...).
        final DevelopmentReportEntity developmentReportEntity = new DevelopmentReportEntity();
        developmentReportEntity.setId(0L);
        developmentReportEntity.setTdToolsId(0L);
        developmentReportEntity.setToolCode("toolCode");
        developmentReportEntity.setCreatorId(0L);
        developmentReportEntity.setToolType(1);
        developmentReportEntity.setToolStatus(2);
        developmentReportEntity.setCreatorName("creatorName");

        final List<DevelopmentReportEntity> developmentReportEntities = Collections.singletonList(developmentReportEntity);
        MockedStatic<PageMethod> pageMethodMock = mockStatic(PageMethod.class);
        Page<DevelopmentReportEntity> pageMock = new Page<>(0,0);
        pageMethodMock.when(()->PageMethod.startPage(anyInt(),anyInt())).thenReturn(pageMock);
        pageMock.addAll(developmentReportEntities);
        when(mockDevelopmentReportMapper.selectDevelopmentReportList(any(DevelopmentReportQueryDto.class)))
                .thenReturn(pageMock);

        // Run the test
        final PageInfo<DevelopmentReportResultDto> result = developmentReportServiceImplUnderTest.selectDevelopmentReportList(
                query, 0, 0);


    }

    @Test
    void testSelectDevelopmentReportList_DevelopmentReportMapperReturnsNoItems() {
        // Setup
        final DevelopmentReportQueryDto query = new DevelopmentReportQueryDto();
        query.setBusinessSystemId(0L);
        query.setCreatorId(0L);
        query.setCreateStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
//        query.setCreateEndTime("createEndTime");
        query.setUpdateStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final DevelopmentReportEntity developmentReportEntity = new DevelopmentReportEntity();
        developmentReportEntity.setId(0L);
        developmentReportEntity.setTdToolsId(0L);
        developmentReportEntity.setToolCode("toolCode");
        developmentReportEntity.setCreatorId(0L);
        developmentReportEntity.setToolType(1);
        developmentReportEntity.setToolStatus(2);
        developmentReportEntity.setCreatorName("creatorName");
        final List<DevelopmentReportEntity> developmentReportEntities = Collections.singletonList(developmentReportEntity);
//        MockedStatic<PageMethod> pageMethodMock = mockStatic(PageMethod.class);
//        Page<DevelopmentReportEntity> pageMock = new Page<>(0,0);
//        pageMethodMock.when(()->PageMethod.startPage(anyInt(),anyInt())).thenReturn(pageMock);
//        pageMock.addAll(developmentReportEntities);
//        when(mockDevelopmentReportMapper.selectDevelopmentReportList(any(DevelopmentReportQueryDto.class)))
//                .thenReturn(pageMock);

        // Run the test
        final PageInfo<DevelopmentReportResultDto> result = developmentReportServiceImplUnderTest.selectDevelopmentReportList(
                query, 0, 0);


    }

    @Test
    void testInsertDevelopmentReport() {
        // Setup
        final DevelopmentReportDto developmentReportDto = new DevelopmentReportDto();
        developmentReportDto.setId(0L);
        developmentReportDto.setTdToolsId(0L);
        developmentReportDto.setToolCode("toolCode");
        developmentReportDto.setToolName("toolName");
        developmentReportDto.setBusinessSystemId(0L);
        developmentReportDto.setCreatorId(0L);
        developmentReportDto.setCreatorName("creatorName");
        developmentReportDto.setUpdatorId(0L);
        developmentReportDto.setUpdatorName("updatorName");
        when(mockDevelopmentReportMapper.insertDevelopmentReport(any(DevelopmentReportEntity.class))).thenReturn(0);

        // Run the test
        final int result = developmentReportServiceImplUnderTest.insertDevelopmentReport(developmentReportDto);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testUpdateDevelopmentReport() {
        // Setup
        final DevelopmentReportDto developmentReportDto = new DevelopmentReportDto();
        developmentReportDto.setId(0L);
        developmentReportDto.setTdToolsId(0L);
        developmentReportDto.setToolCode("toolCode");
        developmentReportDto.setToolName("toolName");
        developmentReportDto.setBusinessSystemId(0L);
        developmentReportDto.setCreatorId(0L);
        developmentReportDto.setCreatorName("creatorName");
        developmentReportDto.setUpdatorId(0L);
        developmentReportDto.setUpdatorName("updatorName");
        when(mockDevelopmentReportMapper.updateDevelopmentReport(any(DevelopmentReportEntity.class))).thenReturn(0);

        // Run the test
        final int result = developmentReportServiceImplUnderTest.updateDevelopmentReport(developmentReportDto);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testExportDevelopmentReportData() {
        // Setup
        final DevelopmentReportQueryDto queryDto = new DevelopmentReportQueryDto();
        queryDto.setBusinessSystemId(0L);
        queryDto.setCreatorId(0L);
        queryDto.setCreateStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
//        queryDto.setCreateEndTime("createEndTime");
        queryDto.setUpdateStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Configure DevelopmentReportMapper.selectDevelopmentReportList(...).
        final DevelopmentReportEntity developmentReportEntity = new DevelopmentReportEntity();
        developmentReportEntity.setId(0L);
        developmentReportEntity.setTdToolsId(0L);
        developmentReportEntity.setToolCode("toolCode");
        developmentReportEntity.setCreatorId(0L);
        developmentReportEntity.setCreatorName("creatorName");
        developmentReportEntity.setToolType(1);
        developmentReportEntity.setToolStatus(1);
        final List<DevelopmentReportEntity> developmentReportEntities = Arrays.asList(developmentReportEntity);
        when(mockDevelopmentReportMapper.selectDevelopmentReportList(any(DevelopmentReportQueryDto.class)))
                .thenReturn(developmentReportEntities);

        // Run the test
        final List<DevelopmentReportExcle> result = developmentReportServiceImplUnderTest.exportDevelopmentReportData(
                queryDto);

        // Verify the results
        assertThat(result).isNotNull();
    }

    @Test
    void testExportDevelopmentReportData_DevelopmentReportMapperReturnsNoItems() {
        // Setup
        final DevelopmentReportQueryDto queryDto = new DevelopmentReportQueryDto();
        queryDto.setBusinessSystemId(0L);
        queryDto.setCreatorId(0L);
        queryDto.setCreateStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
//        queryDto.setCreateEndTime("createEndTime");
        queryDto.setUpdateStartTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        when(mockDevelopmentReportMapper.selectDevelopmentReportList(any(DevelopmentReportQueryDto.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<DevelopmentReportExcle> result = developmentReportServiceImplUnderTest.exportDevelopmentReportData(
                queryDto);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }
}

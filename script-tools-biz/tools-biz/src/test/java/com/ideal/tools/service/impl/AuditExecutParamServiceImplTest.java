package com.ideal.tools.service.impl;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import com.github.pagehelper.PageInfo;
import com.ideal.common.util.PageDataUtil;
import com.ideal.tools.exception.AuditException;
import com.ideal.tools.mapper.AuditExecutParamMapper;
import com.ideal.tools.model.dto.AuditExecutParamDto;
import com.ideal.tools.model.dto.AuditExecutParamQueryDto;
import com.ideal.tools.model.entity.AuditExecutParamEntity;

@ExtendWith(MockitoExtension.class)
class AuditExecutParamServiceImplTest {

    @Mock
    private AuditExecutParamMapper mockAuditExecutParamMapper;

    private AuditExecutParamServiceImpl auditExecutParamServiceImplUnderTest;
    @Mock
    private MockedStatic<PageDataUtil> mockedStaticPageDataUtil;
    @BeforeEach
    void setUp() throws Exception {
        auditExecutParamServiceImplUnderTest = new AuditExecutParamServiceImpl(mockAuditExecutParamMapper);
    }
    @AfterEach
    void tearDown() {
        mockedStaticPageDataUtil.close();
    }
    @Test
    void testSelectAuditExecutParamById() {
        // Setup
        // Configure AuditExecutParamMapper.selectAuditExecutParamById(...).
        final AuditExecutParamEntity auditExecutParamEntity = new AuditExecutParamEntity();
        auditExecutParamEntity.setId(0L);
        auditExecutParamEntity.setParamJson("paramJson");
        auditExecutParamEntity.setScriptContent("scriptContent");
        auditExecutParamEntity.setAuditId(0L);
        auditExecutParamEntity.setScriptIds("scriptIds");
        when(mockAuditExecutParamMapper.selectAuditExecutParamById(0L)).thenReturn(auditExecutParamEntity);

        // Run the test
        final AuditExecutParamDto result = auditExecutParamServiceImplUnderTest.selectAuditExecutParamById(0L);

        // Verify the results
    }

    @Test
    void testSelectAuditExecutParamList() {
        // Setup
        final AuditExecutParamQueryDto auditExecutParamQueryDto = new AuditExecutParamQueryDto();
        auditExecutParamQueryDto.setId(0L);
        auditExecutParamQueryDto.setParamJson("paramJson");
        auditExecutParamQueryDto.setScriptContent("scriptContent");
        auditExecutParamQueryDto.setAuditId(0L);
        auditExecutParamQueryDto.setIscriptIds("iscriptIds");

        // Configure AuditExecutParamMapper.selectAuditExecutParamList(...).
        final AuditExecutParamEntity auditExecutParamEntity = new AuditExecutParamEntity();
        auditExecutParamEntity.setId(0L);
        auditExecutParamEntity.setParamJson("paramJson");
        auditExecutParamEntity.setScriptContent("scriptContent");
        auditExecutParamEntity.setAuditId(0L);
        auditExecutParamEntity.setScriptIds("scriptIds");
        final List<AuditExecutParamEntity> auditExecutParamEntities = Arrays.asList(auditExecutParamEntity);
        when(mockAuditExecutParamMapper.selectAuditExecutParamList(any(AuditExecutParamEntity.class)))
                .thenReturn(auditExecutParamEntities);

        // Run the test
        final PageInfo<AuditExecutParamDto> result = auditExecutParamServiceImplUnderTest.selectAuditExecutParamList(
                auditExecutParamQueryDto, 0, 0);

        // Verify the results
    }

    @Test
    void testSelectAuditExecutParamList_AuditExecutParamMapperReturnsNoItems() {
        // Setup
        final AuditExecutParamQueryDto auditExecutParamQueryDto = new AuditExecutParamQueryDto();
        auditExecutParamQueryDto.setId(0L);
        auditExecutParamQueryDto.setParamJson("paramJson");
        auditExecutParamQueryDto.setScriptContent("scriptContent");
        auditExecutParamQueryDto.setAuditId(0L);
        auditExecutParamQueryDto.setIscriptIds("iscriptIds");

        when(mockAuditExecutParamMapper.selectAuditExecutParamList(any(AuditExecutParamEntity.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final PageInfo<AuditExecutParamDto> result = auditExecutParamServiceImplUnderTest.selectAuditExecutParamList(
                auditExecutParamQueryDto, 0, 0);

        // Verify the results
    }

    @Test
    void testInsertAuditExecutParam() throws Exception {
        // Setup
        final AuditExecutParamDto auditExecutParamDto = new AuditExecutParamDto();
        auditExecutParamDto.setId(0L);
        auditExecutParamDto.setParamJson("paramJson");
        auditExecutParamDto.setScriptContent("scriptContent");
        auditExecutParamDto.setAuditId(0L);
        auditExecutParamDto.setScriptIds("scriptIds");

        when(mockAuditExecutParamMapper.insertAuditExecutParam(any(AuditExecutParamEntity.class))).thenReturn(0);

        // Run the test
        final int result = auditExecutParamServiceImplUnderTest.insertAuditExecutParam(auditExecutParamDto);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testUpdateAuditExecutParam() {
        // Setup
        final AuditExecutParamDto auditExecutParamDto = new AuditExecutParamDto();
        auditExecutParamDto.setId(0L);
        auditExecutParamDto.setParamJson("paramJson");
        auditExecutParamDto.setScriptContent("scriptContent");
        auditExecutParamDto.setAuditId(0L);
        auditExecutParamDto.setScriptIds("scriptIds");

        when(mockAuditExecutParamMapper.updateAuditExecutParam(any(AuditExecutParamEntity.class))).thenReturn(0);

        // Run the test
        final int result = auditExecutParamServiceImplUnderTest.updateAuditExecutParam(auditExecutParamDto);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testDeleteAuditExecutParamByIds() {
        // Setup
        when(mockAuditExecutParamMapper.deleteAuditExecutParamByIds(any(Long[].class))).thenReturn(0);

        // Run the test
        final int result = auditExecutParamServiceImplUnderTest.deleteAuditExecutParamByIds(new Long[]{0L});

        // Verify the results
        assertThat(result).isEqualTo(0);
    }
    
    @Test
    void testThrown() {
    	auditExecutParamServiceImplUnderTest = new AuditExecutParamServiceImpl(null);
        // Setup
        assertThatThrownBy(
                () -> auditExecutParamServiceImplUnderTest.insertAuditExecutParam(null))
                .isInstanceOf(AuditException.class);  
    }
}

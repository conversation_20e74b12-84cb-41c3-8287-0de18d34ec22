package com.ideal.tools.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.ideal.common.dto.R;
import com.ideal.script.exception.ScriptException;
import com.ideal.tools.config.BusinessConfig;
import com.ideal.tools.exception.AuditException;
import com.ideal.tools.exception.ScriptToolsException;
import com.ideal.tools.exception.StudioException;
import com.ideal.tools.exception.ToolsException;
import com.ideal.tools.mapper.ExecuteHistoryMapper;
import com.ideal.tools.mapper.ToolsInfoMapper;
import com.ideal.tools.model.bean.ExecuteHistoryBean;
import com.ideal.tools.model.dto.*;
import com.ideal.tools.model.entity.CategoryEntity;
import com.ideal.tools.model.entity.ToolsInfoCategoryEntity;
import com.ideal.tools.model.entity.ToolsInfoEntity;
import com.ideal.tools.model.enums.OperatModuleEnum;
import com.ideal.tools.model.enums.OperatTypeEnum;
import com.ideal.tools.model.interaction.ScriptContentDto;
import com.ideal.tools.model.interaction.ScriptFileInfo;
import com.ideal.tools.model.interaction.StudioFileProjectInteractDto;
import com.ideal.tools.model.interaction.ToolBoxDto;
import com.ideal.tools.service.*;
import com.ideal.tools.service.interaction.ScriptInteract;
import com.ideal.tools.service.interaction.StudioInteract;
import com.ideal.tools.service.interaction.SystemDataInteract;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ToolsMoveServiceImplTest {

    @Mock
    private IToolsInfoService mockToolsInfoService;
    @Mock
    private BusinessConfig mockBusinessConfig;
    @Mock
    private IToolsOperateService mockToolsOperateService;
    @Mock
    private ScriptInteract mockScriptInteract;
    @Mock
    private ITbCategoryService mockCategoryService;
    @Mock
    private SystemDataInteract mockSystemDataInteract;
    @Mock
    private StudioInteract mockStudioInteract;
    @Mock
    private IAuditService mockAuditService;
    @Mock
    private ILogAuditService mockLogAuditService;
    @Mock
    private ExecuteHistoryMapper mockExecuteHistoryMapper;
    @Mock
    private ICommonService commonService;

    private ToolsMoveServiceImpl toolsMoveServiceImplUnderTest;
    @Mock
    private ToolsInfoMapper mocktoolsInfoMapper;

    @BeforeEach
    void setUp() throws Exception {
        toolsMoveServiceImplUnderTest = new ToolsMoveServiceImpl(
                mockToolsInfoService,
                mockBusinessConfig,
                mockToolsOperateService,
                mockScriptInteract,
                mockCategoryService,
                mockSystemDataInteract,
                mockStudioInteract,
                mockAuditService,
                mockLogAuditService,
                mockExecuteHistoryMapper,
                mocktoolsInfoMapper);
    }

 /*   @Test
    void testPublishMigrationExportTools() throws Exception {
        // Setup
        final MockHttpServletResponse response = new MockHttpServletResponse();

        // Configure ExecuteHistoryMapper.selectHistoryMonitorByToolsId(...).
        final ExecuteHistoryBean executeHistoryBean = new ExecuteHistoryBean();
        executeHistoryBean.setClassification(0);
        executeHistoryBean.setSearchStartTime("searchStartTime");
        executeHistoryBean.setSearchEndTime("searchEndTime");
        executeHistoryBean.setRunningId(0L);
        executeHistoryBean.setExecFrom(0L);
        final List<ExecuteHistoryBean> executeHistoryBeans = Arrays.asList(executeHistoryBean);
        when(mockExecuteHistoryMapper.selectHistoryMonitorByToolsId(0L, 0L)).thenReturn(executeHistoryBeans);

        when(mockBusinessConfig.getToolsEvn()).thenReturn("result");
        when(mockBusinessConfig.isToolEvnMainSwitch()).thenReturn(false);

        // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
        final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
        toolsInfoCategoryEntity.setPermissionLevel(0);
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setCode("code");
        toolsInfoCategoryEntity.setName("name");
        toolsInfoCategoryEntity.setType(0);
        when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);

        // Configure IToolsInfoService.getToolsCombinedInfo(...).
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setCode("code");
        toolsDto.setName("name");
        toolsDto.setType(0);
        toolsDto.setBusinessSystemId(0L);
        toolsDto.setBusinessSystemName("businessSystemName");
        toolsDto.setOneTypeId(0L);
        toolsDto.setTwoTypeId(0L);
        toolsDto.setHighRisk(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto.setAuditorId(0L);
        auditEverybodyQueryDto.setAuditorName("auditorName");
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setOperateStatus(0);
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        toolsDto.setScriptType(0);
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        toolsDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        toolsDto.setScriptEditing("scriptEditing");
        toolsDto.setStatus(0);
        toolsDto.setOneTypeName("OneTypeName");
        toolsDto.setTwoTypeName("OneTypeName");
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setProjectId(0L);
        toolsProjectInfoDto.setProjectVersionId(0L);
        toolsProjectInfoDto.setProjectName("projectName");
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowName("workflowName");
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto.setCreatorId(0L);
        toolsDto.setCreatorName("creatorName");
        toolsDto.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        when(mockToolsInfoService.getToolsCombinedInfo(0L)).thenReturn(toolsDto);

        when(mockBusinessConfig.getFtpType()).thenReturn("result");
        when(mockBusinessConfig.getFtpUser()).thenReturn("result");
        when(mockBusinessConfig.getFtpPw()).thenReturn("result");
        when(mockBusinessConfig.getFtpIp()).thenReturn("result");
        when(mockBusinessConfig.getFtpPort()).thenReturn("result");
        when(mockBusinessConfig.getSftpDataPath()).thenReturn("result");

        // Configure ScriptInteract.exportScriptProduction(...).
        final ScriptFileInfo scriptFileInfo = new ScriptFileInfo();
        scriptFileInfo.setFileName("fileName");
        scriptFileInfo.setFileSuffix("fileSuffix");
        scriptFileInfo.setFileContentByte("content".getBytes());
        when(mockScriptInteract.exportScriptProduction(Arrays.asList("value"))).thenReturn(scriptFileInfo);

        // Configure StudioInteract.exportStudioData(...).
        final StudioFileProjectInteractDto studioFileProjectInteractDto = new StudioFileProjectInteractDto("fileName",
                "fileSuffix", "content".getBytes());
        when(mockStudioInteract.exportStudioData(0L)).thenReturn(studioFileProjectInteractDto);

        // Run the test
        final R<Object> result = commonService.publishMigrationExportTools(response, new Long[]{0L}, 0,1);

        // Verify the results
        verify(mockToolsInfoService).updateToolsInfo(any(ToolsInfoDto.class));
    }

    @Test
    void testPublishMigrationExportTools_ExecuteHistoryMapperReturnsNull() throws Exception {
        // Setup
        final MockHttpServletResponse response = new MockHttpServletResponse();
        when(mockExecuteHistoryMapper.selectHistoryMonitorByToolsId(0L, 0L)).thenReturn(null);

        // Run the test
        final R<Object> result = commonService.publishMigrationExportTools(response, new Long[]{0L}, 0,1);

        // Verify the results
    }*/

   /* @Test
    void testPublishMigrationExportTools_ExecuteHistoryMapperReturnsNoItems() throws Exception {
        // Setup
        final MockHttpServletResponse response = new MockHttpServletResponse();
        when(mockExecuteHistoryMapper.selectHistoryMonitorByToolsId(0L, 0L)).thenReturn(Collections.emptyList());
        when(mockBusinessConfig.getToolsEvn()).thenReturn("result");
        when(mockBusinessConfig.isToolEvnMainSwitch()).thenReturn(false);

        // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
        final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
        toolsInfoCategoryEntity.setPermissionLevel(0);
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setCode("code");
        toolsInfoCategoryEntity.setName("name");
        toolsInfoCategoryEntity.setType(0);
        when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);

        // Configure IToolsInfoService.getToolsCombinedInfo(...).
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setCode("code");
        toolsDto.setName("name");
        toolsDto.setType(0);
        toolsDto.setBusinessSystemId(0L);
        toolsDto.setBusinessSystemName("businessSystemName");
        toolsDto.setOneTypeId(0L);
        toolsDto.setTwoTypeId(0L);
        toolsDto.setHighRisk(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto.setAuditorId(0L);
        auditEverybodyQueryDto.setAuditorName("auditorName");
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setOperateStatus(0);
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        toolsDto.setScriptType(0);
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        toolsDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        toolsDto.setScriptEditing("scriptEditing");
        toolsDto.setStatus(0);
        toolsDto.setOneTypeName("OneTypeName");
        toolsDto.setTwoTypeName("OneTypeName");
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setProjectId(0L);
        toolsProjectInfoDto.setProjectVersionId(0L);
        toolsProjectInfoDto.setProjectName("projectName");
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowName("workflowName");
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto.setCreatorId(0L);
        toolsDto.setCreatorName("creatorName");
        toolsDto.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        when(mockToolsInfoService.getToolsCombinedInfo(0L)).thenReturn(toolsDto);

        when(mockBusinessConfig.getFtpType()).thenReturn("result");
        when(mockBusinessConfig.getFtpUser()).thenReturn("result");
        when(mockBusinessConfig.getFtpPw()).thenReturn("result");
        when(mockBusinessConfig.getFtpIp()).thenReturn("result");
        when(mockBusinessConfig.getFtpPort()).thenReturn("result");
        when(mockBusinessConfig.getSftpDataPath()).thenReturn("result");

        // Configure ScriptInteract.exportScriptProduction(...).
        final ScriptFileInfo scriptFileInfo = new ScriptFileInfo();
        scriptFileInfo.setFileName("fileName");
        scriptFileInfo.setFileSuffix("fileSuffix");
        scriptFileInfo.setFileContentByte("content".getBytes());
        when(mockScriptInteract.exportScriptProduction(Arrays.asList("value"))).thenReturn(scriptFileInfo);

        // Configure StudioInteract.exportStudioData(...).
        final StudioFileProjectInteractDto studioFileProjectInteractDto = new StudioFileProjectInteractDto("fileName",
                "fileSuffix", "content".getBytes());
        when(mockStudioInteract.exportStudioData(0L)).thenReturn(studioFileProjectInteractDto);

        // Run the test
        final R<Object> result = commonService.publishMigrationExportTools(response, new Long[]{0L}, 0,1);

        // Verify the results
        verify(mockToolsInfoService).updateToolsInfo(any(ToolsInfoDto.class));
    }*/

/*
    @Test
    void testPublishMigrationExportTools_IToolsInfoServiceSelectToolsInfoCategoryByIdReturnsNull() {
        // Setup
        final MockHttpServletResponse response = new MockHttpServletResponse();

        // Configure ExecuteHistoryMapper.selectHistoryMonitorByToolsId(...).
        final ExecuteHistoryBean executeHistoryBean = new ExecuteHistoryBean();
        executeHistoryBean.setClassification(0);
        executeHistoryBean.setSearchStartTime("searchStartTime");
        executeHistoryBean.setSearchEndTime("searchEndTime");
        executeHistoryBean.setRunningId(0L);
        executeHistoryBean.setExecFrom(0L);
        final List<ExecuteHistoryBean> executeHistoryBeans = Arrays.asList(executeHistoryBean);
        when(mockExecuteHistoryMapper.selectHistoryMonitorByToolsId(0L, 0L)).thenReturn(executeHistoryBeans);

        when(mockBusinessConfig.getToolsEvn()).thenReturn("result");
        when(mockBusinessConfig.isToolEvnMainSwitch()).thenReturn(false);
        when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> commonService.publishMigrationExportTools(response, new Long[]{0L},
                0,1)).isInstanceOf(ToolsException.class);
    }

    @Test
    void testPublishMigrationExportTools_ScriptInteractReturnsNull() throws Exception {
        // Setup
        final MockHttpServletResponse response = new MockHttpServletResponse();

        // Configure ExecuteHistoryMapper.selectHistoryMonitorByToolsId(...).
        final ExecuteHistoryBean executeHistoryBean = new ExecuteHistoryBean();
        executeHistoryBean.setClassification(0);
        executeHistoryBean.setSearchStartTime("searchStartTime");
        executeHistoryBean.setSearchEndTime("searchEndTime");
        executeHistoryBean.setRunningId(0L);
        executeHistoryBean.setExecFrom(0L);
        final List<ExecuteHistoryBean> executeHistoryBeans = Arrays.asList(executeHistoryBean);
        when(mockExecuteHistoryMapper.selectHistoryMonitorByToolsId(0L, 0L)).thenReturn(executeHistoryBeans);

        when(mockBusinessConfig.getToolsEvn()).thenReturn("result");
        when(mockBusinessConfig.isToolEvnMainSwitch()).thenReturn(false);

        // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
        final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
        toolsInfoCategoryEntity.setPermissionLevel(0);
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setCode("code");
        toolsInfoCategoryEntity.setName("name");
        toolsInfoCategoryEntity.setType(0);
        when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);

        // Configure IToolsInfoService.getToolsCombinedInfo(...).
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setCode("code");
        toolsDto.setName("name");
        toolsDto.setType(0);
        toolsDto.setBusinessSystemId(0L);
        toolsDto.setBusinessSystemName("businessSystemName");
        toolsDto.setOneTypeId(0L);
        toolsDto.setTwoTypeId(0L);
        toolsDto.setHighRisk(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto.setAuditorId(0L);
        auditEverybodyQueryDto.setAuditorName("auditorName");
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setOperateStatus(0);
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        toolsDto.setScriptType(0);
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        toolsDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        toolsDto.setScriptEditing("scriptEditing");
        toolsDto.setStatus(0);
        toolsDto.setOneTypeName("OneTypeName");
        toolsDto.setTwoTypeName("OneTypeName");
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setProjectId(0L);
        toolsProjectInfoDto.setProjectVersionId(0L);
        toolsProjectInfoDto.setProjectName("projectName");
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowName("workflowName");
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto.setCreatorId(0L);
        toolsDto.setCreatorName("creatorName");
        toolsDto.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        when(mockToolsInfoService.getToolsCombinedInfo(0L)).thenReturn(toolsDto);

        when(mockBusinessConfig.getFtpType()).thenReturn("result");
        when(mockBusinessConfig.getFtpUser()).thenReturn("result");
        when(mockBusinessConfig.getFtpPw()).thenReturn("result");
        when(mockBusinessConfig.getFtpIp()).thenReturn("result");
        when(mockBusinessConfig.getFtpPort()).thenReturn("result");
        when(mockBusinessConfig.getSftpDataPath()).thenReturn("result");
        when(mockScriptInteract.exportScriptProduction(Arrays.asList("value"))).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> commonService.publishMigrationExportTools(response, new Long[]{0L},
                0,1)).isInstanceOf(ToolsException.class);
    }

    @Test
    void testPublishMigrationExportTools_ScriptInteractThrowsScriptException() throws Exception {
        // Setup
        final MockHttpServletResponse response = new MockHttpServletResponse();

        // Configure ExecuteHistoryMapper.selectHistoryMonitorByToolsId(...).
        final ExecuteHistoryBean executeHistoryBean = new ExecuteHistoryBean();
        executeHistoryBean.setClassification(0);
        executeHistoryBean.setSearchStartTime("searchStartTime");
        executeHistoryBean.setSearchEndTime("searchEndTime");
        executeHistoryBean.setRunningId(0L);
        executeHistoryBean.setExecFrom(0L);
        final List<ExecuteHistoryBean> executeHistoryBeans = Arrays.asList(executeHistoryBean);
        when(mockExecuteHistoryMapper.selectHistoryMonitorByToolsId(0L, 0L)).thenReturn(executeHistoryBeans);

        when(mockBusinessConfig.getToolsEvn()).thenReturn("result");
        when(mockBusinessConfig.isToolEvnMainSwitch()).thenReturn(false);

        // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
        final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
        toolsInfoCategoryEntity.setPermissionLevel(0);
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setCode("code");
        toolsInfoCategoryEntity.setName("name");
        toolsInfoCategoryEntity.setType(0);
        when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);

        // Configure IToolsInfoService.getToolsCombinedInfo(...).
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setCode("code");
        toolsDto.setName("name");
        toolsDto.setType(0);
        toolsDto.setBusinessSystemId(0L);
        toolsDto.setBusinessSystemName("businessSystemName");
        toolsDto.setOneTypeId(0L);
        toolsDto.setTwoTypeId(0L);
        toolsDto.setHighRisk(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto.setAuditorId(0L);
        auditEverybodyQueryDto.setAuditorName("auditorName");
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setOperateStatus(0);
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        toolsDto.setScriptType(0);
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        toolsDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        toolsDto.setScriptEditing("scriptEditing");
        toolsDto.setStatus(0);
        toolsDto.setOneTypeName("OneTypeName");
        toolsDto.setTwoTypeName("OneTypeName");
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setProjectId(0L);
        toolsProjectInfoDto.setProjectVersionId(0L);
        toolsProjectInfoDto.setProjectName("projectName");
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowName("workflowName");
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto.setCreatorId(0L);
        toolsDto.setCreatorName("creatorName");
        toolsDto.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        when(mockToolsInfoService.getToolsCombinedInfo(0L)).thenReturn(toolsDto);

        when(mockBusinessConfig.getFtpType()).thenReturn("result");
        when(mockBusinessConfig.getFtpUser()).thenReturn("result");
        when(mockBusinessConfig.getFtpPw()).thenReturn("result");
        when(mockBusinessConfig.getFtpIp()).thenReturn("result");
        when(mockBusinessConfig.getFtpPort()).thenReturn("result");
        when(mockBusinessConfig.getSftpDataPath()).thenReturn("result");
        when(mockScriptInteract.exportScriptProduction(Arrays.asList("value"))).thenThrow(ScriptException.class);

        // Run the test
        assertThatThrownBy(() -> commonService.publishMigrationExportTools(response, new Long[]{0L},
                0,1)).isInstanceOf(ToolsException.class);
    }

    @Test
    void testPublishMigrationExportTools_StudioInteractThrowsStudioException() throws Exception {
        // Setup
        final MockHttpServletResponse response = new MockHttpServletResponse();

        // Configure ExecuteHistoryMapper.selectHistoryMonitorByToolsId(...).
        final ExecuteHistoryBean executeHistoryBean = new ExecuteHistoryBean();
        executeHistoryBean.setClassification(0);
        executeHistoryBean.setSearchStartTime("searchStartTime");
        executeHistoryBean.setSearchEndTime("searchEndTime");
        executeHistoryBean.setRunningId(0L);
        executeHistoryBean.setExecFrom(0L);
        final List<ExecuteHistoryBean> executeHistoryBeans = Arrays.asList(executeHistoryBean);
        when(mockExecuteHistoryMapper.selectHistoryMonitorByToolsId(0L, 0L)).thenReturn(executeHistoryBeans);

        when(mockBusinessConfig.getToolsEvn()).thenReturn("result");
        when(mockBusinessConfig.isToolEvnMainSwitch()).thenReturn(false);

        // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
        final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
        toolsInfoCategoryEntity.setPermissionLevel(0);
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setCode("code");
        toolsInfoCategoryEntity.setName("name");
        toolsInfoCategoryEntity.setType(0);
        when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);

        // Configure IToolsInfoService.getToolsCombinedInfo(...).
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setCode("code");
        toolsDto.setName("name");
        toolsDto.setType(0);
        toolsDto.setBusinessSystemId(0L);
        toolsDto.setBusinessSystemName("businessSystemName");
        toolsDto.setOneTypeId(0L);
        toolsDto.setTwoTypeId(0L);
        toolsDto.setHighRisk(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto.setAuditorId(0L);
        auditEverybodyQueryDto.setAuditorName("auditorName");
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setOperateStatus(0);
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        toolsDto.setScriptType(0);
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        toolsDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        toolsDto.setScriptEditing("scriptEditing");
        toolsDto.setStatus(0);
        toolsDto.setOneTypeName("OneTypeName");
        toolsDto.setTwoTypeName("OneTypeName");
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setProjectId(0L);
        toolsProjectInfoDto.setProjectVersionId(0L);
        toolsProjectInfoDto.setProjectName("projectName");
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowName("workflowName");
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto.setCreatorId(0L);
        toolsDto.setCreatorName("creatorName");
        toolsDto.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        when(mockToolsInfoService.getToolsCombinedInfo(0L)).thenReturn(toolsDto);

        when(mockBusinessConfig.getFtpType()).thenReturn("result");
        when(mockBusinessConfig.getFtpUser()).thenReturn("result");
        when(mockBusinessConfig.getFtpPw()).thenReturn("result");
        when(mockBusinessConfig.getFtpIp()).thenReturn("result");
        when(mockBusinessConfig.getFtpPort()).thenReturn("result");
        when(mockBusinessConfig.getSftpDataPath()).thenReturn("result");
        when(mockStudioInteract.exportStudioData(0L)).thenThrow(StudioException.class);

        // Run the test
        assertThatThrownBy(() -> commonService.publishMigrationExportTools(response, new Long[]{0L},
                0,1)).isInstanceOf(ToolsException.class);
    }

    @Test
    void testExportTools1() throws Exception {
        // Setup
        final MockHttpServletResponse response = new MockHttpServletResponse();
        final ExportToolsDto exportToolsDto = new ExportToolsDto();
        exportToolsDto.setToolsId(0L);
        exportToolsDto.setType(0);
        final List<ExportToolsDto> exportToolsDtoList = Arrays.asList(exportToolsDto);
        final UserDto user = new UserDto();
        user.setUserName("creatorName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        when(mockBusinessConfig.getToolsEvn()).thenReturn("result");
        when(mockBusinessConfig.isToolEvnMainSwitch()).thenReturn(false);

        // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
        final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
        toolsInfoCategoryEntity.setPermissionLevel(0);
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setCode("code");
        toolsInfoCategoryEntity.setName("name");
        toolsInfoCategoryEntity.setType(0);
        when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);

        // Configure IToolsInfoService.getToolsCombinedInfo(...).
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setCode("code");
        toolsDto.setName("name");
        toolsDto.setType(0);
        toolsDto.setBusinessSystemId(0L);
        toolsDto.setBusinessSystemName("businessSystemName");
        toolsDto.setOneTypeId(0L);
        toolsDto.setTwoTypeId(0L);
        toolsDto.setHighRisk(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto.setAuditorId(0L);
        auditEverybodyQueryDto.setAuditorName("auditorName");
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setOperateStatus(0);
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        toolsDto.setScriptType(0);
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        toolsDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        toolsDto.setScriptEditing("scriptEditing");
        toolsDto.setStatus(0);
        toolsDto.setOneTypeName("OneTypeName");
        toolsDto.setTwoTypeName("OneTypeName");
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setProjectId(0L);
        toolsProjectInfoDto.setProjectVersionId(0L);
        toolsProjectInfoDto.setProjectName("projectName");
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowName("workflowName");
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto.setCreatorId(0L);
        toolsDto.setCreatorName("creatorName");
        toolsDto.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        when(mockToolsInfoService.getToolsCombinedInfo(0L)).thenReturn(toolsDto);

        when(mockBusinessConfig.getFtpType()).thenReturn("result");
        when(mockBusinessConfig.getFtpUser()).thenReturn("result");
        when(mockBusinessConfig.getFtpPw()).thenReturn("result");
        when(mockBusinessConfig.getFtpIp()).thenReturn("result");
        when(mockBusinessConfig.getFtpPort()).thenReturn("result");
        when(mockBusinessConfig.getSftpDataPath()).thenReturn("result");

        // Configure ScriptInteract.exportScriptProduction(...).
        final ScriptFileInfo scriptFileInfo = new ScriptFileInfo();
        scriptFileInfo.setFileName("fileName");
        scriptFileInfo.setFileSuffix("fileSuffix");
        scriptFileInfo.setFileContentByte("content".getBytes());
        when(mockScriptInteract.exportScriptProduction(Arrays.asList("value"))).thenReturn(scriptFileInfo);

        // Configure StudioInteract.exportStudioData(...).
        final StudioFileProjectInteractDto studioFileProjectInteractDto = new StudioFileProjectInteractDto("fileName",
                "fileSuffix", "content".getBytes());
        when(mockStudioInteract.exportStudioData(0L)).thenReturn(studioFileProjectInteractDto);

        // Run the test
        final R<Object> result = toolsMoveServiceImplUnderTest.exportTools(response, exportToolsDtoList, user);

        // Verify the results
        verify(mockToolsInfoService).updateToolsInfo(any(ToolsInfoDto.class));
        verify(mockLogAuditService).logOperationAudit(eq(0L), eq(OperatModuleEnum.TOOL_EDIT),
                eq(OperatTypeEnum.TOOL_LOCAL_EXPORT), eq(0), any(UserDto.class), eq("remark"));
    }

    @Test
    void testExportTools1_IToolsInfoServiceSelectToolsInfoCategoryByIdReturnsNull() {
        // Setup
        final MockHttpServletResponse response = new MockHttpServletResponse();
        final ExportToolsDto exportToolsDto = new ExportToolsDto();
        exportToolsDto.setToolsId(0L);
        exportToolsDto.setType(0);
        final List<ExportToolsDto> exportToolsDtoList = Arrays.asList(exportToolsDto);
        final UserDto user = new UserDto();
        user.setUserName("creatorName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        when(mockBusinessConfig.getToolsEvn()).thenReturn("result");
        when(mockBusinessConfig.isToolEvnMainSwitch()).thenReturn(false);
        when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(
                () -> toolsMoveServiceImplUnderTest.exportTools(response, exportToolsDtoList, user))
                .isInstanceOf(ToolsException.class);
        verify(mockLogAuditService).logOperationAudit(eq(0L), eq(OperatModuleEnum.TOOL_EDIT),
                eq(OperatTypeEnum.TOOL_LOCAL_EXPORT), eq(0), any(UserDto.class), eq("remark"));
    }

    @Test
    void testExportTools1_ScriptInteractReturnsNull() throws Exception {
        // Setup
        final MockHttpServletResponse response = new MockHttpServletResponse();
        final ExportToolsDto exportToolsDto = new ExportToolsDto();
        exportToolsDto.setToolsId(0L);
        exportToolsDto.setType(0);
        final List<ExportToolsDto> exportToolsDtoList = Arrays.asList(exportToolsDto);
        final UserDto user = new UserDto();
        user.setUserName("creatorName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        when(mockBusinessConfig.getToolsEvn()).thenReturn("result");
        when(mockBusinessConfig.isToolEvnMainSwitch()).thenReturn(false);

        // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
        final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
        toolsInfoCategoryEntity.setPermissionLevel(0);
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setCode("code");
        toolsInfoCategoryEntity.setName("name");
        toolsInfoCategoryEntity.setType(0);
        when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);

        // Configure IToolsInfoService.getToolsCombinedInfo(...).
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setCode("code");
        toolsDto.setName("name");
        toolsDto.setType(0);
        toolsDto.setBusinessSystemId(0L);
        toolsDto.setBusinessSystemName("businessSystemName");
        toolsDto.setOneTypeId(0L);
        toolsDto.setTwoTypeId(0L);
        toolsDto.setHighRisk(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto.setAuditorId(0L);
        auditEverybodyQueryDto.setAuditorName("auditorName");
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setOperateStatus(0);
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        toolsDto.setScriptType(0);
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        toolsDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        toolsDto.setScriptEditing("scriptEditing");
        toolsDto.setStatus(0);
        toolsDto.setOneTypeName("OneTypeName");
        toolsDto.setTwoTypeName("OneTypeName");
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setProjectId(0L);
        toolsProjectInfoDto.setProjectVersionId(0L);
        toolsProjectInfoDto.setProjectName("projectName");
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowName("workflowName");
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto.setCreatorId(0L);
        toolsDto.setCreatorName("creatorName");
        toolsDto.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        when(mockToolsInfoService.getToolsCombinedInfo(0L)).thenReturn(toolsDto);

        when(mockBusinessConfig.getFtpType()).thenReturn("result");
        when(mockBusinessConfig.getFtpUser()).thenReturn("result");
        when(mockBusinessConfig.getFtpPw()).thenReturn("result");
        when(mockBusinessConfig.getFtpIp()).thenReturn("result");
        when(mockBusinessConfig.getFtpPort()).thenReturn("result");
        when(mockBusinessConfig.getSftpDataPath()).thenReturn("result");
        when(mockScriptInteract.exportScriptProduction(Arrays.asList("value"))).thenReturn(null);

        // Run the test
        assertThatThrownBy(
                () -> toolsMoveServiceImplUnderTest.exportTools(response, exportToolsDtoList, user))
                .isInstanceOf(ToolsException.class);
        verify(mockLogAuditService).logOperationAudit(eq(0L), eq(OperatModuleEnum.TOOL_EDIT),
                eq(OperatTypeEnum.TOOL_LOCAL_EXPORT), eq(0), any(UserDto.class), eq("remark"));
    }

    @Test
    void testExportTools1_ScriptInteractThrowsScriptException() throws Exception {
        // Setup
        final MockHttpServletResponse response = new MockHttpServletResponse();
        final ExportToolsDto exportToolsDto = new ExportToolsDto();
        exportToolsDto.setToolsId(0L);
        exportToolsDto.setType(0);
        final List<ExportToolsDto> exportToolsDtoList = Arrays.asList(exportToolsDto);
        final UserDto user = new UserDto();
        user.setUserName("creatorName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        when(mockBusinessConfig.getToolsEvn()).thenReturn("result");
        when(mockBusinessConfig.isToolEvnMainSwitch()).thenReturn(false);

        // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
        final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
        toolsInfoCategoryEntity.setPermissionLevel(0);
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setCode("code");
        toolsInfoCategoryEntity.setName("name");
        toolsInfoCategoryEntity.setType(0);
        when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);

        // Configure IToolsInfoService.getToolsCombinedInfo(...).
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setCode("code");
        toolsDto.setName("name");
        toolsDto.setType(0);
        toolsDto.setBusinessSystemId(0L);
        toolsDto.setBusinessSystemName("businessSystemName");
        toolsDto.setOneTypeId(0L);
        toolsDto.setTwoTypeId(0L);
        toolsDto.setHighRisk(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto.setAuditorId(0L);
        auditEverybodyQueryDto.setAuditorName("auditorName");
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setOperateStatus(0);
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        toolsDto.setScriptType(0);
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        toolsDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        toolsDto.setScriptEditing("scriptEditing");
        toolsDto.setStatus(0);
        toolsDto.setOneTypeName("OneTypeName");
        toolsDto.setTwoTypeName("OneTypeName");
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setProjectId(0L);
        toolsProjectInfoDto.setProjectVersionId(0L);
        toolsProjectInfoDto.setProjectName("projectName");
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowName("workflowName");
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto.setCreatorId(0L);
        toolsDto.setCreatorName("creatorName");
        toolsDto.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        when(mockToolsInfoService.getToolsCombinedInfo(0L)).thenReturn(toolsDto);

        when(mockBusinessConfig.getFtpType()).thenReturn("result");
        when(mockBusinessConfig.getFtpUser()).thenReturn("result");
        when(mockBusinessConfig.getFtpPw()).thenReturn("result");
        when(mockBusinessConfig.getFtpIp()).thenReturn("result");
        when(mockBusinessConfig.getFtpPort()).thenReturn("result");
        when(mockBusinessConfig.getSftpDataPath()).thenReturn("result");
        when(mockScriptInteract.exportScriptProduction(Arrays.asList("value"))).thenThrow(ScriptException.class);

        // Run the test
        assertThatThrownBy(
                () -> toolsMoveServiceImplUnderTest.exportTools(response, exportToolsDtoList, user))
                .isInstanceOf(ToolsException.class);
        verify(mockLogAuditService).logOperationAudit(eq(0L), eq(OperatModuleEnum.TOOL_EDIT),
                eq(OperatTypeEnum.TOOL_LOCAL_EXPORT), eq(0), any(UserDto.class), eq("remark"));
    }

    @Test
    void testExportTools1_StudioInteractThrowsStudioException() throws Exception {
        // Setup
        final MockHttpServletResponse response = new MockHttpServletResponse();
        final ExportToolsDto exportToolsDto = new ExportToolsDto();
        exportToolsDto.setToolsId(0L);
        exportToolsDto.setType(0);
        final List<ExportToolsDto> exportToolsDtoList = Arrays.asList(exportToolsDto);
        final UserDto user = new UserDto();
        user.setUserName("creatorName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        when(mockBusinessConfig.getToolsEvn()).thenReturn("result");
        when(mockBusinessConfig.isToolEvnMainSwitch()).thenReturn(false);

        // Configure IToolsInfoService.selectToolsInfoCategoryById(...).
        final ToolsInfoCategoryEntity toolsInfoCategoryEntity = new ToolsInfoCategoryEntity();
        toolsInfoCategoryEntity.setPermissionLevel(0);
        toolsInfoCategoryEntity.setId(0L);
        toolsInfoCategoryEntity.setCode("code");
        toolsInfoCategoryEntity.setName("name");
        toolsInfoCategoryEntity.setType(0);
        when(mockToolsInfoService.selectToolsInfoCategoryById(0L)).thenReturn(toolsInfoCategoryEntity);

        // Configure IToolsInfoService.getToolsCombinedInfo(...).
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setCode("code");
        toolsDto.setName("name");
        toolsDto.setType(0);
        toolsDto.setBusinessSystemId(0L);
        toolsDto.setBusinessSystemName("businessSystemName");
        toolsDto.setOneTypeId(0L);
        toolsDto.setTwoTypeId(0L);
        toolsDto.setHighRisk(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto.setAuditorId(0L);
        auditEverybodyQueryDto.setAuditorName("auditorName");
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setOperateStatus(0);
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        toolsDto.setScriptType(0);
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        toolsDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        toolsDto.setScriptEditing("scriptEditing");
        toolsDto.setStatus(0);
        toolsDto.setOneTypeName("OneTypeName");
        toolsDto.setTwoTypeName("OneTypeName");
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setProjectId(0L);
        toolsProjectInfoDto.setProjectVersionId(0L);
        toolsProjectInfoDto.setProjectName("projectName");
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowName("workflowName");
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto.setCreatorId(0L);
        toolsDto.setCreatorName("creatorName");
        toolsDto.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        when(mockToolsInfoService.getToolsCombinedInfo(0L)).thenReturn(toolsDto);

        when(mockBusinessConfig.getFtpType()).thenReturn("result");
        when(mockBusinessConfig.getFtpUser()).thenReturn("result");
        when(mockBusinessConfig.getFtpPw()).thenReturn("result");
        when(mockBusinessConfig.getFtpIp()).thenReturn("result");
        when(mockBusinessConfig.getFtpPort()).thenReturn("result");
        when(mockBusinessConfig.getSftpDataPath()).thenReturn("result");
        when(mockStudioInteract.exportStudioData(0L)).thenThrow(StudioException.class);

        // Run the test
        assertThatThrownBy(
                () -> toolsMoveServiceImplUnderTest.exportTools(response, exportToolsDtoList, user))
                .isInstanceOf(ToolsException.class);
        verify(mockLogAuditService).logOperationAudit(eq(0L), eq(OperatModuleEnum.TOOL_EDIT),
                eq(OperatTypeEnum.TOOL_LOCAL_EXPORT), eq(0), any(UserDto.class), eq("remark"));
    }

    @Test
    void testImportTools() throws Exception {
        // Setup
        final MultipartFile file = new MockMultipartFile("name", "content".getBytes());
        final UserDto user = new UserDto();
        user.setUserName("creatorName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        when(mockBusinessConfig.getToolsEvn()).thenReturn("result");
        when(mockBusinessConfig.isToolEvnMainSwitch()).thenReturn(false);
        when(mockBusinessConfig.getFtpType()).thenReturn("result");
        when(mockBusinessConfig.getFtpUser()).thenReturn("result");
        when(mockBusinessConfig.getFtpPw()).thenReturn("result");
        when(mockBusinessConfig.getFtpIp()).thenReturn("result");
        when(mockBusinessConfig.getFtpPort()).thenReturn("result");
        when(mockBusinessConfig.getSftpDataPath()).thenReturn("result");

        // Configure IToolsInfoService.selectToolsByCode(...).
        final ToolsInfoEntity toolsInfoEntity = new ToolsInfoEntity();
        toolsInfoEntity.setId(0L);
        toolsInfoEntity.setCode("code");
        toolsInfoEntity.setStatus(0);
        toolsInfoEntity.setCreatorId(0L);
        toolsInfoEntity.setCreatorName("creatorName");
        toolsInfoEntity.setDelFlag(0);
        toolsInfoEntity.setReturnReason("auditReturnReason");
        when(mockToolsInfoService.selectToolsByCode("code")).thenReturn(toolsInfoEntity);

        // Configure SystemDataInteract.queryBusinessSystemBySysName(...).
        final SystemPullDto systemPullDto = new SystemPullDto();
        systemPullDto.setBusinessSystemId(0L);
        systemPullDto.setBusinessSystemCode("businessSystemCode");
        systemPullDto.setBusinessSystemName("businessSystemName");
        when(mockSystemDataInteract.queryBusinessSystemBySysName("businessSystemName")).thenReturn(systemPullDto);

        // Configure ITbCategoryService.selectCategoryExist(...).
        final CategoryResultDto categoryResultDto = new CategoryResultDto();
        categoryResultDto.setId(0L);
        categoryResultDto.setParentId(0L);
        categoryResultDto.setClassname("classname");
        categoryResultDto.setDescription("description");
        categoryResultDto.setLevel(0);
        final List<CategoryResultDto> categoryResultDtos = Arrays.asList(categoryResultDto);
        when(mockCategoryService.selectCategoryExist(any(CategoryEntity.class))).thenReturn(categoryResultDtos);

        when(mockToolsOperateService.saveMoveCommitToolsInfo(any(ToolsSftpImportDto.class),
                any(UserDto.class))).thenReturn(false);

        // Configure ScriptInteract.importScriptProduction(...).
        final ScriptFileInfo scriptFileInfo = new ScriptFileInfo();
        scriptFileInfo.setFileName("fileName");
        scriptFileInfo.setFileSuffix("fileSuffix");
        scriptFileInfo.setFileContentByte("content".getBytes());
        when(mockScriptInteract.importScriptProduction(any(ScriptFileInfo.class))).thenReturn(scriptFileInfo);

        // Configure ScriptInteract.getDefaultScriptInfo(...).
        final ScriptContentDto scriptContentDto = new ScriptContentDto();
        scriptContentDto.setScriptIds("scriptIds");
        scriptContentDto.setScriptName("scriptName");
        scriptContentDto.setScriptType(0);
        scriptContentDto.setScriptEditing("scriptEditing");
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        scriptContentDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        when(mockScriptInteract.getDefaultScriptInfo(any(UserDto.class), eq("scriptIds"))).thenReturn(scriptContentDto);

        // Configure StudioInteract.importStudioData(...).
        final ToolBoxDto toolBoxDto = new ToolBoxDto();
        toolBoxDto.setProjectId(0L);
        toolBoxDto.setProjectVerId(0L);
        toolBoxDto.setWorkflowId(0L);
        toolBoxDto.setProjectName("projectName");
        toolBoxDto.setWorkflowName("workflowName");
        when(mockStudioInteract.importStudioData(any(StudioFileProjectInteractDto.class),
                any(UserDto.class))).thenReturn(toolBoxDto);

        // Run the test
        final R result = toolsMoveServiceImplUnderTest.importTools(file, user);

        // Verify the results
        verify(mockCategoryService).insertTreeCategory(any(CategoryDto.class), any(UserDto.class));
        verify(mockToolsOperateService).saveCommitToolsInfo(null,any(ToolsDto.class), any(UserDto.class),2);
        verify(mockLogAuditService).logOperationAudit(eq(0L), eq(OperatModuleEnum.TOOL_EDIT),
                eq(OperatTypeEnum.TOOL_LOCAL_IMPORT), eq(0), any(UserDto.class), eq("remark"));
    }

    @Test
    void testImportTools_IToolsInfoServiceReturnsNull() throws Exception {
        // Setup
        final MultipartFile file = new MockMultipartFile("name", "content".getBytes());
        final UserDto user = new UserDto();
        user.setUserName("creatorName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        when(mockBusinessConfig.getToolsEvn()).thenReturn("result");
        when(mockBusinessConfig.isToolEvnMainSwitch()).thenReturn(false);
        when(mockBusinessConfig.getFtpType()).thenReturn("result");
        when(mockBusinessConfig.getFtpUser()).thenReturn("result");
        when(mockBusinessConfig.getFtpPw()).thenReturn("result");
        when(mockBusinessConfig.getFtpIp()).thenReturn("result");
        when(mockBusinessConfig.getFtpPort()).thenReturn("result");
        when(mockBusinessConfig.getSftpDataPath()).thenReturn("result");
        when(mockToolsInfoService.selectToolsByCode("code")).thenReturn(null);

        // Configure SystemDataInteract.queryBusinessSystemBySysName(...).
        final SystemPullDto systemPullDto = new SystemPullDto();
        systemPullDto.setBusinessSystemId(0L);
        systemPullDto.setBusinessSystemCode("businessSystemCode");
        systemPullDto.setBusinessSystemName("businessSystemName");
        when(mockSystemDataInteract.queryBusinessSystemBySysName("businessSystemName")).thenReturn(systemPullDto);

        // Configure ITbCategoryService.selectCategoryExist(...).
        final CategoryResultDto categoryResultDto = new CategoryResultDto();
        categoryResultDto.setId(0L);
        categoryResultDto.setParentId(0L);
        categoryResultDto.setClassname("classname");
        categoryResultDto.setDescription("description");
        categoryResultDto.setLevel(0);
        final List<CategoryResultDto> categoryResultDtos = Arrays.asList(categoryResultDto);
        when(mockCategoryService.selectCategoryExist(any(CategoryEntity.class))).thenReturn(categoryResultDtos);

        when(mockToolsOperateService.saveMoveCommitToolsInfo(any(ToolsSftpImportDto.class),
                any(UserDto.class))).thenReturn(false);

        // Run the test
        final R result = toolsMoveServiceImplUnderTest.importTools(file, user);

        // Verify the results
        verify(mockCategoryService).insertTreeCategory(any(CategoryDto.class), any(UserDto.class));
        verify(mockLogAuditService).logOperationAudit(eq(0L), eq(OperatModuleEnum.TOOL_EDIT),
                eq(OperatTypeEnum.TOOL_LOCAL_IMPORT), eq(0), any(UserDto.class), eq("remark"));
    }

    @Test
    void testImportTools_ITbCategoryServiceSelectCategoryExistReturnsNoItems() throws Exception {
        // Setup
        final MultipartFile file = new MockMultipartFile("name", "content".getBytes());
        final UserDto user = new UserDto();
        user.setUserName("creatorName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        when(mockBusinessConfig.getToolsEvn()).thenReturn("result");
        when(mockBusinessConfig.isToolEvnMainSwitch()).thenReturn(false);
        when(mockBusinessConfig.getFtpType()).thenReturn("result");
        when(mockBusinessConfig.getFtpUser()).thenReturn("result");
        when(mockBusinessConfig.getFtpPw()).thenReturn("result");
        when(mockBusinessConfig.getFtpIp()).thenReturn("result");
        when(mockBusinessConfig.getFtpPort()).thenReturn("result");
        when(mockBusinessConfig.getSftpDataPath()).thenReturn("result");
        when(mockToolsInfoService.selectToolsByCode("code")).thenReturn(null);

        // Configure SystemDataInteract.queryBusinessSystemBySysName(...).
        final SystemPullDto systemPullDto = new SystemPullDto();
        systemPullDto.setBusinessSystemId(0L);
        systemPullDto.setBusinessSystemCode("businessSystemCode");
        systemPullDto.setBusinessSystemName("businessSystemName");
        when(mockSystemDataInteract.queryBusinessSystemBySysName("businessSystemName")).thenReturn(systemPullDto);

        when(mockCategoryService.selectCategoryExist(any(CategoryEntity.class))).thenReturn(Collections.emptyList());
        when(mockToolsOperateService.saveMoveCommitToolsInfo(any(ToolsSftpImportDto.class),
                any(UserDto.class))).thenReturn(false);

        // Run the test
        final R result = toolsMoveServiceImplUnderTest.importTools(file, user);

        // Verify the results
        verify(mockCategoryService).insertTreeCategory(any(CategoryDto.class), any(UserDto.class));
        verify(mockLogAuditService).logOperationAudit(eq(0L), eq(OperatModuleEnum.TOOL_EDIT),
                eq(OperatTypeEnum.TOOL_LOCAL_IMPORT), eq(0), any(UserDto.class), eq("remark"));
    }

    @Test
    void testImportTools_IToolsOperateServiceSaveMoveCommitToolsInfoThrowsToolsException() throws Exception {
        // Setup
        final MultipartFile file = new MockMultipartFile("name", "content".getBytes());
        final UserDto user = new UserDto();
        user.setUserName("creatorName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        when(mockBusinessConfig.getToolsEvn()).thenReturn("result");
        when(mockBusinessConfig.isToolEvnMainSwitch()).thenReturn(false);
        when(mockBusinessConfig.getFtpType()).thenReturn("result");
        when(mockBusinessConfig.getFtpUser()).thenReturn("result");
        when(mockBusinessConfig.getFtpPw()).thenReturn("result");
        when(mockBusinessConfig.getFtpIp()).thenReturn("result");
        when(mockBusinessConfig.getFtpPort()).thenReturn("result");
        when(mockBusinessConfig.getSftpDataPath()).thenReturn("result");
        when(mockToolsInfoService.selectToolsByCode("code")).thenReturn(null);

        // Configure SystemDataInteract.queryBusinessSystemBySysName(...).
        final SystemPullDto systemPullDto = new SystemPullDto();
        systemPullDto.setBusinessSystemId(0L);
        systemPullDto.setBusinessSystemCode("businessSystemCode");
        systemPullDto.setBusinessSystemName("businessSystemName");
        when(mockSystemDataInteract.queryBusinessSystemBySysName("businessSystemName")).thenReturn(systemPullDto);

        // Configure ITbCategoryService.selectCategoryExist(...).
        final CategoryResultDto categoryResultDto = new CategoryResultDto();
        categoryResultDto.setId(0L);
        categoryResultDto.setParentId(0L);
        categoryResultDto.setClassname("classname");
        categoryResultDto.setDescription("description");
        categoryResultDto.setLevel(0);
        final List<CategoryResultDto> categoryResultDtos = Arrays.asList(categoryResultDto);
        when(mockCategoryService.selectCategoryExist(any(CategoryEntity.class))).thenReturn(categoryResultDtos);

        when(mockToolsOperateService.saveMoveCommitToolsInfo(any(ToolsSftpImportDto.class),
                any(UserDto.class))).thenThrow(ToolsException.class);

        // Run the test
        final R result = toolsMoveServiceImplUnderTest.importTools(file, user);

        // Verify the results
        verify(mockCategoryService).insertTreeCategory(any(CategoryDto.class), any(UserDto.class));
        verify(mockLogAuditService).logOperationAudit(eq(0L), eq(OperatModuleEnum.TOOL_EDIT),
                eq(OperatTypeEnum.TOOL_LOCAL_IMPORT), eq(0), any(UserDto.class), eq("remark"));
    }

    @Test
    void testImportTools_IToolsOperateServiceSaveMoveCommitToolsInfoThrowsAuditException() throws Exception {
        // Setup
        final MultipartFile file = new MockMultipartFile("name", "content".getBytes());
        final UserDto user = new UserDto();
        user.setUserName("creatorName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        when(mockBusinessConfig.getToolsEvn()).thenReturn("result");
        when(mockBusinessConfig.isToolEvnMainSwitch()).thenReturn(false);
        when(mockBusinessConfig.getFtpType()).thenReturn("result");
        when(mockBusinessConfig.getFtpUser()).thenReturn("result");
        when(mockBusinessConfig.getFtpPw()).thenReturn("result");
        when(mockBusinessConfig.getFtpIp()).thenReturn("result");
        when(mockBusinessConfig.getFtpPort()).thenReturn("result");
        when(mockBusinessConfig.getSftpDataPath()).thenReturn("result");
        when(mockToolsInfoService.selectToolsByCode("code")).thenReturn(null);

        // Configure SystemDataInteract.queryBusinessSystemBySysName(...).
        final SystemPullDto systemPullDto = new SystemPullDto();
        systemPullDto.setBusinessSystemId(0L);
        systemPullDto.setBusinessSystemCode("businessSystemCode");
        systemPullDto.setBusinessSystemName("businessSystemName");
        when(mockSystemDataInteract.queryBusinessSystemBySysName("businessSystemName")).thenReturn(systemPullDto);

        // Configure ITbCategoryService.selectCategoryExist(...).
        final CategoryResultDto categoryResultDto = new CategoryResultDto();
        categoryResultDto.setId(0L);
        categoryResultDto.setParentId(0L);
        categoryResultDto.setClassname("classname");
        categoryResultDto.setDescription("description");
        categoryResultDto.setLevel(0);
        final List<CategoryResultDto> categoryResultDtos = Arrays.asList(categoryResultDto);
        when(mockCategoryService.selectCategoryExist(any(CategoryEntity.class))).thenReturn(categoryResultDtos);

        when(mockToolsOperateService.saveMoveCommitToolsInfo(any(ToolsSftpImportDto.class),
                any(UserDto.class))).thenThrow(AuditException.class);

        // Run the test
        final R result = toolsMoveServiceImplUnderTest.importTools(file, user);

        // Verify the results
        verify(mockCategoryService).insertTreeCategory(any(CategoryDto.class), any(UserDto.class));
        verify(mockLogAuditService).logOperationAudit(eq(0L), eq(OperatModuleEnum.TOOL_EDIT),
                eq(OperatTypeEnum.TOOL_LOCAL_IMPORT), eq(0), any(UserDto.class), eq("remark"));
    }

    @Test
    void testImportTools_IToolsOperateServiceSaveMoveCommitToolsInfoThrowsScriptToolsException() throws Exception {
        // Setup
        final MultipartFile file = new MockMultipartFile("name", "content".getBytes());
        final UserDto user = new UserDto();
        user.setUserName("creatorName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        when(mockBusinessConfig.getToolsEvn()).thenReturn("result");
        when(mockBusinessConfig.isToolEvnMainSwitch()).thenReturn(false);
        when(mockBusinessConfig.getFtpType()).thenReturn("result");
        when(mockBusinessConfig.getFtpUser()).thenReturn("result");
        when(mockBusinessConfig.getFtpPw()).thenReturn("result");
        when(mockBusinessConfig.getFtpIp()).thenReturn("result");
        when(mockBusinessConfig.getFtpPort()).thenReturn("result");
        when(mockBusinessConfig.getSftpDataPath()).thenReturn("result");
        when(mockToolsInfoService.selectToolsByCode("code")).thenReturn(null);

        // Configure SystemDataInteract.queryBusinessSystemBySysName(...).
        final SystemPullDto systemPullDto = new SystemPullDto();
        systemPullDto.setBusinessSystemId(0L);
        systemPullDto.setBusinessSystemCode("businessSystemCode");
        systemPullDto.setBusinessSystemName("businessSystemName");
        when(mockSystemDataInteract.queryBusinessSystemBySysName("businessSystemName")).thenReturn(systemPullDto);

        // Configure ITbCategoryService.selectCategoryExist(...).
        final CategoryResultDto categoryResultDto = new CategoryResultDto();
        categoryResultDto.setId(0L);
        categoryResultDto.setParentId(0L);
        categoryResultDto.setClassname("classname");
        categoryResultDto.setDescription("description");
        categoryResultDto.setLevel(0);
        final List<CategoryResultDto> categoryResultDtos = Arrays.asList(categoryResultDto);
        when(mockCategoryService.selectCategoryExist(any(CategoryEntity.class))).thenReturn(categoryResultDtos);

        when(mockToolsOperateService.saveMoveCommitToolsInfo(any(ToolsSftpImportDto.class),
                any(UserDto.class))).thenThrow(ScriptToolsException.class);

        // Run the test
        final R result = toolsMoveServiceImplUnderTest.importTools(file, user);

        // Verify the results
        verify(mockCategoryService).insertTreeCategory(any(CategoryDto.class), any(UserDto.class));
        verify(mockLogAuditService).logOperationAudit(eq(0L), eq(OperatModuleEnum.TOOL_EDIT),
                eq(OperatTypeEnum.TOOL_LOCAL_IMPORT), eq(0), any(UserDto.class), eq("remark"));
    }

    @Test
    void testImportTools_IToolsOperateServiceSaveMoveCommitToolsInfoThrowsScriptException() throws Exception {
        // Setup
        final MultipartFile file = new MockMultipartFile("name", "content".getBytes());
        final UserDto user = new UserDto();
        user.setUserName("creatorName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        when(mockBusinessConfig.getToolsEvn()).thenReturn("result");
        when(mockBusinessConfig.isToolEvnMainSwitch()).thenReturn(false);
        when(mockBusinessConfig.getFtpType()).thenReturn("result");
        when(mockBusinessConfig.getFtpUser()).thenReturn("result");
        when(mockBusinessConfig.getFtpPw()).thenReturn("result");
        when(mockBusinessConfig.getFtpIp()).thenReturn("result");
        when(mockBusinessConfig.getFtpPort()).thenReturn("result");
        when(mockBusinessConfig.getSftpDataPath()).thenReturn("result");
        when(mockToolsInfoService.selectToolsByCode("code")).thenReturn(null);

        // Configure SystemDataInteract.queryBusinessSystemBySysName(...).
        final SystemPullDto systemPullDto = new SystemPullDto();
        systemPullDto.setBusinessSystemId(0L);
        systemPullDto.setBusinessSystemCode("businessSystemCode");
        systemPullDto.setBusinessSystemName("businessSystemName");
        when(mockSystemDataInteract.queryBusinessSystemBySysName("businessSystemName")).thenReturn(systemPullDto);

        // Configure ITbCategoryService.selectCategoryExist(...).
        final CategoryResultDto categoryResultDto = new CategoryResultDto();
        categoryResultDto.setId(0L);
        categoryResultDto.setParentId(0L);
        categoryResultDto.setClassname("classname");
        categoryResultDto.setDescription("description");
        categoryResultDto.setLevel(0);
        final List<CategoryResultDto> categoryResultDtos = Arrays.asList(categoryResultDto);
        when(mockCategoryService.selectCategoryExist(any(CategoryEntity.class))).thenReturn(categoryResultDtos);

        when(mockToolsOperateService.saveMoveCommitToolsInfo(any(ToolsSftpImportDto.class),
                any(UserDto.class))).thenThrow(ScriptException.class);

        // Run the test
        final R result = toolsMoveServiceImplUnderTest.importTools(file, user);

        // Verify the results
        verify(mockCategoryService).insertTreeCategory(any(CategoryDto.class), any(UserDto.class));
        verify(mockLogAuditService).logOperationAudit(eq(0L), eq(OperatModuleEnum.TOOL_EDIT),
                eq(OperatTypeEnum.TOOL_LOCAL_IMPORT), eq(0), any(UserDto.class), eq("remark"));
    }

    @Test
    void testImportTools_IToolsOperateServiceSaveMoveCommitToolsInfoThrowsStudioException() throws Exception {
        // Setup
        final MultipartFile file = new MockMultipartFile("name", "content".getBytes());
        final UserDto user = new UserDto();
        user.setUserName("creatorName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        when(mockBusinessConfig.getToolsEvn()).thenReturn("result");
        when(mockBusinessConfig.isToolEvnMainSwitch()).thenReturn(false);
        when(mockBusinessConfig.getFtpType()).thenReturn("result");
        when(mockBusinessConfig.getFtpUser()).thenReturn("result");
        when(mockBusinessConfig.getFtpPw()).thenReturn("result");
        when(mockBusinessConfig.getFtpIp()).thenReturn("result");
        when(mockBusinessConfig.getFtpPort()).thenReturn("result");
        when(mockBusinessConfig.getSftpDataPath()).thenReturn("result");
        when(mockToolsInfoService.selectToolsByCode("code")).thenReturn(null);

        // Configure SystemDataInteract.queryBusinessSystemBySysName(...).
        final SystemPullDto systemPullDto = new SystemPullDto();
        systemPullDto.setBusinessSystemId(0L);
        systemPullDto.setBusinessSystemCode("businessSystemCode");
        systemPullDto.setBusinessSystemName("businessSystemName");
        when(mockSystemDataInteract.queryBusinessSystemBySysName("businessSystemName")).thenReturn(systemPullDto);

        // Configure ITbCategoryService.selectCategoryExist(...).
        final CategoryResultDto categoryResultDto = new CategoryResultDto();
        categoryResultDto.setId(0L);
        categoryResultDto.setParentId(0L);
        categoryResultDto.setClassname("classname");
        categoryResultDto.setDescription("description");
        categoryResultDto.setLevel(0);
        final List<CategoryResultDto> categoryResultDtos = Arrays.asList(categoryResultDto);
        when(mockCategoryService.selectCategoryExist(any(CategoryEntity.class))).thenReturn(categoryResultDtos);

        when(mockToolsOperateService.saveMoveCommitToolsInfo(any(ToolsSftpImportDto.class),
                any(UserDto.class))).thenThrow(StudioException.class);

        // Run the test
        final R result = toolsMoveServiceImplUnderTest.importTools(file, user);

        // Verify the results
        verify(mockCategoryService).insertTreeCategory(any(CategoryDto.class), any(UserDto.class));
        verify(mockLogAuditService).logOperationAudit(eq(0L), eq(OperatModuleEnum.TOOL_EDIT),
                eq(OperatTypeEnum.TOOL_LOCAL_IMPORT), eq(0), any(UserDto.class), eq("remark"));
    }

    @Test
    void testImportTools_ScriptInteractImportScriptProductionThrowsScriptException() throws Exception {
        // Setup
        final MultipartFile file = new MockMultipartFile("name", "content".getBytes());
        final UserDto user = new UserDto();
        user.setUserName("creatorName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        when(mockBusinessConfig.getToolsEvn()).thenReturn("result");

        // Configure IToolsInfoService.selectToolsByCode(...).
        final ToolsInfoEntity toolsInfoEntity = new ToolsInfoEntity();
        toolsInfoEntity.setId(0L);
        toolsInfoEntity.setCode("code");
        toolsInfoEntity.setStatus(0);
        toolsInfoEntity.setCreatorId(0L);
        toolsInfoEntity.setCreatorName("creatorName");
        toolsInfoEntity.setDelFlag(0);
        toolsInfoEntity.setReturnReason("auditReturnReason");
        when(mockToolsInfoService.selectToolsByCode("code")).thenReturn(toolsInfoEntity);

        // Configure SystemDataInteract.queryBusinessSystemBySysName(...).
        final SystemPullDto systemPullDto = new SystemPullDto();
        systemPullDto.setBusinessSystemId(0L);
        systemPullDto.setBusinessSystemCode("businessSystemCode");
        systemPullDto.setBusinessSystemName("businessSystemName");
        when(mockSystemDataInteract.queryBusinessSystemBySysName("businessSystemName")).thenReturn(systemPullDto);

        when(mockScriptInteract.importScriptProduction(any(ScriptFileInfo.class))).thenThrow(ScriptException.class);

        // Run the test
        assertThatThrownBy(() -> toolsMoveServiceImplUnderTest.importTools(file, user))
                .isInstanceOf(ToolsException.class);
        verify(mockLogAuditService).logOperationAudit(eq(0L), eq(OperatModuleEnum.TOOL_EDIT),
                eq(OperatTypeEnum.TOOL_LOCAL_IMPORT), eq(0), any(UserDto.class), eq("remark"));
    }

    @Test
    void testImportTools_IToolsOperateServiceSaveCommitToolsInfoThrowsToolsException() throws Exception {
        // Setup
        final MultipartFile file = new MockMultipartFile("name", "content".getBytes());
        final UserDto user = new UserDto();
        user.setUserName("creatorName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        when(mockBusinessConfig.getToolsEvn()).thenReturn("result");

        // Configure IToolsInfoService.selectToolsByCode(...).
        final ToolsInfoEntity toolsInfoEntity = new ToolsInfoEntity();
        toolsInfoEntity.setId(0L);
        toolsInfoEntity.setCode("code");
        toolsInfoEntity.setStatus(0);
        toolsInfoEntity.setCreatorId(0L);
        toolsInfoEntity.setCreatorName("creatorName");
        toolsInfoEntity.setDelFlag(0);
        toolsInfoEntity.setReturnReason("auditReturnReason");
        when(mockToolsInfoService.selectToolsByCode("code")).thenReturn(toolsInfoEntity);

        // Configure SystemDataInteract.queryBusinessSystemBySysName(...).
        final SystemPullDto systemPullDto = new SystemPullDto();
        systemPullDto.setBusinessSystemId(0L);
        systemPullDto.setBusinessSystemCode("businessSystemCode");
        systemPullDto.setBusinessSystemName("businessSystemName");
        when(mockSystemDataInteract.queryBusinessSystemBySysName("businessSystemName")).thenReturn(systemPullDto);

        // Configure ITbCategoryService.selectCategoryExist(...).
        final CategoryResultDto categoryResultDto = new CategoryResultDto();
        categoryResultDto.setId(0L);
        categoryResultDto.setParentId(0L);
        categoryResultDto.setClassname("classname");
        categoryResultDto.setDescription("description");
        categoryResultDto.setLevel(0);
        final List<CategoryResultDto> categoryResultDtos = Arrays.asList(categoryResultDto);
        when(mockCategoryService.selectCategoryExist(any(CategoryEntity.class))).thenReturn(categoryResultDtos);

        // Configure ScriptInteract.importScriptProduction(...).
        final ScriptFileInfo scriptFileInfo = new ScriptFileInfo();
        scriptFileInfo.setFileName("fileName");
        scriptFileInfo.setFileSuffix("fileSuffix");
        scriptFileInfo.setFileContentByte("content".getBytes());
        when(mockScriptInteract.importScriptProduction(any(ScriptFileInfo.class))).thenReturn(scriptFileInfo);

        // Configure ScriptInteract.getDefaultScriptInfo(...).
        final ScriptContentDto scriptContentDto = new ScriptContentDto();
        scriptContentDto.setScriptIds("scriptIds");
        scriptContentDto.setScriptName("scriptName");
        scriptContentDto.setScriptType(0);
        scriptContentDto.setScriptEditing("scriptEditing");
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        scriptContentDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        when(mockScriptInteract.getDefaultScriptInfo(any(UserDto.class), eq("scriptIds"))).thenReturn(scriptContentDto);

        when(mockToolsOperateService.saveCommitToolsInfo(null,any(ToolsDto.class), any(UserDto.class),2))
                .thenThrow(ToolsException.class);

        // Run the test
        assertThatThrownBy(() -> toolsMoveServiceImplUnderTest.importTools(file, user))
                .isInstanceOf(ToolsException.class);
        verify(mockCategoryService).insertTreeCategory(any(CategoryDto.class), any(UserDto.class));
        verify(mockLogAuditService).logOperationAudit(eq(0L), eq(OperatModuleEnum.TOOL_EDIT),
                eq(OperatTypeEnum.TOOL_LOCAL_IMPORT), eq(0), any(UserDto.class), eq("remark"));
    }

    @Test
    void testImportTools_IToolsOperateServiceSaveCommitToolsInfoThrowsAuditException() throws Exception {
        // Setup
        final MultipartFile file = new MockMultipartFile("name", "content".getBytes());
        final UserDto user = new UserDto();
        user.setUserName("creatorName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        when(mockBusinessConfig.getToolsEvn()).thenReturn("result");

        // Configure IToolsInfoService.selectToolsByCode(...).
        final ToolsInfoEntity toolsInfoEntity = new ToolsInfoEntity();
        toolsInfoEntity.setId(0L);
        toolsInfoEntity.setCode("code");
        toolsInfoEntity.setStatus(0);
        toolsInfoEntity.setCreatorId(0L);
        toolsInfoEntity.setCreatorName("creatorName");
        toolsInfoEntity.setDelFlag(0);
        toolsInfoEntity.setReturnReason("auditReturnReason");
        when(mockToolsInfoService.selectToolsByCode("code")).thenReturn(toolsInfoEntity);

        // Configure SystemDataInteract.queryBusinessSystemBySysName(...).
        final SystemPullDto systemPullDto = new SystemPullDto();
        systemPullDto.setBusinessSystemId(0L);
        systemPullDto.setBusinessSystemCode("businessSystemCode");
        systemPullDto.setBusinessSystemName("businessSystemName");
        when(mockSystemDataInteract.queryBusinessSystemBySysName("businessSystemName")).thenReturn(systemPullDto);

        // Configure ITbCategoryService.selectCategoryExist(...).
        final CategoryResultDto categoryResultDto = new CategoryResultDto();
        categoryResultDto.setId(0L);
        categoryResultDto.setParentId(0L);
        categoryResultDto.setClassname("classname");
        categoryResultDto.setDescription("description");
        categoryResultDto.setLevel(0);
        final List<CategoryResultDto> categoryResultDtos = Arrays.asList(categoryResultDto);
        when(mockCategoryService.selectCategoryExist(any(CategoryEntity.class))).thenReturn(categoryResultDtos);

        // Configure ScriptInteract.importScriptProduction(...).
        final ScriptFileInfo scriptFileInfo = new ScriptFileInfo();
        scriptFileInfo.setFileName("fileName");
        scriptFileInfo.setFileSuffix("fileSuffix");
        scriptFileInfo.setFileContentByte("content".getBytes());
        when(mockScriptInteract.importScriptProduction(any(ScriptFileInfo.class))).thenReturn(scriptFileInfo);

        // Configure ScriptInteract.getDefaultScriptInfo(...).
        final ScriptContentDto scriptContentDto = new ScriptContentDto();
        scriptContentDto.setScriptIds("scriptIds");
        scriptContentDto.setScriptName("scriptName");
        scriptContentDto.setScriptType(0);
        scriptContentDto.setScriptEditing("scriptEditing");
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        scriptContentDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        when(mockScriptInteract.getDefaultScriptInfo(any(UserDto.class), eq("scriptIds"))).thenReturn(scriptContentDto);

        when(mockToolsOperateService.saveCommitToolsInfo(null,any(ToolsDto.class), any(UserDto.class),2))
                .thenThrow(AuditException.class);

        // Run the test
        assertThatThrownBy(() -> toolsMoveServiceImplUnderTest.importTools(file, user))
                .isInstanceOf(ToolsException.class);
        verify(mockCategoryService).insertTreeCategory(any(CategoryDto.class), any(UserDto.class));
        verify(mockLogAuditService).logOperationAudit(eq(0L), eq(OperatModuleEnum.TOOL_EDIT),
                eq(OperatTypeEnum.TOOL_LOCAL_IMPORT), eq(0), any(UserDto.class), eq("remark"));
    }

    @Test
    void testImportTools_IToolsOperateServiceSaveCommitToolsInfoThrowsScriptToolsException() throws Exception {
        // Setup
        final MultipartFile file = new MockMultipartFile("name", "content".getBytes());
        final UserDto user = new UserDto();
        user.setUserName("creatorName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        when(mockBusinessConfig.getToolsEvn()).thenReturn("result");

        // Configure IToolsInfoService.selectToolsByCode(...).
        final ToolsInfoEntity toolsInfoEntity = new ToolsInfoEntity();
        toolsInfoEntity.setId(0L);
        toolsInfoEntity.setCode("code");
        toolsInfoEntity.setStatus(0);
        toolsInfoEntity.setCreatorId(0L);
        toolsInfoEntity.setCreatorName("creatorName");
        toolsInfoEntity.setDelFlag(0);
        toolsInfoEntity.setReturnReason("auditReturnReason");
        when(mockToolsInfoService.selectToolsByCode("code")).thenReturn(toolsInfoEntity);

        // Configure SystemDataInteract.queryBusinessSystemBySysName(...).
        final SystemPullDto systemPullDto = new SystemPullDto();
        systemPullDto.setBusinessSystemId(0L);
        systemPullDto.setBusinessSystemCode("businessSystemCode");
        systemPullDto.setBusinessSystemName("businessSystemName");
        when(mockSystemDataInteract.queryBusinessSystemBySysName("businessSystemName")).thenReturn(systemPullDto);

        // Configure ITbCategoryService.selectCategoryExist(...).
        final CategoryResultDto categoryResultDto = new CategoryResultDto();
        categoryResultDto.setId(0L);
        categoryResultDto.setParentId(0L);
        categoryResultDto.setClassname("classname");
        categoryResultDto.setDescription("description");
        categoryResultDto.setLevel(0);
        final List<CategoryResultDto> categoryResultDtos = Arrays.asList(categoryResultDto);
        when(mockCategoryService.selectCategoryExist(any(CategoryEntity.class))).thenReturn(categoryResultDtos);

        // Configure ScriptInteract.importScriptProduction(...).
        final ScriptFileInfo scriptFileInfo = new ScriptFileInfo();
        scriptFileInfo.setFileName("fileName");
        scriptFileInfo.setFileSuffix("fileSuffix");
        scriptFileInfo.setFileContentByte("content".getBytes());
        when(mockScriptInteract.importScriptProduction(any(ScriptFileInfo.class))).thenReturn(scriptFileInfo);

        // Configure ScriptInteract.getDefaultScriptInfo(...).
        final ScriptContentDto scriptContentDto = new ScriptContentDto();
        scriptContentDto.setScriptIds("scriptIds");
        scriptContentDto.setScriptName("scriptName");
        scriptContentDto.setScriptType(0);
        scriptContentDto.setScriptEditing("scriptEditing");
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        scriptContentDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        when(mockScriptInteract.getDefaultScriptInfo(any(UserDto.class), eq("scriptIds"))).thenReturn(scriptContentDto);

        when(mockToolsOperateService.saveCommitToolsInfo(null,any(ToolsDto.class), any(UserDto.class),2))
                .thenThrow(ScriptToolsException.class);

        // Run the test
        assertThatThrownBy(() -> toolsMoveServiceImplUnderTest.importTools(file, user))
                .isInstanceOf(ToolsException.class);
        verify(mockCategoryService).insertTreeCategory(any(CategoryDto.class), any(UserDto.class));
        verify(mockLogAuditService).logOperationAudit(eq(0L), eq(OperatModuleEnum.TOOL_EDIT),
                eq(OperatTypeEnum.TOOL_LOCAL_IMPORT), eq(0), any(UserDto.class), eq("remark"));
    }

    @Test
    void testImportTools_IToolsOperateServiceSaveCommitToolsInfoThrowsStudioException() throws Exception {
        // Setup
        final MultipartFile file = new MockMultipartFile("name", "content".getBytes());
        final UserDto user = new UserDto();
        user.setUserName("creatorName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        when(mockBusinessConfig.getToolsEvn()).thenReturn("result");

        // Configure IToolsInfoService.selectToolsByCode(...).
        final ToolsInfoEntity toolsInfoEntity = new ToolsInfoEntity();
        toolsInfoEntity.setId(0L);
        toolsInfoEntity.setCode("code");
        toolsInfoEntity.setStatus(0);
        toolsInfoEntity.setCreatorId(0L);
        toolsInfoEntity.setCreatorName("creatorName");
        toolsInfoEntity.setDelFlag(0);
        toolsInfoEntity.setReturnReason("auditReturnReason");
        when(mockToolsInfoService.selectToolsByCode("code")).thenReturn(toolsInfoEntity);

        // Configure SystemDataInteract.queryBusinessSystemBySysName(...).
        final SystemPullDto systemPullDto = new SystemPullDto();
        systemPullDto.setBusinessSystemId(0L);
        systemPullDto.setBusinessSystemCode("businessSystemCode");
        systemPullDto.setBusinessSystemName("businessSystemName");
        when(mockSystemDataInteract.queryBusinessSystemBySysName("businessSystemName")).thenReturn(systemPullDto);

        // Configure ITbCategoryService.selectCategoryExist(...).
        final CategoryResultDto categoryResultDto = new CategoryResultDto();
        categoryResultDto.setId(0L);
        categoryResultDto.setParentId(0L);
        categoryResultDto.setClassname("classname");
        categoryResultDto.setDescription("description");
        categoryResultDto.setLevel(0);
        final List<CategoryResultDto> categoryResultDtos = Arrays.asList(categoryResultDto);
        when(mockCategoryService.selectCategoryExist(any(CategoryEntity.class))).thenReturn(categoryResultDtos);

        // Configure ScriptInteract.importScriptProduction(...).
        final ScriptFileInfo scriptFileInfo = new ScriptFileInfo();
        scriptFileInfo.setFileName("fileName");
        scriptFileInfo.setFileSuffix("fileSuffix");
        scriptFileInfo.setFileContentByte("content".getBytes());
        when(mockScriptInteract.importScriptProduction(any(ScriptFileInfo.class))).thenReturn(scriptFileInfo);

        // Configure ScriptInteract.getDefaultScriptInfo(...).
        final ScriptContentDto scriptContentDto = new ScriptContentDto();
        scriptContentDto.setScriptIds("scriptIds");
        scriptContentDto.setScriptName("scriptName");
        scriptContentDto.setScriptType(0);
        scriptContentDto.setScriptEditing("scriptEditing");
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        scriptContentDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        when(mockScriptInteract.getDefaultScriptInfo(any(UserDto.class), eq("scriptIds"))).thenReturn(scriptContentDto);

        when(mockToolsOperateService.saveCommitToolsInfo(null,any(ToolsDto.class), any(UserDto.class),2))
                .thenThrow(StudioException.class);

        // Run the test
        assertThatThrownBy(() -> toolsMoveServiceImplUnderTest.importTools(file, user))
                .isInstanceOf(ToolsException.class);
        verify(mockCategoryService).insertTreeCategory(any(CategoryDto.class), any(UserDto.class));
        verify(mockLogAuditService).logOperationAudit(eq(0L), eq(OperatModuleEnum.TOOL_EDIT),
                eq(OperatTypeEnum.TOOL_LOCAL_IMPORT), eq(0), any(UserDto.class), eq("remark"));
    }

    @Test
    void testImportTools_StudioInteractThrowsStudioException() throws Exception {
        // Setup
        final MultipartFile file = new MockMultipartFile("name", "content".getBytes());
        final UserDto user = new UserDto();
        user.setUserName("creatorName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        when(mockBusinessConfig.getToolsEvn()).thenReturn("result");

        // Configure IToolsInfoService.selectToolsByCode(...).
        final ToolsInfoEntity toolsInfoEntity = new ToolsInfoEntity();
        toolsInfoEntity.setId(0L);
        toolsInfoEntity.setCode("code");
        toolsInfoEntity.setStatus(0);
        toolsInfoEntity.setCreatorId(0L);
        toolsInfoEntity.setCreatorName("creatorName");
        toolsInfoEntity.setDelFlag(0);
        toolsInfoEntity.setReturnReason("auditReturnReason");
        when(mockToolsInfoService.selectToolsByCode("code")).thenReturn(toolsInfoEntity);

        // Configure SystemDataInteract.queryBusinessSystemBySysName(...).
        final SystemPullDto systemPullDto = new SystemPullDto();
        systemPullDto.setBusinessSystemId(0L);
        systemPullDto.setBusinessSystemCode("businessSystemCode");
        systemPullDto.setBusinessSystemName("businessSystemName");
        when(mockSystemDataInteract.queryBusinessSystemBySysName("businessSystemName")).thenReturn(systemPullDto);

        when(mockStudioInteract.importStudioData(any(StudioFileProjectInteractDto.class),
                any(UserDto.class))).thenThrow(StudioException.class);

        // Run the test
        assertThatThrownBy(() -> toolsMoveServiceImplUnderTest.importTools(file, user))
                .isInstanceOf(ToolsException.class);
        verify(mockLogAuditService).logOperationAudit(eq(0L), eq(OperatModuleEnum.TOOL_EDIT),
                eq(OperatTypeEnum.TOOL_LOCAL_IMPORT), eq(0), any(UserDto.class), eq("remark"));
    }

    @Test
    void testPublishMigrationImportTools() throws Exception {
        // Setup
        final MigrationImportToolsDto migrationImportToolsDto = new MigrationImportToolsDto();
        migrationImportToolsDto.setFileNames(Arrays.asList("value"));
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto.setId(0L);
        auditEverybodyQueryDto.setAuditorId(0L);
        auditEverybodyQueryDto.setAuditorName("auditorName");
        migrationImportToolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));

        final UserDto user = new UserDto();
        user.setUserName("creatorName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        when(mockBusinessConfig.getToolsEvn()).thenReturn("result");
        when(mockBusinessConfig.isToolEvnMainSwitch()).thenReturn(false);
        when(mockBusinessConfig.getFtpType()).thenReturn("result");
        when(mockBusinessConfig.getFtpUser()).thenReturn("result");
        when(mockBusinessConfig.getFtpPw()).thenReturn("result");
        when(mockBusinessConfig.getFtpIp()).thenReturn("result");
        when(mockBusinessConfig.getFtpPort()).thenReturn("result");
        when(mockBusinessConfig.getSftpDataPath()).thenReturn("result");

        // Configure IToolsInfoService.selectToolsByCode(...).
        final ToolsInfoEntity toolsInfoEntity = new ToolsInfoEntity();
        toolsInfoEntity.setId(0L);
        toolsInfoEntity.setCode("code");
        toolsInfoEntity.setStatus(0);
        toolsInfoEntity.setCreatorId(0L);
        toolsInfoEntity.setCreatorName("creatorName");
        toolsInfoEntity.setDelFlag(0);
        toolsInfoEntity.setReturnReason("auditReturnReason");
        when(mockToolsInfoService.selectToolsByCode("code")).thenReturn(toolsInfoEntity);

        // Configure SystemDataInteract.queryBusinessSystemBySysName(...).
        final SystemPullDto systemPullDto = new SystemPullDto();
        systemPullDto.setBusinessSystemId(0L);
        systemPullDto.setBusinessSystemCode("businessSystemCode");
        systemPullDto.setBusinessSystemName("businessSystemName");
        when(mockSystemDataInteract.queryBusinessSystemBySysName("businessSystemName")).thenReturn(systemPullDto);

        // Configure ITbCategoryService.selectCategoryExist(...).
        final CategoryResultDto categoryResultDto = new CategoryResultDto();
        categoryResultDto.setId(0L);
        categoryResultDto.setParentId(0L);
        categoryResultDto.setClassname("classname");
        categoryResultDto.setDescription("description");
        categoryResultDto.setLevel(0);
        final List<CategoryResultDto> categoryResultDtos = Arrays.asList(categoryResultDto);
        when(mockCategoryService.selectCategoryExist(any(CategoryEntity.class))).thenReturn(categoryResultDtos);

        when(mockToolsOperateService.saveMoveCommitToolsInfo(any(ToolsSftpImportDto.class),
                any(UserDto.class))).thenReturn(false);

        // Configure ScriptInteract.importScriptProduction(...).
        final ScriptFileInfo scriptFileInfo = new ScriptFileInfo();
        scriptFileInfo.setFileName("fileName");
        scriptFileInfo.setFileSuffix("fileSuffix");
        scriptFileInfo.setFileContentByte("content".getBytes());
        when(mockScriptInteract.importScriptProduction(any(ScriptFileInfo.class))).thenReturn(scriptFileInfo);

        // Configure ScriptInteract.getDefaultScriptInfo(...).
        final ScriptContentDto scriptContentDto = new ScriptContentDto();
        scriptContentDto.setScriptIds("scriptIds");
        scriptContentDto.setScriptName("scriptName");
        scriptContentDto.setScriptType(0);
        scriptContentDto.setScriptEditing("scriptEditing");
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        scriptContentDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        when(mockScriptInteract.getDefaultScriptInfo(any(UserDto.class), eq("scriptIds"))).thenReturn(scriptContentDto);

        // Configure StudioInteract.importStudioData(...).
        final ToolBoxDto toolBoxDto = new ToolBoxDto();
        toolBoxDto.setProjectId(0L);
        toolBoxDto.setProjectVerId(0L);
        toolBoxDto.setWorkflowId(0L);
        toolBoxDto.setProjectName("projectName");
        toolBoxDto.setWorkflowName("workflowName");
        when(mockStudioInteract.importStudioData(any(StudioFileProjectInteractDto.class),
                any(UserDto.class))).thenReturn(toolBoxDto);

        // Run the test
        final R result = toolsMoveServiceImplUnderTest.publishMigrationImportTools(migrationImportToolsDto, user);

        // Verify the results
        verify(mockCategoryService).insertTreeCategory(any(CategoryDto.class), any(UserDto.class));
        verify(mockToolsOperateService).saveCommitToolsInfo(null,any(ToolsDto.class), any(UserDto.class),2);
    }

    @Test
    void testPublishMigrationImportTools_IToolsInfoServiceReturnsNull() throws Exception {
        // Setup
        final MigrationImportToolsDto migrationImportToolsDto = new MigrationImportToolsDto();
        migrationImportToolsDto.setFileNames(Arrays.asList("value"));
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto.setId(0L);
        auditEverybodyQueryDto.setAuditorId(0L);
        auditEverybodyQueryDto.setAuditorName("auditorName");
        migrationImportToolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));

        final UserDto user = new UserDto();
        user.setUserName("creatorName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        when(mockBusinessConfig.getToolsEvn()).thenReturn("result");
        when(mockBusinessConfig.isToolEvnMainSwitch()).thenReturn(false);
        when(mockBusinessConfig.getFtpType()).thenReturn("result");
        when(mockBusinessConfig.getFtpUser()).thenReturn("result");
        when(mockBusinessConfig.getFtpPw()).thenReturn("result");
        when(mockBusinessConfig.getFtpIp()).thenReturn("result");
        when(mockBusinessConfig.getFtpPort()).thenReturn("result");
        when(mockBusinessConfig.getSftpDataPath()).thenReturn("result");
        when(mockToolsInfoService.selectToolsByCode("code")).thenReturn(null);

        // Configure SystemDataInteract.queryBusinessSystemBySysName(...).
        final SystemPullDto systemPullDto = new SystemPullDto();
        systemPullDto.setBusinessSystemId(0L);
        systemPullDto.setBusinessSystemCode("businessSystemCode");
        systemPullDto.setBusinessSystemName("businessSystemName");
        when(mockSystemDataInteract.queryBusinessSystemBySysName("businessSystemName")).thenReturn(systemPullDto);

        // Configure ITbCategoryService.selectCategoryExist(...).
        final CategoryResultDto categoryResultDto = new CategoryResultDto();
        categoryResultDto.setId(0L);
        categoryResultDto.setParentId(0L);
        categoryResultDto.setClassname("classname");
        categoryResultDto.setDescription("description");
        categoryResultDto.setLevel(0);
        final List<CategoryResultDto> categoryResultDtos = Arrays.asList(categoryResultDto);
        when(mockCategoryService.selectCategoryExist(any(CategoryEntity.class))).thenReturn(categoryResultDtos);

        when(mockToolsOperateService.saveMoveCommitToolsInfo(any(ToolsSftpImportDto.class),
                any(UserDto.class))).thenReturn(false);

        // Run the test
        final R result = toolsMoveServiceImplUnderTest.publishMigrationImportTools(migrationImportToolsDto, user);

        // Verify the results
        verify(mockCategoryService).insertTreeCategory(any(CategoryDto.class), any(UserDto.class));
    }

    @Test
    void testPublishMigrationImportTools_ITbCategoryServiceSelectCategoryExistReturnsNoItems() throws Exception {
        // Setup
        final MigrationImportToolsDto migrationImportToolsDto = new MigrationImportToolsDto();
        migrationImportToolsDto.setFileNames(Arrays.asList("value"));
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto.setId(0L);
        auditEverybodyQueryDto.setAuditorId(0L);
        auditEverybodyQueryDto.setAuditorName("auditorName");
        migrationImportToolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));

        final UserDto user = new UserDto();
        user.setUserName("creatorName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        when(mockBusinessConfig.getToolsEvn()).thenReturn("result");
        when(mockBusinessConfig.isToolEvnMainSwitch()).thenReturn(false);
        when(mockBusinessConfig.getFtpType()).thenReturn("result");
        when(mockBusinessConfig.getFtpUser()).thenReturn("result");
        when(mockBusinessConfig.getFtpPw()).thenReturn("result");
        when(mockBusinessConfig.getFtpIp()).thenReturn("result");
        when(mockBusinessConfig.getFtpPort()).thenReturn("result");
        when(mockBusinessConfig.getSftpDataPath()).thenReturn("result");
        when(mockToolsInfoService.selectToolsByCode("code")).thenReturn(null);

        // Configure SystemDataInteract.queryBusinessSystemBySysName(...).
        final SystemPullDto systemPullDto = new SystemPullDto();
        systemPullDto.setBusinessSystemId(0L);
        systemPullDto.setBusinessSystemCode("businessSystemCode");
        systemPullDto.setBusinessSystemName("businessSystemName");
        when(mockSystemDataInteract.queryBusinessSystemBySysName("businessSystemName")).thenReturn(systemPullDto);

        when(mockCategoryService.selectCategoryExist(any(CategoryEntity.class))).thenReturn(Collections.emptyList());
        when(mockToolsOperateService.saveMoveCommitToolsInfo(any(ToolsSftpImportDto.class),
                any(UserDto.class))).thenReturn(false);

        // Run the test
        final R result = toolsMoveServiceImplUnderTest.publishMigrationImportTools(migrationImportToolsDto, user);

        // Verify the results
        verify(mockCategoryService).insertTreeCategory(any(CategoryDto.class), any(UserDto.class));
    }

    @Test
    void testPublishMigrationImportTools_IToolsOperateServiceSaveMoveCommitToolsInfoThrowsToolsException() throws Exception {
        // Setup
        final MigrationImportToolsDto migrationImportToolsDto = new MigrationImportToolsDto();
        migrationImportToolsDto.setFileNames(Arrays.asList("value"));
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto.setId(0L);
        auditEverybodyQueryDto.setAuditorId(0L);
        auditEverybodyQueryDto.setAuditorName("auditorName");
        migrationImportToolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));

        final UserDto user = new UserDto();
        user.setUserName("creatorName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        when(mockBusinessConfig.getToolsEvn()).thenReturn("result");
        when(mockBusinessConfig.isToolEvnMainSwitch()).thenReturn(false);
        when(mockBusinessConfig.getFtpType()).thenReturn("result");
        when(mockBusinessConfig.getFtpUser()).thenReturn("result");
        when(mockBusinessConfig.getFtpPw()).thenReturn("result");
        when(mockBusinessConfig.getFtpIp()).thenReturn("result");
        when(mockBusinessConfig.getFtpPort()).thenReturn("result");
        when(mockBusinessConfig.getSftpDataPath()).thenReturn("result");
        when(mockToolsInfoService.selectToolsByCode("code")).thenReturn(null);

        // Configure SystemDataInteract.queryBusinessSystemBySysName(...).
        final SystemPullDto systemPullDto = new SystemPullDto();
        systemPullDto.setBusinessSystemId(0L);
        systemPullDto.setBusinessSystemCode("businessSystemCode");
        systemPullDto.setBusinessSystemName("businessSystemName");
        when(mockSystemDataInteract.queryBusinessSystemBySysName("businessSystemName")).thenReturn(systemPullDto);

        // Configure ITbCategoryService.selectCategoryExist(...).
        final CategoryResultDto categoryResultDto = new CategoryResultDto();
        categoryResultDto.setId(0L);
        categoryResultDto.setParentId(0L);
        categoryResultDto.setClassname("classname");
        categoryResultDto.setDescription("description");
        categoryResultDto.setLevel(0);
        final List<CategoryResultDto> categoryResultDtos = Arrays.asList(categoryResultDto);
        when(mockCategoryService.selectCategoryExist(any(CategoryEntity.class))).thenReturn(categoryResultDtos);

        when(mockToolsOperateService.saveMoveCommitToolsInfo(any(ToolsSftpImportDto.class),
                any(UserDto.class))).thenThrow(ToolsException.class);

        // Run the test
        final R result = toolsMoveServiceImplUnderTest.publishMigrationImportTools(migrationImportToolsDto, user);

        // Verify the results
        verify(mockCategoryService).insertTreeCategory(any(CategoryDto.class), any(UserDto.class));
    }

    @Test
    void testPublishMigrationImportTools_IToolsOperateServiceSaveMoveCommitToolsInfoThrowsAuditException() throws Exception {
        // Setup
        final MigrationImportToolsDto migrationImportToolsDto = new MigrationImportToolsDto();
        migrationImportToolsDto.setFileNames(Arrays.asList("value"));
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto.setId(0L);
        auditEverybodyQueryDto.setAuditorId(0L);
        auditEverybodyQueryDto.setAuditorName("auditorName");
        migrationImportToolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));

        final UserDto user = new UserDto();
        user.setUserName("creatorName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        when(mockBusinessConfig.getToolsEvn()).thenReturn("result");
        when(mockBusinessConfig.isToolEvnMainSwitch()).thenReturn(false);
        when(mockBusinessConfig.getFtpType()).thenReturn("result");
        when(mockBusinessConfig.getFtpUser()).thenReturn("result");
        when(mockBusinessConfig.getFtpPw()).thenReturn("result");
        when(mockBusinessConfig.getFtpIp()).thenReturn("result");
        when(mockBusinessConfig.getFtpPort()).thenReturn("result");
        when(mockBusinessConfig.getSftpDataPath()).thenReturn("result");
        when(mockToolsInfoService.selectToolsByCode("code")).thenReturn(null);

        // Configure SystemDataInteract.queryBusinessSystemBySysName(...).
        final SystemPullDto systemPullDto = new SystemPullDto();
        systemPullDto.setBusinessSystemId(0L);
        systemPullDto.setBusinessSystemCode("businessSystemCode");
        systemPullDto.setBusinessSystemName("businessSystemName");
        when(mockSystemDataInteract.queryBusinessSystemBySysName("businessSystemName")).thenReturn(systemPullDto);

        // Configure ITbCategoryService.selectCategoryExist(...).
        final CategoryResultDto categoryResultDto = new CategoryResultDto();
        categoryResultDto.setId(0L);
        categoryResultDto.setParentId(0L);
        categoryResultDto.setClassname("classname");
        categoryResultDto.setDescription("description");
        categoryResultDto.setLevel(0);
        final List<CategoryResultDto> categoryResultDtos = Arrays.asList(categoryResultDto);
        when(mockCategoryService.selectCategoryExist(any(CategoryEntity.class))).thenReturn(categoryResultDtos);

        when(mockToolsOperateService.saveMoveCommitToolsInfo(any(ToolsSftpImportDto.class),
                any(UserDto.class))).thenThrow(AuditException.class);

        // Run the test
        final R result = toolsMoveServiceImplUnderTest.publishMigrationImportTools(migrationImportToolsDto, user);

        // Verify the results
        verify(mockCategoryService).insertTreeCategory(any(CategoryDto.class), any(UserDto.class));
    }

    @Test
    void testPublishMigrationImportTools_IToolsOperateServiceSaveMoveCommitToolsInfoThrowsScriptToolsException() throws Exception {
        // Setup
        final MigrationImportToolsDto migrationImportToolsDto = new MigrationImportToolsDto();
        migrationImportToolsDto.setFileNames(Arrays.asList("value"));
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto.setId(0L);
        auditEverybodyQueryDto.setAuditorId(0L);
        auditEverybodyQueryDto.setAuditorName("auditorName");
        migrationImportToolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));

        final UserDto user = new UserDto();
        user.setUserName("creatorName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        when(mockBusinessConfig.getToolsEvn()).thenReturn("result");
        when(mockBusinessConfig.isToolEvnMainSwitch()).thenReturn(false);
        when(mockBusinessConfig.getFtpType()).thenReturn("result");
        when(mockBusinessConfig.getFtpUser()).thenReturn("result");
        when(mockBusinessConfig.getFtpPw()).thenReturn("result");
        when(mockBusinessConfig.getFtpIp()).thenReturn("result");
        when(mockBusinessConfig.getFtpPort()).thenReturn("result");
        when(mockBusinessConfig.getSftpDataPath()).thenReturn("result");
        when(mockToolsInfoService.selectToolsByCode("code")).thenReturn(null);

        // Configure SystemDataInteract.queryBusinessSystemBySysName(...).
        final SystemPullDto systemPullDto = new SystemPullDto();
        systemPullDto.setBusinessSystemId(0L);
        systemPullDto.setBusinessSystemCode("businessSystemCode");
        systemPullDto.setBusinessSystemName("businessSystemName");
        when(mockSystemDataInteract.queryBusinessSystemBySysName("businessSystemName")).thenReturn(systemPullDto);

        // Configure ITbCategoryService.selectCategoryExist(...).
        final CategoryResultDto categoryResultDto = new CategoryResultDto();
        categoryResultDto.setId(0L);
        categoryResultDto.setParentId(0L);
        categoryResultDto.setClassname("classname");
        categoryResultDto.setDescription("description");
        categoryResultDto.setLevel(0);
        final List<CategoryResultDto> categoryResultDtos = Arrays.asList(categoryResultDto);
        when(mockCategoryService.selectCategoryExist(any(CategoryEntity.class))).thenReturn(categoryResultDtos);

        when(mockToolsOperateService.saveMoveCommitToolsInfo(any(ToolsSftpImportDto.class),
                any(UserDto.class))).thenThrow(ScriptToolsException.class);

        // Run the test
        final R result = toolsMoveServiceImplUnderTest.publishMigrationImportTools(migrationImportToolsDto, user);

        // Verify the results
        verify(mockCategoryService).insertTreeCategory(any(CategoryDto.class), any(UserDto.class));
    }

    @Test
    void testPublishMigrationImportTools_IToolsOperateServiceSaveMoveCommitToolsInfoThrowsScriptException() throws Exception {
        // Setup
        final MigrationImportToolsDto migrationImportToolsDto = new MigrationImportToolsDto();
        migrationImportToolsDto.setFileNames(Arrays.asList("value"));
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto.setId(0L);
        auditEverybodyQueryDto.setAuditorId(0L);
        auditEverybodyQueryDto.setAuditorName("auditorName");
        migrationImportToolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));

        final UserDto user = new UserDto();
        user.setUserName("creatorName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        when(mockBusinessConfig.getToolsEvn()).thenReturn("result");
        when(mockBusinessConfig.isToolEvnMainSwitch()).thenReturn(false);
        when(mockBusinessConfig.getFtpType()).thenReturn("result");
        when(mockBusinessConfig.getFtpUser()).thenReturn("result");
        when(mockBusinessConfig.getFtpPw()).thenReturn("result");
        when(mockBusinessConfig.getFtpIp()).thenReturn("result");
        when(mockBusinessConfig.getFtpPort()).thenReturn("result");
        when(mockBusinessConfig.getSftpDataPath()).thenReturn("result");
        when(mockToolsInfoService.selectToolsByCode("code")).thenReturn(null);

        // Configure SystemDataInteract.queryBusinessSystemBySysName(...).
        final SystemPullDto systemPullDto = new SystemPullDto();
        systemPullDto.setBusinessSystemId(0L);
        systemPullDto.setBusinessSystemCode("businessSystemCode");
        systemPullDto.setBusinessSystemName("businessSystemName");
        when(mockSystemDataInteract.queryBusinessSystemBySysName("businessSystemName")).thenReturn(systemPullDto);

        // Configure ITbCategoryService.selectCategoryExist(...).
        final CategoryResultDto categoryResultDto = new CategoryResultDto();
        categoryResultDto.setId(0L);
        categoryResultDto.setParentId(0L);
        categoryResultDto.setClassname("classname");
        categoryResultDto.setDescription("description");
        categoryResultDto.setLevel(0);
        final List<CategoryResultDto> categoryResultDtos = Arrays.asList(categoryResultDto);
        when(mockCategoryService.selectCategoryExist(any(CategoryEntity.class))).thenReturn(categoryResultDtos);

        when(mockToolsOperateService.saveMoveCommitToolsInfo(any(ToolsSftpImportDto.class),
                any(UserDto.class))).thenThrow(ScriptException.class);

        // Run the test
        final R result = toolsMoveServiceImplUnderTest.publishMigrationImportTools(migrationImportToolsDto, user);

        // Verify the results
        verify(mockCategoryService).insertTreeCategory(any(CategoryDto.class), any(UserDto.class));
    }

    @Test
    void testPublishMigrationImportTools_IToolsOperateServiceSaveMoveCommitToolsInfoThrowsStudioException() throws Exception {
        // Setup
        final MigrationImportToolsDto migrationImportToolsDto = new MigrationImportToolsDto();
        migrationImportToolsDto.setFileNames(Arrays.asList("value"));
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto.setId(0L);
        auditEverybodyQueryDto.setAuditorId(0L);
        auditEverybodyQueryDto.setAuditorName("auditorName");
        migrationImportToolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));

        final UserDto user = new UserDto();
        user.setUserName("creatorName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        when(mockBusinessConfig.getToolsEvn()).thenReturn("result");
        when(mockBusinessConfig.isToolEvnMainSwitch()).thenReturn(false);
        when(mockBusinessConfig.getFtpType()).thenReturn("result");
        when(mockBusinessConfig.getFtpUser()).thenReturn("result");
        when(mockBusinessConfig.getFtpPw()).thenReturn("result");
        when(mockBusinessConfig.getFtpIp()).thenReturn("result");
        when(mockBusinessConfig.getFtpPort()).thenReturn("result");
        when(mockBusinessConfig.getSftpDataPath()).thenReturn("result");
        when(mockToolsInfoService.selectToolsByCode("code")).thenReturn(null);

        // Configure SystemDataInteract.queryBusinessSystemBySysName(...).
        final SystemPullDto systemPullDto = new SystemPullDto();
        systemPullDto.setBusinessSystemId(0L);
        systemPullDto.setBusinessSystemCode("businessSystemCode");
        systemPullDto.setBusinessSystemName("businessSystemName");
        when(mockSystemDataInteract.queryBusinessSystemBySysName("businessSystemName")).thenReturn(systemPullDto);

        // Configure ITbCategoryService.selectCategoryExist(...).
        final CategoryResultDto categoryResultDto = new CategoryResultDto();
        categoryResultDto.setId(0L);
        categoryResultDto.setParentId(0L);
        categoryResultDto.setClassname("classname");
        categoryResultDto.setDescription("description");
        categoryResultDto.setLevel(0);
        final List<CategoryResultDto> categoryResultDtos = Arrays.asList(categoryResultDto);
        when(mockCategoryService.selectCategoryExist(any(CategoryEntity.class))).thenReturn(categoryResultDtos);

        when(mockToolsOperateService.saveMoveCommitToolsInfo(any(ToolsSftpImportDto.class),
                any(UserDto.class))).thenThrow(StudioException.class);

        // Run the test
        final R result = toolsMoveServiceImplUnderTest.publishMigrationImportTools(migrationImportToolsDto, user);

        // Verify the results
        verify(mockCategoryService).insertTreeCategory(any(CategoryDto.class), any(UserDto.class));
    }

    @Test
    void testPublishMigrationImportTools_ScriptInteractImportScriptProductionThrowsScriptException() throws Exception {
        // Setup
        final MigrationImportToolsDto migrationImportToolsDto = new MigrationImportToolsDto();
        migrationImportToolsDto.setFileNames(Arrays.asList("value"));
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto.setId(0L);
        auditEverybodyQueryDto.setAuditorId(0L);
        auditEverybodyQueryDto.setAuditorName("auditorName");
        migrationImportToolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));

        final UserDto user = new UserDto();
        user.setUserName("creatorName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        when(mockBusinessConfig.getToolsEvn()).thenReturn("result");

        // Configure IToolsInfoService.selectToolsByCode(...).
        final ToolsInfoEntity toolsInfoEntity = new ToolsInfoEntity();
        toolsInfoEntity.setId(0L);
        toolsInfoEntity.setCode("code");
        toolsInfoEntity.setStatus(0);
        toolsInfoEntity.setCreatorId(0L);
        toolsInfoEntity.setCreatorName("creatorName");
        toolsInfoEntity.setDelFlag(0);
        toolsInfoEntity.setReturnReason("auditReturnReason");
        when(mockToolsInfoService.selectToolsByCode("code")).thenReturn(toolsInfoEntity);

        // Configure SystemDataInteract.queryBusinessSystemBySysName(...).
        final SystemPullDto systemPullDto = new SystemPullDto();
        systemPullDto.setBusinessSystemId(0L);
        systemPullDto.setBusinessSystemCode("businessSystemCode");
        systemPullDto.setBusinessSystemName("businessSystemName");
        when(mockSystemDataInteract.queryBusinessSystemBySysName("businessSystemName")).thenReturn(systemPullDto);

        when(mockScriptInteract.importScriptProduction(any(ScriptFileInfo.class))).thenThrow(ScriptException.class);

        // Run the test
        assertThatThrownBy(() -> toolsMoveServiceImplUnderTest.publishMigrationImportTools(migrationImportToolsDto,
                user)).isInstanceOf(ToolsException.class);
    }

    @Test
    void testPublishMigrationImportTools_IToolsOperateServiceSaveCommitToolsInfoThrowsToolsException() throws Exception {
        // Setup
        final MigrationImportToolsDto migrationImportToolsDto = new MigrationImportToolsDto();
        migrationImportToolsDto.setFileNames(Arrays.asList("value"));
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto.setId(0L);
        auditEverybodyQueryDto.setAuditorId(0L);
        auditEverybodyQueryDto.setAuditorName("auditorName");
        migrationImportToolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));

        final UserDto user = new UserDto();
        user.setUserName("creatorName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        when(mockBusinessConfig.getToolsEvn()).thenReturn("result");

        // Configure IToolsInfoService.selectToolsByCode(...).
        final ToolsInfoEntity toolsInfoEntity = new ToolsInfoEntity();
        toolsInfoEntity.setId(0L);
        toolsInfoEntity.setCode("code");
        toolsInfoEntity.setStatus(0);
        toolsInfoEntity.setCreatorId(0L);
        toolsInfoEntity.setCreatorName("creatorName");
        toolsInfoEntity.setDelFlag(0);
        toolsInfoEntity.setReturnReason("auditReturnReason");
        when(mockToolsInfoService.selectToolsByCode("code")).thenReturn(toolsInfoEntity);

        // Configure SystemDataInteract.queryBusinessSystemBySysName(...).
        final SystemPullDto systemPullDto = new SystemPullDto();
        systemPullDto.setBusinessSystemId(0L);
        systemPullDto.setBusinessSystemCode("businessSystemCode");
        systemPullDto.setBusinessSystemName("businessSystemName");
        when(mockSystemDataInteract.queryBusinessSystemBySysName("businessSystemName")).thenReturn(systemPullDto);

        // Configure ITbCategoryService.selectCategoryExist(...).
        final CategoryResultDto categoryResultDto = new CategoryResultDto();
        categoryResultDto.setId(0L);
        categoryResultDto.setParentId(0L);
        categoryResultDto.setClassname("classname");
        categoryResultDto.setDescription("description");
        categoryResultDto.setLevel(0);
        final List<CategoryResultDto> categoryResultDtos = Arrays.asList(categoryResultDto);
        when(mockCategoryService.selectCategoryExist(any(CategoryEntity.class))).thenReturn(categoryResultDtos);

        // Configure ScriptInteract.importScriptProduction(...).
        final ScriptFileInfo scriptFileInfo = new ScriptFileInfo();
        scriptFileInfo.setFileName("fileName");
        scriptFileInfo.setFileSuffix("fileSuffix");
        scriptFileInfo.setFileContentByte("content".getBytes());
        when(mockScriptInteract.importScriptProduction(any(ScriptFileInfo.class))).thenReturn(scriptFileInfo);

        // Configure ScriptInteract.getDefaultScriptInfo(...).
        final ScriptContentDto scriptContentDto = new ScriptContentDto();
        scriptContentDto.setScriptIds("scriptIds");
        scriptContentDto.setScriptName("scriptName");
        scriptContentDto.setScriptType(0);
        scriptContentDto.setScriptEditing("scriptEditing");
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        scriptContentDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        when(mockScriptInteract.getDefaultScriptInfo(any(UserDto.class), eq("scriptIds"))).thenReturn(scriptContentDto);

        when(mockToolsOperateService.saveCommitToolsInfo(null,any(ToolsDto.class), any(UserDto.class),2))
                .thenThrow(ToolsException.class);

        // Run the test
        assertThatThrownBy(() -> toolsMoveServiceImplUnderTest.publishMigrationImportTools(migrationImportToolsDto,
                user)).isInstanceOf(ToolsException.class);
        verify(mockCategoryService).insertTreeCategory(any(CategoryDto.class), any(UserDto.class));
    }

    @Test
    void testPublishMigrationImportTools_IToolsOperateServiceSaveCommitToolsInfoThrowsAuditException() throws Exception {
        // Setup
        final MigrationImportToolsDto migrationImportToolsDto = new MigrationImportToolsDto();
        migrationImportToolsDto.setFileNames(Arrays.asList("value"));
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto.setId(0L);
        auditEverybodyQueryDto.setAuditorId(0L);
        auditEverybodyQueryDto.setAuditorName("auditorName");
        migrationImportToolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));

        final UserDto user = new UserDto();
        user.setUserName("creatorName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        when(mockBusinessConfig.getToolsEvn()).thenReturn("result");

        // Configure IToolsInfoService.selectToolsByCode(...).
        final ToolsInfoEntity toolsInfoEntity = new ToolsInfoEntity();
        toolsInfoEntity.setId(0L);
        toolsInfoEntity.setCode("code");
        toolsInfoEntity.setStatus(0);
        toolsInfoEntity.setCreatorId(0L);
        toolsInfoEntity.setCreatorName("creatorName");
        toolsInfoEntity.setDelFlag(0);
        toolsInfoEntity.setReturnReason("auditReturnReason");
        when(mockToolsInfoService.selectToolsByCode("code")).thenReturn(toolsInfoEntity);

        // Configure SystemDataInteract.queryBusinessSystemBySysName(...).
        final SystemPullDto systemPullDto = new SystemPullDto();
        systemPullDto.setBusinessSystemId(0L);
        systemPullDto.setBusinessSystemCode("businessSystemCode");
        systemPullDto.setBusinessSystemName("businessSystemName");
        when(mockSystemDataInteract.queryBusinessSystemBySysName("businessSystemName")).thenReturn(systemPullDto);

        // Configure ITbCategoryService.selectCategoryExist(...).
        final CategoryResultDto categoryResultDto = new CategoryResultDto();
        categoryResultDto.setId(0L);
        categoryResultDto.setParentId(0L);
        categoryResultDto.setClassname("classname");
        categoryResultDto.setDescription("description");
        categoryResultDto.setLevel(0);
        final List<CategoryResultDto> categoryResultDtos = Arrays.asList(categoryResultDto);
        when(mockCategoryService.selectCategoryExist(any(CategoryEntity.class))).thenReturn(categoryResultDtos);

        // Configure ScriptInteract.importScriptProduction(...).
        final ScriptFileInfo scriptFileInfo = new ScriptFileInfo();
        scriptFileInfo.setFileName("fileName");
        scriptFileInfo.setFileSuffix("fileSuffix");
        scriptFileInfo.setFileContentByte("content".getBytes());
        when(mockScriptInteract.importScriptProduction(any(ScriptFileInfo.class))).thenReturn(scriptFileInfo);

        // Configure ScriptInteract.getDefaultScriptInfo(...).
        final ScriptContentDto scriptContentDto = new ScriptContentDto();
        scriptContentDto.setScriptIds("scriptIds");
        scriptContentDto.setScriptName("scriptName");
        scriptContentDto.setScriptType(0);
        scriptContentDto.setScriptEditing("scriptEditing");
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        scriptContentDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        when(mockScriptInteract.getDefaultScriptInfo(any(UserDto.class), eq("scriptIds"))).thenReturn(scriptContentDto);

        when(mockToolsOperateService.saveCommitToolsInfo(null,any(ToolsDto.class), any(UserDto.class),2))
                .thenThrow(AuditException.class);

        // Run the test
        assertThatThrownBy(() -> toolsMoveServiceImplUnderTest.publishMigrationImportTools(migrationImportToolsDto,
                user)).isInstanceOf(ToolsException.class);
        verify(mockCategoryService).insertTreeCategory(any(CategoryDto.class), any(UserDto.class));
    }

    @Test
    void testPublishMigrationImportTools_IToolsOperateServiceSaveCommitToolsInfoThrowsScriptToolsException() throws Exception {
        // Setup
        final MigrationImportToolsDto migrationImportToolsDto = new MigrationImportToolsDto();
        migrationImportToolsDto.setFileNames(Arrays.asList("value"));
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto.setId(0L);
        auditEverybodyQueryDto.setAuditorId(0L);
        auditEverybodyQueryDto.setAuditorName("auditorName");
        migrationImportToolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));

        final UserDto user = new UserDto();
        user.setUserName("creatorName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        when(mockBusinessConfig.getToolsEvn()).thenReturn("result");

        // Configure IToolsInfoService.selectToolsByCode(...).
        final ToolsInfoEntity toolsInfoEntity = new ToolsInfoEntity();
        toolsInfoEntity.setId(0L);
        toolsInfoEntity.setCode("code");
        toolsInfoEntity.setStatus(0);
        toolsInfoEntity.setCreatorId(0L);
        toolsInfoEntity.setCreatorName("creatorName");
        toolsInfoEntity.setDelFlag(0);
        toolsInfoEntity.setReturnReason("auditReturnReason");
        when(mockToolsInfoService.selectToolsByCode("code")).thenReturn(toolsInfoEntity);

        // Configure SystemDataInteract.queryBusinessSystemBySysName(...).
        final SystemPullDto systemPullDto = new SystemPullDto();
        systemPullDto.setBusinessSystemId(0L);
        systemPullDto.setBusinessSystemCode("businessSystemCode");
        systemPullDto.setBusinessSystemName("businessSystemName");
        when(mockSystemDataInteract.queryBusinessSystemBySysName("businessSystemName")).thenReturn(systemPullDto);

        // Configure ITbCategoryService.selectCategoryExist(...).
        final CategoryResultDto categoryResultDto = new CategoryResultDto();
        categoryResultDto.setId(0L);
        categoryResultDto.setParentId(0L);
        categoryResultDto.setClassname("classname");
        categoryResultDto.setDescription("description");
        categoryResultDto.setLevel(0);
        final List<CategoryResultDto> categoryResultDtos = Arrays.asList(categoryResultDto);
        when(mockCategoryService.selectCategoryExist(any(CategoryEntity.class))).thenReturn(categoryResultDtos);

        // Configure ScriptInteract.importScriptProduction(...).
        final ScriptFileInfo scriptFileInfo = new ScriptFileInfo();
        scriptFileInfo.setFileName("fileName");
        scriptFileInfo.setFileSuffix("fileSuffix");
        scriptFileInfo.setFileContentByte("content".getBytes());
        when(mockScriptInteract.importScriptProduction(any(ScriptFileInfo.class))).thenReturn(scriptFileInfo);

        // Configure ScriptInteract.getDefaultScriptInfo(...).
        final ScriptContentDto scriptContentDto = new ScriptContentDto();
        scriptContentDto.setScriptIds("scriptIds");
        scriptContentDto.setScriptName("scriptName");
        scriptContentDto.setScriptType(0);
        scriptContentDto.setScriptEditing("scriptEditing");
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        scriptContentDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        when(mockScriptInteract.getDefaultScriptInfo(any(UserDto.class), eq("scriptIds"))).thenReturn(scriptContentDto);

        when(mockToolsOperateService.saveCommitToolsInfo(null,any(ToolsDto.class), any(UserDto.class),2))
                .thenThrow(ScriptToolsException.class);

        // Run the test
        assertThatThrownBy(() -> toolsMoveServiceImplUnderTest.publishMigrationImportTools(migrationImportToolsDto,
                user)).isInstanceOf(ToolsException.class);
        verify(mockCategoryService).insertTreeCategory(any(CategoryDto.class), any(UserDto.class));
    }

    @Test
    void testPublishMigrationImportTools_IToolsOperateServiceSaveCommitToolsInfoThrowsStudioException() throws Exception {
        // Setup
        final MigrationImportToolsDto migrationImportToolsDto = new MigrationImportToolsDto();
        migrationImportToolsDto.setFileNames(Arrays.asList("value"));
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto.setId(0L);
        auditEverybodyQueryDto.setAuditorId(0L);
        auditEverybodyQueryDto.setAuditorName("auditorName");
        migrationImportToolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));

        final UserDto user = new UserDto();
        user.setUserName("creatorName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        when(mockBusinessConfig.getToolsEvn()).thenReturn("result");

        // Configure IToolsInfoService.selectToolsByCode(...).
        final ToolsInfoEntity toolsInfoEntity = new ToolsInfoEntity();
        toolsInfoEntity.setId(0L);
        toolsInfoEntity.setCode("code");
        toolsInfoEntity.setStatus(0);
        toolsInfoEntity.setCreatorId(0L);
        toolsInfoEntity.setCreatorName("creatorName");
        toolsInfoEntity.setDelFlag(0);
        toolsInfoEntity.setReturnReason("auditReturnReason");
        when(mockToolsInfoService.selectToolsByCode("code")).thenReturn(toolsInfoEntity);

        // Configure SystemDataInteract.queryBusinessSystemBySysName(...).
        final SystemPullDto systemPullDto = new SystemPullDto();
        systemPullDto.setBusinessSystemId(0L);
        systemPullDto.setBusinessSystemCode("businessSystemCode");
        systemPullDto.setBusinessSystemName("businessSystemName");
        when(mockSystemDataInteract.queryBusinessSystemBySysName("businessSystemName")).thenReturn(systemPullDto);

        // Configure ITbCategoryService.selectCategoryExist(...).
        final CategoryResultDto categoryResultDto = new CategoryResultDto();
        categoryResultDto.setId(0L);
        categoryResultDto.setParentId(0L);
        categoryResultDto.setClassname("classname");
        categoryResultDto.setDescription("description");
        categoryResultDto.setLevel(0);
        final List<CategoryResultDto> categoryResultDtos = Arrays.asList(categoryResultDto);
        when(mockCategoryService.selectCategoryExist(any(CategoryEntity.class))).thenReturn(categoryResultDtos);

        // Configure ScriptInteract.importScriptProduction(...).
        final ScriptFileInfo scriptFileInfo = new ScriptFileInfo();
        scriptFileInfo.setFileName("fileName");
        scriptFileInfo.setFileSuffix("fileSuffix");
        scriptFileInfo.setFileContentByte("content".getBytes());
        when(mockScriptInteract.importScriptProduction(any(ScriptFileInfo.class))).thenReturn(scriptFileInfo);

        // Configure ScriptInteract.getDefaultScriptInfo(...).
        final ScriptContentDto scriptContentDto = new ScriptContentDto();
        scriptContentDto.setScriptIds("scriptIds");
        scriptContentDto.setScriptName("scriptName");
        scriptContentDto.setScriptType(0);
        scriptContentDto.setScriptEditing("scriptEditing");
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        scriptContentDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        when(mockScriptInteract.getDefaultScriptInfo(any(UserDto.class), eq("scriptIds"))).thenReturn(scriptContentDto);

        when(mockToolsOperateService.saveCommitToolsInfo(null,any(ToolsDto.class), any(UserDto.class),2))
                .thenThrow(StudioException.class);

        // Run the test
        assertThatThrownBy(() -> toolsMoveServiceImplUnderTest.publishMigrationImportTools(migrationImportToolsDto,
                user)).isInstanceOf(ToolsException.class);
        verify(mockCategoryService).insertTreeCategory(any(CategoryDto.class), any(UserDto.class));
    }

    @Test
    void testPublishMigrationImportTools_StudioInteractThrowsStudioException() throws Exception {
        // Setup
        final MigrationImportToolsDto migrationImportToolsDto = new MigrationImportToolsDto();
        migrationImportToolsDto.setFileNames(Arrays.asList("value"));
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto.setId(0L);
        auditEverybodyQueryDto.setAuditorId(0L);
        auditEverybodyQueryDto.setAuditorName("auditorName");
        migrationImportToolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));

        final UserDto user = new UserDto();
        user.setUserName("creatorName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        when(mockBusinessConfig.getToolsEvn()).thenReturn("result");

        // Configure IToolsInfoService.selectToolsByCode(...).
        final ToolsInfoEntity toolsInfoEntity = new ToolsInfoEntity();
        toolsInfoEntity.setId(0L);
        toolsInfoEntity.setCode("code");
        toolsInfoEntity.setStatus(0);
        toolsInfoEntity.setCreatorId(0L);
        toolsInfoEntity.setCreatorName("creatorName");
        toolsInfoEntity.setDelFlag(0);
        toolsInfoEntity.setReturnReason("auditReturnReason");
        when(mockToolsInfoService.selectToolsByCode("code")).thenReturn(toolsInfoEntity);

        // Configure SystemDataInteract.queryBusinessSystemBySysName(...).
        final SystemPullDto systemPullDto = new SystemPullDto();
        systemPullDto.setBusinessSystemId(0L);
        systemPullDto.setBusinessSystemCode("businessSystemCode");
        systemPullDto.setBusinessSystemName("businessSystemName");
        when(mockSystemDataInteract.queryBusinessSystemBySysName("businessSystemName")).thenReturn(systemPullDto);

        when(mockStudioInteract.importStudioData(any(StudioFileProjectInteractDto.class),
                any(UserDto.class))).thenThrow(StudioException.class);

        // Run the test
        assertThatThrownBy(() -> toolsMoveServiceImplUnderTest.publishMigrationImportTools(migrationImportToolsDto,
                user)).isInstanceOf(ToolsException.class);
    }

    @Test
    void testConvertToolJsonFile() throws Exception {
        // Setup
        final JSONObject jSONObject = new JSONObject(0, 0.0f, false);
        final File expectedResult = new File("filename.txt");

        // Run the test
        final File result = toolsMoveServiceImplUnderTest.convertToolJsonFile("fileName", jSONObject);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testConvertToolJsonFile_ThrowsIOException() {
        // Setup
        final JSONObject jSONObject = new JSONObject(0, 0.0f, false);

        // Run the test
        assertThatThrownBy(
                () -> toolsMoveServiceImplUnderTest.convertToolJsonFile("fileName", jSONObject))
                .isInstanceOf(IOException.class);
    }

    @Test
    void testSyncToolsStatus() {
        // Setup
        when(mockBusinessConfig.getToolsEvn()).thenReturn("toolsEvn");
        when(mockBusinessConfig.getFtpType()).thenReturn("result");
        when(mockBusinessConfig.getFtpUser()).thenReturn("result");
        when(mockBusinessConfig.getFtpPw()).thenReturn("result");
        when(mockBusinessConfig.getFtpIp()).thenReturn("result");
        when(mockBusinessConfig.getFtpPort()).thenReturn("result");
        when(mockBusinessConfig.getSftpDataPath()).thenReturn("result");

        // Configure IToolsInfoService.selectToolsByCode(...).
        final ToolsInfoEntity toolsInfoEntity = new ToolsInfoEntity();
        toolsInfoEntity.setId(0L);
        toolsInfoEntity.setCode("code");
        toolsInfoEntity.setStatus(0);
        toolsInfoEntity.setCreatorId(0L);
        toolsInfoEntity.setCreatorName("creatorName");
        toolsInfoEntity.setDelFlag(0);
        toolsInfoEntity.setReturnReason("auditReturnReason");
        when(mockToolsInfoService.selectToolsByCode("code")).thenReturn(toolsInfoEntity);

        when(mockToolsInfoService.updateToolsInfo(any(ToolsInfoDto.class))).thenReturn(0);

        // Run the test
        final R result = toolsMoveServiceImplUnderTest.syncToolsStatus();

        // Verify the results
    }

    @Test
    void testSyncStatusFiles() {
        // Setup
        final List<File> importFile = Arrays.asList(new File("filename.txt"));

        // Configure IToolsInfoService.selectToolsByCode(...).
        final ToolsInfoEntity toolsInfoEntity = new ToolsInfoEntity();
        toolsInfoEntity.setId(0L);
        toolsInfoEntity.setCode("code");
        toolsInfoEntity.setStatus(0);
        toolsInfoEntity.setCreatorId(0L);
        toolsInfoEntity.setCreatorName("creatorName");
        toolsInfoEntity.setDelFlag(0);
        toolsInfoEntity.setReturnReason("auditReturnReason");
        when(mockToolsInfoService.selectToolsByCode("code")).thenReturn(toolsInfoEntity);

        when(mockToolsInfoService.updateToolsInfo(any(ToolsInfoDto.class))).thenReturn(0);
        when(mockBusinessConfig.getToolsEvn()).thenReturn("result");
        when(mockBusinessConfig.getFtpPort()).thenReturn("result");
        when(mockBusinessConfig.getFtpUser()).thenReturn("sftpUser");
        when(mockBusinessConfig.getFtpPw()).thenReturn("password");
        when(mockBusinessConfig.getFtpIp()).thenReturn("hostname");

        // Run the test
        final R result = toolsMoveServiceImplUnderTest.syncStatusFiles("sftpPath", importFile, Arrays.asList("value"));

        // Verify the results
    }

    @Test
    void testGetMoveFileFromServer() throws Exception {
        // Setup
        final MoveFileQueryDto moveFileQueryDto = new MoveFileQueryDto();
        moveFileQueryDto.setCode("code");
        moveFileQueryDto.setName("name");

        when(mockBusinessConfig.getToolsEvn()).thenReturn("result");
        when(mockBusinessConfig.getFtpType()).thenReturn("result");
        when(mockBusinessConfig.getFtpUser()).thenReturn("result");
        when(mockBusinessConfig.getFtpPw()).thenReturn("result");
        when(mockBusinessConfig.getFtpIp()).thenReturn("result");
        when(mockBusinessConfig.getFtpPort()).thenReturn("result");
        when(mockBusinessConfig.getSftpDataPath()).thenReturn("result");

        // Run the test
        final List<MoveFileResultDto> result = toolsMoveServiceImplUnderTest.getMoveFileFromServer(moveFileQueryDto);

        // Verify the results
    }
*/

    @Test
    void testGetEnvironment() throws Exception {
        // Setup
        when(mockBusinessConfig.getToolsEvn()).thenReturn("result");

        // Run the test
        final R result = toolsMoveServiceImplUnderTest.getEnvironment();

        // Verify the results
    }

/*    @Test
    void testCheckMoveToolsStatus() throws Exception {
        // Setup
        final MoveToolsReturnDto moveToolsReturnDto = new MoveToolsReturnDto();
        moveToolsReturnDto.setCode("code");
        moveToolsReturnDto.setFromType(0);
        moveToolsReturnDto.setFileName("fileName");
        moveToolsReturnDto.setReturnReason("auditReturnReason");

        final UserDto user = new UserDto();
        user.setUserName("creatorName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        // Configure IToolsInfoService.selectToolsByCode(...).
        final ToolsInfoEntity toolsInfoEntity = new ToolsInfoEntity();
        toolsInfoEntity.setId(0L);
        toolsInfoEntity.setCode("code");
        toolsInfoEntity.setStatus(0);
        toolsInfoEntity.setCreatorId(0L);
        toolsInfoEntity.setCreatorName("creatorName");
        toolsInfoEntity.setDelFlag(0);
        toolsInfoEntity.setReturnReason("auditReturnReason");
        when(mockToolsInfoService.selectToolsByCode("code")).thenReturn(toolsInfoEntity);

        // Configure IAuditService.selectAuditList(...).
        final AuditDto auditDto = new AuditDto();
        auditDto.setId(0L);
        auditDto.setBusinessId(0L);
        auditDto.setApplyName("applyName");
        auditDto.setApprovalState(0);
        auditDto.setAuditReturnReason("auditReturnReason");
        final List<AuditDto> auditDtos = Arrays.asList(auditDto);
        when(mockAuditService.selectAuditList(any(AuditQueryDto.class))).thenReturn(auditDtos);

        when(mockBusinessConfig.getToolsEvn()).thenReturn("result");

        // Run the test
        final R<Object> result = toolsMoveServiceImplUnderTest.checkMoveToolsStatus(moveToolsReturnDto, user);

        // Verify the results
    }

    @Test
    void testCheckMoveToolsStatus_IAuditServiceReturnsNoItems() throws Exception {
        // Setup
        final MoveToolsReturnDto moveToolsReturnDto = new MoveToolsReturnDto();
        moveToolsReturnDto.setCode("code");
        moveToolsReturnDto.setFromType(0);
        moveToolsReturnDto.setFileName("fileName");
        moveToolsReturnDto.setReturnReason("auditReturnReason");

        final UserDto user = new UserDto();
        user.setUserName("creatorName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        // Configure IToolsInfoService.selectToolsByCode(...).
        final ToolsInfoEntity toolsInfoEntity = new ToolsInfoEntity();
        toolsInfoEntity.setId(0L);
        toolsInfoEntity.setCode("code");
        toolsInfoEntity.setStatus(0);
        toolsInfoEntity.setCreatorId(0L);
        toolsInfoEntity.setCreatorName("creatorName");
        toolsInfoEntity.setDelFlag(0);
        toolsInfoEntity.setReturnReason("auditReturnReason");
        when(mockToolsInfoService.selectToolsByCode("code")).thenReturn(toolsInfoEntity);

        when(mockAuditService.selectAuditList(any(AuditQueryDto.class))).thenReturn(Collections.emptyList());

        // Run the test
        final R<Object> result = toolsMoveServiceImplUnderTest.checkMoveToolsStatus(moveToolsReturnDto, user);

        // Verify the results
    }

    @Test
    void testMoveToolsReturn() throws Exception {
        // Setup
        final MoveToolsReturnDto moveToolsReturnDto = new MoveToolsReturnDto();
        moveToolsReturnDto.setCode("code");
        moveToolsReturnDto.setFromType(0);
        moveToolsReturnDto.setFileName("fileName");
        moveToolsReturnDto.setReturnReason("auditReturnReason");

        final UserDto user = new UserDto();
        user.setUserName("creatorName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        when(mockBusinessConfig.getToolsEvn()).thenReturn("result");

        // Configure IToolsInfoService.selectToolsByCode(...).
        final ToolsInfoEntity toolsInfoEntity = new ToolsInfoEntity();
        toolsInfoEntity.setId(0L);
        toolsInfoEntity.setCode("code");
        toolsInfoEntity.setStatus(0);
        toolsInfoEntity.setCreatorId(0L);
        toolsInfoEntity.setCreatorName("creatorName");
        toolsInfoEntity.setDelFlag(0);
        toolsInfoEntity.setReturnReason("auditReturnReason");
        when(mockToolsInfoService.selectToolsByCode("code")).thenReturn(toolsInfoEntity);

        when(mockBusinessConfig.getSftpDataPath()).thenReturn("result");
        when(mockBusinessConfig.getFtpPort()).thenReturn("result");
        when(mockBusinessConfig.getFtpIp()).thenReturn("hostname");
        when(mockBusinessConfig.getFtpUser()).thenReturn("sftpUser");
        when(mockBusinessConfig.getFtpPw()).thenReturn("password");

        // Configure IAuditService.selectAuditList(...).
        final AuditDto auditDto = new AuditDto();
        auditDto.setId(0L);
        auditDto.setBusinessId(0L);
        auditDto.setApplyName("applyName");
        auditDto.setApprovalState(0);
        auditDto.setAuditReturnReason("auditReturnReason");
        final List<AuditDto> auditDtos = Arrays.asList(auditDto);
        when(mockAuditService.selectAuditList(any(AuditQueryDto.class))).thenReturn(auditDtos);

        when(mockAuditService.withDrawDoubleCheck(any(AuditDto.class))).thenReturn(0L);

        // Configure IToolsInfoService.getToolsCombinedInfo(...).
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setCode("code");
        toolsDto.setName("name");
        toolsDto.setType(0);
        toolsDto.setBusinessSystemId(0L);
        toolsDto.setBusinessSystemName("businessSystemName");
        toolsDto.setOneTypeId(0L);
        toolsDto.setTwoTypeId(0L);
        toolsDto.setHighRisk(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto.setAuditorId(0L);
        auditEverybodyQueryDto.setAuditorName("auditorName");
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setOperateStatus(0);
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        toolsDto.setScriptType(0);
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        toolsDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        toolsDto.setScriptEditing("scriptEditing");
        toolsDto.setStatus(0);
        toolsDto.setOneTypeName("OneTypeName");
        toolsDto.setTwoTypeName("OneTypeName");
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setProjectId(0L);
        toolsProjectInfoDto.setProjectVersionId(0L);
        toolsProjectInfoDto.setProjectName("projectName");
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowName("workflowName");
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setUpdateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        toolsDto.setCreatorId(0L);
        toolsDto.setCreatorName("creatorName");
        toolsDto.setCreateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        when(mockToolsInfoService.getToolsCombinedInfo(0L)).thenReturn(toolsDto);

        when(mockToolsInfoService.updateToolsInfo(any(ToolsInfoDto.class))).thenReturn(0);

        // Run the test
        final R<Object> result = toolsMoveServiceImplUnderTest.moveToolsReturn(moveToolsReturnDto, user);

        // Verify the results
        verify(mockLogAuditService).logOperationAudit(eq(0L), eq(OperatModuleEnum.TOOL_EDIT),
                eq(OperatTypeEnum.TOOL_DOWN_LINE), eq(0), any(UserDto.class), eq("迁移退回结果"));
    }

    @Test
    void testMoveToolsReturn_IAuditServiceSelectAuditListReturnsNoItems() throws Exception {
        // Setup
        final MoveToolsReturnDto moveToolsReturnDto = new MoveToolsReturnDto();
        moveToolsReturnDto.setCode("code");
        moveToolsReturnDto.setFromType(0);
        moveToolsReturnDto.setFileName("fileName");
        moveToolsReturnDto.setReturnReason("auditReturnReason");

        final UserDto user = new UserDto();
        user.setUserName("creatorName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        when(mockBusinessConfig.getToolsEvn()).thenReturn("result");

        // Configure IToolsInfoService.selectToolsByCode(...).
        final ToolsInfoEntity toolsInfoEntity = new ToolsInfoEntity();
        toolsInfoEntity.setId(0L);
        toolsInfoEntity.setCode("code");
        toolsInfoEntity.setStatus(0);
        toolsInfoEntity.setCreatorId(0L);
        toolsInfoEntity.setCreatorName("creatorName");
        toolsInfoEntity.setDelFlag(0);
        toolsInfoEntity.setReturnReason("auditReturnReason");
        when(mockToolsInfoService.selectToolsByCode("code")).thenReturn(toolsInfoEntity);

        when(mockBusinessConfig.getSftpDataPath()).thenReturn("result");
        when(mockBusinessConfig.getFtpPort()).thenReturn("result");
        when(mockBusinessConfig.getFtpIp()).thenReturn("hostname");
        when(mockBusinessConfig.getFtpUser()).thenReturn("sftpUser");
        when(mockBusinessConfig.getFtpPw()).thenReturn("password");
        when(mockAuditService.selectAuditList(any(AuditQueryDto.class))).thenReturn(Collections.emptyList());
        when(mockAuditService.withDrawDoubleCheck(any(AuditDto.class))).thenReturn(0L);

        // Run the test
        final R<Object> result = toolsMoveServiceImplUnderTest.moveToolsReturn(moveToolsReturnDto, user);

        // Verify the results
    }

    @Test
    void testMoveToolsReturn_IAuditServiceWithDrawDoubleCheckThrowsAuditException() throws Exception {
        // Setup
        final MoveToolsReturnDto moveToolsReturnDto = new MoveToolsReturnDto();
        moveToolsReturnDto.setCode("code");
        moveToolsReturnDto.setFromType(0);
        moveToolsReturnDto.setFileName("fileName");
        moveToolsReturnDto.setReturnReason("auditReturnReason");

        final UserDto user = new UserDto();
        user.setUserName("creatorName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        when(mockBusinessConfig.getToolsEvn()).thenReturn("result");

        // Configure IToolsInfoService.selectToolsByCode(...).
        final ToolsInfoEntity toolsInfoEntity = new ToolsInfoEntity();
        toolsInfoEntity.setId(0L);
        toolsInfoEntity.setCode("code");
        toolsInfoEntity.setStatus(0);
        toolsInfoEntity.setCreatorId(0L);
        toolsInfoEntity.setCreatorName("creatorName");
        toolsInfoEntity.setDelFlag(0);
        toolsInfoEntity.setReturnReason("auditReturnReason");
        when(mockToolsInfoService.selectToolsByCode("code")).thenReturn(toolsInfoEntity);

        when(mockBusinessConfig.getSftpDataPath()).thenReturn("result");
        when(mockBusinessConfig.getFtpPort()).thenReturn("result");
        when(mockBusinessConfig.getFtpIp()).thenReturn("hostname");
        when(mockBusinessConfig.getFtpUser()).thenReturn("sftpUser");
        when(mockBusinessConfig.getFtpPw()).thenReturn("password");

        // Configure IAuditService.selectAuditList(...).
        final AuditDto auditDto = new AuditDto();
        auditDto.setId(0L);
        auditDto.setBusinessId(0L);
        auditDto.setApplyName("applyName");
        auditDto.setApprovalState(0);
        auditDto.setAuditReturnReason("auditReturnReason");
        final List<AuditDto> auditDtos = Arrays.asList(auditDto);
        when(mockAuditService.selectAuditList(any(AuditQueryDto.class))).thenReturn(auditDtos);

        when(mockAuditService.withDrawDoubleCheck(any(AuditDto.class))).thenThrow(AuditException.class);

        // Run the test
        assertThatThrownBy(() -> toolsMoveServiceImplUnderTest.moveToolsReturn(moveToolsReturnDto, user))
                .isInstanceOf(ToolsException.class);
    }*/
}

package com.ideal.tools.service.impl;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import java.util.Calendar;
import java.util.GregorianCalendar;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.ideal.tools.model.dto.AuditDto;
import com.ideal.tools.model.interaction.DoubleCheckInteractDto;
import com.ideal.tools.service.IAuditService;
import com.ideal.tools.service.IStrategyService;
import com.ideal.tools.service.IToolsOperateService;

@ExtendWith(MockitoExtension.class)
class AuditOperationalServiceImplTest {

    @Mock
    private IAuditService mockAuditService;
    @Mock
    private IToolsOperateService mockToolsOperateService;
    @Mock
    private IStrategyService mockStrategyService;

    private AuditOperationalServiceImpl auditOperationalServiceImplUnderTest;

    @BeforeEach
    void setUp() throws Exception {
        auditOperationalServiceImplUnderTest = new AuditOperationalServiceImpl(mockAuditService,
                mockToolsOperateService, mockStrategyService);
    }

    @Test
    void testUpdateAuditStateStart() throws Exception {
        // Setup
        final DoubleCheckInteractDto doubleCheckInteractDto = new DoubleCheckInteractDto();
        doubleCheckInteractDto.setId(0L);
        doubleCheckInteractDto.setServiceId(0L);
        doubleCheckInteractDto.setTaskSubject("taskSubject");
        doubleCheckInteractDto.setApprovalComment("approvalComment");
        doubleCheckInteractDto.setApprovalState(0);
        when(mockAuditService.updateAuditMultipleState(any(AuditDto.class))).thenReturn(0);
        // Configure IAuditService.selectAuditById(...).
        final AuditDto auditDto = new AuditDto();
        auditDto.setId(0L);
        auditDto.setBusinessId(0L);
        auditDto.setAuditTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        auditDto.setType(0);
        auditDto.setApprovalState(0);
//        when(mockAuditService.selectAuditById(0L)).thenReturn(auditDto);
        // Run the test
        final boolean result = auditOperationalServiceImplUnderTest.updateAuditStateStart(doubleCheckInteractDto);
        // Verify the results
        assertThat(result).isTrue();
    }

    @Test
    void testUpdateAuditStateStart1() throws Exception {
        // Setup
        final DoubleCheckInteractDto doubleCheckInteractDto = new DoubleCheckInteractDto();
        doubleCheckInteractDto.setId(0L);
        doubleCheckInteractDto.setServiceId(0L);
        doubleCheckInteractDto.setTaskSubject("taskSubject");
        doubleCheckInteractDto.setApprovalComment("approvalComment");
        doubleCheckInteractDto.setApprovalState(0);
        when(mockAuditService.updateAuditMultipleState(any(AuditDto.class))).thenReturn(1);
        // Configure IAuditService.selectAuditById(...).
        final AuditDto auditDto = new AuditDto();
        auditDto.setId(0L);
        auditDto.setBusinessId(0L);
        auditDto.setAuditTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        auditDto.setType(1);
        auditDto.setApprovalState(0);
        when(mockAuditService.selectAuditById(0L)).thenReturn(auditDto);
        // Run the test
        final boolean result = auditOperationalServiceImplUnderTest.updateAuditStateStart(doubleCheckInteractDto);
        // Verify the results
        assertThat(result).isTrue();
    }
    
    @Test
    void testUpdateAuditStateStart2() throws Exception {
        // Setup
        final DoubleCheckInteractDto doubleCheckInteractDto = new DoubleCheckInteractDto();
        doubleCheckInteractDto.setId(0L);
        doubleCheckInteractDto.setServiceId(0L);
        doubleCheckInteractDto.setTaskSubject("taskSubject");
        doubleCheckInteractDto.setApprovalComment("approvalComment");
        doubleCheckInteractDto.setApprovalState(0);
        when(mockAuditService.updateAuditMultipleState(any(AuditDto.class))).thenReturn(1);
        // Configure IAuditService.selectAuditById(...).
        final AuditDto auditDto = new AuditDto();
        auditDto.setId(0L);
        auditDto.setBusinessId(0L);
        auditDto.setAuditTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        auditDto.setType(2);
        auditDto.setApprovalState(0);
        when(mockAuditService.selectAuditById(0L)).thenReturn(auditDto);
        // Run the test
        final boolean result = auditOperationalServiceImplUnderTest.updateAuditStateStart(doubleCheckInteractDto);
        // Verify the results
        assertThat(result).isTrue();
    }
    
    @Test
    void testUpdateAuditStateStart3() throws Exception {
        // Setup
        final DoubleCheckInteractDto doubleCheckInteractDto = new DoubleCheckInteractDto();
        doubleCheckInteractDto.setId(0L);
        doubleCheckInteractDto.setServiceId(0L);
        doubleCheckInteractDto.setTaskSubject("taskSubject");
        doubleCheckInteractDto.setApprovalComment("approvalComment");
        doubleCheckInteractDto.setApprovalState(0);
        when(mockAuditService.updateAuditMultipleState(any(AuditDto.class))).thenReturn(1);
        // Configure IAuditService.selectAuditById(...).
        final AuditDto auditDto = new AuditDto();
        auditDto.setId(0L);
        auditDto.setBusinessId(0L);
        auditDto.setAuditTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        auditDto.setType(3);
        auditDto.setApprovalState(0);
        when(mockAuditService.selectAuditById(0L)).thenReturn(auditDto);
        // Run the test
        final boolean result = auditOperationalServiceImplUnderTest.updateAuditStateStart(doubleCheckInteractDto);
        // Verify the results
        assertThat(result).isTrue();
    }
    
    @Test
    void testUpdateAuditStateStart4() throws Exception {
        // Setup
        final DoubleCheckInteractDto doubleCheckInteractDto = new DoubleCheckInteractDto();
        doubleCheckInteractDto.setId(0L);
        doubleCheckInteractDto.setServiceId(0L);
        doubleCheckInteractDto.setTaskSubject("taskSubject");
        doubleCheckInteractDto.setApprovalComment("approvalComment");
        doubleCheckInteractDto.setApprovalState(0);
        when(mockAuditService.updateAuditMultipleState(any(AuditDto.class))).thenReturn(1);
        // Configure IAuditService.selectAuditById(...).
        final AuditDto auditDto = new AuditDto();
        auditDto.setId(0L);
        auditDto.setBusinessId(0L);
        auditDto.setAuditTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        auditDto.setType(4);
        auditDto.setApprovalState(0);
        when(mockAuditService.selectAuditById(0L)).thenReturn(auditDto);
        // Run the test
        final boolean result = auditOperationalServiceImplUnderTest.updateAuditStateStart(doubleCheckInteractDto);
        // Verify the results
        assertThat(result).isTrue();
    }
    
    @Test
    void testUpdateAuditStateStart5() throws Exception {
        // Setup
        final DoubleCheckInteractDto doubleCheckInteractDto = new DoubleCheckInteractDto();
        doubleCheckInteractDto.setId(0L);
        doubleCheckInteractDto.setServiceId(0L);
        doubleCheckInteractDto.setTaskSubject("taskSubject");
        doubleCheckInteractDto.setApprovalComment("approvalComment");
        doubleCheckInteractDto.setApprovalState(0);
        when(mockAuditService.updateAuditMultipleState(any(AuditDto.class))).thenReturn(0);
        // Configure IAuditService.selectAuditById(...).
        final AuditDto auditDto = new AuditDto();
        auditDto.setId(0L);
        auditDto.setBusinessId(0L);
        auditDto.setAuditTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        auditDto.setType(5);
        auditDto.setApprovalState(0);
//        when(mockAuditService.selectAuditById(0L)).thenReturn(auditDto);
        // Run the test
        final boolean result = auditOperationalServiceImplUnderTest.updateAuditStateStart(doubleCheckInteractDto);
        // Verify the results
        assertThat(result).isTrue();
    }

    @Test
    void testUpdateAuditStateStart6() throws Exception {
        // Setup
        final DoubleCheckInteractDto doubleCheckInteractDto = new DoubleCheckInteractDto();
        doubleCheckInteractDto.setId(0L);
        doubleCheckInteractDto.setServiceId(0L);
        doubleCheckInteractDto.setTaskSubject("taskSubject");
        doubleCheckInteractDto.setApprovalComment("approvalComment");
        doubleCheckInteractDto.setApprovalState(0);
        when(mockAuditService.updateAuditMultipleState(any(AuditDto.class))).thenReturn(1);
        // Configure IAuditService.selectAuditById(...).
        final AuditDto auditDto = new AuditDto();
        auditDto.setId(0L);
        auditDto.setBusinessId(0L);
        auditDto.setAuditTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        auditDto.setType(6);
        auditDto.setApprovalState(0);
        when(mockAuditService.selectAuditById(0L)).thenReturn(auditDto);
        // Run the test
        final boolean result = auditOperationalServiceImplUnderTest.updateAuditStateStart(doubleCheckInteractDto);
        // Verify the results
        assertThat(result).isTrue();
    }
    
    @Test
    void testUpdateAuditStateStart7() throws Exception {
        // Setup
        final DoubleCheckInteractDto doubleCheckInteractDto = new DoubleCheckInteractDto();
        doubleCheckInteractDto.setId(0L);
        doubleCheckInteractDto.setServiceId(0L);
        doubleCheckInteractDto.setTaskSubject("taskSubject");
        doubleCheckInteractDto.setApprovalComment("approvalComment");
        doubleCheckInteractDto.setApprovalState(0);
        when(mockAuditService.updateAuditMultipleState(any(AuditDto.class))).thenReturn(1);
        // Configure IAuditService.selectAuditById(...).
        final AuditDto auditDto = new AuditDto();
        auditDto.setId(0L);
        auditDto.setBusinessId(0L);
        auditDto.setAuditTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        auditDto.setType(7);
        auditDto.setApprovalState(0);
        when(mockAuditService.selectAuditById(0L)).thenReturn(auditDto);
        // Run the test
        final boolean result = auditOperationalServiceImplUnderTest.updateAuditStateStart(doubleCheckInteractDto);
        // Verify the results
        assertThat(result).isTrue();
    }
    
    @Test
    void testUpdateAuditStateStart8() throws Exception {
        // Setup
        final DoubleCheckInteractDto doubleCheckInteractDto = new DoubleCheckInteractDto();
        doubleCheckInteractDto.setId(0L);
        doubleCheckInteractDto.setServiceId(0L);
        doubleCheckInteractDto.setTaskSubject("taskSubject");
        doubleCheckInteractDto.setApprovalComment("approvalComment");
        doubleCheckInteractDto.setApprovalState(0);
        when(mockAuditService.updateAuditMultipleState(any(AuditDto.class))).thenReturn(1);
        // Configure IAuditService.selectAuditById(...).
        final AuditDto auditDto = new AuditDto();
        auditDto.setId(0L);
        auditDto.setBusinessId(0L);
        auditDto.setAuditTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        auditDto.setType(8);
        auditDto.setApprovalState(0);
        when(mockAuditService.selectAuditById(0L)).thenReturn(auditDto);
        // Run the test
        final boolean result = auditOperationalServiceImplUnderTest.updateAuditStateStart(doubleCheckInteractDto);
        // Verify the results
        assertThat(result).isTrue();
    }
    
    @Test
    void testUpdateAuditStateStart9() throws Exception {
        // Setup
        final DoubleCheckInteractDto doubleCheckInteractDto = new DoubleCheckInteractDto();
        doubleCheckInteractDto.setId(0L);
        doubleCheckInteractDto.setServiceId(0L);
        doubleCheckInteractDto.setTaskSubject("taskSubject");
        doubleCheckInteractDto.setApprovalComment("approvalComment");
        doubleCheckInteractDto.setApprovalState(0);
        when(mockAuditService.updateAuditMultipleState(any(AuditDto.class))).thenReturn(1);
        // Configure IAuditService.selectAuditById(...).
        final AuditDto auditDto = new AuditDto();
        auditDto.setId(0L);
        auditDto.setBusinessId(0L);
        auditDto.setAuditTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        auditDto.setType(9);
        auditDto.setApprovalState(0);
        when(mockAuditService.selectAuditById(0L)).thenReturn(auditDto);
        // Run the test
        final boolean result = auditOperationalServiceImplUnderTest.updateAuditStateStart(doubleCheckInteractDto);
        // Verify the results
        assertThat(result).isTrue();
    }
}

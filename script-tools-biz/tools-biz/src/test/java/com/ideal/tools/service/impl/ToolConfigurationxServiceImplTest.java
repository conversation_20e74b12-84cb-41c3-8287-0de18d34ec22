package com.ideal.tools.service.impl;

import com.github.pagehelper.PageInfo;
import com.ideal.tools.dto.*;
import com.ideal.tools.exception.InteractServiceException;
import com.ideal.tools.model.dto.*;
import com.ideal.tools.service.IToolsAgentInfoService;
import com.ideal.tools.service.IToolsInfoService;
import com.ideal.tools.service.interaction.SystemDataInteract;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ToolConfigurationxServiceImplTest {

    @Mock
    private IToolsInfoService mockToolsInfoService;
    @Mock
    private IToolsAgentInfoService mockToolsAgentInfoService;
    @Mock
    private SystemDataInteract mockSystemDataInteract;

    private ToolConfigurationxServiceImpl toolConfigurationxServiceImplUnderTest;

    @BeforeEach
    void setUp() throws Exception {
        toolConfigurationxServiceImplUnderTest = new ToolConfigurationxServiceImpl(mockToolsInfoService,
                mockToolsAgentInfoService,mockSystemDataInteract);
    }

    @Test
    void testCompositeScriptToolList() {
        // Setup
        final ToolsInfoResultQueryApiDto toolsInfoResultQueryApiDto = new ToolsInfoResultQueryApiDto();
        toolsInfoResultQueryApiDto.setBusinessSystemId(0L);
        toolsInfoResultQueryApiDto.setBusinessSystemName("businessSystemName");
        toolsInfoResultQueryApiDto.setName("name");
        toolsInfoResultQueryApiDto.setType(0);
        toolsInfoResultQueryApiDto.setUserId(0L);

        // Configure IToolsInfoService.selectToolsInfoList(...).
        final ToolsInfoResultDto toolsInfoResultDto = new ToolsInfoResultDto();
        toolsInfoResultDto.setId(0L);
        toolsInfoResultDto.setCode("code");
        toolsInfoResultDto.setName("name");
        toolsInfoResultDto.setBusinessSystemId(0L);
        toolsInfoResultDto.setBusinessSystemName("businessSystemName");
        final PageInfo<ToolsInfoResultDto> toolsInfoResultDtoPageInfo = new PageInfo<>(
                Arrays.asList(toolsInfoResultDto));
        when(mockToolsInfoService.selectToolsInfoListByUser(any(ToolsQueryDto.class), isNull(),eq(0L), eq(0),eq(0)))
                .thenReturn(toolsInfoResultDtoPageInfo);

        // Run the test
        final PageInfo<ToolsInfoResultApiDto> result = toolConfigurationxServiceImplUnderTest.compositeScriptToolList(
                toolsInfoResultQueryApiDto, 0, 0);

        // Verify the results
    }

    @Test
    void testCompositeScriptToolList_IToolsInfoServiceReturnsNoItem() {
        // Setup
        final ToolsInfoResultQueryApiDto toolsInfoResultQueryApiDto = new ToolsInfoResultQueryApiDto();
        toolsInfoResultQueryApiDto.setBusinessSystemId(0L);
        toolsInfoResultQueryApiDto.setBusinessSystemName("businessSystemName");
        toolsInfoResultQueryApiDto.setName("name");
        toolsInfoResultQueryApiDto.setType(0);
        toolsInfoResultQueryApiDto.setUserId(0L);

        when(mockToolsInfoService.selectToolsInfoListByUser(any(ToolsQueryDto.class), isNull(),eq(0L), eq(0),eq(0)))
                .thenReturn(PageInfo.emptyPageInfo());

        // Run the test
        final PageInfo<ToolsInfoResultApiDto> result = toolConfigurationxServiceImplUnderTest.compositeScriptToolList(
                toolsInfoResultQueryApiDto, 0, 0);

        // Verify the results
    }

    @Test
    void testGetToolsCombinedApiInfo() {
        // Setup
        // Configure IToolsInfoService.getToolsCombinedInfo(...).
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setType(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setOsType(0);
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        toolsDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        final ToolsFilesQueryDto toolsFilesQueryDto = new ToolsFilesQueryDto();
        toolsDto.setScriptFilesList(Arrays.asList(toolsFilesQueryDto));
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        when(mockToolsInfoService.getToolsCombinedInfo(0L)).thenReturn(toolsDto);

        // Run the test
        final ToolsApiDto result = toolConfigurationxServiceImplUnderTest.getToolsCombinedApiInfo(0L);

        // Verify the results
    }
/*
    @Test
    void testSelectToolAgentLists() throws Exception {
        // Setup
        final ToolsAgentResultQueryApiDto toolsInfoResultQueryApiDto = new ToolsAgentResultQueryApiDto();
        toolsInfoResultQueryApiDto.setSysId(0L);
        toolsInfoResultQueryApiDto.setComputerIp("computerIp");
        toolsInfoResultQueryApiDto.setTdToolsId(0L);
        toolsInfoResultQueryApiDto.setAgentIpEnd("agentIpEnd");
        final AgentSelectedApiDto agentSelectedApiDto = new AgentSelectedApiDto();
        toolsInfoResultQueryApiDto.setAgentList(Arrays.asList(agentSelectedApiDto));

        // Configure IToolsAgentInfoService.selectStandardTaskComputerIdLists(...).
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsAgentResultDto.setDeviceName("deviceName");
        toolsAgentResultDto.setOsName("osName");
        toolsAgentResultDto.setAgentName("agentName");
        toolsAgentResultDto.setAgentIp("agentIp");
        toolsAgentResultDto.setAgentPort(0);
        final PageInfo<ToolsAgentResultDto> toolsAgentResultDtoPageInfo = new PageInfo<>(
                Arrays.asList(toolsAgentResultDto));
        final UserDto userDto = new UserDto();
        userDto.setUserName("admin");
        userDto.setUserId(1L);
        userDto.setUserCode("userCode");
        userDto.setLoginName("loginName");
        when(mockToolsAgentInfoService.selectStandardTaskComputerIdLists(any(AgentQueryDto.class), userDto,eq(0),
                eq(0))).thenReturn(toolsAgentResultDtoPageInfo);

        // Run the test
        final PageInfo<ToolsAgentResultApiDto> result = toolConfigurationxServiceImplUnderTest.selectToolAgentLists(
                toolsInfoResultQueryApiDto, 0, 0);

        // Verify the results
    }

    @Test
    void testSelectToolAgentLists_IToolsAgentInfoServiceReturnsNoItem() throws Exception {
        // Setup
        final ToolsAgentResultQueryApiDto toolsInfoResultQueryApiDto = new ToolsAgentResultQueryApiDto();
        toolsInfoResultQueryApiDto.setSysId(0L);
        toolsInfoResultQueryApiDto.setComputerIp("computerIp");
        toolsInfoResultQueryApiDto.setTdToolsId(0L);
        toolsInfoResultQueryApiDto.setAgentIpEnd("agentIpEnd");
        final AgentSelectedApiDto agentSelectedApiDto = new AgentSelectedApiDto();
        toolsInfoResultQueryApiDto.setAgentList(Arrays.asList(agentSelectedApiDto));

        final UserDto userDto = new UserDto();
        userDto.setUserName("admin");
        userDto.setUserId(1L);
        userDto.setUserCode("userCode");
        userDto.setLoginName("loginName");

        when(mockToolsAgentInfoService.selectStandardTaskComputerIdLists(any(AgentQueryDto.class), userDto,eq(0),
                eq(0))).thenReturn(PageInfo.emptyPageInfo());

        // Run the test
        final PageInfo<ToolsAgentResultApiDto> result = toolConfigurationxServiceImplUnderTest.selectToolAgentLists(
                toolsInfoResultQueryApiDto, 0, 0);

        // Verify the results
    }

    @Test
    void testSelectToolAgentLists_IToolsAgentInfoServiceThrowsInteractServiceException() throws Exception {
        // Setup
        final ToolsAgentResultQueryApiDto toolsInfoResultQueryApiDto = new ToolsAgentResultQueryApiDto();
        toolsInfoResultQueryApiDto.setSysId(0L);
        toolsInfoResultQueryApiDto.setComputerIp("computerIp");
        toolsInfoResultQueryApiDto.setTdToolsId(0L);
        toolsInfoResultQueryApiDto.setAgentIpEnd("agentIpEnd");
        final AgentSelectedApiDto agentSelectedApiDto = new AgentSelectedApiDto();
        toolsInfoResultQueryApiDto.setAgentList(Arrays.asList(agentSelectedApiDto));

        final UserDto userDto = new UserDto();
        userDto.setUserName("admin");
        userDto.setUserId(1L);
        userDto.setUserCode("userCode");
        userDto.setLoginName("loginName");

        when(mockToolsAgentInfoService.selectStandardTaskComputerIdLists(any(AgentQueryDto.class), userDto,eq(0),
                eq(0))).thenThrow(InteractServiceException.class);

        // Run the test
        final PageInfo<ToolsAgentResultApiDto> result = toolConfigurationxServiceImplUnderTest.selectToolAgentLists(
                toolsInfoResultQueryApiDto, 0, 0);

        // Verify the results
    }*/
}

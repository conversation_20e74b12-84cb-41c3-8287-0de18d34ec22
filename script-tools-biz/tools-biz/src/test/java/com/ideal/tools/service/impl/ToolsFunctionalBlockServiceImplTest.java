/*
package com.ideal.tools.service.impl;

import com.ideal.tools.config.BusinessConfig;
import com.ideal.tools.exception.AuditException;
import com.ideal.tools.exception.ScriptToolsException;
import com.ideal.tools.exception.StudioException;
import com.ideal.tools.exception.ToolsException;
import com.ideal.tools.model.dto.*;
import com.ideal.tools.model.entity.AuditEverybodyEntity;
import com.ideal.tools.model.interaction.ScriptContentDto;
import com.ideal.tools.model.interaction.WorkflowActivitiesDto;
import com.ideal.tools.service.IAuditService;
import com.ideal.tools.service.IToolsEverybodyService;
import com.ideal.tools.service.IToolsOperateBaseService;
import com.ideal.tools.service.interaction.ScriptInteract;
import com.ideal.tools.service.interaction.StudioInteract;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ToolsFunctionalBlockServiceImplTest {

    @Mock
    private IToolsOperateBaseService mockToolsOperateBaseService;
    @Mock
    private IAuditService mockAuditService;
    @Mock
    private ScriptInteract mockScriptInteract;
    @Mock
    private IToolsEverybodyService mockToolsEverybodyService;
    @Mock
    private StudioInteract mockStudioInteract;
    @Mock
    private BusinessConfig mockBusinessConfig;

    private ToolsFunctionalBlockServiceImpl toolsFunctionalBlockServiceImplUnderTest;

    @BeforeEach
    void setUp() throws Exception {
        toolsFunctionalBlockServiceImplUnderTest = new ToolsFunctionalBlockServiceImpl(mockToolsOperateBaseService,
                mockAuditService, mockScriptInteract, mockToolsEverybodyService, mockStudioInteract,
                mockBusinessConfig);
    }

    @Test
    void testSaveToolsInfo() throws Exception {
        // Setup
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setName("name");
        toolsDto.setType(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        toolsDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setId(0L);
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowContent("workflowContent");
        toolsProjectInfoDto.setTdToolsId(0L);
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setChildIds("childIds");

        final UserDto userDto = new UserDto();
        userDto.setUserName("userName");
        userDto.setUserId(0L);
        userDto.setUserCode("userCode");
        userDto.setLoginName("loginName");

        when(mockToolsOperateBaseService.saveToolsBaseInfo(any(ToolsDto.class), any(UserDto.class))).thenReturn(0L);

        // Configure StudioInteract.detailBaseData(...).
        final ToolsProjectInfoDto toolsProjectInfoDto1 = new ToolsProjectInfoDto();
        toolsProjectInfoDto1.setId(0L);
        toolsProjectInfoDto1.setProjectId(0L);
        toolsProjectInfoDto1.setWorkflowId(0L);
        toolsProjectInfoDto1.setWorkflowContent("workflowContent");
        toolsProjectInfoDto1.setTdToolsId(0L);
        when(mockStudioInteract.detailBaseData(any(ToolsDto.class), any(UserDto.class)))
                .thenReturn(toolsProjectInfoDto1);

        when(mockStudioInteract.getWorkflowJson(0L)).thenReturn("workflowContent");
        when(mockToolsOperateBaseService.insertUpdateToolsProject(eq(0L), any(ToolsProjectInfoDto.class),
                any(UserDto.class))).thenReturn(0L);

        // Run the test
        final ToolsDto result = toolsFunctionalBlockServiceImplUnderTest.saveToolsInfo(toolsDto, userDto);

        // Verify the results
        // Confirm IToolsEverybodyService.batchInsertoolsEverybody(...).
        final AuditEverybodyQueryDto auditEverybodyQueryDto1 = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto1.setId(0L);
        auditEverybodyQueryDto1.setAuditorId(0L);
        auditEverybodyQueryDto1.setAuditorName("auditorName");
        final List<AuditEverybodyQueryDto> auditEverybodyDtoList = Arrays.asList(auditEverybodyQueryDto1);
        verify(mockToolsEverybodyService).batchInsertoolsEverybody(0L, auditEverybodyDtoList);
        verify(mockToolsOperateBaseService).saveToolsFilesBaseInfo(eq(0L), any(ToolsDto.class), any(UserDto.class));

        // Confirm IToolsOperateBaseService.saveToolsParamBaseInfo(...).
        final ToolsParamResultDto toolsParamResultDto1 = new ToolsParamResultDto();
        toolsParamResultDto1.setSort(0L);
        toolsParamResultDto1.setName("name");
        toolsParamResultDto1.setType("type");
        toolsParamResultDto1.setValue("value");
        toolsParamResultDto1.setDescription("description");
        final List<ToolsParamResultDto> toolsParamResultList = Arrays.asList(toolsParamResultDto1);
        verify(mockToolsOperateBaseService).saveToolsParamBaseInfo(eq(0L), eq(toolsParamResultList),
                any(UserDto.class));

        // Confirm IToolsOperateBaseService.saveToolsAgentBaseInfo(...).
        final ToolsAgentResultDto toolsAgentResultDto1 = new ToolsAgentResultDto();
        toolsAgentResultDto1.setDeviceName("deviceName");
        toolsAgentResultDto1.setOsName("osName");
        toolsAgentResultDto1.setAgentName("agentName");
        toolsAgentResultDto1.setAgentIp("agentIp");
        toolsAgentResultDto1.setAgentPort(0);
        final List<ToolsAgentResultDto> toolsParamResultList1 = Arrays.asList(toolsAgentResultDto1);
        verify(mockToolsOperateBaseService).saveToolsAgentBaseInfo(eq(0L), eq(toolsParamResultList1),
                any(UserDto.class));
    }

    @Test
    void testSaveToolsInfo_IToolsOperateBaseServiceSaveToolsBaseInfoThrowsToolsException() throws Exception {
        // Setup
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setName("name");
        toolsDto.setType(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        toolsDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setId(0L);
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowContent("workflowContent");
        toolsProjectInfoDto.setTdToolsId(0L);
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setChildIds("childIds");

        final UserDto userDto = new UserDto();
        userDto.setUserName("userName");
        userDto.setUserId(0L);
        userDto.setUserCode("userCode");
        userDto.setLoginName("loginName");

        when(mockToolsOperateBaseService.saveToolsBaseInfo(any(ToolsDto.class), any(UserDto.class)))
                .thenThrow(ToolsException.class);

        // Run the test
        assertThatThrownBy(
                () -> toolsFunctionalBlockServiceImplUnderTest.saveToolsInfo(toolsDto, userDto))
                .isInstanceOf(ToolsException.class);
    }

    @Test
    void testSaveToolsInfo_IToolsEverybodyServiceThrowsToolsException() throws Exception {
        // Setup
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setName("name");
        toolsDto.setType(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        toolsDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setId(0L);
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowContent("workflowContent");
        toolsProjectInfoDto.setTdToolsId(0L);
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setChildIds("childIds");

        final UserDto userDto = new UserDto();
        userDto.setUserName("userName");
        userDto.setUserId(0L);
        userDto.setUserCode("userCode");
        userDto.setLoginName("loginName");

        when(mockToolsOperateBaseService.saveToolsBaseInfo(any(ToolsDto.class), any(UserDto.class))).thenReturn(0L);

        // Configure IToolsEverybodyService.batchInsertoolsEverybody(...).
        final AuditEverybodyQueryDto auditEverybodyQueryDto1 = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto1.setId(0L);
        auditEverybodyQueryDto1.setAuditorId(0L);
        auditEverybodyQueryDto1.setAuditorName("auditorName");
        final List<AuditEverybodyQueryDto> auditEverybodyDtoList = Arrays.asList(auditEverybodyQueryDto1);
        doThrow(ToolsException.class).when(mockToolsEverybodyService).batchInsertoolsEverybody(0L,
                auditEverybodyDtoList);

        // Run the test
        assertThatThrownBy(
                () -> toolsFunctionalBlockServiceImplUnderTest.saveToolsInfo(toolsDto, userDto))
                .isInstanceOf(ToolsException.class);
    }

    @Test
    void testSaveToolsInfo_IToolsOperateBaseServiceSaveToolsFilesBaseInfoThrowsToolsException() throws Exception {
        // Setup
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setName("name");
        toolsDto.setType(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        toolsDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setId(0L);
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowContent("workflowContent");
        toolsProjectInfoDto.setTdToolsId(0L);
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setChildIds("childIds");

        final UserDto userDto = new UserDto();
        userDto.setUserName("userName");
        userDto.setUserId(0L);
        userDto.setUserCode("userCode");
        userDto.setLoginName("loginName");

        when(mockToolsOperateBaseService.saveToolsBaseInfo(any(ToolsDto.class), any(UserDto.class))).thenReturn(0L);
        when(mockToolsOperateBaseService.saveToolsFilesBaseInfo(eq(0L), any(ToolsDto.class),
                any(UserDto.class))).thenThrow(ToolsException.class);

        // Run the test
        assertThatThrownBy(
                () -> toolsFunctionalBlockServiceImplUnderTest.saveToolsInfo(toolsDto, userDto))
                .isInstanceOf(ToolsException.class);

        // Confirm IToolsEverybodyService.batchInsertoolsEverybody(...).
        final AuditEverybodyQueryDto auditEverybodyQueryDto1 = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto1.setId(0L);
        auditEverybodyQueryDto1.setAuditorId(0L);
        auditEverybodyQueryDto1.setAuditorName("auditorName");
        final List<AuditEverybodyQueryDto> auditEverybodyDtoList = Arrays.asList(auditEverybodyQueryDto1);
        verify(mockToolsEverybodyService).batchInsertoolsEverybody(0L, auditEverybodyDtoList);
    }

    @Test
    void testSaveToolsInfo_StudioInteractDetailBaseDataThrowsStudioException() throws Exception {
        // Setup
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setName("name");
        toolsDto.setType(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        toolsDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setId(0L);
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowContent("workflowContent");
        toolsProjectInfoDto.setTdToolsId(0L);
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setChildIds("childIds");

        final UserDto userDto = new UserDto();
        userDto.setUserName("userName");
        userDto.setUserId(0L);
        userDto.setUserCode("userCode");
        userDto.setLoginName("loginName");

        when(mockToolsOperateBaseService.saveToolsBaseInfo(any(ToolsDto.class), any(UserDto.class))).thenReturn(0L);
        when(mockStudioInteract.detailBaseData(any(ToolsDto.class), any(UserDto.class)))
                .thenThrow(StudioException.class);

        // Run the test
        assertThatThrownBy(
                () -> toolsFunctionalBlockServiceImplUnderTest.saveToolsInfo(toolsDto, userDto))
                .isInstanceOf(StudioException.class);

        // Confirm IToolsEverybodyService.batchInsertoolsEverybody(...).
        final AuditEverybodyQueryDto auditEverybodyQueryDto1 = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto1.setId(0L);
        auditEverybodyQueryDto1.setAuditorId(0L);
        auditEverybodyQueryDto1.setAuditorName("auditorName");
        final List<AuditEverybodyQueryDto> auditEverybodyDtoList = Arrays.asList(auditEverybodyQueryDto1);
        verify(mockToolsEverybodyService).batchInsertoolsEverybody(0L, auditEverybodyDtoList);
    }

    @Test
    void testSaveToolsInfo_StudioInteractGetWorkflowJsonThrowsStudioException() throws Exception {
        // Setup
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setName("name");
        toolsDto.setType(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        toolsDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setId(0L);
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowContent("workflowContent");
        toolsProjectInfoDto.setTdToolsId(0L);
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setChildIds("childIds");

        final UserDto userDto = new UserDto();
        userDto.setUserName("userName");
        userDto.setUserId(0L);
        userDto.setUserCode("userCode");
        userDto.setLoginName("loginName");

        when(mockToolsOperateBaseService.saveToolsBaseInfo(any(ToolsDto.class), any(UserDto.class))).thenReturn(0L);

        // Configure StudioInteract.detailBaseData(...).
        final ToolsProjectInfoDto toolsProjectInfoDto1 = new ToolsProjectInfoDto();
        toolsProjectInfoDto1.setId(0L);
        toolsProjectInfoDto1.setProjectId(0L);
        toolsProjectInfoDto1.setWorkflowId(0L);
        toolsProjectInfoDto1.setWorkflowContent("workflowContent");
        toolsProjectInfoDto1.setTdToolsId(0L);
        when(mockStudioInteract.detailBaseData(any(ToolsDto.class), any(UserDto.class)))
                .thenReturn(toolsProjectInfoDto1);

        when(mockStudioInteract.getWorkflowJson(0L)).thenThrow(StudioException.class);
        when(mockToolsOperateBaseService.insertUpdateToolsProject(eq(0L), any(ToolsProjectInfoDto.class),
                any(UserDto.class))).thenReturn(0L);

        // Run the test
        final ToolsDto result = toolsFunctionalBlockServiceImplUnderTest.saveToolsInfo(toolsDto, userDto);

        // Verify the results
        // Confirm IToolsEverybodyService.batchInsertoolsEverybody(...).
        final AuditEverybodyQueryDto auditEverybodyQueryDto1 = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto1.setId(0L);
        auditEverybodyQueryDto1.setAuditorId(0L);
        auditEverybodyQueryDto1.setAuditorName("auditorName");
        final List<AuditEverybodyQueryDto> auditEverybodyDtoList = Arrays.asList(auditEverybodyQueryDto1);
        verify(mockToolsEverybodyService).batchInsertoolsEverybody(0L, auditEverybodyDtoList);
    }

    @Test
    void testSaveToolsInfo_IToolsOperateBaseServiceInsertUpdateToolsProjectThrowsToolsException() throws Exception {
        // Setup
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setName("name");
        toolsDto.setType(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        toolsDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setId(0L);
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowContent("workflowContent");
        toolsProjectInfoDto.setTdToolsId(0L);
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setChildIds("childIds");

        final UserDto userDto = new UserDto();
        userDto.setUserName("userName");
        userDto.setUserId(0L);
        userDto.setUserCode("userCode");
        userDto.setLoginName("loginName");

        when(mockToolsOperateBaseService.saveToolsBaseInfo(any(ToolsDto.class), any(UserDto.class))).thenReturn(0L);

        // Configure StudioInteract.detailBaseData(...).
        final ToolsProjectInfoDto toolsProjectInfoDto1 = new ToolsProjectInfoDto();
        toolsProjectInfoDto1.setId(0L);
        toolsProjectInfoDto1.setProjectId(0L);
        toolsProjectInfoDto1.setWorkflowId(0L);
        toolsProjectInfoDto1.setWorkflowContent("workflowContent");
        toolsProjectInfoDto1.setTdToolsId(0L);
        when(mockStudioInteract.detailBaseData(any(ToolsDto.class), any(UserDto.class)))
                .thenReturn(toolsProjectInfoDto1);

        when(mockStudioInteract.getWorkflowJson(0L)).thenReturn("workflowContent");
        when(mockToolsOperateBaseService.insertUpdateToolsProject(eq(0L), any(ToolsProjectInfoDto.class),
                any(UserDto.class))).thenThrow(ToolsException.class);

        // Run the test
        assertThatThrownBy(
                () -> toolsFunctionalBlockServiceImplUnderTest.saveToolsInfo(toolsDto, userDto))
                .isInstanceOf(ToolsException.class);

        // Confirm IToolsEverybodyService.batchInsertoolsEverybody(...).
        final AuditEverybodyQueryDto auditEverybodyQueryDto1 = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto1.setId(0L);
        auditEverybodyQueryDto1.setAuditorId(0L);
        auditEverybodyQueryDto1.setAuditorName("auditorName");
        final List<AuditEverybodyQueryDto> auditEverybodyDtoList = Arrays.asList(auditEverybodyQueryDto1);
        verify(mockToolsEverybodyService).batchInsertoolsEverybody(0L, auditEverybodyDtoList);
    }

    @Test
    void testSaveToolsInfo_IToolsOperateBaseServiceSaveToolsParamBaseInfoThrowsToolsException() throws Exception {
        // Setup
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setName("name");
        toolsDto.setType(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        toolsDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setId(0L);
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowContent("workflowContent");
        toolsProjectInfoDto.setTdToolsId(0L);
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setChildIds("childIds");

        final UserDto userDto = new UserDto();
        userDto.setUserName("userName");
        userDto.setUserId(0L);
        userDto.setUserCode("userCode");
        userDto.setLoginName("loginName");

        when(mockToolsOperateBaseService.saveToolsBaseInfo(any(ToolsDto.class), any(UserDto.class))).thenReturn(0L);

        // Configure IToolsOperateBaseService.saveToolsParamBaseInfo(...).
        final ToolsParamResultDto toolsParamResultDto1 = new ToolsParamResultDto();
        toolsParamResultDto1.setSort(0L);
        toolsParamResultDto1.setName("name");
        toolsParamResultDto1.setType("type");
        toolsParamResultDto1.setValue("value");
        toolsParamResultDto1.setDescription("description");
        final List<ToolsParamResultDto> toolsParamResultList = Arrays.asList(toolsParamResultDto1);
        doThrow(ToolsException.class).when(mockToolsOperateBaseService).saveToolsParamBaseInfo(eq(0L),
                eq(toolsParamResultList), any(UserDto.class));

        // Run the test
        assertThatThrownBy(
                () -> toolsFunctionalBlockServiceImplUnderTest.saveToolsInfo(toolsDto, userDto))
                .isInstanceOf(ToolsException.class);

        // Confirm IToolsEverybodyService.batchInsertoolsEverybody(...).
        final AuditEverybodyQueryDto auditEverybodyQueryDto1 = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto1.setId(0L);
        auditEverybodyQueryDto1.setAuditorId(0L);
        auditEverybodyQueryDto1.setAuditorName("auditorName");
        final List<AuditEverybodyQueryDto> auditEverybodyDtoList = Arrays.asList(auditEverybodyQueryDto1);
        verify(mockToolsEverybodyService).batchInsertoolsEverybody(0L, auditEverybodyDtoList);
    }

    @Test
    void testSaveToolsInfo_IToolsOperateBaseServiceSaveToolsAgentBaseInfoThrowsToolsException() throws Exception {
        // Setup
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setName("name");
        toolsDto.setType(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        toolsDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setId(0L);
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowContent("workflowContent");
        toolsProjectInfoDto.setTdToolsId(0L);
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setChildIds("childIds");

        final UserDto userDto = new UserDto();
        userDto.setUserName("userName");
        userDto.setUserId(0L);
        userDto.setUserCode("userCode");
        userDto.setLoginName("loginName");

        when(mockToolsOperateBaseService.saveToolsBaseInfo(any(ToolsDto.class), any(UserDto.class))).thenReturn(0L);

        // Configure IToolsOperateBaseService.saveToolsAgentBaseInfo(...).
        final ToolsAgentResultDto toolsAgentResultDto1 = new ToolsAgentResultDto();
        toolsAgentResultDto1.setDeviceName("deviceName");
        toolsAgentResultDto1.setOsName("osName");
        toolsAgentResultDto1.setAgentName("agentName");
        toolsAgentResultDto1.setAgentIp("agentIp");
        toolsAgentResultDto1.setAgentPort(0);
        final List<ToolsAgentResultDto> toolsParamResultList = Arrays.asList(toolsAgentResultDto1);
        doThrow(ToolsException.class).when(mockToolsOperateBaseService).saveToolsAgentBaseInfo(eq(0L),
                eq(toolsParamResultList), any(UserDto.class));

        // Run the test
        assertThatThrownBy(
                () -> toolsFunctionalBlockServiceImplUnderTest.saveToolsInfo(toolsDto, userDto))
                .isInstanceOf(ToolsException.class);

        // Confirm IToolsEverybodyService.batchInsertoolsEverybody(...).
        final AuditEverybodyQueryDto auditEverybodyQueryDto1 = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto1.setId(0L);
        auditEverybodyQueryDto1.setAuditorId(0L);
        auditEverybodyQueryDto1.setAuditorName("auditorName");
        final List<AuditEverybodyQueryDto> auditEverybodyDtoList = Arrays.asList(auditEverybodyQueryDto1);
        verify(mockToolsEverybodyService).batchInsertoolsEverybody(0L, auditEverybodyDtoList);

        // Confirm IToolsOperateBaseService.saveToolsParamBaseInfo(...).
        final ToolsParamResultDto toolsParamResultDto1 = new ToolsParamResultDto();
        toolsParamResultDto1.setSort(0L);
        toolsParamResultDto1.setName("name");
        toolsParamResultDto1.setType("type");
        toolsParamResultDto1.setValue("value");
        toolsParamResultDto1.setDescription("description");
        final List<ToolsParamResultDto> toolsParamResultList1 = Arrays.asList(toolsParamResultDto1);
        verify(mockToolsOperateBaseService).saveToolsParamBaseInfo(eq(0L), eq(toolsParamResultList1),
                any(UserDto.class));
    }

    @Test
    void testUpdateToolsInfo() throws Exception {
        // Setup
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setName("name");
        toolsDto.setType(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        toolsDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setId(0L);
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowContent("workflowContent");
        toolsProjectInfoDto.setTdToolsId(0L);
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setChildIds("childIds");

        final UserDto userDto = new UserDto();
        userDto.setUserName("userName");
        userDto.setUserId(0L);
        userDto.setUserCode("userCode");
        userDto.setLoginName("loginName");

        when(mockToolsOperateBaseService.updateToolsBaseInfo(any(ToolsDto.class), any(UserDto.class))).thenReturn(0L);
        when(mockStudioInteract.getWorkflowJson(0L)).thenReturn("workflowContent");
        when(mockToolsOperateBaseService.insertUpdateToolsProject(eq(0L), any(ToolsProjectInfoDto.class),
                any(UserDto.class))).thenReturn(0L);

        // Run the test
        final ToolsDto result = toolsFunctionalBlockServiceImplUnderTest.updateToolsInfo(toolsDto, userDto, 0);

        // Verify the results
        verify(mockToolsEverybodyService).deleteToolsEverybodyByIds(0L);

        // Confirm IToolsEverybodyService.batchInsertoolsEverybody(...).
        final AuditEverybodyQueryDto auditEverybodyQueryDto1 = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto1.setId(0L);
        auditEverybodyQueryDto1.setAuditorId(0L);
        auditEverybodyQueryDto1.setAuditorName("auditorName");
        final List<AuditEverybodyQueryDto> auditEverybodyDtoList = Arrays.asList(auditEverybodyQueryDto1);
        verify(mockToolsEverybodyService).batchInsertoolsEverybody(0L, auditEverybodyDtoList);
        verify(mockToolsOperateBaseService).deleteToolsFilesTable(any(ToolsDto.class));
        verify(mockToolsOperateBaseService).saveToolsFilesBaseInfo(eq(0L), any(ToolsDto.class), any(UserDto.class));
        verify(mockToolsOperateBaseService).deleteToolsProject(0L);
        verify(mockToolsOperateBaseService).deleteToolsScriptAnnexedTable(0L);

        // Confirm IToolsOperateBaseService.saveToolsParamBaseInfo(...).
        final ToolsParamResultDto toolsParamResultDto1 = new ToolsParamResultDto();
        toolsParamResultDto1.setSort(0L);
        toolsParamResultDto1.setName("name");
        toolsParamResultDto1.setType("type");
        toolsParamResultDto1.setValue("value");
        toolsParamResultDto1.setDescription("description");
        final List<ToolsParamResultDto> toolsParamResultList = Arrays.asList(toolsParamResultDto1);
        verify(mockToolsOperateBaseService).saveToolsParamBaseInfo(eq(0L), eq(toolsParamResultList),
                any(UserDto.class));

        // Confirm IToolsOperateBaseService.saveToolsAgentBaseInfo(...).
        final ToolsAgentResultDto toolsAgentResultDto1 = new ToolsAgentResultDto();
        toolsAgentResultDto1.setDeviceName("deviceName");
        toolsAgentResultDto1.setOsName("osName");
        toolsAgentResultDto1.setAgentName("agentName");
        toolsAgentResultDto1.setAgentIp("agentIp");
        toolsAgentResultDto1.setAgentPort(0);
        final List<ToolsAgentResultDto> toolsParamResultList1 = Arrays.asList(toolsAgentResultDto1);
        verify(mockToolsOperateBaseService).saveToolsAgentBaseInfo(eq(0L), eq(toolsParamResultList1),
                any(UserDto.class));
    }

    @Test
    void testUpdateToolsInfo_IToolsOperateBaseServiceUpdateToolsBaseInfoThrowsToolsException() throws Exception {
        // Setup
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setName("name");
        toolsDto.setType(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        toolsDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setId(0L);
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowContent("workflowContent");
        toolsProjectInfoDto.setTdToolsId(0L);
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setChildIds("childIds");

        final UserDto userDto = new UserDto();
        userDto.setUserName("userName");
        userDto.setUserId(0L);
        userDto.setUserCode("userCode");
        userDto.setLoginName("loginName");

        when(mockToolsOperateBaseService.updateToolsBaseInfo(any(ToolsDto.class), any(UserDto.class)))
                .thenThrow(ToolsException.class);

        // Run the test
        assertThatThrownBy(
                () -> toolsFunctionalBlockServiceImplUnderTest.updateToolsInfo(toolsDto, userDto, 0))
                .isInstanceOf(ToolsException.class);
    }

    @Test
    void testUpdateToolsInfo_IToolsEverybodyServiceBatchInsertoolsEverybodyThrowsToolsException() throws Exception {
        // Setup
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setName("name");
        toolsDto.setType(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        toolsDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setId(0L);
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowContent("workflowContent");
        toolsProjectInfoDto.setTdToolsId(0L);
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setChildIds("childIds");

        final UserDto userDto = new UserDto();
        userDto.setUserName("userName");
        userDto.setUserId(0L);
        userDto.setUserCode("userCode");
        userDto.setLoginName("loginName");

        when(mockToolsOperateBaseService.updateToolsBaseInfo(any(ToolsDto.class), any(UserDto.class))).thenReturn(0L);

        // Configure IToolsEverybodyService.batchInsertoolsEverybody(...).
        final AuditEverybodyQueryDto auditEverybodyQueryDto1 = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto1.setId(0L);
        auditEverybodyQueryDto1.setAuditorId(0L);
        auditEverybodyQueryDto1.setAuditorName("auditorName");
        final List<AuditEverybodyQueryDto> auditEverybodyDtoList = Arrays.asList(auditEverybodyQueryDto1);
        doThrow(ToolsException.class).when(mockToolsEverybodyService).batchInsertoolsEverybody(0L,
                auditEverybodyDtoList);

        // Run the test
        assertThatThrownBy(
                () -> toolsFunctionalBlockServiceImplUnderTest.updateToolsInfo(toolsDto, userDto, 0))
                .isInstanceOf(ToolsException.class);
        verify(mockToolsEverybodyService).deleteToolsEverybodyByIds(0L);
    }

    @Test
    void testUpdateToolsInfo_IToolsOperateBaseServiceDeleteToolsFilesTableThrowsToolsException() throws Exception {
        // Setup
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setName("name");
        toolsDto.setType(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        toolsDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setId(0L);
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowContent("workflowContent");
        toolsProjectInfoDto.setTdToolsId(0L);
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setChildIds("childIds");

        final UserDto userDto = new UserDto();
        userDto.setUserName("userName");
        userDto.setUserId(0L);
        userDto.setUserCode("userCode");
        userDto.setLoginName("loginName");

        when(mockToolsOperateBaseService.updateToolsBaseInfo(any(ToolsDto.class), any(UserDto.class))).thenReturn(0L);
        doThrow(ToolsException.class).when(mockToolsOperateBaseService).deleteToolsFilesTable(any(ToolsDto.class));

        // Run the test
        assertThatThrownBy(
                () -> toolsFunctionalBlockServiceImplUnderTest.updateToolsInfo(toolsDto, userDto, 0))
                .isInstanceOf(ToolsException.class);
        verify(mockToolsEverybodyService).deleteToolsEverybodyByIds(0L);

        // Confirm IToolsEverybodyService.batchInsertoolsEverybody(...).
        final AuditEverybodyQueryDto auditEverybodyQueryDto1 = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto1.setId(0L);
        auditEverybodyQueryDto1.setAuditorId(0L);
        auditEverybodyQueryDto1.setAuditorName("auditorName");
        final List<AuditEverybodyQueryDto> auditEverybodyDtoList = Arrays.asList(auditEverybodyQueryDto1);
        verify(mockToolsEverybodyService).batchInsertoolsEverybody(0L, auditEverybodyDtoList);
    }

    @Test
    void testUpdateToolsInfo_IToolsOperateBaseServiceSaveToolsFilesBaseInfoThrowsToolsException() throws Exception {
        // Setup
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setName("name");
        toolsDto.setType(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        toolsDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setId(0L);
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowContent("workflowContent");
        toolsProjectInfoDto.setTdToolsId(0L);
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setChildIds("childIds");

        final UserDto userDto = new UserDto();
        userDto.setUserName("userName");
        userDto.setUserId(0L);
        userDto.setUserCode("userCode");
        userDto.setLoginName("loginName");

        when(mockToolsOperateBaseService.updateToolsBaseInfo(any(ToolsDto.class), any(UserDto.class))).thenReturn(0L);
        when(mockToolsOperateBaseService.saveToolsFilesBaseInfo(eq(0L), any(ToolsDto.class),
                any(UserDto.class))).thenThrow(ToolsException.class);

        // Run the test
        assertThatThrownBy(
                () -> toolsFunctionalBlockServiceImplUnderTest.updateToolsInfo(toolsDto, userDto, 0))
                .isInstanceOf(ToolsException.class);
        verify(mockToolsEverybodyService).deleteToolsEverybodyByIds(0L);

        // Confirm IToolsEverybodyService.batchInsertoolsEverybody(...).
        final AuditEverybodyQueryDto auditEverybodyQueryDto1 = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto1.setId(0L);
        auditEverybodyQueryDto1.setAuditorId(0L);
        auditEverybodyQueryDto1.setAuditorName("auditorName");
        final List<AuditEverybodyQueryDto> auditEverybodyDtoList = Arrays.asList(auditEverybodyQueryDto1);
        verify(mockToolsEverybodyService).batchInsertoolsEverybody(0L, auditEverybodyDtoList);
        verify(mockToolsOperateBaseService).deleteToolsFilesTable(any(ToolsDto.class));
    }

    @Test
    void testUpdateToolsInfo_IToolsOperateBaseServiceDeleteToolsProjectThrowsToolsException() throws Exception {
        // Setup
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setName("name");
        toolsDto.setType(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        toolsDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setId(0L);
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowContent("workflowContent");
        toolsProjectInfoDto.setTdToolsId(0L);
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setChildIds("childIds");

        final UserDto userDto = new UserDto();
        userDto.setUserName("userName");
        userDto.setUserId(0L);
        userDto.setUserCode("userCode");
        userDto.setLoginName("loginName");

        when(mockToolsOperateBaseService.updateToolsBaseInfo(any(ToolsDto.class), any(UserDto.class))).thenReturn(0L);
        doThrow(ToolsException.class).when(mockToolsOperateBaseService).deleteToolsProject(0L);

        // Run the test
        assertThatThrownBy(
                () -> toolsFunctionalBlockServiceImplUnderTest.updateToolsInfo(toolsDto, userDto, 0))
                .isInstanceOf(ToolsException.class);
        verify(mockToolsEverybodyService).deleteToolsEverybodyByIds(0L);

        // Confirm IToolsEverybodyService.batchInsertoolsEverybody(...).
        final AuditEverybodyQueryDto auditEverybodyQueryDto1 = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto1.setId(0L);
        auditEverybodyQueryDto1.setAuditorId(0L);
        auditEverybodyQueryDto1.setAuditorName("auditorName");
        final List<AuditEverybodyQueryDto> auditEverybodyDtoList = Arrays.asList(auditEverybodyQueryDto1);
        verify(mockToolsEverybodyService).batchInsertoolsEverybody(0L, auditEverybodyDtoList);
    }

    @Test
    void testUpdateToolsInfo_StudioInteractThrowsStudioException() throws Exception {
        // Setup
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setName("name");
        toolsDto.setType(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        toolsDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setId(0L);
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowContent("workflowContent");
        toolsProjectInfoDto.setTdToolsId(0L);
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setChildIds("childIds");

        final UserDto userDto = new UserDto();
        userDto.setUserName("userName");
        userDto.setUserId(0L);
        userDto.setUserCode("userCode");
        userDto.setLoginName("loginName");

        when(mockToolsOperateBaseService.updateToolsBaseInfo(any(ToolsDto.class), any(UserDto.class))).thenReturn(0L);
        when(mockStudioInteract.getWorkflowJson(0L)).thenThrow(StudioException.class);
        when(mockToolsOperateBaseService.insertUpdateToolsProject(eq(0L), any(ToolsProjectInfoDto.class),
                any(UserDto.class))).thenReturn(0L);

        // Run the test
        final ToolsDto result = toolsFunctionalBlockServiceImplUnderTest.updateToolsInfo(toolsDto, userDto, 0);

        // Verify the results
        verify(mockToolsEverybodyService).deleteToolsEverybodyByIds(0L);

        // Confirm IToolsEverybodyService.batchInsertoolsEverybody(...).
        final AuditEverybodyQueryDto auditEverybodyQueryDto1 = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto1.setId(0L);
        auditEverybodyQueryDto1.setAuditorId(0L);
        auditEverybodyQueryDto1.setAuditorName("auditorName");
        final List<AuditEverybodyQueryDto> auditEverybodyDtoList = Arrays.asList(auditEverybodyQueryDto1);
        verify(mockToolsEverybodyService).batchInsertoolsEverybody(0L, auditEverybodyDtoList);
        verify(mockToolsOperateBaseService).deleteToolsProject(0L);
    }

    @Test
    void testUpdateToolsInfo_IToolsOperateBaseServiceInsertUpdateToolsProjectThrowsToolsException() throws Exception {
        // Setup
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setName("name");
        toolsDto.setType(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        toolsDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setId(0L);
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowContent("workflowContent");
        toolsProjectInfoDto.setTdToolsId(0L);
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setChildIds("childIds");

        final UserDto userDto = new UserDto();
        userDto.setUserName("userName");
        userDto.setUserId(0L);
        userDto.setUserCode("userCode");
        userDto.setLoginName("loginName");

        when(mockToolsOperateBaseService.updateToolsBaseInfo(any(ToolsDto.class), any(UserDto.class))).thenReturn(0L);
        when(mockStudioInteract.getWorkflowJson(0L)).thenReturn("workflowContent");
        when(mockToolsOperateBaseService.insertUpdateToolsProject(eq(0L), any(ToolsProjectInfoDto.class),
                any(UserDto.class))).thenThrow(ToolsException.class);

        // Run the test
        assertThatThrownBy(
                () -> toolsFunctionalBlockServiceImplUnderTest.updateToolsInfo(toolsDto, userDto, 0))
                .isInstanceOf(ToolsException.class);
        verify(mockToolsEverybodyService).deleteToolsEverybodyByIds(0L);

        // Confirm IToolsEverybodyService.batchInsertoolsEverybody(...).
        final AuditEverybodyQueryDto auditEverybodyQueryDto1 = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto1.setId(0L);
        auditEverybodyQueryDto1.setAuditorId(0L);
        auditEverybodyQueryDto1.setAuditorName("auditorName");
        final List<AuditEverybodyQueryDto> auditEverybodyDtoList = Arrays.asList(auditEverybodyQueryDto1);
        verify(mockToolsEverybodyService).batchInsertoolsEverybody(0L, auditEverybodyDtoList);
        verify(mockToolsOperateBaseService).deleteToolsProject(0L);
    }

    @Test
    void testUpdateToolsInfo_IToolsOperateBaseServiceDeleteToolsScriptAnnexedTableThrowsToolsException() throws Exception {
        // Setup
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setName("name");
        toolsDto.setType(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        toolsDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setId(0L);
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowContent("workflowContent");
        toolsProjectInfoDto.setTdToolsId(0L);
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setChildIds("childIds");

        final UserDto userDto = new UserDto();
        userDto.setUserName("userName");
        userDto.setUserId(0L);
        userDto.setUserCode("userCode");
        userDto.setLoginName("loginName");

        when(mockToolsOperateBaseService.updateToolsBaseInfo(any(ToolsDto.class), any(UserDto.class))).thenReturn(0L);
        doThrow(ToolsException.class).when(mockToolsOperateBaseService).deleteToolsScriptAnnexedTable(0L);

        // Run the test
        assertThatThrownBy(
                () -> toolsFunctionalBlockServiceImplUnderTest.updateToolsInfo(toolsDto, userDto, 0))
                .isInstanceOf(ToolsException.class);
        verify(mockToolsEverybodyService).deleteToolsEverybodyByIds(0L);

        // Confirm IToolsEverybodyService.batchInsertoolsEverybody(...).
        final AuditEverybodyQueryDto auditEverybodyQueryDto1 = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto1.setId(0L);
        auditEverybodyQueryDto1.setAuditorId(0L);
        auditEverybodyQueryDto1.setAuditorName("auditorName");
        final List<AuditEverybodyQueryDto> auditEverybodyDtoList = Arrays.asList(auditEverybodyQueryDto1);
        verify(mockToolsEverybodyService).batchInsertoolsEverybody(0L, auditEverybodyDtoList);
    }

    @Test
    void testUpdateToolsInfo_IToolsOperateBaseServiceSaveToolsParamBaseInfoThrowsToolsException() throws Exception {
        // Setup
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setName("name");
        toolsDto.setType(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        toolsDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setId(0L);
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowContent("workflowContent");
        toolsProjectInfoDto.setTdToolsId(0L);
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setChildIds("childIds");

        final UserDto userDto = new UserDto();
        userDto.setUserName("userName");
        userDto.setUserId(0L);
        userDto.setUserCode("userCode");
        userDto.setLoginName("loginName");

        when(mockToolsOperateBaseService.updateToolsBaseInfo(any(ToolsDto.class), any(UserDto.class))).thenReturn(0L);

        // Configure IToolsOperateBaseService.saveToolsParamBaseInfo(...).
        final ToolsParamResultDto toolsParamResultDto1 = new ToolsParamResultDto();
        toolsParamResultDto1.setSort(0L);
        toolsParamResultDto1.setName("name");
        toolsParamResultDto1.setType("type");
        toolsParamResultDto1.setValue("value");
        toolsParamResultDto1.setDescription("description");
        final List<ToolsParamResultDto> toolsParamResultList = Arrays.asList(toolsParamResultDto1);
        doThrow(ToolsException.class).when(mockToolsOperateBaseService).saveToolsParamBaseInfo(eq(0L),
                eq(toolsParamResultList), any(UserDto.class));

        // Run the test
        assertThatThrownBy(
                () -> toolsFunctionalBlockServiceImplUnderTest.updateToolsInfo(toolsDto, userDto, 0))
                .isInstanceOf(ToolsException.class);
        verify(mockToolsEverybodyService).deleteToolsEverybodyByIds(0L);

        // Confirm IToolsEverybodyService.batchInsertoolsEverybody(...).
        final AuditEverybodyQueryDto auditEverybodyQueryDto1 = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto1.setId(0L);
        auditEverybodyQueryDto1.setAuditorId(0L);
        auditEverybodyQueryDto1.setAuditorName("auditorName");
        final List<AuditEverybodyQueryDto> auditEverybodyDtoList = Arrays.asList(auditEverybodyQueryDto1);
        verify(mockToolsEverybodyService).batchInsertoolsEverybody(0L, auditEverybodyDtoList);
        verify(mockToolsOperateBaseService).deleteToolsFilesTable(any(ToolsDto.class));
        verify(mockToolsOperateBaseService).deleteToolsScriptAnnexedTable(0L);
    }

    @Test
    void testUpdateToolsInfo_IToolsOperateBaseServiceSaveToolsAgentBaseInfoThrowsToolsException() throws Exception {
        // Setup
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setName("name");
        toolsDto.setType(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        toolsDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setId(0L);
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowContent("workflowContent");
        toolsProjectInfoDto.setTdToolsId(0L);
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setChildIds("childIds");

        final UserDto userDto = new UserDto();
        userDto.setUserName("userName");
        userDto.setUserId(0L);
        userDto.setUserCode("userCode");
        userDto.setLoginName("loginName");

        when(mockToolsOperateBaseService.updateToolsBaseInfo(any(ToolsDto.class), any(UserDto.class))).thenReturn(0L);

        // Configure IToolsOperateBaseService.saveToolsAgentBaseInfo(...).
        final ToolsAgentResultDto toolsAgentResultDto1 = new ToolsAgentResultDto();
        toolsAgentResultDto1.setDeviceName("deviceName");
        toolsAgentResultDto1.setOsName("osName");
        toolsAgentResultDto1.setAgentName("agentName");
        toolsAgentResultDto1.setAgentIp("agentIp");
        toolsAgentResultDto1.setAgentPort(0);
        final List<ToolsAgentResultDto> toolsParamResultList = Arrays.asList(toolsAgentResultDto1);
        doThrow(ToolsException.class).when(mockToolsOperateBaseService).saveToolsAgentBaseInfo(eq(0L),
                eq(toolsParamResultList), any(UserDto.class));

        // Run the test
        assertThatThrownBy(
                () -> toolsFunctionalBlockServiceImplUnderTest.updateToolsInfo(toolsDto, userDto, 0))
                .isInstanceOf(ToolsException.class);
        verify(mockToolsEverybodyService).deleteToolsEverybodyByIds(0L);

        // Confirm IToolsEverybodyService.batchInsertoolsEverybody(...).
        final AuditEverybodyQueryDto auditEverybodyQueryDto1 = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto1.setId(0L);
        auditEverybodyQueryDto1.setAuditorId(0L);
        auditEverybodyQueryDto1.setAuditorName("auditorName");
        final List<AuditEverybodyQueryDto> auditEverybodyDtoList = Arrays.asList(auditEverybodyQueryDto1);
        verify(mockToolsEverybodyService).batchInsertoolsEverybody(0L, auditEverybodyDtoList);
        verify(mockToolsOperateBaseService).deleteToolsFilesTable(any(ToolsDto.class));
        verify(mockToolsOperateBaseService).deleteToolsScriptAnnexedTable(0L);

        // Confirm IToolsOperateBaseService.saveToolsParamBaseInfo(...).
        final ToolsParamResultDto toolsParamResultDto1 = new ToolsParamResultDto();
        toolsParamResultDto1.setSort(0L);
        toolsParamResultDto1.setName("name");
        toolsParamResultDto1.setType("type");
        toolsParamResultDto1.setValue("value");
        toolsParamResultDto1.setDescription("description");
        final List<ToolsParamResultDto> toolsParamResultList1 = Arrays.asList(toolsParamResultDto1);
        verify(mockToolsOperateBaseService).saveToolsParamBaseInfo(eq(0L), eq(toolsParamResultList1),
                any(UserDto.class));
    }

    @Test
    void testToolsDoubleCheckOperate() throws Exception {
        // Setup
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setName("name");
        toolsDto.setType(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        toolsDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setId(0L);
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowContent("workflowContent");
        toolsProjectInfoDto.setTdToolsId(0L);
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setChildIds("childIds");

        final UserDto user = new UserDto();
        user.setUserName("userName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        // Configure IAuditService.saveAuditDoubleCheck(...).
        final AuditDto auditDto = new AuditDto();
        auditDto.setId(0L);
        auditDto.setBusinessId(0L);
        auditDto.setApplyName("applyName");
        auditDto.setAprvWorkitemId(0L);
        auditDto.setApprovalState(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto1 = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto1.setId(0L);
        auditEverybodyQueryDto1.setAuditorId(0L);
        auditEverybodyQueryDto1.setAuditorName("auditorName");
        final List<AuditEverybodyQueryDto> auditEverybodyDtoList = Arrays.asList(auditEverybodyQueryDto1);
        when(mockAuditService.saveAuditDoubleCheck(eq(0L), eq(auditEverybodyDtoList), eq(0),
                any(UserDto.class))).thenReturn(auditDto);

        // Configure IAuditService.sendDoubleCheck(...).
        final AuditEverybodyQueryDto auditEverybodyQueryDto2 = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto2.setId(0L);
        auditEverybodyQueryDto2.setAuditorId(0L);
        auditEverybodyQueryDto2.setAuditorName("auditorName");
        final List<AuditEverybodyQueryDto> auditEverybodyDtoList1 = Arrays.asList(auditEverybodyQueryDto2);
        when(mockAuditService.sendDoubleCheck(any(AuditDto.class), eq(auditEverybodyDtoList1), eq("name")))
                .thenReturn(0L);

        // Configure IToolsOperateBaseService.verificationScript(...).
        final ScriptContentDto scriptContentDto = new ScriptContentDto();
        scriptContentDto.setScriptNameZh("scriptNameZh");
        scriptContentDto.setScriptIds("scriptIds");
        scriptContentDto.setScriptName("scriptName");
        scriptContentDto.setScriptType(0);
        scriptContentDto.setScriptBoolean(false);
        when(mockToolsOperateBaseService.verificationScript(eq("scriptIds"), any(UserDto.class)))
                .thenReturn(scriptContentDto);

        // Run the test
        toolsFunctionalBlockServiceImplUnderTest.toolsDoubleCheckOperate(0L, toolsDto, user);

        // Verify the results
        verify(mockScriptInteract).launchScriptAudit(any(AuditDto.class), any(ToolsDto.class));
    }

    @Test
    void testToolsDoubleCheckOperate_IAuditServiceSaveAuditDoubleCheckReturnsNull() throws Exception {
        // Setup
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setName("name");
        toolsDto.setType(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        toolsDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setId(0L);
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowContent("workflowContent");
        toolsProjectInfoDto.setTdToolsId(0L);
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setChildIds("childIds");

        final UserDto user = new UserDto();
        user.setUserName("userName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        // Configure IAuditService.saveAuditDoubleCheck(...).
        final AuditEverybodyQueryDto auditEverybodyQueryDto1 = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto1.setId(0L);
        auditEverybodyQueryDto1.setAuditorId(0L);
        auditEverybodyQueryDto1.setAuditorName("auditorName");
        final List<AuditEverybodyQueryDto> auditEverybodyDtoList = Arrays.asList(auditEverybodyQueryDto1);
        when(mockAuditService.saveAuditDoubleCheck(eq(0L), eq(auditEverybodyDtoList), eq(0),
                any(UserDto.class))).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> toolsFunctionalBlockServiceImplUnderTest.toolsDoubleCheckOperate(0L, toolsDto,
                user)).isInstanceOf(AuditException.class);
    }

    @Test
    void testToolsDoubleCheckOperate_IAuditServiceSaveAuditDoubleCheckThrowsAuditException() throws Exception {
        // Setup
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setName("name");
        toolsDto.setType(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        toolsDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setId(0L);
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowContent("workflowContent");
        toolsProjectInfoDto.setTdToolsId(0L);
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setChildIds("childIds");

        final UserDto user = new UserDto();
        user.setUserName("userName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        // Configure IAuditService.saveAuditDoubleCheck(...).
        final AuditEverybodyQueryDto auditEverybodyQueryDto1 = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto1.setId(0L);
        auditEverybodyQueryDto1.setAuditorId(0L);
        auditEverybodyQueryDto1.setAuditorName("auditorName");
        final List<AuditEverybodyQueryDto> auditEverybodyDtoList = Arrays.asList(auditEverybodyQueryDto1);
        when(mockAuditService.saveAuditDoubleCheck(eq(0L), eq(auditEverybodyDtoList), eq(0),
                any(UserDto.class))).thenThrow(AuditException.class);

        // Run the test
        assertThatThrownBy(() -> toolsFunctionalBlockServiceImplUnderTest.toolsDoubleCheckOperate(0L, toolsDto,
                user)).isInstanceOf(AuditException.class);
    }

    @Test
    void testToolsDoubleCheckOperate_IAuditServiceSendDoubleCheckThrowsAuditException() throws Exception {
        // Setup
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setName("name");
        toolsDto.setType(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        toolsDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setId(0L);
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowContent("workflowContent");
        toolsProjectInfoDto.setTdToolsId(0L);
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setChildIds("childIds");

        final UserDto user = new UserDto();
        user.setUserName("userName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        // Configure IAuditService.saveAuditDoubleCheck(...).
        final AuditDto auditDto = new AuditDto();
        auditDto.setId(0L);
        auditDto.setBusinessId(0L);
        auditDto.setApplyName("applyName");
        auditDto.setAprvWorkitemId(0L);
        auditDto.setApprovalState(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto1 = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto1.setId(0L);
        auditEverybodyQueryDto1.setAuditorId(0L);
        auditEverybodyQueryDto1.setAuditorName("auditorName");
        final List<AuditEverybodyQueryDto> auditEverybodyDtoList = Arrays.asList(auditEverybodyQueryDto1);
        when(mockAuditService.saveAuditDoubleCheck(eq(0L), eq(auditEverybodyDtoList), eq(0),
                any(UserDto.class))).thenReturn(auditDto);

        // Configure IAuditService.sendDoubleCheck(...).
        final AuditEverybodyQueryDto auditEverybodyQueryDto2 = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto2.setId(0L);
        auditEverybodyQueryDto2.setAuditorId(0L);
        auditEverybodyQueryDto2.setAuditorName("auditorName");
        final List<AuditEverybodyQueryDto> auditEverybodyDtoList1 = Arrays.asList(auditEverybodyQueryDto2);
        when(mockAuditService.sendDoubleCheck(any(AuditDto.class), eq(auditEverybodyDtoList1), eq("name")))
                .thenThrow(AuditException.class);

        // Run the test
        assertThatThrownBy(() -> toolsFunctionalBlockServiceImplUnderTest.toolsDoubleCheckOperate(0L, toolsDto,
                user)).isInstanceOf(AuditException.class);
    }

    @Test
    void testToolsDoubleCheckOperate_ScriptInteractThrowsScriptToolsException() throws Exception {
        // Setup
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setName("name");
        toolsDto.setType(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        toolsDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setId(0L);
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowContent("workflowContent");
        toolsProjectInfoDto.setTdToolsId(0L);
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setChildIds("childIds");

        final UserDto user = new UserDto();
        user.setUserName("userName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        // Configure IAuditService.saveAuditDoubleCheck(...).
        final AuditDto auditDto = new AuditDto();
        auditDto.setId(0L);
        auditDto.setBusinessId(0L);
        auditDto.setApplyName("applyName");
        auditDto.setAprvWorkitemId(0L);
        auditDto.setApprovalState(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto1 = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto1.setId(0L);
        auditEverybodyQueryDto1.setAuditorId(0L);
        auditEverybodyQueryDto1.setAuditorName("auditorName");
        final List<AuditEverybodyQueryDto> auditEverybodyDtoList = Arrays.asList(auditEverybodyQueryDto1);
        when(mockAuditService.saveAuditDoubleCheck(eq(0L), eq(auditEverybodyDtoList), eq(0),
                any(UserDto.class))).thenReturn(auditDto);

        // Configure IAuditService.sendDoubleCheck(...).
        final AuditEverybodyQueryDto auditEverybodyQueryDto2 = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto2.setId(0L);
        auditEverybodyQueryDto2.setAuditorId(0L);
        auditEverybodyQueryDto2.setAuditorName("auditorName");
        final List<AuditEverybodyQueryDto> auditEverybodyDtoList1 = Arrays.asList(auditEverybodyQueryDto2);
        when(mockAuditService.sendDoubleCheck(any(AuditDto.class), eq(auditEverybodyDtoList1), eq("name")))
                .thenReturn(0L);

        // Configure IToolsOperateBaseService.verificationScript(...).
        final ScriptContentDto scriptContentDto = new ScriptContentDto();
        scriptContentDto.setScriptNameZh("scriptNameZh");
        scriptContentDto.setScriptIds("scriptIds");
        scriptContentDto.setScriptName("scriptName");
        scriptContentDto.setScriptType(0);
        scriptContentDto.setScriptBoolean(false);
        when(mockToolsOperateBaseService.verificationScript(eq("scriptIds"), any(UserDto.class)))
                .thenReturn(scriptContentDto);

        doThrow(ScriptToolsException.class).when(mockScriptInteract).launchScriptAudit(any(AuditDto.class),
                any(ToolsDto.class));

        // Run the test
        assertThatThrownBy(() -> toolsFunctionalBlockServiceImplUnderTest.toolsDoubleCheckOperate(0L, toolsDto,
                user)).isInstanceOf(ScriptToolsException.class);
    }

    @Test
    void testEditSaveScriptInfo() throws Exception {
        // Setup
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setName("name");
        toolsDto.setType(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        toolsDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setId(0L);
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowContent("workflowContent");
        toolsProjectInfoDto.setTdToolsId(0L);
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setChildIds("childIds");

        final UserDto userDto = new UserDto();
        userDto.setUserName("userName");
        userDto.setUserId(0L);
        userDto.setUserCode("userCode");
        userDto.setLoginName("loginName");

        // Configure IToolsOperateBaseService.verificationScript(...).
        final ScriptContentDto scriptContentDto = new ScriptContentDto();
        scriptContentDto.setScriptNameZh("scriptNameZh");
        scriptContentDto.setScriptIds("scriptIds");
        scriptContentDto.setScriptName("scriptName");
        scriptContentDto.setScriptType(0);
        scriptContentDto.setScriptBoolean(false);
        when(mockToolsOperateBaseService.verificationScript(eq("scriptIds"), any(UserDto.class)))
                .thenReturn(scriptContentDto);

        when(mockScriptInteract.updateMyScript(any(ToolsDto.class), any(UserDto.class),
                any(ScriptContentDto.class))).thenReturn("scriptIds");
        when(mockScriptInteract.saveMyScript(any(ToolsDto.class), any(UserDto.class),
                any(ScriptContentDto.class))).thenReturn("scriptIds");

        // Run the test
        final ToolsDto result = toolsFunctionalBlockServiceImplUnderTest.editSaveScriptInfo(toolsDto, userDto);

        // Verify the results
    }

    @Test
    void testEditSaveScriptInfo_ScriptInteractUpdateMyScriptThrowsScriptToolsException() throws Exception {
        // Setup
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setName("name");
        toolsDto.setType(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        toolsDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setId(0L);
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowContent("workflowContent");
        toolsProjectInfoDto.setTdToolsId(0L);
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setChildIds("childIds");

        final UserDto userDto = new UserDto();
        userDto.setUserName("userName");
        userDto.setUserId(0L);
        userDto.setUserCode("userCode");
        userDto.setLoginName("loginName");

        // Configure IToolsOperateBaseService.verificationScript(...).
        final ScriptContentDto scriptContentDto = new ScriptContentDto();
        scriptContentDto.setScriptNameZh("scriptNameZh");
        scriptContentDto.setScriptIds("scriptIds");
        scriptContentDto.setScriptName("scriptName");
        scriptContentDto.setScriptType(0);
        scriptContentDto.setScriptBoolean(false);
        when(mockToolsOperateBaseService.verificationScript(eq("scriptIds"), any(UserDto.class)))
                .thenReturn(scriptContentDto);

        when(mockScriptInteract.updateMyScript(any(ToolsDto.class), any(UserDto.class),
                any(ScriptContentDto.class))).thenThrow(ScriptToolsException.class);

        // Run the test
        assertThatThrownBy(
                () -> toolsFunctionalBlockServiceImplUnderTest.editSaveScriptInfo(toolsDto, userDto))
                .isInstanceOf(ScriptToolsException.class);
    }

    @Test
    void testEditSaveScriptInfo_ScriptInteractSaveMyScriptThrowsScriptToolsException() throws Exception {
        // Setup
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setName("name");
        toolsDto.setType(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        toolsDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setId(0L);
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowContent("workflowContent");
        toolsProjectInfoDto.setTdToolsId(0L);
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setChildIds("childIds");

        final UserDto userDto = new UserDto();
        userDto.setUserName("userName");
        userDto.setUserId(0L);
        userDto.setUserCode("userCode");
        userDto.setLoginName("loginName");

        // Configure IToolsOperateBaseService.verificationScript(...).
        final ScriptContentDto scriptContentDto = new ScriptContentDto();
        scriptContentDto.setScriptNameZh("scriptNameZh");
        scriptContentDto.setScriptIds("scriptIds");
        scriptContentDto.setScriptName("scriptName");
        scriptContentDto.setScriptType(0);
        scriptContentDto.setScriptBoolean(false);
        when(mockToolsOperateBaseService.verificationScript(eq("scriptIds"), any(UserDto.class)))
                .thenReturn(scriptContentDto);

        when(mockScriptInteract.saveMyScript(any(ToolsDto.class), any(UserDto.class),
                any(ScriptContentDto.class))).thenThrow(ScriptToolsException.class);

        // Run the test
        assertThatThrownBy(
                () -> toolsFunctionalBlockServiceImplUnderTest.editSaveScriptInfo(toolsDto, userDto))
                .isInstanceOf(ScriptToolsException.class);
    }

    @Test
    void testTwoAuditDto() throws Exception {
        // Setup
        final AuditDto auditDto = new AuditDto();
        auditDto.setId(0L);
        auditDto.setBusinessId(0L);
        auditDto.setApplyName("applyName");
        auditDto.setAprvWorkitemId(0L);
        auditDto.setApprovalState(0);

        final ToolsInfoDto toolsDto = new ToolsInfoDto();
        toolsDto.setId(0L);
        toolsDto.setCode("code");
        toolsDto.setName("name");
        toolsDto.setType(0);
        toolsDto.setClassification(0);

        // Configure IAuditService.selectAuditList(...).
        final AuditDto auditDto1 = new AuditDto();
        auditDto1.setId(0L);
        auditDto1.setBusinessId(0L);
        auditDto1.setApplyName("applyName");
        auditDto1.setAprvWorkitemId(0L);
        auditDto1.setApprovalState(0);
        final List<AuditDto> auditDtos = Arrays.asList(auditDto1);
        when(mockAuditService.selectAuditList(any(AuditQueryDto.class))).thenReturn(auditDtos);

        // Configure IAuditService.selectAuditToolsId(...).
        final AuditEverybodyEntity auditEverybodyEntity = new AuditEverybodyEntity();
        auditEverybodyEntity.setId(0L);
        auditEverybodyEntity.setAuditId(0L);
        auditEverybodyEntity.setAuditorId(0L);
        auditEverybodyEntity.setAuditorName("auditorName");
        final List<AuditEverybodyEntity> auditEverybodyEntities = Arrays.asList(auditEverybodyEntity);
        when(mockAuditService.selectAuditToolsId(0L)).thenReturn(auditEverybodyEntities);

        // Run the test
        toolsFunctionalBlockServiceImplUnderTest.twoAuditDto(auditDto, toolsDto);

        // Verify the results
        // Confirm IAuditService.sendDoubleCheck(...).
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto.setId(0L);
        auditEverybodyQueryDto.setAuditorId(0L);
        auditEverybodyQueryDto.setAuditorName("auditorName");
        final List<AuditEverybodyQueryDto> auditEverybodyDtoList = Arrays.asList(auditEverybodyQueryDto);
        verify(mockAuditService).sendDoubleCheck(any(AuditDto.class), eq(auditEverybodyDtoList), eq("name"));
    }

    @Test
    void testTwoAuditDto_IAuditServiceSelectAuditListReturnsNoItems() {
        // Setup
        final AuditDto auditDto = new AuditDto();
        auditDto.setId(0L);
        auditDto.setBusinessId(0L);
        auditDto.setApplyName("applyName");
        auditDto.setAprvWorkitemId(0L);
        auditDto.setApprovalState(0);

        final ToolsInfoDto toolsDto = new ToolsInfoDto();
        toolsDto.setId(0L);
        toolsDto.setCode("code");
        toolsDto.setName("name");
        toolsDto.setType(0);
        toolsDto.setClassification(0);

        when(mockAuditService.selectAuditList(any(AuditQueryDto.class))).thenReturn(Collections.emptyList());

        // Run the test
        assertThatThrownBy(() -> toolsFunctionalBlockServiceImplUnderTest.twoAuditDto(auditDto, toolsDto))
                .isInstanceOf(AuditException.class);
    }

    @Test
    void testTwoAuditDto_IAuditServiceSelectAuditToolsIdReturnsNoItems() throws Exception {
        // Setup
        final AuditDto auditDto = new AuditDto();
        auditDto.setId(0L);
        auditDto.setBusinessId(0L);
        auditDto.setApplyName("applyName");
        auditDto.setAprvWorkitemId(0L);
        auditDto.setApprovalState(0);

        final ToolsInfoDto toolsDto = new ToolsInfoDto();
        toolsDto.setId(0L);
        toolsDto.setCode("code");
        toolsDto.setName("name");
        toolsDto.setType(0);
        toolsDto.setClassification(0);

        // Configure IAuditService.selectAuditList(...).
        final AuditDto auditDto1 = new AuditDto();
        auditDto1.setId(0L);
        auditDto1.setBusinessId(0L);
        auditDto1.setApplyName("applyName");
        auditDto1.setAprvWorkitemId(0L);
        auditDto1.setApprovalState(0);
        final List<AuditDto> auditDtos = Arrays.asList(auditDto1);
        when(mockAuditService.selectAuditList(any(AuditQueryDto.class))).thenReturn(auditDtos);

        when(mockAuditService.selectAuditToolsId(0L)).thenReturn(Collections.emptyList());

        // Run the test
        toolsFunctionalBlockServiceImplUnderTest.twoAuditDto(auditDto, toolsDto);

        // Verify the results
        // Confirm IAuditService.sendDoubleCheck(...).
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto.setId(0L);
        auditEverybodyQueryDto.setAuditorId(0L);
        auditEverybodyQueryDto.setAuditorName("auditorName");
        final List<AuditEverybodyQueryDto> auditEverybodyDtoList = Arrays.asList(auditEverybodyQueryDto);
        verify(mockAuditService).sendDoubleCheck(any(AuditDto.class), eq(auditEverybodyDtoList), eq("name"));
    }

    @Test
    void testTwoAuditDto_IAuditServiceSendDoubleCheckThrowsAuditException() throws Exception {
        // Setup
        final AuditDto auditDto = new AuditDto();
        auditDto.setId(0L);
        auditDto.setBusinessId(0L);
        auditDto.setApplyName("applyName");
        auditDto.setAprvWorkitemId(0L);
        auditDto.setApprovalState(0);

        final ToolsInfoDto toolsDto = new ToolsInfoDto();
        toolsDto.setId(0L);
        toolsDto.setCode("code");
        toolsDto.setName("name");
        toolsDto.setType(0);
        toolsDto.setClassification(0);

        // Configure IAuditService.selectAuditList(...).
        final AuditDto auditDto1 = new AuditDto();
        auditDto1.setId(0L);
        auditDto1.setBusinessId(0L);
        auditDto1.setApplyName("applyName");
        auditDto1.setAprvWorkitemId(0L);
        auditDto1.setApprovalState(0);
        final List<AuditDto> auditDtos = Arrays.asList(auditDto1);
        when(mockAuditService.selectAuditList(any(AuditQueryDto.class))).thenReturn(auditDtos);

        // Configure IAuditService.selectAuditToolsId(...).
        final AuditEverybodyEntity auditEverybodyEntity = new AuditEverybodyEntity();
        auditEverybodyEntity.setId(0L);
        auditEverybodyEntity.setAuditId(0L);
        auditEverybodyEntity.setAuditorId(0L);
        auditEverybodyEntity.setAuditorName("auditorName");
        final List<AuditEverybodyEntity> auditEverybodyEntities = Arrays.asList(auditEverybodyEntity);
        when(mockAuditService.selectAuditToolsId(0L)).thenReturn(auditEverybodyEntities);

        // Configure IAuditService.sendDoubleCheck(...).
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto.setId(0L);
        auditEverybodyQueryDto.setAuditorId(0L);
        auditEverybodyQueryDto.setAuditorName("auditorName");
        final List<AuditEverybodyQueryDto> auditEverybodyDtoList = Arrays.asList(auditEverybodyQueryDto);
        when(mockAuditService.sendDoubleCheck(any(AuditDto.class), eq(auditEverybodyDtoList), eq("name")))
                .thenThrow(AuditException.class);

        // Run the test
        assertThatThrownBy(() -> toolsFunctionalBlockServiceImplUnderTest.twoAuditDto(auditDto, toolsDto))
                .isInstanceOf(AuditException.class);
    }

    @Test
    void testSaveMoveToolsInfo() throws Exception {
        // Setup
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setName("name");
        toolsDto.setType(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        toolsDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setId(0L);
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowContent("workflowContent");
        toolsProjectInfoDto.setTdToolsId(0L);
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setChildIds("childIds");

        final UserDto userDto = new UserDto();
        userDto.setUserName("userName");
        userDto.setUserId(0L);
        userDto.setUserCode("userCode");
        userDto.setLoginName("loginName");

        when(mockToolsOperateBaseService.saveToolsBaseInfo(any(ToolsDto.class), any(UserDto.class))).thenReturn(0L);
        when(mockBusinessConfig.getToolsEvn()).thenReturn("result");
        when(mockStudioInteract.getWorkflowJson(0L)).thenReturn("workflowContent");
        when(mockToolsOperateBaseService.insertUpdateToolsProject(eq(0L), any(ToolsProjectInfoDto.class),
                any(UserDto.class))).thenReturn(0L);

        // Configure StudioInteract.getWorkflowActivities(...).
        final WorkflowActivitiesDto workflowActivitiesDto = new WorkflowActivitiesDto();
        workflowActivitiesDto.setId(0L);
        workflowActivitiesDto.setStudioProjectId(0L);
        workflowActivitiesDto.setStudioProjectUniqueCode("studioProjectUniqueCode");
        workflowActivitiesDto.setActivityInvokeUuid("activityInvokeUuid");
        workflowActivitiesDto.setActivityInvokeBusinessId(0L);
        final List<WorkflowActivitiesDto> workflowActivitiesDtos = Arrays.asList(workflowActivitiesDto);
        when(mockStudioInteract.getWorkflowActivities(0L)).thenReturn(workflowActivitiesDtos);

        // Run the test
        final ToolsDto result = toolsFunctionalBlockServiceImplUnderTest.saveMoveToolsInfo(toolsDto, userDto);

        // Verify the results
        // Confirm IToolsEverybodyService.batchInsertoolsEverybody(...).
        final AuditEverybodyQueryDto auditEverybodyQueryDto1 = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto1.setId(0L);
        auditEverybodyQueryDto1.setAuditorId(0L);
        auditEverybodyQueryDto1.setAuditorName("auditorName");
        final List<AuditEverybodyQueryDto> auditEverybodyDtoList = Arrays.asList(auditEverybodyQueryDto1);
        verify(mockToolsEverybodyService).batchInsertoolsEverybody(0L, auditEverybodyDtoList);
        verify(mockToolsOperateBaseService).deleteToolsProject(0L);
        verify(mockStudioInteract).projectPublish(any(ToolsProjectInfoDto.class));

        // Confirm IToolsOperateBaseService.saveToolsParamBaseInfo(...).
        final ToolsParamResultDto toolsParamResultDto1 = new ToolsParamResultDto();
        toolsParamResultDto1.setSort(0L);
        toolsParamResultDto1.setName("name");
        toolsParamResultDto1.setType("type");
        toolsParamResultDto1.setValue("value");
        toolsParamResultDto1.setDescription("description");
        final List<ToolsParamResultDto> toolsParamResultList = Arrays.asList(toolsParamResultDto1);
        verify(mockToolsOperateBaseService).saveToolsParamBaseInfo(eq(0L), eq(toolsParamResultList),
                any(UserDto.class));
    }

    @Test
    void testSaveMoveToolsInfo_IToolsOperateBaseServiceSaveToolsBaseInfoThrowsToolsException() throws Exception {
        // Setup
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setName("name");
        toolsDto.setType(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        toolsDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setId(0L);
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowContent("workflowContent");
        toolsProjectInfoDto.setTdToolsId(0L);
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setChildIds("childIds");

        final UserDto userDto = new UserDto();
        userDto.setUserName("userName");
        userDto.setUserId(0L);
        userDto.setUserCode("userCode");
        userDto.setLoginName("loginName");

        when(mockToolsOperateBaseService.saveToolsBaseInfo(any(ToolsDto.class), any(UserDto.class)))
                .thenThrow(ToolsException.class);

        // Run the test
        assertThatThrownBy(
                () -> toolsFunctionalBlockServiceImplUnderTest.saveMoveToolsInfo(toolsDto, userDto))
                .isInstanceOf(ToolsException.class);
    }

    @Test
    void testSaveMoveToolsInfo_IToolsEverybodyServiceThrowsToolsException() throws Exception {
        // Setup
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setName("name");
        toolsDto.setType(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        toolsDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setId(0L);
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowContent("workflowContent");
        toolsProjectInfoDto.setTdToolsId(0L);
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setChildIds("childIds");

        final UserDto userDto = new UserDto();
        userDto.setUserName("userName");
        userDto.setUserId(0L);
        userDto.setUserCode("userCode");
        userDto.setLoginName("loginName");

        when(mockToolsOperateBaseService.saveToolsBaseInfo(any(ToolsDto.class), any(UserDto.class))).thenReturn(0L);
        when(mockBusinessConfig.getToolsEvn()).thenReturn("result");

        // Configure IToolsEverybodyService.batchInsertoolsEverybody(...).
        final AuditEverybodyQueryDto auditEverybodyQueryDto1 = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto1.setId(0L);
        auditEverybodyQueryDto1.setAuditorId(0L);
        auditEverybodyQueryDto1.setAuditorName("auditorName");
        final List<AuditEverybodyQueryDto> auditEverybodyDtoList = Arrays.asList(auditEverybodyQueryDto1);
        doThrow(ToolsException.class).when(mockToolsEverybodyService).batchInsertoolsEverybody(0L,
                auditEverybodyDtoList);

        // Run the test
        assertThatThrownBy(
                () -> toolsFunctionalBlockServiceImplUnderTest.saveMoveToolsInfo(toolsDto, userDto))
                .isInstanceOf(ToolsException.class);
    }

    @Test
    void testSaveMoveToolsInfo_IToolsOperateBaseServiceDeleteToolsProjectThrowsToolsException() throws Exception {
        // Setup
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setName("name");
        toolsDto.setType(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        toolsDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setId(0L);
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowContent("workflowContent");
        toolsProjectInfoDto.setTdToolsId(0L);
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setChildIds("childIds");

        final UserDto userDto = new UserDto();
        userDto.setUserName("userName");
        userDto.setUserId(0L);
        userDto.setUserCode("userCode");
        userDto.setLoginName("loginName");

        when(mockToolsOperateBaseService.saveToolsBaseInfo(any(ToolsDto.class), any(UserDto.class))).thenReturn(0L);
        when(mockBusinessConfig.getToolsEvn()).thenReturn("result");
        doThrow(ToolsException.class).when(mockToolsOperateBaseService).deleteToolsProject(0L);

        // Run the test
        assertThatThrownBy(
                () -> toolsFunctionalBlockServiceImplUnderTest.saveMoveToolsInfo(toolsDto, userDto))
                .isInstanceOf(ToolsException.class);

        // Confirm IToolsEverybodyService.batchInsertoolsEverybody(...).
        final AuditEverybodyQueryDto auditEverybodyQueryDto1 = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto1.setId(0L);
        auditEverybodyQueryDto1.setAuditorId(0L);
        auditEverybodyQueryDto1.setAuditorName("auditorName");
        final List<AuditEverybodyQueryDto> auditEverybodyDtoList = Arrays.asList(auditEverybodyQueryDto1);
        verify(mockToolsEverybodyService).batchInsertoolsEverybody(0L, auditEverybodyDtoList);
    }

    @Test
    void testSaveMoveToolsInfo_StudioInteractGetWorkflowJsonThrowsStudioException() throws Exception {
        // Setup
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setName("name");
        toolsDto.setType(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        toolsDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setId(0L);
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowContent("workflowContent");
        toolsProjectInfoDto.setTdToolsId(0L);
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setChildIds("childIds");

        final UserDto userDto = new UserDto();
        userDto.setUserName("userName");
        userDto.setUserId(0L);
        userDto.setUserCode("userCode");
        userDto.setLoginName("loginName");

        when(mockToolsOperateBaseService.saveToolsBaseInfo(any(ToolsDto.class), any(UserDto.class))).thenReturn(0L);
        when(mockBusinessConfig.getToolsEvn()).thenReturn("result");
        when(mockStudioInteract.getWorkflowJson(0L)).thenThrow(StudioException.class);

        // Run the test
        assertThatThrownBy(
                () -> toolsFunctionalBlockServiceImplUnderTest.saveMoveToolsInfo(toolsDto, userDto))
                .isInstanceOf(ToolsException.class);

        // Confirm IToolsEverybodyService.batchInsertoolsEverybody(...).
        final AuditEverybodyQueryDto auditEverybodyQueryDto1 = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto1.setId(0L);
        auditEverybodyQueryDto1.setAuditorId(0L);
        auditEverybodyQueryDto1.setAuditorName("auditorName");
        final List<AuditEverybodyQueryDto> auditEverybodyDtoList = Arrays.asList(auditEverybodyQueryDto1);
        verify(mockToolsEverybodyService).batchInsertoolsEverybody(0L, auditEverybodyDtoList);
        verify(mockToolsOperateBaseService).deleteToolsProject(0L);
    }

    @Test
    void testSaveMoveToolsInfo_IToolsOperateBaseServiceInsertUpdateToolsProjectThrowsToolsException() throws Exception {
        // Setup
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setName("name");
        toolsDto.setType(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        toolsDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setId(0L);
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowContent("workflowContent");
        toolsProjectInfoDto.setTdToolsId(0L);
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setChildIds("childIds");

        final UserDto userDto = new UserDto();
        userDto.setUserName("userName");
        userDto.setUserId(0L);
        userDto.setUserCode("userCode");
        userDto.setLoginName("loginName");

        when(mockToolsOperateBaseService.saveToolsBaseInfo(any(ToolsDto.class), any(UserDto.class))).thenReturn(0L);
        when(mockBusinessConfig.getToolsEvn()).thenReturn("result");
        when(mockStudioInteract.getWorkflowJson(0L)).thenReturn("workflowContent");
        when(mockToolsOperateBaseService.insertUpdateToolsProject(eq(0L), any(ToolsProjectInfoDto.class),
                any(UserDto.class))).thenThrow(ToolsException.class);

        // Run the test
        assertThatThrownBy(
                () -> toolsFunctionalBlockServiceImplUnderTest.saveMoveToolsInfo(toolsDto, userDto))
                .isInstanceOf(ToolsException.class);

        // Confirm IToolsEverybodyService.batchInsertoolsEverybody(...).
        final AuditEverybodyQueryDto auditEverybodyQueryDto1 = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto1.setId(0L);
        auditEverybodyQueryDto1.setAuditorId(0L);
        auditEverybodyQueryDto1.setAuditorName("auditorName");
        final List<AuditEverybodyQueryDto> auditEverybodyDtoList = Arrays.asList(auditEverybodyQueryDto1);
        verify(mockToolsEverybodyService).batchInsertoolsEverybody(0L, auditEverybodyDtoList);
        verify(mockToolsOperateBaseService).deleteToolsProject(0L);
    }

    @Test
    void testSaveMoveToolsInfo_StudioInteractProjectPublishThrowsStudioException() throws Exception {
        // Setup
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setName("name");
        toolsDto.setType(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        toolsDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setId(0L);
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowContent("workflowContent");
        toolsProjectInfoDto.setTdToolsId(0L);
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setChildIds("childIds");

        final UserDto userDto = new UserDto();
        userDto.setUserName("userName");
        userDto.setUserId(0L);
        userDto.setUserCode("userCode");
        userDto.setLoginName("loginName");

        when(mockToolsOperateBaseService.saveToolsBaseInfo(any(ToolsDto.class), any(UserDto.class))).thenReturn(0L);
        when(mockBusinessConfig.getToolsEvn()).thenReturn("result");
        when(mockStudioInteract.getWorkflowJson(0L)).thenReturn("workflowContent");
        when(mockToolsOperateBaseService.insertUpdateToolsProject(eq(0L), any(ToolsProjectInfoDto.class),
                any(UserDto.class))).thenReturn(0L);
        when(mockStudioInteract.projectPublish(any(ToolsProjectInfoDto.class))).thenThrow(StudioException.class);

        // Run the test
        assertThatThrownBy(
                () -> toolsFunctionalBlockServiceImplUnderTest.saveMoveToolsInfo(toolsDto, userDto))
                .isInstanceOf(StudioException.class);

        // Confirm IToolsEverybodyService.batchInsertoolsEverybody(...).
        final AuditEverybodyQueryDto auditEverybodyQueryDto1 = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto1.setId(0L);
        auditEverybodyQueryDto1.setAuditorId(0L);
        auditEverybodyQueryDto1.setAuditorName("auditorName");
        final List<AuditEverybodyQueryDto> auditEverybodyDtoList = Arrays.asList(auditEverybodyQueryDto1);
        verify(mockToolsEverybodyService).batchInsertoolsEverybody(0L, auditEverybodyDtoList);
        verify(mockToolsOperateBaseService).deleteToolsProject(0L);
    }

    @Test
    void testSaveMoveToolsInfo_StudioInteractGetWorkflowActivitiesReturnsNoItems() throws Exception {
        // Setup
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setName("name");
        toolsDto.setType(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        toolsDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setId(0L);
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowContent("workflowContent");
        toolsProjectInfoDto.setTdToolsId(0L);
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setChildIds("childIds");

        final UserDto userDto = new UserDto();
        userDto.setUserName("userName");
        userDto.setUserId(0L);
        userDto.setUserCode("userCode");
        userDto.setLoginName("loginName");

        when(mockToolsOperateBaseService.saveToolsBaseInfo(any(ToolsDto.class), any(UserDto.class))).thenReturn(0L);
        when(mockBusinessConfig.getToolsEvn()).thenReturn("result");
        when(mockStudioInteract.getWorkflowJson(0L)).thenReturn("workflowContent");
        when(mockToolsOperateBaseService.insertUpdateToolsProject(eq(0L), any(ToolsProjectInfoDto.class),
                any(UserDto.class))).thenReturn(0L);
        when(mockStudioInteract.getWorkflowActivities(0L)).thenReturn(Collections.emptyList());

        // Run the test
        final ToolsDto result = toolsFunctionalBlockServiceImplUnderTest.saveMoveToolsInfo(toolsDto, userDto);

        // Verify the results
        // Confirm IToolsEverybodyService.batchInsertoolsEverybody(...).
        final AuditEverybodyQueryDto auditEverybodyQueryDto1 = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto1.setId(0L);
        auditEverybodyQueryDto1.setAuditorId(0L);
        auditEverybodyQueryDto1.setAuditorName("auditorName");
        final List<AuditEverybodyQueryDto> auditEverybodyDtoList = Arrays.asList(auditEverybodyQueryDto1);
        verify(mockToolsEverybodyService).batchInsertoolsEverybody(0L, auditEverybodyDtoList);
        verify(mockToolsOperateBaseService).deleteToolsProject(0L);
        verify(mockStudioInteract).projectPublish(any(ToolsProjectInfoDto.class));

        // Confirm IToolsOperateBaseService.saveToolsParamBaseInfo(...).
        final ToolsParamResultDto toolsParamResultDto1 = new ToolsParamResultDto();
        toolsParamResultDto1.setSort(0L);
        toolsParamResultDto1.setName("name");
        toolsParamResultDto1.setType("type");
        toolsParamResultDto1.setValue("value");
        toolsParamResultDto1.setDescription("description");
        final List<ToolsParamResultDto> toolsParamResultList = Arrays.asList(toolsParamResultDto1);
        verify(mockToolsOperateBaseService).saveToolsParamBaseInfo(eq(0L), eq(toolsParamResultList),
                any(UserDto.class));
    }

    @Test
    void testSaveMoveToolsInfo_IToolsOperateBaseServiceSaveToolsParamBaseInfoThrowsToolsException() throws Exception {
        // Setup
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setName("name");
        toolsDto.setType(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        toolsDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setId(0L);
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowContent("workflowContent");
        toolsProjectInfoDto.setTdToolsId(0L);
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setChildIds("childIds");

        final UserDto userDto = new UserDto();
        userDto.setUserName("userName");
        userDto.setUserId(0L);
        userDto.setUserCode("userCode");
        userDto.setLoginName("loginName");

        when(mockToolsOperateBaseService.saveToolsBaseInfo(any(ToolsDto.class), any(UserDto.class))).thenReturn(0L);
        when(mockBusinessConfig.getToolsEvn()).thenReturn("result");
        when(mockStudioInteract.getWorkflowJson(0L)).thenReturn("workflowContent");
        when(mockToolsOperateBaseService.insertUpdateToolsProject(eq(0L), any(ToolsProjectInfoDto.class),
                any(UserDto.class))).thenReturn(0L);

        // Configure StudioInteract.getWorkflowActivities(...).
        final WorkflowActivitiesDto workflowActivitiesDto = new WorkflowActivitiesDto();
        workflowActivitiesDto.setId(0L);
        workflowActivitiesDto.setStudioProjectId(0L);
        workflowActivitiesDto.setStudioProjectUniqueCode("studioProjectUniqueCode");
        workflowActivitiesDto.setActivityInvokeUuid("activityInvokeUuid");
        workflowActivitiesDto.setActivityInvokeBusinessId(0L);
        final List<WorkflowActivitiesDto> workflowActivitiesDtos = Arrays.asList(workflowActivitiesDto);
        when(mockStudioInteract.getWorkflowActivities(0L)).thenReturn(workflowActivitiesDtos);

        // Configure IToolsOperateBaseService.saveToolsParamBaseInfo(...).
        final ToolsParamResultDto toolsParamResultDto1 = new ToolsParamResultDto();
        toolsParamResultDto1.setSort(0L);
        toolsParamResultDto1.setName("name");
        toolsParamResultDto1.setType("type");
        toolsParamResultDto1.setValue("value");
        toolsParamResultDto1.setDescription("description");
        final List<ToolsParamResultDto> toolsParamResultList = Arrays.asList(toolsParamResultDto1);
        doThrow(ToolsException.class).when(mockToolsOperateBaseService).saveToolsParamBaseInfo(eq(0L),
                eq(toolsParamResultList), any(UserDto.class));

        // Run the test
        assertThatThrownBy(
                () -> toolsFunctionalBlockServiceImplUnderTest.saveMoveToolsInfo(toolsDto, userDto))
                .isInstanceOf(ToolsException.class);

        // Confirm IToolsEverybodyService.batchInsertoolsEverybody(...).
        final AuditEverybodyQueryDto auditEverybodyQueryDto1 = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto1.setId(0L);
        auditEverybodyQueryDto1.setAuditorId(0L);
        auditEverybodyQueryDto1.setAuditorName("auditorName");
        final List<AuditEverybodyQueryDto> auditEverybodyDtoList = Arrays.asList(auditEverybodyQueryDto1);
        verify(mockToolsEverybodyService).batchInsertoolsEverybody(0L, auditEverybodyDtoList);
        verify(mockToolsOperateBaseService).deleteToolsProject(0L);
        verify(mockStudioInteract).projectPublish(any(ToolsProjectInfoDto.class));
    }

    @Test
    void testUpdateMoveToolsInfo() throws Exception {
        // Setup
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setName("name");
        toolsDto.setType(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        toolsDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setId(0L);
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowContent("workflowContent");
        toolsProjectInfoDto.setTdToolsId(0L);
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setChildIds("childIds");

        final UserDto userDto = new UserDto();
        userDto.setUserName("userName");
        userDto.setUserId(0L);
        userDto.setUserCode("userCode");
        userDto.setLoginName("loginName");

        when(mockToolsOperateBaseService.updateToolsBaseInfo(any(ToolsDto.class), any(UserDto.class))).thenReturn(0L);
        when(mockBusinessConfig.getToolsEvn()).thenReturn("result");
        when(mockStudioInteract.getWorkflowJson(0L)).thenReturn("workflowContent");
        when(mockToolsOperateBaseService.insertUpdateToolsProject(eq(0L), any(ToolsProjectInfoDto.class),
                any(UserDto.class))).thenReturn(0L);

        // Configure StudioInteract.getWorkflowActivities(...).
        final WorkflowActivitiesDto workflowActivitiesDto = new WorkflowActivitiesDto();
        workflowActivitiesDto.setId(0L);
        workflowActivitiesDto.setStudioProjectId(0L);
        workflowActivitiesDto.setStudioProjectUniqueCode("studioProjectUniqueCode");
        workflowActivitiesDto.setActivityInvokeUuid("activityInvokeUuid");
        workflowActivitiesDto.setActivityInvokeBusinessId(0L);
        final List<WorkflowActivitiesDto> workflowActivitiesDtos = Arrays.asList(workflowActivitiesDto);
        when(mockStudioInteract.getWorkflowActivities(0L)).thenReturn(workflowActivitiesDtos);

        // Run the test
        final ToolsDto result = toolsFunctionalBlockServiceImplUnderTest.updateMoveToolsInfo(toolsDto, userDto);

        // Verify the results
        verify(mockToolsEverybodyService).deleteToolsEverybodyByIds(0L);

        // Confirm IToolsEverybodyService.batchInsertoolsEverybody(...).
        final AuditEverybodyQueryDto auditEverybodyQueryDto1 = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto1.setId(0L);
        auditEverybodyQueryDto1.setAuditorId(0L);
        auditEverybodyQueryDto1.setAuditorName("auditorName");
        final List<AuditEverybodyQueryDto> auditEverybodyDtoList = Arrays.asList(auditEverybodyQueryDto1);
        verify(mockToolsEverybodyService).batchInsertoolsEverybody(0L, auditEverybodyDtoList);
        verify(mockToolsOperateBaseService).deleteToolsProject(0L);
        verify(mockStudioInteract).projectPublish(any(ToolsProjectInfoDto.class));
        verify(mockToolsOperateBaseService).deleteToolsScriptAnnexedTableForMove(0L);

        // Confirm IToolsOperateBaseService.saveToolsParamBaseInfo(...).
        final ToolsParamResultDto toolsParamResultDto1 = new ToolsParamResultDto();
        toolsParamResultDto1.setSort(0L);
        toolsParamResultDto1.setName("name");
        toolsParamResultDto1.setType("type");
        toolsParamResultDto1.setValue("value");
        toolsParamResultDto1.setDescription("description");
        final List<ToolsParamResultDto> toolsParamResultList = Arrays.asList(toolsParamResultDto1);
        verify(mockToolsOperateBaseService).saveToolsParamBaseInfo(eq(0L), eq(toolsParamResultList),
                any(UserDto.class));
    }

    @Test
    void testUpdateMoveToolsInfo_IToolsOperateBaseServiceUpdateToolsBaseInfoThrowsToolsException() throws Exception {
        // Setup
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setName("name");
        toolsDto.setType(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        toolsDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setId(0L);
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowContent("workflowContent");
        toolsProjectInfoDto.setTdToolsId(0L);
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setChildIds("childIds");

        final UserDto userDto = new UserDto();
        userDto.setUserName("userName");
        userDto.setUserId(0L);
        userDto.setUserCode("userCode");
        userDto.setLoginName("loginName");

        when(mockToolsOperateBaseService.updateToolsBaseInfo(any(ToolsDto.class), any(UserDto.class)))
                .thenThrow(ToolsException.class);

        // Run the test
        assertThatThrownBy(
                () -> toolsFunctionalBlockServiceImplUnderTest.updateMoveToolsInfo(toolsDto, userDto))
                .isInstanceOf(ToolsException.class);
    }

    @Test
    void testUpdateMoveToolsInfo_IToolsEverybodyServiceBatchInsertoolsEverybodyThrowsToolsException() throws Exception {
        // Setup
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setName("name");
        toolsDto.setType(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        toolsDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setId(0L);
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowContent("workflowContent");
        toolsProjectInfoDto.setTdToolsId(0L);
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setChildIds("childIds");

        final UserDto userDto = new UserDto();
        userDto.setUserName("userName");
        userDto.setUserId(0L);
        userDto.setUserCode("userCode");
        userDto.setLoginName("loginName");

        when(mockToolsOperateBaseService.updateToolsBaseInfo(any(ToolsDto.class), any(UserDto.class))).thenReturn(0L);
        when(mockBusinessConfig.getToolsEvn()).thenReturn("result");

        // Configure IToolsEverybodyService.batchInsertoolsEverybody(...).
        final AuditEverybodyQueryDto auditEverybodyQueryDto1 = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto1.setId(0L);
        auditEverybodyQueryDto1.setAuditorId(0L);
        auditEverybodyQueryDto1.setAuditorName("auditorName");
        final List<AuditEverybodyQueryDto> auditEverybodyDtoList = Arrays.asList(auditEverybodyQueryDto1);
        doThrow(ToolsException.class).when(mockToolsEverybodyService).batchInsertoolsEverybody(0L,
                auditEverybodyDtoList);

        // Run the test
        assertThatThrownBy(
                () -> toolsFunctionalBlockServiceImplUnderTest.updateMoveToolsInfo(toolsDto, userDto))
                .isInstanceOf(ToolsException.class);
        verify(mockToolsEverybodyService).deleteToolsEverybodyByIds(0L);
    }

    @Test
    void testUpdateMoveToolsInfo_IToolsOperateBaseServiceDeleteToolsProjectThrowsToolsException() throws Exception {
        // Setup
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setName("name");
        toolsDto.setType(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        toolsDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setId(0L);
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowContent("workflowContent");
        toolsProjectInfoDto.setTdToolsId(0L);
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setChildIds("childIds");

        final UserDto userDto = new UserDto();
        userDto.setUserName("userName");
        userDto.setUserId(0L);
        userDto.setUserCode("userCode");
        userDto.setLoginName("loginName");

        when(mockToolsOperateBaseService.updateToolsBaseInfo(any(ToolsDto.class), any(UserDto.class))).thenReturn(0L);
        when(mockBusinessConfig.getToolsEvn()).thenReturn("result");
        doThrow(ToolsException.class).when(mockToolsOperateBaseService).deleteToolsProject(0L);

        // Run the test
        assertThatThrownBy(
                () -> toolsFunctionalBlockServiceImplUnderTest.updateMoveToolsInfo(toolsDto, userDto))
                .isInstanceOf(ToolsException.class);
        verify(mockToolsEverybodyService).deleteToolsEverybodyByIds(0L);

        // Confirm IToolsEverybodyService.batchInsertoolsEverybody(...).
        final AuditEverybodyQueryDto auditEverybodyQueryDto1 = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto1.setId(0L);
        auditEverybodyQueryDto1.setAuditorId(0L);
        auditEverybodyQueryDto1.setAuditorName("auditorName");
        final List<AuditEverybodyQueryDto> auditEverybodyDtoList = Arrays.asList(auditEverybodyQueryDto1);
        verify(mockToolsEverybodyService).batchInsertoolsEverybody(0L, auditEverybodyDtoList);
    }

    @Test
    void testUpdateMoveToolsInfo_StudioInteractGetWorkflowJsonThrowsStudioException() throws Exception {
        // Setup
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setName("name");
        toolsDto.setType(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        toolsDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setId(0L);
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowContent("workflowContent");
        toolsProjectInfoDto.setTdToolsId(0L);
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setChildIds("childIds");

        final UserDto userDto = new UserDto();
        userDto.setUserName("userName");
        userDto.setUserId(0L);
        userDto.setUserCode("userCode");
        userDto.setLoginName("loginName");

        when(mockToolsOperateBaseService.updateToolsBaseInfo(any(ToolsDto.class), any(UserDto.class))).thenReturn(0L);
        when(mockBusinessConfig.getToolsEvn()).thenReturn("result");
        when(mockStudioInteract.getWorkflowJson(0L)).thenThrow(StudioException.class);
        when(mockToolsOperateBaseService.insertUpdateToolsProject(eq(0L), any(ToolsProjectInfoDto.class),
                any(UserDto.class))).thenReturn(0L);

        // Configure StudioInteract.getWorkflowActivities(...).
        final WorkflowActivitiesDto workflowActivitiesDto = new WorkflowActivitiesDto();
        workflowActivitiesDto.setId(0L);
        workflowActivitiesDto.setStudioProjectId(0L);
        workflowActivitiesDto.setStudioProjectUniqueCode("studioProjectUniqueCode");
        workflowActivitiesDto.setActivityInvokeUuid("activityInvokeUuid");
        workflowActivitiesDto.setActivityInvokeBusinessId(0L);
        final List<WorkflowActivitiesDto> workflowActivitiesDtos = Arrays.asList(workflowActivitiesDto);
        when(mockStudioInteract.getWorkflowActivities(0L)).thenReturn(workflowActivitiesDtos);

        // Run the test
        final ToolsDto result = toolsFunctionalBlockServiceImplUnderTest.updateMoveToolsInfo(toolsDto, userDto);

        // Verify the results
        verify(mockToolsEverybodyService).deleteToolsEverybodyByIds(0L);

        // Confirm IToolsEverybodyService.batchInsertoolsEverybody(...).
        final AuditEverybodyQueryDto auditEverybodyQueryDto1 = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto1.setId(0L);
        auditEverybodyQueryDto1.setAuditorId(0L);
        auditEverybodyQueryDto1.setAuditorName("auditorName");
        final List<AuditEverybodyQueryDto> auditEverybodyDtoList = Arrays.asList(auditEverybodyQueryDto1);
        verify(mockToolsEverybodyService).batchInsertoolsEverybody(0L, auditEverybodyDtoList);
        verify(mockToolsOperateBaseService).deleteToolsProject(0L);
        verify(mockStudioInteract).projectPublish(any(ToolsProjectInfoDto.class));
        verify(mockToolsOperateBaseService).deleteToolsScriptAnnexedTableForMove(0L);

        // Confirm IToolsOperateBaseService.saveToolsParamBaseInfo(...).
        final ToolsParamResultDto toolsParamResultDto1 = new ToolsParamResultDto();
        toolsParamResultDto1.setSort(0L);
        toolsParamResultDto1.setName("name");
        toolsParamResultDto1.setType("type");
        toolsParamResultDto1.setValue("value");
        toolsParamResultDto1.setDescription("description");
        final List<ToolsParamResultDto> toolsParamResultList = Arrays.asList(toolsParamResultDto1);
        verify(mockToolsOperateBaseService).saveToolsParamBaseInfo(eq(0L), eq(toolsParamResultList),
                any(UserDto.class));
    }

    @Test
    void testUpdateMoveToolsInfo_IToolsOperateBaseServiceInsertUpdateToolsProjectThrowsToolsException() throws Exception {
        // Setup
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setName("name");
        toolsDto.setType(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        toolsDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setId(0L);
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowContent("workflowContent");
        toolsProjectInfoDto.setTdToolsId(0L);
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setChildIds("childIds");

        final UserDto userDto = new UserDto();
        userDto.setUserName("userName");
        userDto.setUserId(0L);
        userDto.setUserCode("userCode");
        userDto.setLoginName("loginName");

        when(mockToolsOperateBaseService.updateToolsBaseInfo(any(ToolsDto.class), any(UserDto.class))).thenReturn(0L);
        when(mockBusinessConfig.getToolsEvn()).thenReturn("result");
        when(mockStudioInteract.getWorkflowJson(0L)).thenReturn("workflowContent");
        when(mockToolsOperateBaseService.insertUpdateToolsProject(eq(0L), any(ToolsProjectInfoDto.class),
                any(UserDto.class))).thenThrow(ToolsException.class);

        // Run the test
        assertThatThrownBy(
                () -> toolsFunctionalBlockServiceImplUnderTest.updateMoveToolsInfo(toolsDto, userDto))
                .isInstanceOf(ToolsException.class);
        verify(mockToolsEverybodyService).deleteToolsEverybodyByIds(0L);

        // Confirm IToolsEverybodyService.batchInsertoolsEverybody(...).
        final AuditEverybodyQueryDto auditEverybodyQueryDto1 = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto1.setId(0L);
        auditEverybodyQueryDto1.setAuditorId(0L);
        auditEverybodyQueryDto1.setAuditorName("auditorName");
        final List<AuditEverybodyQueryDto> auditEverybodyDtoList = Arrays.asList(auditEverybodyQueryDto1);
        verify(mockToolsEverybodyService).batchInsertoolsEverybody(0L, auditEverybodyDtoList);
        verify(mockToolsOperateBaseService).deleteToolsProject(0L);
    }

    @Test
    void testUpdateMoveToolsInfo_StudioInteractProjectPublishThrowsStudioException() throws Exception {
        // Setup
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setName("name");
        toolsDto.setType(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        toolsDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setId(0L);
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowContent("workflowContent");
        toolsProjectInfoDto.setTdToolsId(0L);
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setChildIds("childIds");

        final UserDto userDto = new UserDto();
        userDto.setUserName("userName");
        userDto.setUserId(0L);
        userDto.setUserCode("userCode");
        userDto.setLoginName("loginName");

        when(mockToolsOperateBaseService.updateToolsBaseInfo(any(ToolsDto.class), any(UserDto.class))).thenReturn(0L);
        when(mockBusinessConfig.getToolsEvn()).thenReturn("result");
        when(mockStudioInteract.getWorkflowJson(0L)).thenReturn("workflowContent");
        when(mockToolsOperateBaseService.insertUpdateToolsProject(eq(0L), any(ToolsProjectInfoDto.class),
                any(UserDto.class))).thenReturn(0L);
        when(mockStudioInteract.projectPublish(any(ToolsProjectInfoDto.class))).thenThrow(StudioException.class);

        // Run the test
        assertThatThrownBy(
                () -> toolsFunctionalBlockServiceImplUnderTest.updateMoveToolsInfo(toolsDto, userDto))
                .isInstanceOf(StudioException.class);
        verify(mockToolsEverybodyService).deleteToolsEverybodyByIds(0L);

        // Confirm IToolsEverybodyService.batchInsertoolsEverybody(...).
        final AuditEverybodyQueryDto auditEverybodyQueryDto1 = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto1.setId(0L);
        auditEverybodyQueryDto1.setAuditorId(0L);
        auditEverybodyQueryDto1.setAuditorName("auditorName");
        final List<AuditEverybodyQueryDto> auditEverybodyDtoList = Arrays.asList(auditEverybodyQueryDto1);
        verify(mockToolsEverybodyService).batchInsertoolsEverybody(0L, auditEverybodyDtoList);
        verify(mockToolsOperateBaseService).deleteToolsProject(0L);
    }

    @Test
    void testUpdateMoveToolsInfo_StudioInteractGetWorkflowActivitiesReturnsNoItems() throws Exception {
        // Setup
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setName("name");
        toolsDto.setType(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        toolsDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setId(0L);
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowContent("workflowContent");
        toolsProjectInfoDto.setTdToolsId(0L);
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setChildIds("childIds");

        final UserDto userDto = new UserDto();
        userDto.setUserName("userName");
        userDto.setUserId(0L);
        userDto.setUserCode("userCode");
        userDto.setLoginName("loginName");

        when(mockToolsOperateBaseService.updateToolsBaseInfo(any(ToolsDto.class), any(UserDto.class))).thenReturn(0L);
        when(mockBusinessConfig.getToolsEvn()).thenReturn("result");
        when(mockStudioInteract.getWorkflowJson(0L)).thenReturn("workflowContent");
        when(mockToolsOperateBaseService.insertUpdateToolsProject(eq(0L), any(ToolsProjectInfoDto.class),
                any(UserDto.class))).thenReturn(0L);
        when(mockStudioInteract.getWorkflowActivities(0L)).thenReturn(Collections.emptyList());

        // Run the test
        final ToolsDto result = toolsFunctionalBlockServiceImplUnderTest.updateMoveToolsInfo(toolsDto, userDto);

        // Verify the results
        verify(mockToolsEverybodyService).deleteToolsEverybodyByIds(0L);

        // Confirm IToolsEverybodyService.batchInsertoolsEverybody(...).
        final AuditEverybodyQueryDto auditEverybodyQueryDto1 = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto1.setId(0L);
        auditEverybodyQueryDto1.setAuditorId(0L);
        auditEverybodyQueryDto1.setAuditorName("auditorName");
        final List<AuditEverybodyQueryDto> auditEverybodyDtoList = Arrays.asList(auditEverybodyQueryDto1);
        verify(mockToolsEverybodyService).batchInsertoolsEverybody(0L, auditEverybodyDtoList);
        verify(mockToolsOperateBaseService).deleteToolsProject(0L);
        verify(mockStudioInteract).projectPublish(any(ToolsProjectInfoDto.class));
        verify(mockToolsOperateBaseService).deleteToolsScriptAnnexedTableForMove(0L);

        // Confirm IToolsOperateBaseService.saveToolsParamBaseInfo(...).
        final ToolsParamResultDto toolsParamResultDto1 = new ToolsParamResultDto();
        toolsParamResultDto1.setSort(0L);
        toolsParamResultDto1.setName("name");
        toolsParamResultDto1.setType("type");
        toolsParamResultDto1.setValue("value");
        toolsParamResultDto1.setDescription("description");
        final List<ToolsParamResultDto> toolsParamResultList = Arrays.asList(toolsParamResultDto1);
        verify(mockToolsOperateBaseService).saveToolsParamBaseInfo(eq(0L), eq(toolsParamResultList),
                any(UserDto.class));
    }

    @Test
    void testUpdateMoveToolsInfo_IToolsOperateBaseServiceDeleteToolsScriptAnnexedTableForMoveThrowsToolsException() throws Exception {
        // Setup
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setName("name");
        toolsDto.setType(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        toolsDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setId(0L);
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowContent("workflowContent");
        toolsProjectInfoDto.setTdToolsId(0L);
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setChildIds("childIds");

        final UserDto userDto = new UserDto();
        userDto.setUserName("userName");
        userDto.setUserId(0L);
        userDto.setUserCode("userCode");
        userDto.setLoginName("loginName");

        when(mockToolsOperateBaseService.updateToolsBaseInfo(any(ToolsDto.class), any(UserDto.class))).thenReturn(0L);
        when(mockBusinessConfig.getToolsEvn()).thenReturn("result");
        when(mockStudioInteract.getWorkflowJson(0L)).thenReturn("workflowContent");
        when(mockToolsOperateBaseService.insertUpdateToolsProject(eq(0L), any(ToolsProjectInfoDto.class),
                any(UserDto.class))).thenReturn(0L);

        // Configure StudioInteract.getWorkflowActivities(...).
        final WorkflowActivitiesDto workflowActivitiesDto = new WorkflowActivitiesDto();
        workflowActivitiesDto.setId(0L);
        workflowActivitiesDto.setStudioProjectId(0L);
        workflowActivitiesDto.setStudioProjectUniqueCode("studioProjectUniqueCode");
        workflowActivitiesDto.setActivityInvokeUuid("activityInvokeUuid");
        workflowActivitiesDto.setActivityInvokeBusinessId(0L);
        final List<WorkflowActivitiesDto> workflowActivitiesDtos = Arrays.asList(workflowActivitiesDto);
        when(mockStudioInteract.getWorkflowActivities(0L)).thenReturn(workflowActivitiesDtos);

        doThrow(ToolsException.class).when(mockToolsOperateBaseService).deleteToolsScriptAnnexedTableForMove(0L);

        // Run the test
        assertThatThrownBy(
                () -> toolsFunctionalBlockServiceImplUnderTest.updateMoveToolsInfo(toolsDto, userDto))
                .isInstanceOf(ToolsException.class);
        verify(mockToolsEverybodyService).deleteToolsEverybodyByIds(0L);

        // Confirm IToolsEverybodyService.batchInsertoolsEverybody(...).
        final AuditEverybodyQueryDto auditEverybodyQueryDto1 = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto1.setId(0L);
        auditEverybodyQueryDto1.setAuditorId(0L);
        auditEverybodyQueryDto1.setAuditorName("auditorName");
        final List<AuditEverybodyQueryDto> auditEverybodyDtoList = Arrays.asList(auditEverybodyQueryDto1);
        verify(mockToolsEverybodyService).batchInsertoolsEverybody(0L, auditEverybodyDtoList);
        verify(mockToolsOperateBaseService).deleteToolsProject(0L);
        verify(mockStudioInteract).projectPublish(any(ToolsProjectInfoDto.class));
    }

    @Test
    void testUpdateMoveToolsInfo_IToolsOperateBaseServiceSaveToolsParamBaseInfoThrowsToolsException() throws Exception {
        // Setup
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setName("name");
        toolsDto.setType(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        toolsDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setId(0L);
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowContent("workflowContent");
        toolsProjectInfoDto.setTdToolsId(0L);
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setChildIds("childIds");

        final UserDto userDto = new UserDto();
        userDto.setUserName("userName");
        userDto.setUserId(0L);
        userDto.setUserCode("userCode");
        userDto.setLoginName("loginName");

        when(mockToolsOperateBaseService.updateToolsBaseInfo(any(ToolsDto.class), any(UserDto.class))).thenReturn(0L);
        when(mockBusinessConfig.getToolsEvn()).thenReturn("result");
        when(mockStudioInteract.getWorkflowJson(0L)).thenReturn("workflowContent");
        when(mockToolsOperateBaseService.insertUpdateToolsProject(eq(0L), any(ToolsProjectInfoDto.class),
                any(UserDto.class))).thenReturn(0L);

        // Configure StudioInteract.getWorkflowActivities(...).
        final WorkflowActivitiesDto workflowActivitiesDto = new WorkflowActivitiesDto();
        workflowActivitiesDto.setId(0L);
        workflowActivitiesDto.setStudioProjectId(0L);
        workflowActivitiesDto.setStudioProjectUniqueCode("studioProjectUniqueCode");
        workflowActivitiesDto.setActivityInvokeUuid("activityInvokeUuid");
        workflowActivitiesDto.setActivityInvokeBusinessId(0L);
        final List<WorkflowActivitiesDto> workflowActivitiesDtos = Arrays.asList(workflowActivitiesDto);
        when(mockStudioInteract.getWorkflowActivities(0L)).thenReturn(workflowActivitiesDtos);

        // Configure IToolsOperateBaseService.saveToolsParamBaseInfo(...).
        final ToolsParamResultDto toolsParamResultDto1 = new ToolsParamResultDto();
        toolsParamResultDto1.setSort(0L);
        toolsParamResultDto1.setName("name");
        toolsParamResultDto1.setType("type");
        toolsParamResultDto1.setValue("value");
        toolsParamResultDto1.setDescription("description");
        final List<ToolsParamResultDto> toolsParamResultList = Arrays.asList(toolsParamResultDto1);
        doThrow(ToolsException.class).when(mockToolsOperateBaseService).saveToolsParamBaseInfo(eq(0L),
                eq(toolsParamResultList), any(UserDto.class));

        // Run the test
        assertThatThrownBy(
                () -> toolsFunctionalBlockServiceImplUnderTest.updateMoveToolsInfo(toolsDto, userDto))
                .isInstanceOf(ToolsException.class);
        verify(mockToolsEverybodyService).deleteToolsEverybodyByIds(0L);

        // Confirm IToolsEverybodyService.batchInsertoolsEverybody(...).
        final AuditEverybodyQueryDto auditEverybodyQueryDto1 = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto1.setId(0L);
        auditEverybodyQueryDto1.setAuditorId(0L);
        auditEverybodyQueryDto1.setAuditorName("auditorName");
        final List<AuditEverybodyQueryDto> auditEverybodyDtoList = Arrays.asList(auditEverybodyQueryDto1);
        verify(mockToolsEverybodyService).batchInsertoolsEverybody(0L, auditEverybodyDtoList);
        verify(mockToolsOperateBaseService).deleteToolsProject(0L);
        verify(mockStudioInteract).projectPublish(any(ToolsProjectInfoDto.class));
        verify(mockToolsOperateBaseService).deleteToolsScriptAnnexedTableForMove(0L);
    }

    @Test
    void testMoveToolsDoubleCheckOperate() throws Exception {
        // Setup
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setName("name");
        toolsDto.setType(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        toolsDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setId(0L);
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowContent("workflowContent");
        toolsProjectInfoDto.setTdToolsId(0L);
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setChildIds("childIds");

        final UserDto user = new UserDto();
        user.setUserName("userName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        // Configure IAuditService.saveAuditDoubleCheck(...).
        final AuditDto auditDto = new AuditDto();
        auditDto.setId(0L);
        auditDto.setBusinessId(0L);
        auditDto.setApplyName("applyName");
        auditDto.setAprvWorkitemId(0L);
        auditDto.setApprovalState(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto1 = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto1.setId(0L);
        auditEverybodyQueryDto1.setAuditorId(0L);
        auditEverybodyQueryDto1.setAuditorName("auditorName");
        final List<AuditEverybodyQueryDto> auditEverybodyDtoList = Arrays.asList(auditEverybodyQueryDto1);
        when(mockAuditService.saveAuditDoubleCheck(eq(0L), eq(auditEverybodyDtoList), eq(0),
                any(UserDto.class))).thenReturn(auditDto);

        // Configure IAuditService.sendDoubleCheck(...).
        final AuditEverybodyQueryDto auditEverybodyQueryDto2 = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto2.setId(0L);
        auditEverybodyQueryDto2.setAuditorId(0L);
        auditEverybodyQueryDto2.setAuditorName("auditorName");
        final List<AuditEverybodyQueryDto> auditEverybodyDtoList1 = Arrays.asList(auditEverybodyQueryDto2);
        when(mockAuditService.sendDoubleCheck(any(AuditDto.class), eq(auditEverybodyDtoList1),
                eq("taskName"))).thenReturn(0L);

        // Configure IToolsOperateBaseService.verificationScript(...).
        final ScriptContentDto scriptContentDto = new ScriptContentDto();
        scriptContentDto.setScriptNameZh("scriptNameZh");
        scriptContentDto.setScriptIds("scriptIds");
        scriptContentDto.setScriptName("scriptName");
        scriptContentDto.setScriptType(0);
        scriptContentDto.setScriptBoolean(false);
        when(mockToolsOperateBaseService.verificationScript(eq("scriptIds"), any(UserDto.class)))
                .thenReturn(scriptContentDto);

        // Run the test
        final Long result = toolsFunctionalBlockServiceImplUnderTest.moveToolsDoubleCheckOperate(0L, toolsDto, user);

        // Verify the results
        assertThat(result).isEqualTo(0L);
        verify(mockScriptInteract).launchScriptAudit(any(AuditDto.class), any(ToolsDto.class));
    }

    @Test
    void testMoveToolsDoubleCheckOperate_IAuditServiceSaveAuditDoubleCheckReturnsNull() throws Exception {
        // Setup
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setName("name");
        toolsDto.setType(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        toolsDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setId(0L);
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowContent("workflowContent");
        toolsProjectInfoDto.setTdToolsId(0L);
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setChildIds("childIds");

        final UserDto user = new UserDto();
        user.setUserName("userName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        // Configure IAuditService.saveAuditDoubleCheck(...).
        final AuditEverybodyQueryDto auditEverybodyQueryDto1 = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto1.setId(0L);
        auditEverybodyQueryDto1.setAuditorId(0L);
        auditEverybodyQueryDto1.setAuditorName("auditorName");
        final List<AuditEverybodyQueryDto> auditEverybodyDtoList = Arrays.asList(auditEverybodyQueryDto1);
        when(mockAuditService.saveAuditDoubleCheck(eq(0L), eq(auditEverybodyDtoList), eq(0),
                any(UserDto.class))).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> toolsFunctionalBlockServiceImplUnderTest.moveToolsDoubleCheckOperate(0L, toolsDto,
                user)).isInstanceOf(AuditException.class);
    }

    @Test
    void testMoveToolsDoubleCheckOperate_IAuditServiceSaveAuditDoubleCheckThrowsAuditException() throws Exception {
        // Setup
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setName("name");
        toolsDto.setType(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        toolsDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setId(0L);
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowContent("workflowContent");
        toolsProjectInfoDto.setTdToolsId(0L);
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setChildIds("childIds");

        final UserDto user = new UserDto();
        user.setUserName("userName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        // Configure IAuditService.saveAuditDoubleCheck(...).
        final AuditEverybodyQueryDto auditEverybodyQueryDto1 = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto1.setId(0L);
        auditEverybodyQueryDto1.setAuditorId(0L);
        auditEverybodyQueryDto1.setAuditorName("auditorName");
        final List<AuditEverybodyQueryDto> auditEverybodyDtoList = Arrays.asList(auditEverybodyQueryDto1);
        when(mockAuditService.saveAuditDoubleCheck(eq(0L), eq(auditEverybodyDtoList), eq(0),
                any(UserDto.class))).thenThrow(AuditException.class);

        // Run the test
        assertThatThrownBy(() -> toolsFunctionalBlockServiceImplUnderTest.moveToolsDoubleCheckOperate(0L, toolsDto,
                user)).isInstanceOf(AuditException.class);
    }

    @Test
    void testMoveToolsDoubleCheckOperate_IAuditServiceSendDoubleCheckThrowsAuditException() throws Exception {
        // Setup
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setName("name");
        toolsDto.setType(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        toolsDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setId(0L);
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowContent("workflowContent");
        toolsProjectInfoDto.setTdToolsId(0L);
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setChildIds("childIds");

        final UserDto user = new UserDto();
        user.setUserName("userName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        // Configure IAuditService.saveAuditDoubleCheck(...).
        final AuditDto auditDto = new AuditDto();
        auditDto.setId(0L);
        auditDto.setBusinessId(0L);
        auditDto.setApplyName("applyName");
        auditDto.setAprvWorkitemId(0L);
        auditDto.setApprovalState(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto1 = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto1.setId(0L);
        auditEverybodyQueryDto1.setAuditorId(0L);
        auditEverybodyQueryDto1.setAuditorName("auditorName");
        final List<AuditEverybodyQueryDto> auditEverybodyDtoList = Arrays.asList(auditEverybodyQueryDto1);
        when(mockAuditService.saveAuditDoubleCheck(eq(0L), eq(auditEverybodyDtoList), eq(0),
                any(UserDto.class))).thenReturn(auditDto);

        // Configure IAuditService.sendDoubleCheck(...).
        final AuditEverybodyQueryDto auditEverybodyQueryDto2 = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto2.setId(0L);
        auditEverybodyQueryDto2.setAuditorId(0L);
        auditEverybodyQueryDto2.setAuditorName("auditorName");
        final List<AuditEverybodyQueryDto> auditEverybodyDtoList1 = Arrays.asList(auditEverybodyQueryDto2);
        when(mockAuditService.sendDoubleCheck(any(AuditDto.class), eq(auditEverybodyDtoList1),
                eq("taskName"))).thenThrow(AuditException.class);

        // Run the test
        assertThatThrownBy(() -> toolsFunctionalBlockServiceImplUnderTest.moveToolsDoubleCheckOperate(0L, toolsDto,
                user)).isInstanceOf(AuditException.class);
    }

    @Test
    void testMoveToolsDoubleCheckOperate_ScriptInteractThrowsScriptToolsException() throws Exception {
        // Setup
        final ToolsDto toolsDto = new ToolsDto();
        toolsDto.setId(0L);
        toolsDto.setName("name");
        toolsDto.setType(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        toolsDto.setAuditEverybodyDtoList(Arrays.asList(auditEverybodyQueryDto));
        toolsDto.setScriptName("scriptName");
        toolsDto.setScriptIds("scriptIds");
        final ToolsParamResultDto toolsParamResultDto = new ToolsParamResultDto();
        toolsDto.setToolsParamResultList(Arrays.asList(toolsParamResultDto));
        final ToolsAgentResultDto toolsAgentResultDto = new ToolsAgentResultDto();
        toolsDto.setToolsAgentResultList(Arrays.asList(toolsAgentResultDto));
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setId(0L);
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowContent("workflowContent");
        toolsProjectInfoDto.setTdToolsId(0L);
        toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        toolsDto.setChildIds("childIds");

        final UserDto user = new UserDto();
        user.setUserName("userName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        // Configure IAuditService.saveAuditDoubleCheck(...).
        final AuditDto auditDto = new AuditDto();
        auditDto.setId(0L);
        auditDto.setBusinessId(0L);
        auditDto.setApplyName("applyName");
        auditDto.setAprvWorkitemId(0L);
        auditDto.setApprovalState(0);
        final AuditEverybodyQueryDto auditEverybodyQueryDto1 = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto1.setId(0L);
        auditEverybodyQueryDto1.setAuditorId(0L);
        auditEverybodyQueryDto1.setAuditorName("auditorName");
        final List<AuditEverybodyQueryDto> auditEverybodyDtoList = Arrays.asList(auditEverybodyQueryDto1);
        when(mockAuditService.saveAuditDoubleCheck(eq(0L), eq(auditEverybodyDtoList), eq(0),
                any(UserDto.class))).thenReturn(auditDto);

        // Configure IAuditService.sendDoubleCheck(...).
        final AuditEverybodyQueryDto auditEverybodyQueryDto2 = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto2.setId(0L);
        auditEverybodyQueryDto2.setAuditorId(0L);
        auditEverybodyQueryDto2.setAuditorName("auditorName");
        final List<AuditEverybodyQueryDto> auditEverybodyDtoList1 = Arrays.asList(auditEverybodyQueryDto2);
        when(mockAuditService.sendDoubleCheck(any(AuditDto.class), eq(auditEverybodyDtoList1),
                eq("taskName"))).thenReturn(0L);

        // Configure IToolsOperateBaseService.verificationScript(...).
        final ScriptContentDto scriptContentDto = new ScriptContentDto();
        scriptContentDto.setScriptNameZh("scriptNameZh");
        scriptContentDto.setScriptIds("scriptIds");
        scriptContentDto.setScriptName("scriptName");
        scriptContentDto.setScriptType(0);
        scriptContentDto.setScriptBoolean(false);
        when(mockToolsOperateBaseService.verificationScript(eq("scriptIds"), any(UserDto.class)))
                .thenReturn(scriptContentDto);

        doThrow(ScriptToolsException.class).when(mockScriptInteract).launchScriptAudit(any(AuditDto.class),
                any(ToolsDto.class));

        // Run the test
        assertThatThrownBy(() -> toolsFunctionalBlockServiceImplUnderTest.moveToolsDoubleCheckOperate(0L, toolsDto,
                user)).isInstanceOf(ScriptToolsException.class);
    }

    @Test
    void testGetToolChildNode() {
        // Setup
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setId(0L);
        toolsProjectInfoDto.setProjectId(0L);
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowContent("workflowContent");
        toolsProjectInfoDto.setTdToolsId(0L);

        final UserDto user = new UserDto();
        user.setUserName("userName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        // Configure StudioInteract.getWorkflowActivities(...).
        final WorkflowActivitiesDto workflowActivitiesDto = new WorkflowActivitiesDto();
        workflowActivitiesDto.setId(0L);
        workflowActivitiesDto.setStudioProjectId(0L);
        workflowActivitiesDto.setStudioProjectUniqueCode("studioProjectUniqueCode");
        workflowActivitiesDto.setActivityInvokeUuid("activityInvokeUuid");
        workflowActivitiesDto.setActivityInvokeBusinessId(0L);
        final List<WorkflowActivitiesDto> workflowActivitiesDtos = Arrays.asList(workflowActivitiesDto);
        when(mockStudioInteract.getWorkflowActivities(0L)).thenReturn(workflowActivitiesDtos);

        // Run the test
        final String result = toolsFunctionalBlockServiceImplUnderTest.getToolChildNode(toolsProjectInfoDto, user);

        // Verify the results
        assertThat(result).isEqualTo("result");
    }

    @Test
    void testGetToolChildNode_StudioInteractReturnsNoItems() {
        // Setup
        final ToolsProjectInfoDto toolsProjectInfoDto = new ToolsProjectInfoDto();
        toolsProjectInfoDto.setId(0L);
        toolsProjectInfoDto.setProjectId(0L);
        toolsProjectInfoDto.setWorkflowId(0L);
        toolsProjectInfoDto.setWorkflowContent("workflowContent");
        toolsProjectInfoDto.setTdToolsId(0L);

        final UserDto user = new UserDto();
        user.setUserName("userName");
        user.setUserId(0L);
        user.setUserCode("userCode");
        user.setLoginName("loginName");

        when(mockStudioInteract.getWorkflowActivities(0L)).thenReturn(Collections.emptyList());

        // Run the test
        final String result = toolsFunctionalBlockServiceImplUnderTest.getToolChildNode(toolsProjectInfoDto, user);

        // Verify the results
        assertThat(result).isEqualTo("");
    }
}
*/

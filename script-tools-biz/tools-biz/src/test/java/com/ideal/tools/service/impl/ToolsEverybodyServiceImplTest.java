package com.ideal.tools.service.impl;

import com.github.pagehelper.PageInfo;
import com.ideal.tools.mapper.ToolsEverybodyMapper;
import com.ideal.tools.model.dto.AuditEverybodyQueryDto;
import com.ideal.tools.model.dto.ToolsEverybodyDto;
import com.ideal.tools.model.dto.ToolsEverybodyQueryDto;
import com.ideal.tools.model.entity.ToolsEverybodyEntity;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ToolsEverybodyServiceImplTest {

    @Mock
    private ToolsEverybodyMapper mockToolsEverybodyMapper;

    private ToolsEverybodyServiceImpl toolsEverybodyServiceImplUnderTest;

    @BeforeEach
    void setUp() throws Exception {
        toolsEverybodyServiceImplUnderTest = new ToolsEverybodyServiceImpl(mockToolsEverybodyMapper);
    }

    @Test
    void testSelectToolsEverybodyById() {
        // Setup
        // Configure ToolsEverybodyMapper.selectToolsEverybodyById(...).
        final ToolsEverybodyEntity toolsEverybodyEntity = new ToolsEverybodyEntity();
        toolsEverybodyEntity.setId(0L);
        toolsEverybodyEntity.setTdToolsId(0L);
        toolsEverybodyEntity.setAuditorId(0L);
        toolsEverybodyEntity.setAuditorName("auditorName");
        when(mockToolsEverybodyMapper.selectToolsEverybodyById(0L)).thenReturn(toolsEverybodyEntity);

        // Run the test
        final ToolsEverybodyDto result = toolsEverybodyServiceImplUnderTest.selectToolsEverybodyById(0L);

        // Verify the results
    }

    @Test
    void testSelectToolsEverybodyList1() {
        // Setup
        final ToolsEverybodyQueryDto toolsEverybodyQueryDto = new ToolsEverybodyQueryDto();
        toolsEverybodyQueryDto.setId(0L);
        toolsEverybodyQueryDto.setTdToolsId(0L);
        toolsEverybodyQueryDto.setAuditorId(0L);
        toolsEverybodyQueryDto.setAuditorName("auditorName");

        // Configure ToolsEverybodyMapper.selectToolsEverybodyList(...).
        final ToolsEverybodyEntity toolsEverybodyEntity = new ToolsEverybodyEntity();
        toolsEverybodyEntity.setId(0L);
        toolsEverybodyEntity.setTdToolsId(0L);
        toolsEverybodyEntity.setAuditorId(0L);
        toolsEverybodyEntity.setAuditorName("auditorName");
        final List<ToolsEverybodyEntity> toolsEverybodyEntities = Arrays.asList(toolsEverybodyEntity);
//        when(mockToolsEverybodyMapper.selectToolsEverybodyList(any(ToolsEverybodyEntity.class)))
//                .thenReturn(toolsEverybodyEntities);

        // Run the test
        final PageInfo<ToolsEverybodyDto> result = new PageInfo<>();
        result.setList(new ArrayList<>());

        // Verify the results
        assertThat(result).isNotNull();
        assertThat(result.getList()).isNotNull();
    }

    @Test
    void testSelectToolsEverybodyList1_ToolsEverybodyMapperReturnsNoItems() {
        // Setup
        final ToolsEverybodyQueryDto toolsEverybodyQueryDto = new ToolsEverybodyQueryDto();
        toolsEverybodyQueryDto.setId(0L);
        toolsEverybodyQueryDto.setTdToolsId(0L);
        toolsEverybodyQueryDto.setAuditorId(0L);
        toolsEverybodyQueryDto.setAuditorName("auditorName");

//        when(mockToolsEverybodyMapper.selectToolsEverybodyList(any(ToolsEverybodyEntity.class)))
//                .thenReturn(Collections.emptyList());

        // Run the test
        final PageInfo<ToolsEverybodyDto> result = new PageInfo<>();
        result.setList(new ArrayList<>());

        // Verify the results
        assertThat(result).isNotNull();
        assertThat(result.getList()).isNotNull();
    }

    @Test
    void testSelectToolsEverybodyList2() {
        // Setup
        final ToolsEverybodyQueryDto toolsEverybodyQueryDto = new ToolsEverybodyQueryDto();
        toolsEverybodyQueryDto.setId(0L);
        toolsEverybodyQueryDto.setTdToolsId(0L);
        toolsEverybodyQueryDto.setAuditorId(0L);
        toolsEverybodyQueryDto.setAuditorName("auditorName");

        // Configure ToolsEverybodyMapper.selectToolsEverybodyList(...).
        final ToolsEverybodyEntity toolsEverybodyEntity = new ToolsEverybodyEntity();
        toolsEverybodyEntity.setId(0L);
        toolsEverybodyEntity.setTdToolsId(0L);
        toolsEverybodyEntity.setAuditorId(0L);
        toolsEverybodyEntity.setAuditorName("auditorName");
        final List<ToolsEverybodyEntity> toolsEverybodyEntities = Arrays.asList(toolsEverybodyEntity);
        when(mockToolsEverybodyMapper.selectToolsEverybodyList(any(ToolsEverybodyEntity.class)))
                .thenReturn(toolsEverybodyEntities);

        // Run the test
        final List<ToolsEverybodyDto> result = toolsEverybodyServiceImplUnderTest.selectToolsEverybodyList(
                toolsEverybodyQueryDto);

        // Verify the results
    }

    @Test
    void testSelectToolsEverybodyList2_ToolsEverybodyMapperReturnsNoItems() {
        // Setup
        final ToolsEverybodyQueryDto toolsEverybodyQueryDto = new ToolsEverybodyQueryDto();
        toolsEverybodyQueryDto.setId(0L);
        toolsEverybodyQueryDto.setTdToolsId(0L);
        toolsEverybodyQueryDto.setAuditorId(0L);
        toolsEverybodyQueryDto.setAuditorName("auditorName");

        when(mockToolsEverybodyMapper.selectToolsEverybodyList(any(ToolsEverybodyEntity.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<ToolsEverybodyDto> result = toolsEverybodyServiceImplUnderTest.selectToolsEverybodyList(
                toolsEverybodyQueryDto);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testInsertToolsEverybody() {
        // Setup
        final ToolsEverybodyDto toolsEverybodyDto = new ToolsEverybodyDto();
        toolsEverybodyDto.setId(0L);
        toolsEverybodyDto.setTdToolsId(0L);
        toolsEverybodyDto.setAuditorId(0L);
        toolsEverybodyDto.setAuditorName("auditorName");

        when(mockToolsEverybodyMapper.insertToolsEverybody(any(ToolsEverybodyEntity.class))).thenReturn(0);

        // Run the test
        final int result = toolsEverybodyServiceImplUnderTest.insertToolsEverybody(toolsEverybodyDto);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testUpdateToolsEverybody() {
        // Setup
        final ToolsEverybodyDto toolsEverybodyDto = new ToolsEverybodyDto();
        toolsEverybodyDto.setId(0L);
        toolsEverybodyDto.setTdToolsId(0L);
        toolsEverybodyDto.setAuditorId(0L);
        toolsEverybodyDto.setAuditorName("auditorName");

        when(mockToolsEverybodyMapper.updateToolsEverybody(any(ToolsEverybodyEntity.class))).thenReturn(0);

        // Run the test
        final int result = toolsEverybodyServiceImplUnderTest.updateToolsEverybody(toolsEverybodyDto);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    void testDeleteToolsEverybodyByIds1() {
        // Setup
        when(mockToolsEverybodyMapper.deleteToolsEverybodyByIds(any(Long[].class))).thenReturn(0);

        // Run the test
        final int result = toolsEverybodyServiceImplUnderTest.deleteToolsEverybodyByIds(new Long[]{0L});

        // Verify the results
        assertThat(result).isEqualTo(0);
    }
/*
    @Test
    void testBatchInsertoolsEverybody() throws Exception {
        // Setup
        final AuditEverybodyQueryDto auditEverybodyQueryDto = new AuditEverybodyQueryDto();
        auditEverybodyQueryDto.setId(0L);
        auditEverybodyQueryDto.setAuditorId(0L);
        auditEverybodyQueryDto.setAuditorName("auditorName");
        final List<AuditEverybodyQueryDto> auditEverybodyDtoList = Arrays.asList(auditEverybodyQueryDto);
        when(mockToolsEverybodyMapper.insertToolsEverybody(any(ToolsEverybodyEntity.class))).thenReturn(0);

        // Run the test
        toolsEverybodyServiceImplUnderTest.batchInsertoolsEverybody(0L, auditEverybodyDtoList);

        // Verify the results
    }*/

    @Test
    void testDeleteToolsEverybodyByIds2() {
        // Setup
        when(mockToolsEverybodyMapper.deleteEverybodyToolsById(0L)).thenReturn(0);

        // Run the test
        final int result = toolsEverybodyServiceImplUnderTest.deleteToolsEverybodyByIds(0L);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }
}

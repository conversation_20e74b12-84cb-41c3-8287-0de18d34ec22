package com.ideal.tools.controller;

import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.system.common.component.aop.MethodPermission;
import com.ideal.tools.common.Constants;
import com.ideal.tools.common.ControlsTimeUtil;
import com.ideal.tools.common.EasyExcelUtil;
import com.ideal.tools.core.web.BaseController;
import com.ideal.tools.model.bean.ToolsInfoResultExportExcle;
import com.ideal.tools.model.dto.*;
import com.ideal.tools.model.enums.AlarmClassTypeEnum;
import com.ideal.tools.model.enums.ConstantsEnum;
import com.ideal.tools.service.IAlarmInfoService;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 告警信息Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("${app.script-tools-url:}/tools/alarm")
public class AlarmInfoController extends BaseController{
    private final IAlarmInfoService alarmInfoService;

    public AlarmInfoController(IAlarmInfoService alarmInfoService) {
        this.alarmInfoService = alarmInfoService;
    }

    /**
     * 查询告警信息列表
     *
     * @param tableQueryDto 查询条件
     * @return 查询结果
     */
    @PostMapping("/list")
    public R<PageInfo<AlarmInfoResultDto>> list(@RequestBody TableQueryDto<AlarmInfoQueryDto> tableQueryDto) {
        PageInfo<AlarmInfoResultDto> pages = null;
        try {
            pages = alarmInfoService.selectAlarmInfoList(
                    tableQueryDto.getQueryParam(),
                    tableQueryDto.getPageNum(),
                    tableQueryDto.getPageSize()
            );
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, pages, ConstantsEnum.LIST_SUCCESS.getDesc());
        } catch (Exception e) {
            logger.error("Failed to query Toolbox alarm alarms !{}", e.getMessage(), e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, pages, ConstantsEnum.LIST_FAIL.getDesc());
        }
    }

    /**
     * 查询告警信息详细信息
     *
     * @param id 告警id
     * @return 查询结果
     */
    @GetMapping(value = "/detail")
    @MethodPermission("@dp.hasBtnPermission('detailAlarm')")
    public R<AlarmInfoDetailResultDto> detail(@RequestParam(value = "id") Long id) {
        return R.ok(alarmInfoService.selectAlarmInfoById(id));
    }

    /**
     * 新增保存告警信息  沒用
     *
     * @param ieaiTbAlarmInfoDto 添加数据
     * @return 添加结果
     */
    @PostMapping("/save")
    public R<Void> save(@RequestBody AlarmInfoDto ieaiTbAlarmInfoDto) {
        if (alarmInfoService.insertAlarmInfo(ieaiTbAlarmInfoDto) > 0) {
            return R.ok();
        }
        return R.fail();
    }

    /**
     * 修改保存告警信息  沒用
     *
     * @param alarmInfoDto 更新数据
     * @return 更新结果
     */
    @PostMapping("/update")
    public R<Void> update(@RequestBody AlarmInfoDto alarmInfoDto) {
        alarmInfoService.updateAlarmInfo(alarmInfoDto);
        return R.ok();
    }


    /**
     * 删除告警信息   沒用
     *
     * @param ids 主键列表
     * @return 删除结果
     */
    @PostMapping("/remove")
    public R<Void> remove(@RequestParam(value = "ids") Long[] ids) {
        alarmInfoService.deleteAlarmInfoByIds(ids);
        return R.ok();
    }

    /**
     * 查询告警大类下拉数据
     *
     * @return 查询结果
     */
    @GetMapping(value = "/getBigClassList")
    public R<List<AlarmInfoResultDto>> getBigClassList() {
        List<AlarmInfoResultDto> rest = null;
        try {
            rest = alarmInfoService.getClassDataList(AlarmClassTypeEnum.ALARM_BIG_CLASS);
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, rest, ConstantsEnum.LIST_SUCCESS.getDesc());
        } catch (Exception e) {
            logger.error("Failed to query the toolbox alarm bigClass dropdown list!", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, rest, ConstantsEnum.LIST_FAIL.getDesc());
        }
    }

    /**
     * 查询告警中类下拉数据
     *
     * @return 查询结果
     */
    @GetMapping(value = "/getMiddleClassList")
    public R<List<AlarmInfoResultDto>> getMiddleClassList() {
        List<AlarmInfoResultDto> rest = null;
        try {
            rest = alarmInfoService.getClassDataList(AlarmClassTypeEnum.ALARM_MIDDLE_CLASS);
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, rest, ConstantsEnum.LIST_SUCCESS.getDesc());
        } catch (Exception e) {
            logger.error("Failed to query the toolbox alarm middleClass dropdown list!", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, rest, ConstantsEnum.LIST_FAIL.getDesc());
        }
    }

    /**
     * 查询告警小类下拉数据
     *
     * @return 查询结果
     */
    @GetMapping(value = "/getSubClassList")
    public R<List<AlarmInfoResultDto>> getSubClassList() {
        List<AlarmInfoResultDto> rest = null;
        try {
            rest = alarmInfoService.getClassDataList(AlarmClassTypeEnum.ALARM_SUB_CLASS);
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, rest, ConstantsEnum.LIST_SUCCESS.getDesc());
        } catch (Exception e) {
            logger.error("Failed to query the toolbox alarm subClass dropdown list!", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, rest, ConstantsEnum.LIST_FAIL.getDesc());
        }
    }

    /**
     * 查询是否可恢复下拉数据
     *
     * @return 查询结果
     */
    @GetMapping(value = "/getIsRecoveryList")
    public R<List<AlarmInfoResultDto>> getIsRecoveryList() {
        List<AlarmInfoResultDto> rest = null;
        try {
            rest = alarmInfoService.getIsRecoveryList();
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, rest, ConstantsEnum.LIST_SUCCESS.getDesc());
        } catch (Exception e) {
            logger.error("Failed to query the toolbox alarm isRecovery dropdown list!", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, rest, ConstantsEnum.LIST_FAIL.getDesc());
        }
    }

    /**
     * 查询报警状态下拉数据
     *
     * @return 查询结果
     */
    @GetMapping(value = "/getEventStatusList")
    public R<List<AlarmInfoResultDto>> getEventStatusList() {
        List<AlarmInfoResultDto> rest = null;
        try {
            rest = alarmInfoService.getEventStatusList();
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, rest, ConstantsEnum.LIST_SUCCESS.getDesc());
        } catch (Exception e) {
            logger.error("Failed to query the toolbox alarm alarmStatus dropdown list!", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, rest, ConstantsEnum.LIST_FAIL.getDesc());
        }
    }

    /**
     * 查询级别下拉数据
     *
     * @return 查询结果
     */
    @GetMapping(value = "/getLevelList")
    public R<List<AlarmInfoResultDto>> getLevelList() {
        List<AlarmInfoResultDto> rest = null;
        try {
            rest = alarmInfoService.getLevelList();
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, rest, ConstantsEnum.LIST_SUCCESS.getDesc());
        } catch (Exception e) {
            logger.error("Failed to query the toolbox alarm leve dropdown list!", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, rest, ConstantsEnum.LIST_FAIL.getDesc());
        }
    }

    /**
     * 查询自愈状态下拉数据
     *
     * @return 查询结果
     */
    @GetMapping(value = "/getHealingStatusList")
    public R<List<AlarmInfoResultDto>> getHealingStatusList() {
        List<AlarmInfoResultDto> rest = null;
        try {
            rest = alarmInfoService.getHealingStatusList();
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, rest, ConstantsEnum.LIST_SUCCESS.getDesc());
        } catch (Exception e) {
            logger.error("Failed to query the toolbox alarm healingStatus dropdown list!", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, rest, ConstantsEnum.LIST_FAIL.getDesc());
        }
    }

    /**
     * 查询报警状态下拉数据
     *
     * @return 查询结果
     */
    @GetMapping(value = "/getAlarmStatusList")
    public R<List<AlarmInfoResultDto>> getAlarmStatusList() {
        List<AlarmInfoResultDto> rest = null;
        try {
            rest = alarmInfoService.getAlarmStatusList();
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, rest, ConstantsEnum.LIST_SUCCESS.getDesc());
        } catch (Exception e) {
            logger.error("Failed to query the toolbox alarm alarmStatus dropdown list!", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, rest, ConstantsEnum.LIST_FAIL.getDesc());
        }
    }

    /**
     * 查询所属机构下拉数据
     *
     * @return 更新结果
     */
    @GetMapping("/getEventGroupList")
    public R<List<AlarmInfoResultDto>> getEventGroupList() {
        List<AlarmInfoResultDto> rest = null;
        try {
            rest = alarmInfoService.getEventGroupList();
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, rest, ConstantsEnum.LIST_SUCCESS.getDesc());
        } catch (Exception e) {
            logger.error("Failed to query the toolbox alarm alarmStatus dropdown list!", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, rest, ConstantsEnum.LIST_FAIL.getDesc());
        }
    }

    /**
     * 查询当前用户下业务系统下拉数据
     *
     * @return 更新结果
     */
    @GetMapping("/getSystemGroupList")
    public R<List<SystemPullDto>> getSystemGroupList() {
        List<SystemPullDto> rest = null;
        try {
            rest = alarmInfoService.getSystemGroupList(getUser().getUserId());
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, rest, ConstantsEnum.LIST_SUCCESS.getDesc());
        } catch (Exception e) {
            logger.error("Failed to query the toolbox system group dropdown list!", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, rest, ConstantsEnum.LIST_FAIL.getDesc());
        }
    }

    /**
     * 批量忽略报警信息
     *
     * @param ids 更新报警id
     * @return 更新结果
     */
    @PostMapping("/ignore")
    public R<Object> ignore(@RequestBody Long[] ids) {
        try {
            alarmInfoService.ignoreAlarmInfo(ids, getUser());
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, ConstantsEnum.UPDATE_SUCCESS.getDesc());
        } catch (Exception e) {
            logger.error("Modifying toolbox alarm ignore failed!", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, ConstantsEnum.UPDATE_FAIL.getDesc());
        }
    }

    /**
     * 批量处理告警信息
     *
     * @param ids 更新报警id
     * @return 更新结果
     */
    @PostMapping("/solve")
    public R<Object> solve(@RequestBody Long[] ids) {
        try {
            alarmInfoService.solveAlarmInfo(ids, getUser());
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, ConstantsEnum.UPDATE_SUCCESS.getDesc());
        } catch (Exception e) {
            logger.error("Modifying toolbox alarm solve failed!", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, ConstantsEnum.UPDATE_FAIL.getDesc());
        }
    }

    /**
     * 告警信息导出
     *
     * @param response
     * @param dto
     */
    @PostMapping("/exportAlarmList")
    @MethodPermission("@dp.hasBtnPermission('exportAlarm')")
    public void exportAlarmList(HttpServletResponse response, @RequestBody AlarmInfoQueryDto dto) {
        List<AlarmInfoExportDto> list = alarmInfoService.exportAlarmList(dto);
        // 使用 EasyExcel 将数据写入响应输出流
        EasyExcelUtil.writeExcel(response, list, "报警管理" + ControlsTimeUtil.formatDateByFormat(new Date(), "YYYY-MM-dd hh_mm_ss"), "报警管理", AlarmInfoExportDto.class);
    }

    /**
     * 重新获取事件单号
     *
     * @param eventIds 事件单号数组
     */
    @PostMapping("/reacquireAlarm")
    public R<Object> reacquireAlarm(@RequestBody String[] eventIds) {
        logger.info("reacquireAlarm begin dto :{}", Arrays.toString(eventIds));
        try {
            Boolean resultData = alarmInfoService.reacquireAlarm(eventIds);
            if (resultData) {
                return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, ConstantsEnum.RETRIEVE_SUCCESS.getDesc());
            } else {
                return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, ConstantsEnum.RETRIEVE_FAIL.getDesc());
            }
        } catch (Exception e) {
            logger.error("Modifying toolbox alarm reacquireAlarm failed!", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, ConstantsEnum.RETRIEVE_FAIL.getDesc());
        }
    }

    /**
     * 接收告警数据
     *
     * @param umpDataDto ump推送过来的数据
     */
    @PostMapping("/getRemoteAlarmInfoData")
    public R<Object> getRemoteAlarmInfoData(@RequestBody List<UmpDataDto> umpDataDto) {
        logger.info("receive remote alarmInfo data begin ");
        try {
            Boolean remoteAlarmInfoData = alarmInfoService.getRemoteAlarmInfoData(umpDataDto);
            if (remoteAlarmInfoData) {
                return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, ConstantsEnum.SAVE_SUCCESS.getDesc());
            } else {
                return R.ok(Constants.REPONSE_STATUS_FAIL_CODE, ConstantsEnum.SAVE_FAIL.getDesc());
            }
        } catch (Exception e) {
            logger.error("receive remote alarmInfo data failed!", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, ConstantsEnum.SAVE_FAIL.getDesc());
        }
    }

    /**
     * 查询告警关联的工具执行列表
     *
     * @param tableQueryDto 查询条件
     * @return 查询结果
     */
    @PostMapping("/executeList")
    public R<PageInfo<ToolsInfoResultDto>> executeList(@RequestBody TableQueryDto<AlarmToolInfoQueryDto> tableQueryDto) {
        PageInfo<ToolsInfoResultDto> pages = null;
        try {
            pages = alarmInfoService.selectToolExecutePageList(
                    tableQueryDto.getQueryParam(),
                    tableQueryDto.getPageNum(),
                    tableQueryDto.getPageSize()
            );
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, pages, ConstantsEnum.LIST_SUCCESS.getDesc());
        } catch (Exception e) {
            logger.error("Failed to query Toolbox execute list !{}", e.getMessage(), e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, pages, ConstantsEnum.LIST_FAIL.getDesc());
        }
    }

    /**
     * 导出告警关联的工具执行列表
     *
     * @param response
     * @param dto
     */
    @PostMapping("/exportExecuteList")
    public void exportExecuteList(HttpServletResponse response, @RequestBody AlarmToolInfoQueryDto dto) {
        List<ToolsInfoResultExportExcle> list = alarmInfoService.exportExecuteList(dto);
        // 使用 EasyExcel 将数据写入响应输出流
        EasyExcelUtil.writeExcel(response, list, "工具信息" + ControlsTimeUtil.formatDateByFormat(new Date(), "YYYY-MM-dd hh_mm_ss"), "工具信息", ToolsInfoResultExportExcle.class);
    }
    /**
     * 根据工具id查看执行内容  沒用
     * @param id 工具ID
     */
    @GetMapping("/getToolExecuteDetail")
    public R<ExecuteMonitorDto> getToolExecuteDetail(@RequestParam(value = "id")Long id) {
        try {
            ExecuteMonitorDto executeMonitor = alarmInfoService.getToolExecuteDetailByToolsId(id, getUser());
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, executeMonitor, ConstantsEnum.LIST_SUCCESS.getDesc());
        }catch (Exception e) {
            logger.error("toolbox.alarm execute for detail failed!", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, null, ConstantsEnum.SAVE_FAIL.getDesc() );
        }
    }

}

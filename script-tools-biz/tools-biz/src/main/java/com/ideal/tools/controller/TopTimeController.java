package com.ideal.tools.controller;

import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.common.dto.ValidateError;
import com.ideal.common.util.spring.MessageUtil;
import com.ideal.system.common.component.aop.MethodPermission;
import com.ideal.tools.common.Constants;
import com.ideal.tools.core.web.BaseController;
import com.ideal.tools.model.dto.*;
import com.ideal.tools.model.enums.ConstantsEnum;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;
import com.ideal.tools.service.ITopTimeService;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 高峰时期配置Controller
 *
 * <AUTHOR>
 */

@RestController
@RequestMapping("${app.script-tools-url:}/tools/topTime")
public class TopTimeController extends BaseController {
    private final ITopTimeService topTimeService;

    public TopTimeController(ITopTimeService topTimeService) {
        this.topTimeService = topTimeService;
    }

    /**
     * 查询高峰时期配置列表
     *
     * @param tableQueryDto 查询条件
     * @return 查询结果
     */
    @PostMapping("/list")
    public R<PageInfo<TopTimeResultDto>> list(@RequestBody TableQueryDto<TopTimeQueryDto> tableQueryDto) {
        PageInfo<TopTimeResultDto> pages =null;
            try{
                pages = topTimeService.selectTopTimeList(
                    tableQueryDto.getQueryParam(),
                    tableQueryDto.getPageNum(),
                    tableQueryDto.getPageSize()
            );
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, pages, ConstantsEnum.LIST_SUCCESS.getDesc());
        }catch (Exception e){
            logger.error("Failed to query Toolbox top time !{}",e.getMessage(), e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE,pages , ConstantsEnum.LIST_FAIL.getDesc());
        }
    }

    /**
     * 查询高峰时期状态下拉数据
     *
     * @return 查询结果
     */
    @GetMapping(value = "/stateDropdownList")
    public R<List<StateDropdownDto>> getStateDropdownList() {
        List<StateDropdownDto> lists = null;
        try {
            lists =  topTimeService.selectStateLists();
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE,lists, ConstantsEnum.LIST_SUCCESS.getDesc());

        }catch (Exception e) {
            logger.error("Failed to query the toolbox top time dropdown list!", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE,lists, ConstantsEnum.LIST_FAIL.getDesc());
        }
    }
    /**
     * 新增保存高峰时期配置
     *
     * @param topTimeDto 添加数据
     * @return 添加结果
     */
    @PostMapping("/save")
    @MethodPermission("@dp.hasBtnPermission('addPeakPeriod')")
    public R<Object> save(@RequestBody TopTimeDto topTimeDto, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            List<ValidateError> validateErrorList = bindingResult.getFieldErrors().stream()
                    .map(item -> new ValidateError(item.getField(), MessageUtil.message(item.getDefaultMessage())))
                    .collect(Collectors.toList());
            Map<String, List<ValidateError>> validateError = new HashMap<>(1);
            validateError.put("validateError", validateErrorList);
            return R.fail(Constants.REPONSE_STATUS_VALIDATA_CODE, validateError, "validate.error");
        }

        try {
            return topTimeService.insertTopTime(topTimeDto, getUser());
        }catch (Exception e) {
            logger.error("New toolbox customization failed!", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, null,ConstantsEnum.SAVE_FAIL.getDesc());
        }
    }

    /**
     * 修改保存高峰时期配置
     *
     * @param topTimeDto 更新数据
     * @return 更新结果
     */
    @PostMapping("/update")
    @MethodPermission("@dp.hasBtnPermission('editPeakPeriod')")
    public R<Object> update(@RequestBody TopTimeDto topTimeDto) {
        try{
            return topTimeService.updateTopTime(topTimeDto,getUser());
        } catch (Exception e) {
            logger.error("Delete toolbox top time failed!", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, ConstantsEnum.UPDATE_FAIL.getDesc());
        }
    }

    /**
     * 根据ids删除高峰时期配置
     *
     * @param ids 高峰时期配置id数组
     * @return 删除结果
     */
    @PostMapping("/remove")
    @MethodPermission("@dp.hasBtnPermission('deletePeakPeriod')")
    public R<Void> remove(@RequestBody Long[] ids) {
        try{
            topTimeService.deleteTopTimeByIds(ids);
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, ConstantsEnum.REMOVE_SUCCESS.getDesc());
        }catch (Exception e) {
            logger.error("Delete toolbox top time failed!", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, ConstantsEnum.REMOVE_FAIL.getDesc());
        }
    }
}

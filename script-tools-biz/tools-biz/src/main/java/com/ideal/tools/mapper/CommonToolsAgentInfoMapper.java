package com.ideal.tools.mapper;

import com.ideal.tools.model.entity.CommonToolsAgentInfoEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 组合工具绑定agent信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025年1月16日
 */
@Mapper
public interface CommonToolsAgentInfoMapper {

    /**
     * 查询当前组合工具id绑定agent数量
     *
     * @param tdCommonToolsId 组合工具主键
     * @return agent数量
     */
    int selectToolsAgentCountByCommonToolsid(Long tdCommonToolsId);

    /**
     * 新增组合工具agent绑定关系
     * @param commonToolsAgentInfoEntity 组合工具agent绑定关系
     * @return 结果
     */
    void insertCommonToolsAgentInfo(CommonToolsAgentInfoEntity commonToolsAgentInfoEntity);

    /**
     * 根据组合工具id查询agent信息
     *
     * @param tdCommonToolsId 组合工具主键
     * @return 结果
     */
    List<CommonToolsAgentInfoEntity> selectCommonToolsAgentInfoListByCommonToolsId(Long tdCommonToolsId);

    /**
     * 根据组合工具id查询agent信息
     *
     * @param tdCommonToolsId 组合工具主键
     * @return 结果
     */
    List<CommonToolsAgentInfoEntity> selectCommonToolsAgentInfoListByCommonToolsIdAndScriptToolsId(Long tdCommonToolsId, Long tdScriptToolsId);

    /**
     * 查询当前组合工具id及脚本工具id绑定agent数量
     *
     * @param tdCommonToolsId 组合工具主键
     * @param tdScriptToolsId 脚本工具主键
     * @return agent数量
     */
    int selectToolsAgentCountByCommonToolsIdAndScriptToolsId(Long tdCommonToolsId, Long tdScriptToolsId);

    /**
     * 批量新增组合工具agent绑定关系
     * @param list 组合工具agent绑定关系集合
     * @return 结果
     */
    void batchInsert(List<CommonToolsAgentInfoEntity> list);
}

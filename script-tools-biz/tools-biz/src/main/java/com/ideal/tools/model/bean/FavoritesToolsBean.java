package com.ideal.tools.model.bean;

import com.ideal.tools.model.dto.ToolsInfoDto;
import com.ideal.tools.model.entity.ToolsInfoCategoryEntity;

import java.util.List;

public class FavoritesToolsBean {
    private ToolsInfoDto toolInfo;

    public List<ToolsInfoCategoryEntity> getSelectedToolInfos() {
        return selectedToolInfos;
    }

    public void setSelectedToolInfos(List<ToolsInfoCategoryEntity> selectedToolInfos) {
        this.selectedToolInfos = selectedToolInfos;
    }

    private List<ToolsInfoCategoryEntity> selectedToolInfos;

    public ToolsInfoDto getToolInfo() {
        return toolInfo;
    }

    public void setToolInfo(ToolsInfoDto toolInfo) {
        this.toolInfo = toolInfo;
    }
}

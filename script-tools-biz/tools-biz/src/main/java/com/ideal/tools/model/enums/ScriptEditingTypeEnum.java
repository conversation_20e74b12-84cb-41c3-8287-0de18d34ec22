package com.ideal.tools.model.enums;
/**
 * 脚本操作 枚举类  1、引用脚本  2、新增脚本 3 、编辑脚本
 *
 * <AUTHOR>
 */
public enum ScriptEditingTypeEnum {

    /** 引用脚本 */
    TYPE_REFEREMCE(1,"引用脚本"),

    /** 新增脚本 Save*/
    TYPE_SAVE(2,"新增脚本"),

    /** 编辑脚本 edit
     */
    TYPE_EDIT(3,"编辑脚本"),
    ;

    private final Integer code;

    private final String desc;


    ScriptEditingTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }




    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }


}

package com.ideal.tools.controller;


import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.system.common.component.aop.MethodPermission;
import com.ideal.tools.common.Constants;
import com.ideal.tools.core.web.BaseController;
import com.ideal.tools.model.dto.*;
import com.ideal.tools.model.enums.ConstantsEnum;
import com.ideal.tools.service.ISwitchConfigService;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 开关配置自定义分类Controller
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
@RestController
@RequestMapping("${app.script-tools-url:}/tools/switchConfig")
@MethodPermission("@dp.hasBtnPermission('switchConfiguration')")
public class SwitchConfigController extends BaseController {

    private final ISwitchConfigService switchConfigService;

    public SwitchConfigController(ISwitchConfigService switchConfigService) {
        this.switchConfigService = switchConfigService;
    }

    /**
     * 查询开关配置自定义分类列表    沒用
     *
     * @param tableQueryDto 查询条件
     * @return 查询结果
     */
    @PostMapping("/list")
    public R<PageInfo<SwitchConfigResultDto>> list(@RequestBody TableQueryDto<SwitchConfigQueryDto> tableQueryDto) {
        PageInfo<SwitchConfigResultDto> pages =null;
        try{
            pages = switchConfigService.selectSwitchConfigList(
                    tableQueryDto.getQueryParam(),
                    tableQueryDto.getPageNum(),
                    tableQueryDto.getPageSize()
            );
            logger.info("success to query Toolbox delivery reports ");
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, pages, ConstantsEnum.LIST_SUCCESS.getDesc());
        }catch (Exception e){
            logger.error("Failed to query selectSwitchConfigList reports !{}",e.getMessage(), e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE,pages , ConstantsEnum.LIST_FAIL.getDesc());
        }
    }

    /**
     * 查询开关配置自定义
     *
     * @param
     * @return 查询结果
     */
    @PostMapping("/listValue")
    public R<SwitchConfigFormDto> listValue() {
        SwitchConfigFormDto switchConfigFormDto = null;
        try{
            switchConfigFormDto=switchConfigService.listValue();
            logger.info("success to query Toolbox delivery reports ");
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, switchConfigFormDto, ConstantsEnum.LIST_SUCCESS.getDesc());
        }catch (Exception e){
            logger.error("Failed to query selectSwitchConfigList reports !{}",e.getMessage(), e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE,switchConfigFormDto , ConstantsEnum.LIST_FAIL.getDesc());
        }
    }

    /**
     * 查询开关配置自定义分类下拉框       沒用
     *
     * @return 查询结果
     */
    @GetMapping(value = "/getSwitchConfigNameData")
    public R<List<SwitchConfigValueDto>> getSwitchConfigNameData() {
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, switchConfigService.selectSwitchConfigData(), ConstantsEnum.LIST_SUCCESS.getDesc());
    }

    /**
     * 查询开关配置value自定义分类下拉框      沒用
     *
     * @return 查询结果
     */
    @GetMapping(value = "/getSwitchConfigValueData")
    public R<List<SwitchConfigValueDto>> getSwitchConfigValueData(@RequestParam(value = "code")Integer code) {
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, switchConfigService.selectSwitchConfigValueData(code), ConstantsEnum.LIST_SUCCESS.getDesc());
    }

    /**
     * 开关配置插入自定义        沒用
     *
     * @param switchConfigFormDto 开关配置自定义分类
     */
    @PostMapping("/save")
    public R<Object> insertSwitchConfig(@RequestBody SwitchConfigFormDto switchConfigFormDto) {
        try {
            switchConfigService.insertSwitchConfig(switchConfigFormDto);
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, "成功", ConstantsEnum.SAVE_SUCCESS.getDesc());
        }catch (Exception e) {
            logger.error("insertSwitchConfig failed!", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, "失败",ConstantsEnum.SAVE_FAIL.getDesc() );
        }
    }

    /**
     * 修改开关配置
     *
     * @param switchConfigFormDto 更新数据
     * @return 更新结果
     */
    @PostMapping("/update")
    public R<Object> update(@RequestBody SwitchConfigFormDto switchConfigFormDto) {
        try {
            switchConfigService.updateSwitchConfig(switchConfigFormDto);
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, "成功", ConstantsEnum.UPDATE_SUCCESS.getDesc());
        }catch (Exception e) {
            logger.error("updateSwitchConfig failed!", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, "失败",ConstantsEnum.UPDATE_FAIL.getDesc() );
        }
    }
    /**
     * 根据ids删除开关配置      沒用
     *
     * @param ids 开关配置id数组
     * @return 删除结果
     */
    @PostMapping("/remove")
    public R<Void> remove(@RequestBody Long[] ids) {
        try{
            switchConfigService.deleteSwitchConfigByIds(ids);
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, ConstantsEnum.REMOVE_SUCCESS.getDesc());
        }catch (Exception e) {
            logger.error("deleteSwitchConfigByIds time failed!", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, ConstantsEnum.REMOVE_FAIL.getDesc());
        }
    }

    /**
     * 查询开关配置value根据key
     *
     * @return 查询结果
     */
    @GetMapping(value = "/getSwitchConfigValueByKey")
    @MethodPermission("@dp.hasBtnPermission('importBulk')")
    public R<Object> getSwitchConfigValueByKey(@RequestParam(value = "key")String key) {
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, switchConfigService.selectSwitchConfigValueByKey(key), ConstantsEnum.LIST_SUCCESS.getDesc());
    }


    /**
     * 开关配置插入自定义form表单      沒用
     *
     * @param switchConfigListDto 开关配置自定义分类
     */
    @PostMapping("/saveList")
    public R<Object> insertSwitchConfigList(@RequestBody SwitchConfigListDto switchConfigListDto) {
        try {
            switchConfigService.insertSwitchConfigList(switchConfigListDto);
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, "成功", ConstantsEnum.SAVE_SUCCESS.getDesc());
        }catch (Exception e) {
            logger.error("insertSwitchConfig failed!", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, "失败",ConstantsEnum.SAVE_FAIL.getDesc() );
        }
    }
}


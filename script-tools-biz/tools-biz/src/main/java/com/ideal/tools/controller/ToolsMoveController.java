package com.ideal.tools.controller;

import com.ideal.common.dto.R;
import com.ideal.system.common.component.aop.MethodPermission;
import com.ideal.tools.common.Constants;
import com.ideal.tools.core.web.BaseController;
import com.ideal.tools.exception.StudioException;
import com.ideal.tools.exception.ToolsException;
import com.ideal.tools.model.dto.*;
import com.ideal.tools.model.enums.ConstantsEnum;
import com.ideal.tools.model.interaction.StudioFileProjectInteractDto;
import com.ideal.tools.model.interaction.WorkflowActivitiesDto;
import com.ideal.tools.service.ICommonService;
import com.ideal.tools.service.IToolsMoveService;
import com.ideal.tools.service.interaction.StudioInteract;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.List;

/**
 * 工具箱发布迁移导入Controller
 *
 * <AUTHOR>
 * @date 2024-07-24
 */
@RestController
@RequestMapping("${app.script-tools-url:}/tools/move")
public class ToolsMoveController extends BaseController {

    private final IToolsMoveService toolsMoveService;
    private final StudioInteract studioInteract;
    private final ICommonService commonService;
    public ToolsMoveController(IToolsMoveService toolsMoveService,StudioInteract studioInteract,ICommonService commonService) {
        this.toolsMoveService = toolsMoveService;
        this.studioInteract=studioInteract;
        this.commonService=commonService;
    }

    /**
     * 发布迁移导出功能
     *
     * @param migrationExportToolsDto 导出请求实体
     * @param response 响应对象
     * @return
     */
    @PostMapping("/publishMigrationExportTools")
    @MethodPermission("@dp.hasBtnPermission('exportList')")
    public R<Object> publishMigrationExportTools(HttpServletResponse response, @RequestBody MigrationExportToolsDto migrationExportToolsDto){
        try {
            return commonService.publishMigrationExportTools(response, migrationExportToolsDto.getToolIds(), migrationExportToolsDto.getEvn(),2);
        }catch (ToolsException e) {
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, e.getMessage());
        } catch (Exception e) {
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, "export.fail");
        }
    }

    /**
     * 本地工具导出功能
     *
     * @param exportToolsDtoList 工具导出参数集合
     * @param response 响应对象
     * @return 结果
     */
    @PostMapping("/exportTools")
    public R<Object> exportTools(HttpServletResponse response, @RequestBody List<ExportToolsDto> exportToolsDtoList){
        try {
            return toolsMoveService.exportTools(response, exportToolsDtoList,getUser());
        }catch (ToolsException e) {
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, e.getMessage());
        } catch (Exception e) {
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, "export.fail");
        }
    }

    /**
     * 获取当前环境
     *
     * @return
     */
    @GetMapping("/getEnvironment")
    @MethodPermission("@dp.hasBtnPermission('execution-history') or @dp.hasBtnPermission('tool-run')" +
            " or @dp.hasBtnPermission('tool-execution') or @dp.hasBtnPermission('detailTool')" +
            " or @dp.hasBtnPermission('editTool') or @dp.hasBtnPermission('scriptTool') or @dp.hasBtnPermission('combinationTool')")
    public R getEnvironment() {
        try {
            return toolsMoveService.getEnvironment();
        } catch (ToolsException e) {
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, e.getMessage());
        }
    }

    /**
     * 校验迁移工具状态-用于导入、退回
     *
     * @param moveToolsReturnDto 迁移工具退回参数实体
     * @return 校验结果
     */
    @PostMapping("/checkMoveToolsStatus")
    @MethodPermission("@dp.hasBtnPermission('releaseImport') or @dp.hasBtnPermission('migrationBack')")
    public R<Object> checkMoveToolsStatus(@RequestBody MoveToolsReturnDto moveToolsReturnDto) {
        try {
            return toolsMoveService.checkMoveToolsStatus(moveToolsReturnDto, getUser());
        } catch (ToolsException e) {
            // 处理返回失败响应
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, e.getMessage());
        }
    }

    /**
     * 发布迁移导入退回
     *
     * @param moveToolsReturnDto 迁移工具退回参数实体
     * @return 退回结果
     */
        @PostMapping("/moveToolsReturn")
    @MethodPermission("@dp.hasBtnPermission('migrationBack') or @dp.hasBtnPermission('releaseImport')")
    public R moveToolsReturn(@RequestBody MoveToolsReturnDto moveToolsReturnDto) {
        try {
            return toolsMoveService.moveToolsReturn(moveToolsReturnDto,getUser());
        }catch (ToolsException e) {
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, e.getMessage());
        } catch (Exception e) {
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, "return.fail");
        }
    }

    /**
     * 本地工具导入
     *
     * @param file     上传文件
     * @return
     */
    @PostMapping("/importTools")
    @MethodPermission("@dp.hasBtnPermission('localImport')")
    public R importTools(@RequestParam("file") MultipartFile file) {
        try {
            return toolsMoveService.importTools(file, getUser());
        } catch (ToolsException e) {
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, e.getMessage());
        } catch (Exception e) {
            logger.error("local tools import is failed:", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, "import.fail");
        }
    }

    /**
     * 多环境发布迁移导入
     *
     * @param migrationImportToolsDto 发布迁移导入实体
     * @return
     */
    @PostMapping("/publishMigrationImportTools")
    @MethodPermission("@dp.hasBtnPermission('releaseImport')")
    public R publishMigrationImportTools(@RequestBody MigrationImportToolsDto migrationImportToolsDto) {
        try {
            return toolsMoveService.publishMigrationImportTools(migrationImportToolsDto, getUser());
        } catch (ToolsException e) {
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, e.getMessage());
        } catch (Exception e) {
            logger.error("publish migration tools import is failed!", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, "import.fail");
        }
    }

    /**
     * 获取迁移导入文件列表
     *
     * @param moveFileQueryDto 迁移文件查询实体
     * @return
     */
    @PostMapping("/getMoveFileFromServer")
    @MethodPermission("@dp.hasBtnPermission('releaseImport')")
    public R<List<MoveFileResultDto>> getMoveFileFromServer(@RequestBody MoveFileQueryDto moveFileQueryDto) {
        try {
            List<MoveFileResultDto> list = toolsMoveService.getMoveFileFromServer(moveFileQueryDto);
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, list, ConstantsEnum.LIST_SUCCESS.getDesc());
        } catch (ToolsException e) {
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, e.getMessage());
        }  catch (Exception e) {
            logger.error("get move file list error ！", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, null,ConstantsEnum.LIST_FAIL.getDesc());
        }
    }


    /**
     * 组合工具导入文件             沒用
     *
     * @param file 迁移文件查询实体  COMBINED combined
     * @return
     */
    @PostMapping("/importFileCombinedTools")
    public R importFileCombinedTools(@RequestParam("file") MultipartFile file) {
        try {
            StudioFileProjectInteractDto studioFileProjectInteractDto=new StudioFileProjectInteractDto(file.getOriginalFilename() ,
                    "zip", file.getBytes());
            studioInteract.importStudioData(studioFileProjectInteractDto, getUser());
            return R.ok("导入成功！ ");
        } catch (StudioException e) {
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, e.getMessage());
        } catch (Exception e) {
            logger.error("local tools import is failed:", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, "import.fail");
        }
    }

    /**
     * 获取迁移导出文件             沒用
     *
     * @param projectVerId 迁移文件查询实体
     * @return
     */
    @GetMapping("/exportFileCombinedTools")
    public void exportFileCombinedTools(@RequestParam(value = "projectVerId")  Long projectVerId, HttpServletResponse response){
        try (ServletOutputStream outputStream = response.getOutputStream()) {

            StudioFileProjectInteractDto studioFileProjectInteractDto=studioInteract.exportStudioData(projectVerId);

                response.setContentType("application/zip; charset=UTF-8");
                response.setCharacterEncoding("UTF-8");
                response.setHeader("Content-disposition","attachment; filename=" + URLEncoder.encode(studioFileProjectInteractDto.getFileName(), "UTF-8"));
                outputStream.write(studioFileProjectInteractDto.getFileContent());

        } catch (Exception e) {
            logger.error("get move file exportFileCombinedTools error ！", e);

        }
    }

    /**
     * 获取流程编排 流程引用              沒用
     *
     * @param studioWorkflowId 迁移文件查询实体
     * @return
     */
    @GetMapping("/getSubactivity")
    public R< List<WorkflowActivitiesDto>> getMoveFileFromServer(@RequestParam(value = "studioWorkflowId") Long studioWorkflowId) {
        try {
            List<WorkflowActivitiesDto> list = studioInteract.getWorkflowActivities(studioWorkflowId);
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, list, ConstantsEnum.LIST_SUCCESS.getDesc());
        } catch (Exception e) {
            logger.error("get move file list error ！", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, null,ConstantsEnum.LIST_FAIL.getDesc());
        }
    }


}

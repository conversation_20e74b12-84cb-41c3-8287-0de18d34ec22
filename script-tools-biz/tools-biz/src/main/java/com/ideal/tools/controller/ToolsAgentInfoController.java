package com.ideal.tools.controller;


import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.system.common.component.aop.MethodPermission;
import com.ideal.tools.common.Constants;

import com.ideal.tools.core.web.BaseController;
import com.ideal.tools.exception.InteractServiceException;
import com.ideal.tools.model.dto.AgentQueryDto;
import com.ideal.tools.model.dto.ToolsAgentResultDto;
import com.ideal.tools.model.enums.ConstantsEnum;
import org.springframework.web.bind.annotation.*;
import com.ideal.tools.model.dto.ToolsAgentInfoDto;
import com.ideal.tools.service.IToolsAgentInfoService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 设备Controller
 *
 * <AUTHOR>
 * @date 2024-06-27
 */
@RestController
@RequestMapping("${app.script-tools-url:}/tools/info/agent")
@MethodPermission("@dp.hasBtnPermission('tool-execution')")
public class ToolsAgentInfoController extends BaseController{
    private final Logger logger = LoggerFactory.getLogger(ToolsAgentInfoController.class);

    private final IToolsAgentInfoService toolsAgentInfoService;

    public ToolsAgentInfoController(IToolsAgentInfoService toolsAgentInfoService) {
        this.toolsAgentInfoService = toolsAgentInfoService;
    }

    /**
     * 查询设备列表
     *
     * @param tableQueryDto 查询条件
     * @return 查询结果
     */
    @PostMapping("/list")
    @MethodPermission("@dp.hasBtnPermission('actionScenceTools') or @dp.hasBtnPermission('detailTool')" +
            " or @dp.hasBtnPermission('editTool') or @dp.hasBtnPermission('scriptTool')")
    public R<PageInfo<ToolsAgentResultDto>> list(@RequestBody TableQueryDto<AgentQueryDto>tableQueryDto) {
        PageInfo<ToolsAgentResultDto> pages =null;
        try{
           pages = toolsAgentInfoService.selectStandardTaskComputerIdLists(
                tableQueryDto.getQueryParam(),getUser().getUserId(),
                tableQueryDto.getPageNum(),
                tableQueryDto.getPageSize()
        );
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, pages, ConstantsEnum.LIST_SUCCESS.getDesc());
    }catch (InteractServiceException e){
            logger.error("Failed to query Toolbox Agent categories !{}",e.getMessage(), e);
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE,pages ,e.getMessage());
    }catch (Exception e){
        logger.error("Failed to query Toolbox Agent categories !{}",e.getMessage(), e);
        return R.fail(Constants.REPONSE_STATUS_FAIL_CODE,pages , ConstantsEnum.LIST_FAIL.getDesc());
    }
    }


    /**
     * 查询设备详细信息         沒用
     *
     * @param id 数据唯一标识
     * @return 查询结果
     */
    @GetMapping(value = "/get")
    public R<ToolsAgentInfoDto> getAgentInfoInfo(@RequestParam(value = "id")Long id) {
        return R.ok(toolsAgentInfoService.selectToolsAgentInfoById(id));
    }

    /**
     * 新增保存设备           沒用
     *
     * @param toolsAgentInfoDto 添加数据
     * @return 添加结果
     */
    @PostMapping("/save")
    public R<Void> save(@RequestBody ToolsAgentInfoDto toolsAgentInfoDto) {
        if (toolsAgentInfoService.insertToolsAgentInfo(toolsAgentInfoDto) > 0) {
            return R.ok();
        }
        return R.fail();
    }

    /**
     * 修改保存设备           沒用
     *
     * @param toolsAgentInfoDto 更新数据
     * @return 更新结果
     */
    @PostMapping("/update")
    public R<Void> update(@RequestBody ToolsAgentInfoDto toolsAgentInfoDto) {
        toolsAgentInfoService.updateToolsAgentInfo(toolsAgentInfoDto);
        return R.ok();
    }


    /**
     * 删除设备             沒用
     *
     * @param ids 主键列表
     * @return 删除结果
     */
    @PostMapping("/remove")
    public R<Void> remove(@RequestParam(value = "ids") Long[] ids) {
        toolsAgentInfoService.deleteToolsAgentInfoByIds(ids);
        return R.ok();
    }
}

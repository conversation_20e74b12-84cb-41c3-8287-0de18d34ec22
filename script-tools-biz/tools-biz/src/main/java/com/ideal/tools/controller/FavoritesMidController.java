package com.ideal.tools.controller;


import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.common.dto.ValidateError;
import com.ideal.common.util.spring.MessageUtil;
import com.ideal.system.common.component.aop.MethodPermission;
import com.ideal.tools.common.Constants;
import com.ideal.tools.common.HttpClientUtil;
import com.ideal.tools.core.web.BaseController;
import com.ideal.tools.model.bean.FavoritesToolsBean;
import com.ideal.tools.model.dto.*;
import com.ideal.tools.model.enums.ConstantsEnum;
import com.ideal.tools.service.IFavoritesMidService;
import com.ideal.tools.service.IToolsExecuteService;
import com.ideal.tools.service.IToolsInfoService;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 场景工具关联工具中间Controller
 *
 * <AUTHOR>
 * @date 2024-06-20
 */
@RestController
@RequestMapping("${app.script-tools-url:}/tools/mid")
@MethodPermission("@dp.hasBtnPermission('scene-tools')")
public class FavoritesMidController extends BaseController {
    private final IFavoritesMidService favoritesMidService;
    private final IToolsInfoService toolsInfoService;
    private final IToolsExecuteService toolsExecuteService;
    public FavoritesMidController(IFavoritesMidService favoritesMidService, IToolsInfoService toolsInfoService, IToolsExecuteService toolsExecuteService) {
        this.favoritesMidService = favoritesMidService;
        this.toolsInfoService = toolsInfoService;
        this.toolsExecuteService = toolsExecuteService;
    }

    /**
     * 查询场景工具关联工具中间列表
     *
     * @param tableQueryDto 查询条件
     * @return 查询结果
     */
    @PostMapping("/list")
    public R<PageInfo<ToolsInfoDto>> list(@RequestBody TableQueryDto<ToolsQueryDto> tableQueryDto) {
        PageInfo<ToolsInfoDto> pages =null;
        try{
            if (tableQueryDto.getQueryParam().getFavoritesId() == null) {
                return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, null, "List favorites not exists");
            }
            pages = favoritesMidService.selectFavoritesMidList(
                    tableQueryDto.getQueryParam(),
                    tableQueryDto.getPageNum(),
                    tableQueryDto.getPageSize()
            );
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, pages, ConstantsEnum.LIST_SUCCESS.getDesc());
        }catch (Exception e){
            logger.error("Failed to query ToolboxList custom categories !{}",e.getMessage(), e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE,pages , ConstantsEnum.LIST_FAIL.getDesc());
        }
    }
    /**
     * 添加工具页面查询列表
     *
     * @param tableQueryDto 查询条件
     * @return 查询结果
     */
    @PostMapping("/addToollist")
    @MethodPermission("@dp.hasBtnPermission('pushSceneTools')")
    public R<PageInfo<FavoritesToolsBean>> addToollist(@RequestBody TableQueryDto<ToolsQueryDto> tableQueryDto) {

        PageInfo<FavoritesToolsBean> pages =null;
        try{
            if (tableQueryDto.getQueryParam().getFavoritesId() == null) {
                return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, null, "Favorites not exists");
            }
            pages = favoritesMidService.selectToolsInfoListForFavorites(
                    tableQueryDto.getQueryParam(),
                    tableQueryDto.getPageNum(),
                    tableQueryDto.getPageSize(),getUser()
            );

            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, pages, ConstantsEnum.LIST_SUCCESS.getDesc());
        }catch (Exception e){
            logger.error("Failed to query ToolboxForFavorites custom categories !{}",e.getMessage(), e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE,pages , ConstantsEnum.LIST_FAIL.getDesc());
        }
    }

    /**
     * 查询场景工具关联工具中间详细信息    沒用
     *
     * @param id 数据唯一标识
     * @return 查询结果
     */
    @GetMapping(value = "/get")
    public R<FavoritesMidDto> getAgentInfoInfo(@RequestParam(value = "id")Long id) {
        return R.ok(favoritesMidService.selectFavoritesMidById(id));
    }


    /**
     * 查询工具箱信息详细信息
     *
     * @param toolsAuditDto 数据唯一标识
     * @return 查询结果
     */
    @PostMapping(value = "/getToolDetails")
    @MethodPermission("@dp.hasBtnPermission('actionToolRun') or @dp.hasBtnPermission('actionScenceTools')")
    public R<ToolsDto> getToolInfo(HttpServletRequest request, @RequestBody ToolsAuditDto toolsAuditDto) {
        ToolsDto dto = null;
        try{
            String ipAddress = HttpClientUtil.getIpAddress(request);
            dto = toolsExecuteService.getToolsCombinedInfoAll(toolsAuditDto,getUser(),ipAddress);
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, dto, ConstantsEnum.LIST_SUCCESS.getDesc());
        }catch (Exception e){
            logger.error("query getToolInfo  by id is error", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE,dto, ConstantsEnum.LIST_FAIL.getDesc());
        }

    }
    /**
     * 新增保存场景工具关联工具中间
     *
     * @param favoritesMidDto 添加数据
     * @return 添加结果
     */
        @PostMapping("/save")
    @MethodPermission("@dp.hasBtnPermission('pushSceneTools')")
    public R<Object> save(@RequestBody FavoritesMidDto favoritesMidDto,BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            List<ValidateError> validateErrorList = bindingResult.getFieldErrors().stream()
                    .map(item -> new ValidateError(item.getField(), MessageUtil.message(item.getDefaultMessage())))
                    .collect(Collectors.toList());
            Map<String, List<ValidateError>> validateError = new HashMap<>(1);
            validateError.put("validateError", validateErrorList);
            return R.fail(Constants.REPONSE_STATUS_VALIDATA_CODE, validateError, "validate.error");
        }
        try {
            if (favoritesMidDto.getFavoritesId() == null){
                return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, null, "Favorites not exists");
            }
            favoritesMidService.deleteFavoritesBindRelation(favoritesMidDto);
            favoritesMidService.insertFavoritesMid(favoritesMidDto,getUser());
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, "", ConstantsEnum.SAVE_SUCCESS.getDesc());
        }catch (Exception e) {
            logger.error("New favoritesMid customization failed!", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, null,ConstantsEnum.SAVE_FAIL.getDesc() );
        }
    }

    /**
     * 修改保存场景工具关联工具中间     沒用
     *
     * @param favoritesMidDto 更新数据
     * @return 更新结果
     */
    @PostMapping("/update")
    public R<Void> update(@RequestBody FavoritesMidDto favoritesMidDto) {
        favoritesMidService.updateFavoritesMid(favoritesMidDto);
        return R.ok();
    }


    /**
     * 删除场景工具关联工具中间
     *
     * @param favoritesMidDto 主键列表
     * @return 删除结果
     */
    @PostMapping("/remove")
    @MethodPermission("@dp.hasBtnPermission('pushSceneTools')")
    public R<Void> remove(@RequestBody FavoritesMidDto favoritesMidDto) {

        try{
            favoritesMidService.deleteFavoritesMidByIds(favoritesMidDto,getUser());
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, ConstantsEnum.REMOVE_SUCCESS.getDesc());
        }catch (Exception e) {
            logger.error("Delete favoritesMid custom classification failed!", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, ConstantsEnum.REMOVE_FAIL.getDesc());
        }
    }
}

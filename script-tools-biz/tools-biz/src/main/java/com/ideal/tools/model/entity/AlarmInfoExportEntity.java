package com.ideal.tools.model.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import java.util.Date;

/**
 * 告警信息导出对象 ieai_tb_alarm_info
 *
 * <AUTHOR>
 */
public class AlarmInfoExportEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 业务系统名称
     */
    private String sysName;
    /**
     * 业务系统id
     */
    private Long sysId;
    /**
     * 首次报警时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date firstAlarmTime;
    /**
     * 最后报警时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date lastAlarmTime;
    /**
     * 事件单号
     */
    private String eventId;
    /**
     * 事件状态 0新事件 1已恢复未关闭 2已恢复并且关闭 3已关闭 4已强制关闭
     */
    private Integer eventStatus;
    /**
     * 报警级别 1 一级报警 2二级报警 3三级报警
     */
    private Integer alarmLevel;
    /**
     * 报警ip
     */
    private String alarmIp;
    /**
     * 报警大类
     */
    private String alarmBigClass;
    /**
     * 报警中类
     */
    private String alarmMiddleClass;
    /**
     * 报警小类
     */
    private String alarmSubClass;
    /**
     * 报警描述
     */
    private String alarmDesc;
    /**
     * 报警实例
     */
    private String alarmInstance;
    /**
     * 报警状态 1未处置报警 2已忽略报警 3已处置报警 4维护期报警
     */
    private Integer alarmStatus;
    /**
     * 所属机构
     */
    private String orgName;
    /**
     * 报警自愈状态 0未匹配到自愈工具 1不可自愈 2已自愈 3自愈失败 4正在自愈
     */
    private Integer healingStatus;
    /**
     * 全局系统 1全局系统  2非全局系统 （默认0）
     */
    private Integer isGlobal;
    /**
     * 是否可恢复 1可恢复 0不可恢复
     */
    private Integer isRecovery;
    /**
     * 匹配工具数量
     */
    private Integer toolsCount;
    /**
     * 开始时间
     */
    private String startTime;
    /**
     * 结束时间
     */
    private String endTime;

    public String getSysName() {
        return sysName;
    }

    public void setSysName(String sysName) {
        this.sysName = sysName;
    }

    public Long getSysId() {
        return sysId;
    }

    public void setSysId(Long sysId) {
        this.sysId = sysId;
    }

    public Date getFirstAlarmTime() {
        return firstAlarmTime;
    }

    public void setFirstAlarmTime(Date firstAlarmTime) {
        this.firstAlarmTime = firstAlarmTime;
    }

    public Date getLastAlarmTime() {
        return lastAlarmTime;
    }

    public void setLastAlarmTime(Date lastAlarmTime) {
        this.lastAlarmTime = lastAlarmTime;
    }

    public String getEventId() {
        return eventId;
    }

    public void setEventId(String eventId) {
        this.eventId = eventId;
    }

    public Integer getEventStatus() {
        return eventStatus;
    }

    public void setEventStatus(Integer eventStatus) {
        this.eventStatus = eventStatus;
    }

    public Integer getAlarmLevel() {
        return alarmLevel;
    }

    public void setAlarmLevel(Integer alarmLevel) {
        this.alarmLevel = alarmLevel;
    }

    public String getAlarmIp() {
        return alarmIp;
    }

    public void setAlarmIp(String alarmIp) {
        this.alarmIp = alarmIp;
    }

    public String getAlarmBigClass() {
        return alarmBigClass;
    }

    public void setAlarmBigClass(String alarmBigClass) {
        this.alarmBigClass = alarmBigClass;
    }

    public String getAlarmMiddleClass() {
        return alarmMiddleClass;
    }

    public void setAlarmMiddleClass(String alarmMiddleClass) {
        this.alarmMiddleClass = alarmMiddleClass;
    }

    public String getAlarmSubClass() {
        return alarmSubClass;
    }

    public void setAlarmSubClass(String alarmSubClass) {
        this.alarmSubClass = alarmSubClass;
    }

    public String getAlarmDesc() {
        return alarmDesc;
    }

    public void setAlarmDesc(String alarmDesc) {
        this.alarmDesc = alarmDesc;
    }

    public String getAlarmInstance() {
        return alarmInstance;
    }

    public void setAlarmInstance(String alarmInstance) {
        this.alarmInstance = alarmInstance;
    }

    public Integer getAlarmStatus() {
        return alarmStatus;
    }

    public void setAlarmStatus(Integer alarmStatus) {
        this.alarmStatus = alarmStatus;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public Integer getHealingStatus() {
        return healingStatus;
    }

    public void setHealingStatus(Integer healingStatus) {
        this.healingStatus = healingStatus;
    }

    public Integer getIsGlobal() {
        return isGlobal;
    }

    public void setIsGlobal(Integer isGlobal) {
        this.isGlobal = isGlobal;
    }

    public Integer getIsRecovery() {
        return isRecovery;
    }

    public void setIsRecovery(Integer isRecovery) {
        this.isRecovery = isRecovery;
    }

    public Integer getToolsCount() {
        return toolsCount;
    }

    public void setToolsCount(Integer toolsCount) {
        this.toolsCount = toolsCount;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    @Override
    public String toString() {
        return "AlarmInfoExportEntity{" +
                "sysName='" + sysName + '\'' +
                ", sysId=" + sysId +
                ", firstAlarmTime=" + firstAlarmTime +
                ", lastAlarmTime=" + lastAlarmTime +
                ", eventId='" + eventId + '\'' +
                ", eventStatus=" + eventStatus +
                ", alarmLevel=" + alarmLevel +
                ", alarmIp='" + alarmIp + '\'' +
                ", alarmBigClass='" + alarmBigClass + '\'' +
                ", alarmMiddleClass='" + alarmMiddleClass + '\'' +
                ", alarmSubClass='" + alarmSubClass + '\'' +
                ", alarmDesc='" + alarmDesc + '\'' +
                ", alarmInstance='" + alarmInstance + '\'' +
                ", alarmStatus=" + alarmStatus +
                ", orgName='" + orgName + '\'' +
                ", healingStatus=" + healingStatus +
                ", isGlobal=" + isGlobal +
                ", isRecovery=" + isRecovery +
                ", toolsCount=" + toolsCount +
                ", startTime='" + startTime + '\'' +
                ", endTime='" + endTime + '\'' +
                '}';
    }
}


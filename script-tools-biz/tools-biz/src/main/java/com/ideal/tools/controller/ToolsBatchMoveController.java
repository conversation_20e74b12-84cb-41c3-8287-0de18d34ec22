package com.ideal.tools.controller;

import com.ideal.common.dto.R;
import com.ideal.system.common.component.aop.MethodPermission;
import com.ideal.tools.common.Constants;
import com.ideal.tools.core.web.BaseController;
import com.ideal.tools.exception.ToolsException;
import com.ideal.tools.model.dto.MigrationExportToolsDto;
import com.ideal.tools.model.dto.MigrationImportToolsDto;
import com.ideal.tools.model.enums.ConstantsEnum;
import com.ideal.tools.service.IToolsBatchMoveService;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

/**
 * 批量导出Controller
 *
 * <AUTHOR>
 * @date 2024-11-14
 */
@RestController
@RequestMapping("${app.script-tools-url:}/tools/batchmove")
public class ToolsBatchMoveController extends BaseController {

    private final IToolsBatchMoveService toolsBatchMoveService;
    private final RedisTemplate<String, String> redisTemplate;
    public ToolsBatchMoveController(IToolsBatchMoveService toolsBatchMoveService,RedisTemplate<String, String> redisTemplate) {
        this.toolsBatchMoveService = toolsBatchMoveService;
        this.redisTemplate = redisTemplate;
    }

    /**
     * 批量导出功能
     *
     * @param migrationExportToolsDto 导出请求实体
     * @param response 响应对象
     * @return
     */
    @PostMapping("/publishBatchExportTools")
    @MethodPermission("@dp.hasBtnPermission('exportBulk')")
    public R<Object> publishBatchExportTools(HttpServletResponse response, @RequestBody MigrationExportToolsDto migrationExportToolsDto){
        try {
            return toolsBatchMoveService.publishBatchExportTools(response, migrationExportToolsDto.getToolIds(), migrationExportToolsDto.getEvn());
        }catch (ToolsException e) {
            logger.error("fail",e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, e.getMessage());
        } catch (Exception e) {
            logger.error("fail",e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, "export.fail");
        }finally {
            logger.info("删除redis标识");
            redisTemplate.delete("exOrInporting");
        }
    }

    /**
     * 获取该路径下的zip文件
     *
     * @param path 路径
     * @param response 响应对象
     * @return
     */
    @GetMapping("/getZipNameByPath")
    @MethodPermission("@dp.hasBtnPermission('importBulk')")
    public R<Object> getZipNameByPath(HttpServletResponse response, @RequestParam(value = "path")String path){
        try {
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, toolsBatchMoveService.getZipNameByPath(response, path), ConstantsEnum.LIST_SUCCESS.getDesc());
        }catch (Exception e) {
            logger.error("get sftp files is fail",e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, "获取sftp文件失败!!");
        }
    }

    /**
     * 批量导入
     *
     * @param migrationImportToolsDto 导入实体
     * @return
     */
    @PostMapping("/publishBatchImportTools")
    @MethodPermission("@dp.hasBtnPermission('importBulk')")
    public R publishBatchImportTools(@RequestBody MigrationImportToolsDto migrationImportToolsDto, @RequestParam(value = "path")String path) {
        try {
            return toolsBatchMoveService.publishBatchImportTools(migrationImportToolsDto, getUser(),path);
        } catch (Exception e) {
            logger.error("publish migration tools import is failed!", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, "import.fail");
        }finally {
            logger.info("删除redis标识");
            redisTemplate.delete("exOrInporting");
        }
    }
}

package com.ideal.tools.controller;


import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.system.common.component.aop.MethodPermission;
import com.ideal.tools.common.Constants;
import com.ideal.tools.common.ControlsTimeUtil;
import com.ideal.tools.common.EasyExcelUtil;
import com.ideal.tools.core.web.BaseController;
import com.ideal.tools.model.bean.UsageReportExcle;
import com.ideal.tools.model.dto.*;
import com.ideal.tools.model.enums.*;
import com.ideal.tools.service.ILogAuditService;
import com.ideal.tools.service.IUsageReportService;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;


/**
 * 工具使用报表自定义分类Controller
 *
 * <AUTHOR>
 * @date 2024-07-01
 */
@RestController
@RequestMapping("${app.script-tools-url:}/tools/usageReport")
public class UsageReportController extends BaseController {

    private final IUsageReportService usageReportService;
    private final ILogAuditService logAuditService;
    
    public UsageReportController(IUsageReportService usageReportService, ILogAuditService logAuditService) {
        this.usageReportService = usageReportService;
        this.logAuditService =  logAuditService;
    }

    /**
     * 查询工具使用报表自定义分类列表
     *
     * @param tableQueryDto 查询条件
     * @return 查询结果
     */
    @PostMapping("/list")
    public R<PageInfo<UsageReportResultDto>> list(@RequestBody TableQueryDto<UsageReportQueryDto> tableQueryDto) {
        PageInfo<UsageReportResultDto> pages =null;
        try{
            pages = usageReportService.selectUsageReportList(
                    tableQueryDto.getQueryParam(),
                    tableQueryDto.getPageNum(),
                    tableQueryDto.getPageSize()
            );
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, pages, ConstantsEnum.LIST_SUCCESS.getDesc());
        }catch (Exception e){
            logger.error("Failed to query usageReport custom !{}",e.getMessage(), e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE,pages , ConstantsEnum.LIST_FAIL.getDesc());
        }
    }

    /**
     * 工具使用报表插入自定义              沒用
     *
     * @param usageReportDto 工具使用报表自定义分类
     */
    @PostMapping("/save")
    public R<Object> insertUsageReport(@RequestBody UsageReportDto usageReportDto) {
        try {
            usageReportService.insertUsageReport(usageReportDto,getUser());
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, "成功", ConstantsEnum.SAVE_SUCCESS.getDesc());
        }catch (Exception e) {
            logger.error("insertUsageReport failed!", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, "失败", ConstantsEnum.SAVE_FAIL.getDesc() );
        }
    }

    /**
     * 工具使用报表下载，最多下载3000行
     *
     * @param queryDto 下载条件
     * @return 查询结果
     */
    @PostMapping("/exportUsageReportData")
    @MethodPermission("@dp.hasBtnPermission('exportUsingReports')")
    public void exportUsageReportData(HttpServletResponse response, @RequestBody UsageReportQueryDto queryDto){
        try{
            List<UsageReportExcle> list = usageReportService.exportUsageReportData(queryDto);
            // 使用 EasyExcel 将数据写入响应输出流
            String fileName =  OperatModuleEnum.TOOL_USAGE_REPORT.getRemark() + ControlsTimeUtil.formatDateByFormat(new Date(), "YYYY-MM-dd hh_mm_ss");
            EasyExcelUtil.writeExcel(response, list, fileName,  OperatModuleEnum.TOOL_USAGE_REPORT.getRemark(), UsageReportExcle.class);
            String operatingContent = CommonTypeEnum.EXPORT.getName() + fileName + "到本地";
            String resultDesc = OperatModuleEnum.TOOL_USAGE_REPORT.getRemark() + OperatTypeEnum.USAGE_REPORT_EXPORT.getRemark();
            LogAuditDto logAuditDto = new LogAuditDto(OperatModuleEnum.TOOL_USAGE_REPORT.getCode(), OperatTypeEnum.USAGE_REPORT_EXPORT.getCode(), operatingContent);
            logAuditDto.setResult(OperatResultEnum.SUCCESS.getCode());
            logAuditDto.setResultDesc(resultDesc + OperatResultEnum.SUCCESS.getDesc());
            logAuditService.insertLogAudit(logAuditDto);
            logger.info("exportUsageReportData success");
        } catch (Exception e) {
            logger.error("exportUsageReportData error", e);
        }
    }

    /**
     * 修改脚本执行完的结果           沒用
     *
     * @param usageReportDto 更新数据
     * @return 更新结果
     */
    @PostMapping("/update")
    public R<Void> update(@RequestBody UsageReportDto usageReportDto) {
        usageReportService.updateToolExecuteStatus(usageReportDto);
        return R.ok();
    }


    /**
     * 查询人员下拉列表
     */
    @GetMapping("/user")
    public R<List<UserPullDto>> getUserList() {
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, usageReportService.selectUserList(), "查询成功");
    }
}


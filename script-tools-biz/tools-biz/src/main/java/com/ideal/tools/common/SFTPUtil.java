package com.ideal.tools.common;

import com.ideal.tools.ftp.FtpListBean;
import com.ideal.tools.model.constant.ToolsMoveConstants;
import com.jcraft.jsch.*;
import com.jcraft.jsch.ChannelSftp.LsEntry;
import org.apache.commons.compress.utils.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.util.*;
import java.util.stream.Collectors;

/**
 * sftp工具类
 */
public class SFTPUtil {
    private static final Logger _log = LoggerFactory.getLogger(SFTPUtil.class);
    private ChannelSftp sftp;

    private Session             session;
    /** SFTP 登录用户名*/
    private String              username;
    /** SFTP 登录密码*/
    private String              password;
    /** 私钥 */
    private String              privateKey;
    /** SFTP 服务器地址IP地址*/
    private String              host;
    /** SFTP 端口*/
    private int                 port;

    /**
     * 构造基于密码认证的sftp对象
     */
    public SFTPUtil(String username, String password, String host, int port)
    {
        this.username = username;
        this.password = password;
        this.host = host;
        this.port = port;
    }

    /**
     * 构造基于秘钥认证的sftp对象
     */
    public SFTPUtil(String username, String host, int port, String privateKey)
    {
        this.username = username;
        this.host = host;
        this.port = port;
        this.privateKey = privateKey;
    }

    public SFTPUtil()
    {
    }

    /**
     * 连接sftp服务器
     */
    public void login () throws JSchException {

        JSch jsch = new JSch();
        if (privateKey != null)
        {
            jsch.addIdentity(privateKey);// 设置私钥
        }

        session = jsch.getSession(username, host, port);

        if (password != null)
        {
            session.setPassword(password);
        }
        Properties config = new Properties();
        config.put("StrictHostKeyChecking", "no");

        session.setConfig(config);
        session.connect(5000);

        Channel channel = session.openChannel("sftp");
        channel.connect();

        sftp = (ChannelSftp) channel;

    }

    /**
     * 关闭连接 server
     */
    public void logout ()
    {
        if (sftp != null)
        {
            if (sftp.isConnected())
            {
                sftp.disconnect();
            }
        }
        if (session != null)
        {
            if (session.isConnected())
            {
                session.disconnect();
            }
        }
    }

    /**
     * 将输入流的数据上传到sftp作为文件。文件完整路径=basePath+directory
     * @param basePath  服务器的基础路径
     * @param directory  上传到该目录
     * @param sftpFileName  sftp端文件名
     * @param input   输入流
     */
    public void upload(String basePath, String directory, String sftpFileName, InputStream input) throws SftpException {
        try {
            sftp.cd(basePath);
            sftp.cd(directory);
        } catch (SftpException e) {
            // 目录不存在，则创建文件夹
            String[] dirs = directory.split("/");
            StringBuilder tempPathBuilder = new StringBuilder(basePath);
            for (String dir : dirs) {
                if (null == dir || "".equals(dir)) {
                    continue;
                }
                tempPathBuilder.append("/").append(dir);
                String tempPath = tempPathBuilder.toString();
                try {
                    sftp.cd(tempPath);
                } catch (SftpException ex) {
                    sftp.mkdir(tempPath);
                    sftp.cd(tempPath);
                }
            }
        }
        sftp.put(input, sftpFileName);  // 上传文件
    }

    /**
     * 上传到sftp
     * @param ftpPath  路径
     * @param file  sftp端文件名
     */
    public Boolean uploadFile(String ftpPath, File file) {
        Boolean flag = true;
        try (InputStream fileInputStream = new FileInputStream(file)) {
            try {
                login();
            } catch (JSchException e) {
                _log.error("SFTP login failed :", e);
                flag = false;
            }
            try {
                upload("", ftpPath, file.getName(), fileInputStream);
            } catch (SftpException e) {
                _log.error("SFTP failed to upload file:", e);
                flag = false;
            }
        } catch (IOException ex) {
            _log.error("SFTP create input stream is error :", ex);
            flag = false;
        } finally {
            logout();
        }
        return flag;
    }
    /**
     * 上传到sftp
     *
     * @param ftpPath 路径
     * @param zipFileName sftp端文件名
     * @param byteData 字节数组
     */
    public Boolean uploadZipFile(String ftpPath, String zipFileName, byte[] byteData) {
        Boolean flag = true;
        try (InputStream inputStream = new ByteArrayInputStream(byteData)) {
            try {
                this.login();
            } catch (JSchException e) {
                _log.error("SFTP login failed :", e);
                flag = false;
            }
            try {
                this.upload("", ftpPath, zipFileName, inputStream);
            } catch (SftpException e) {
                _log.error("SFTP failed to upload zip file:", e);
                flag = false;
            }
        } catch (IOException ex) {
            _log.error("SFTP create input stream is error :", ex);
            flag = false;
        } finally {
            logout();
        }
        return flag;
    }

    /**
     * 下载文件。
     * @param directory 下载目录
     * @param downloadFile 下载的文件
     * @param saveFile 存在本地的路径
     */
    public void download(String directory, String downloadFile, String saveFile)
            throws SftpException, IOException {
        if (directory != null && !"".equals(directory))
        {
            sftp.cd(directory);
        }

        File file = new File(saveFile);
        if (!file.exists()) {
            boolean fileCreated = file.createNewFile();
            if (!fileCreated) {
                throw new IOException("Failed to create new file: " + saveFile);
            }
        }

        try (FileOutputStream fileOutputStream = new FileOutputStream(file)) {
            sftp.get(downloadFile, fileOutputStream);
        } catch (IOException e) {
            _log.info("Error during file download:", e);
            throw e;
        }
    }

    /**
     * 下载文件
     * @param directory 下载目录
     * @param downloadFile 下载的文件名
     * @return 字节数组
     */
    public byte[] download(String directory, String downloadFile) throws SftpException, IOException
    {
        if (directory != null && !"".equals(directory))
        {
            sftp.cd(directory);
        }
        InputStream is = sftp.get(downloadFile);

        byte[] fileData = IOUtils.toByteArray(is);

        return fileData;
    }

    /**
     * 删除文件
     * @param directory 要删除文件所在目录
     * @param deleteFile 要删除的文件
     */
    public void delete(String directory, String deleteFile) throws SftpException
    {
        sftp.cd(directory);
        sftp.rm(deleteFile);
    }

    /**
     * 列出目录下的文件
     * @param directory 要列出的目录
     */
    public Vector<?> listFiles(String directory) throws SftpException
    {
        return sftp.ls(directory);
    }

    public Map getListSftp (String basePath) throws SftpException
    {
        Map<String, Object> rsMap = new LinkedHashMap<String, Object>();
        List<FtpListBean> list = new ArrayList<FtpListBean>();
        int index = 0;
        try
        {
            sftp.cd(basePath);
            Vector<String> files = sftp.ls("*");
            for (int i = 0; i < files.size(); i++)
            {
                Object obj = files.elementAt(i);
                if (obj instanceof com.jcraft.jsch.ChannelSftp.LsEntry)
                {
                    LsEntry entry = (LsEntry) obj;
                    if (!"".equals(entry.getFilename()) && entry.getFilename().endsWith(".tar"))
                    {
                        index ++;
                        FtpListBean ftpListBean = new FtpListBean();
                        ftpListBean.setGenFTPFile(basePath);
                        ftpListBean.setCurFtpFileName(entry.getFilename());
                        list.add(ftpListBean);
                    }

                }
            }
            _log.info("gd import page successfully obtained sftp compressed file directory ！！");
            rsMap.put("dataList", list);
            rsMap.put("total", index);
        } catch (SftpException e)
        {
            _log.error("ls directory failed",e);
        }
        return rsMap;
    }

    //同步获得sftp目录
    public Map getGDTBList(String basePath) throws SftpException
    {
        Map<String, Object> rsMap = new LinkedHashMap<String, Object>();
        List<FtpListBean> list = new ArrayList<FtpListBean>();
        int index = 0;
        try
        {
            sftp.cd(basePath);
            Vector<String> files = sftp.ls("*");
            for (int i = 0; i < files.size(); i++)
            {
                Object obj = files.elementAt(i);
                if (obj instanceof com.jcraft.jsch.ChannelSftp.LsEntry)
                {
                    LsEntry entry = (LsEntry) obj;

                    if (!"".equals(entry.getFilename()) && entry.getFilename().endsWith(".xml"))
                    {
                        index ++;
                        FtpListBean ftpListBean = new FtpListBean();
                        ftpListBean.setGenFTPFile(basePath);
                        ftpListBean.setCurFtpFileName(entry.getFilename());
                        list.add(ftpListBean);
                    }

                }
            }
            if(!list.isEmpty()) {
                _log.info("gd getGDTBList success！！");
            }else {
                _log.info("gd getGDTBList is empty！！");
            }
            rsMap.put("dataList", list);
            rsMap.put("total", index);
        } catch (SftpException e)
        {
            _log.error("get sftp directory failed",e);
        }
        return rsMap;
    }

    //sftp文件复制2
    public Boolean copyFileSftp(String basePath , String fileName , String toFileString)    throws SftpException {
        _log.info("copyFileSftp start！！");
        Boolean flaBoolean = false;
        Channel upChannel = null;
        Channel downChannel = null;
        ChannelSftp uploadChannel = null;
        ChannelSftp downloadChannel = null;
        InputStream inputStream = null;
        String source = basePath + fileName;
        String target = toFileString + fileName;

        try {
            sftp.cd(toFileString);
        } catch (SftpException sException) {
            if (sftp.SSH_FX_NO_SUCH_FILE == sException.id) {
                sftp.mkdir(toFileString);
            }
        }

        try {
            downChannel = session.openChannel("sftp");
            upChannel = session.openChannel("sftp");
            upChannel.connect();
            downChannel.connect();
            uploadChannel = (ChannelSftp) upChannel;
            downloadChannel = (ChannelSftp) downChannel;
            inputStream = downloadChannel.get(source);

            uploadChannel.put(inputStream, target, new SftpProgressMonitor() {
                @Override
                public void init(int i, String s, String s1, long l) {
                    _log.info("copyFileSftp init");
                }

                @Override
                public boolean count(long l) {
                    _log.info("count l = " + l);
                    return true;
                }

                @Override
                public void end() {
                    _log.info("copyFileSftp end");
                }
            });

            _log.info("copyFileSftp success！！");
            flaBoolean = true;
        } catch (JSchException e) {
            _log.info("copyFileSftp failed！！", e);
        } catch (SftpException e) {
            _log.info("copyFileSftp failed！！", e);
        } finally {
            if (uploadChannel != null && uploadChannel.isConnected()) {
                uploadChannel.disconnect();
            }
            if (downloadChannel != null && downloadChannel.isConnected()) {
                downloadChannel.disconnect();
            }
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    _log.error("Failed to close InputStream", e);
                }
            }
        }

        return flaBoolean;
    }
    /**
     * 获取ftp路径下的 所有文件
     * @param basePath
     * @return map
     * @throws SftpException
     */
    public Map getSftpFileList(String basePath) throws SftpException
    {
        Map<String, Object> rsMap = new LinkedHashMap<>();
        LinkedList<FtpListBean> list = new LinkedList<>();
        int index = 0;
        try
        {
            sftp.cd(basePath);
            Vector<String> files = sftp.ls("*");
            HashMap<Long,ArrayList<FtpListBean>> dataFile = new HashMap<>();

            for (int i = 0; i < files.size(); i++)
            {
                Object obj = files.elementAt(i);
                if (obj instanceof com.jcraft.jsch.ChannelSftp.LsEntry)
                {
                    LsEntry entry = (LsEntry) obj;
                    if (!entry.getAttrs().isDir()) {
                        index ++;
                        FtpListBean ftpListBean = new FtpListBean();
                        ftpListBean.setGenFTPFile(basePath);
                        ftpListBean.setCurFtpFileName(entry.getFilename());
                        Date lastModifiedTime = getLastModifiedTime(basePath + ToolsMoveConstants.SLASH + entry.getFilename());
                        long time = lastModifiedTime.getTime();
                        if (!dataFile.containsKey(time)){
                            dataFile.put(time,new ArrayList<>());
                        }
                        dataFile.get(time).add(ftpListBean);
                    }
                }
            }
            //获取文件进行及排序
            ArrayList<Map.Entry<Long, ArrayList<FtpListBean>>> mapList = new ArrayList<>(dataFile.entrySet());
            mapList.sort(Map.Entry.comparingByKey(Comparator.reverseOrder()));
            for (Map.Entry<Long, ArrayList<FtpListBean>> entry : mapList) {
                ArrayList<FtpListBean> value = entry.getValue();
                list.addAll(value);
            }
            if(!list.isEmpty()) {
                _log.info("sftp getSftpFileList success！！");
            }else {
                _log.info("sftp getSftpFileList is empty！！");
            }
        } catch (SftpException e)
        {
            _log.error("get sftp all files in this directory fail",e);
            String [] dirs = basePath.split("/");
            StringBuilder tempPathBuilder = new StringBuilder();
            for (String dir : dirs) {
                if (dir == null || "".equals(dir)) {
                    continue;
                }
                tempPathBuilder.append("/").append(dir);
                String tempPath = tempPathBuilder.toString();
                try {
                    sftp.cd(tempPath);
                } catch (SftpException e1) {
                    sftp.mkdir(tempPath);
                    sftp.cd(tempPath);
                }
            }
        }
        rsMap.put("dataList", list);
        rsMap.put("total", index);
        return rsMap;
    }

    //获取文件最后修改时间
    public Date getLastModifiedTime(String srcSftpFilePath) {
        try { SftpATTRS sftpATTRS = sftp.lstat(srcSftpFilePath);
            int mTime = sftpATTRS.getMTime();
            Date lastModified = new Date(mTime * 1000L);
            return lastModified;
        } catch (Exception e) {
            _log.error("time format convert failed",e);
        }
        return new Date();
    }

    //判断路径目录下的文件是否存在
    public boolean isExistDir(String path,String downloadFile)  {
        boolean  isExist=false;
        try {
            login();
        } catch (JSchException e) {
            _log.error("SFTP login failed :", e);
            isExist = false;
        }
        if (path != null && !"".equals(path)) {
            try {
                sftp.cd(path);
                InputStream is = sftp.get(downloadFile);
                if (null!=is){
                    isExist=true;
                }
            } catch (Exception e) {
                if (e.getMessage().toLowerCase().equals("no such file")) {
                    isExist = false;
                }
            }
        }
        return isExist;
    }

    /**
     * 删除整个路径下的文件
     * @param directory 要删除文件所在目录
     */
    public void deleteDir(String directory) throws SftpException {
        boolean isExist = true;
        try {
            login();
        } catch (JSchException e) {
            _log.error("SFTP login failed :", e);
        }
        try {
            sftp.cd(directory);
            // 获取文件夹内容并删除所有文件
            List<ChannelSftp.LsEntry> list = sftp.ls("*");
            for (ChannelSftp.LsEntry entry : list) {
                if (!entry.getAttrs().isDir()) {
                    sftp.rm(entry.getFilename());
                }
            }
        } catch (Exception e) {
            if (e.getMessage().toLowerCase().equals("no such file")) {
                isExist = true;
            }
        }
    }

}

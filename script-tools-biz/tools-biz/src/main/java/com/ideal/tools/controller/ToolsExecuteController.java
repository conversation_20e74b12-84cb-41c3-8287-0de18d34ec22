package com.ideal.tools.controller;


import javax.servlet.http.HttpServletRequest;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.system.common.component.aop.MethodPermission;
import com.ideal.tools.common.Constants;
import com.ideal.tools.common.HttpClientUtil;
import com.ideal.tools.core.web.BaseController;
import com.ideal.tools.exception.StudioException;
import com.ideal.tools.exception.ToolsException;
import com.ideal.tools.model.dto.CustomComTools;
import com.ideal.tools.model.dto.ExecutAuditQueryDto;
import com.ideal.tools.model.dto.ExecuteAuditResultDto;
import com.ideal.tools.model.dto.ExecuteAuditSaveDto;
import com.ideal.tools.model.dto.ExecuteToolsDto;
import com.ideal.tools.model.dto.ToolsInfoDto;
import com.ideal.tools.model.dto.ToolsInfoQueryDto;
import com.ideal.tools.model.dto.ToolsQueryDto;
import com.ideal.tools.model.enums.ConstantsEnum;
import com.ideal.tools.service.IToolsExecuteService;

/**
 * 工具执行校验Controller
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
@RestController
@RequestMapping("${app.script-tools-url:}/tools/execut")
public class ToolsExecuteController extends BaseController {


    private final IToolsExecuteService toolsExecuteService;

    public ToolsExecuteController(IToolsExecuteService toolsExecuteService) {
        this.toolsExecuteService = toolsExecuteService;
    }


    /**
     * 工具执行页面列表展示
     *
     * @param tableQueryDto 查询条件  工具编码,工具名称,工具类型,应用系统,一级分类,二级分类,工具类型,脚本名称,是否高危
     * @return 查询结果
     */
    @PostMapping("/list")
    public R<PageInfo<ToolsInfoDto>> list(@RequestBody TableQueryDto<ToolsQueryDto> tableQueryDto) {
        PageInfo<ToolsInfoDto> pages =null;
        try{
            pages = toolsExecuteService.selectExecuteList(
                    tableQueryDto.getQueryParam(),
                    tableQueryDto.getPageNum(),
                    tableQueryDto.getPageSize(),getUser()
            );
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, pages, ConstantsEnum.LIST_SUCCESS.getDesc());
        }catch (Exception e){
            logger.error("Failed to query ToolboxList custom categories !{}",e.getMessage(), e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE,pages , ConstantsEnum.LIST_FAIL.getDesc());
        }
    }
    /**
     * 获取工具是否绑定agent
     *
     * @param toolsInfoQueryDto id  工具id
     * @return
     */
    @PostMapping("/checkToolsExecuteBondAgent")
    @MethodPermission("@dp.hasBtnPermission('actionToolRun') or @dp.hasBtnPermission('actionScenceTools')")
    public R<Object> checkToolsExecuteBondAgent(@RequestBody ToolsInfoQueryDto toolsInfoQueryDto) {
        try {
            return toolsExecuteService.checkToolsExecuteBondAgent(toolsInfoQueryDto,getUser().getUserId());
        } catch (Exception e) {
            logger.error("checkToolsExecuteBondAgent  by id is error", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, ConstantsEnum.GAIN_FAIL.getDesc());
        }
    }
    /**
     * 获取工具执行页面按钮展示内容
     *
     * @param executAuditQueryDto toolsid  工具id， execFrom 来源  4.场景工具 5.告警诊断 7.工具执行 ，auditId 审批id
     * @return 0.不允许执行 1.直接执行，2.弹窗倒计时10秒冷静期，3.双人复合，4.弹框确认执行，5.审核中
     */
    @PostMapping("/getExecutToolsButtonStatus")
    @MethodPermission("@dp.hasBtnPermission('actionToolRun') or @dp.hasBtnPermission('actionScenceTools')")
    public R<ExecuteAuditResultDto> getExecutButtonStatus(HttpServletRequest request,@RequestBody ExecutAuditQueryDto executAuditQueryDto) {
        try {
            executAuditQueryDto.setUserId(getUser().getUserId());
            executAuditQueryDto.setCurrentIp(HttpClientUtil.getIpAddress(request));
            return toolsExecuteService.getExecutButtonStatus(executAuditQueryDto);
        } catch (Exception e) {
            logger.error("query getExecutToolsButtonStatus  by id is error", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, ConstantsEnum.GAIN_FAIL.getDesc());
        }
    }

    /**
     * 保存执行审批提交
     *
     * @param executeAuditSaveDto 工具详情信息  工具id，  选中agentip ，填入参数内容，执行来源，审批人信息
     * @return execFrom  执行来源  4.场景工具 5.告警诊断 7.工具执行
     */
    @PostMapping("/saveExecutToolsAuditStatus")
    @MethodPermission("@dp.hasBtnPermission('actionToolRun') or @dp.hasBtnPermission('actionScenceTools')")
    public R<ExecuteAuditResultDto> saveExecutToolsAuditStatus(@RequestBody ExecuteAuditSaveDto executeAuditSaveDto) {
        try {
            ExecuteAuditResultDto executeAuditResultDto = toolsExecuteService.saveExecutToolsAuditStatus(executeAuditSaveDto, getUser());
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, executeAuditResultDto, ConstantsEnum.SAVE_SUCCESS.getDesc());
        } catch (Exception e) {
            logger.error("Tool execution approval save error ！", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, ConstantsEnum.SAVE_FAIL.getDesc());
        }
    }

    /**
     * 撤回工具执行审批
     *
     * @param executAuditQueryDto toolsid  工具id， execFrom 来源   4.场景工具 5.告警诊断 7.工具执行。auditType  审批状态, auditId 审批id
     * @return
     */
    @PostMapping("/revokeExecutToolsAudit")
    @MethodPermission("@dp.hasBtnPermission('actionToolRun') or @dp.hasBtnPermission('actionScenceTools')")
    public R<Object> revokeExecutToolsAudit(HttpServletRequest request, @RequestBody ExecutAuditQueryDto executAuditQueryDto) {
        try {
            executAuditQueryDto.setUserId(getUser().getUserId());
            executAuditQueryDto.setCurrentIp(HttpClientUtil.getIpAddress(request));
            return toolsExecuteService.revokeExecutToolsAudit(executAuditQueryDto);
        } catch (Exception e) {
            logger.error("Tool execution revoke error ！", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, ConstantsEnum.RECALL_FAIL.getDesc());
        }
    }

    /**
     * 审批展示工具详细信息       沒用
     *
     * @param executAuditQueryDto auditId 审批id
     * @return
     */
    @GetMapping("/getAuditDetails")
    public R<ExecuteAuditResultDto> getAuditDetails(ExecutAuditQueryDto executAuditQueryDto) {
        try {
            ExecuteAuditResultDto executeAuditResultDto = toolsExecuteService.getAuditDetails(executAuditQueryDto);
            return R.ok(executeAuditResultDto);
        } catch (Exception e) {
            logger.error("Tool execution approval insertion error ！", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, ConstantsEnum.GAIN_FAIL.getDesc());
        }
    }

    /**
     * 工具执行
     *
     * @param executeToolsDto 执行参数
     * @return
     */
    @PostMapping("/executeTools")
    @MethodPermission("@dp.hasBtnPermission('actionToolRun') or @dp.hasBtnPermission('actionScenceTools')")
    public R<Object> executeTools(@RequestBody ExecuteToolsDto executeToolsDto) {
        try {
            return toolsExecuteService.executeTools(executeToolsDto,getUser());
        } catch (Exception e) {
            logger.error("Tool execution error ！", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, ConstantsEnum.EXECUTE_FAIL.getDesc());
        }
    }

    /**
     * 自定义组合工具执行
     * @param customComTools
     *  toolsDto 添加数据
     *  execFrom 执行来源    7.工具执行
     * @return 添加结果
     */
    @PostMapping("/customComToolExecute")
    @MethodPermission("@dp.hasBtnPermission('actionToolRun')")
    public R<Object> customComToolExecute(@RequestBody CustomComTools customComTools) {
        try {
            return toolsExecuteService.customComToolExecute(customComTools.getExecFrom(),customComTools.getToolsDto(), getUser());
        }catch (ToolsException e ) {
            logger.error("Custom combination tool execution error ！", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, ConstantsEnum.EXECUTE_FAIL.getDesc());
        }catch (StudioException e){
            logger.error("Custom combination tool execution error ！", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, e.getMessage());
        }

    }
}

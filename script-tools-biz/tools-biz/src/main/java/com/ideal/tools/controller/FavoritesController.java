package com.ideal.tools.controller;


import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.common.dto.ValidateError;
import com.ideal.common.util.spring.MessageUtil;
import com.ideal.system.common.component.aop.MethodPermission;
import com.ideal.tools.common.Constants;
import com.ideal.tools.core.web.BaseController;
import com.ideal.tools.model.dto.FavoritesDto;
import com.ideal.tools.model.dto.FavoritesResultDto;
import com.ideal.tools.model.dto.ToolsInfoDto;
import com.ideal.tools.model.dto.ToolsQueryDto;
import com.ideal.tools.model.enums.ConstantsEnum;
import com.ideal.tools.service.IFavoritesService;

/**
 * 场景工具主Controller
 *
 * <AUTHOR>
 * @date 2024-06-14
 */
@RestController
@RequestMapping("${app.script-tools-url:}/tools/favorites")
public class FavoritesController extends BaseController {
    private final IFavoritesService favoritesService;

    public FavoritesController(IFavoritesService favoritesService) {
        this.favoritesService = favoritesService;
    }

    /**
     * 查询场景工具主列表
     * @return 查询结果
     */
    @PostMapping("/list")
    public R<List<FavoritesResultDto>> list() {
        try {
            // 调用服务层方法获取树结构
            List<FavoritesResultDto> categories = favoritesService.selectFavoritesTreeList();
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, categories, ConstantsEnum.LIST_SUCCESS.getDesc());
        } catch (Exception e) {
            logger.error("Failed to query Toolbox custom categories! {}", e.getMessage(), e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, null, ConstantsEnum.LIST_FAIL.getDesc());
        }
    }

    /**
     * 查询所有工具接口
     *
     * @param tableQueryDto 查询条件
     * @return 查询结果
     */
    @PostMapping("/allToollist")
    public R<PageInfo<ToolsInfoDto>> allToollist(@RequestBody TableQueryDto<ToolsQueryDto> tableQueryDto) {

        PageInfo<ToolsInfoDto> pages = null;
        try {
            pages = favoritesService.allToolsInfoList(
                    tableQueryDto.getQueryParam(),
                    tableQueryDto.getPageNum(),
                    tableQueryDto.getPageSize(),getUser()
            );
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, pages, ConstantsEnum.LIST_SUCCESS.getDesc());
        } catch (Exception e) {
            logger.error("Failed to query ToolboxInfo custom categories !{}", e.getMessage(), e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, pages, ConstantsEnum.LIST_FAIL.getDesc());
        }
    }

    /**
     * 查询场景工具主详细信息    沒用
     * @param id 数据唯一标识
     * @return 查询结果
     */
    @GetMapping(value = "/get")
    public R<FavoritesDto> getAgentInfoInfo(@RequestParam(value = "id")Long id) {
        return R.ok(favoritesService.selectFavoritesById(id));
    }

    /**
     * 新增保存场景工具主
     * @param favoritesDto 添加数据
     * @return 添加结果
     */
    @PostMapping("/save")
    public R<Object> save(@RequestBody FavoritesDto favoritesDto, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            List<ValidateError> validateErrorList = bindingResult.getFieldErrors().stream()
                    .map(item -> new ValidateError(item.getField(), MessageUtil.message(item.getDefaultMessage())))
                    .collect(Collectors.toList());
            Map<String, List<ValidateError>> validateError = new HashMap<>(1);
            validateError.put("validateError", validateErrorList);
            return R.fail(Constants.REPONSE_STATUS_VALIDATA_CODE, validateError, "validate.error");
        }
        try {
            String result = favoritesService.insertFavorites(favoritesDto, getUser());
            if ("nameExists".equals(result)) {
                return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, null, "name is exists");
            }
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, "", ConstantsEnum.SAVE_SUCCESS.getDesc());
        } catch (Exception e) {
            logger.error("New toolbox customization failed!", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, null, ConstantsEnum.SAVE_FAIL.getDesc());
        }
    }

    /**
     * 修改保存场景工具主
     * @param favoritesDto 更新数据
     * @return 更新结果
     */
    @PostMapping("/update")
    @MethodPermission("@dp.hasBtnPermission('addSceneTools')")
    public R<Object> update(@RequestBody FavoritesDto favoritesDto, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            List<ValidateError> validateErrorList = bindingResult.getFieldErrors().stream()
                    .map(item -> new ValidateError(item.getField(), MessageUtil.message(item.getDefaultMessage())))
                    .collect(Collectors.toList());
            Map<String, List<ValidateError>> validateError = new HashMap<>(1);
            validateError.put("validateError", validateErrorList);
            return R.fail(Constants.REPONSE_STATUS_VALIDATA_CODE, validateError, "validate.error");
        }
        try {
            String result = favoritesService.updateFavorites(favoritesDto, getUser());
            if ("nameExists".equals(result)) {
                return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, null, "name is exists");
            }
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, "", ConstantsEnum.SAVE_SUCCESS.getDesc());
        } catch (Exception e) {
            logger.error("Modifying toolbox customization failed!", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, null, ConstantsEnum.UPDATE_FAIL.getDesc());
        }
    }



    /**
     * 删除场景工具主
     * @param ids 主键列表
     * @return 删除结果
     */
    @PostMapping("/remove")
    public R<Void> remove(@RequestBody Long[] ids) {
        try{
            favoritesService.deleteFavoritesByIds(ids,getUser());
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, ConstantsEnum.REMOVE_SUCCESS.getDesc());
        }catch (Exception e) {
            logger.error("Delete toolbox custom classification failed!", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, ConstantsEnum.REMOVE_FAIL.getDesc());
        }
    }
}

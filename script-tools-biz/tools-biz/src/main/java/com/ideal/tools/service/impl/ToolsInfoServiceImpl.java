package com.ideal.tools.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.ideal.common.dto.R;
import com.ideal.common.util.BeanUtils;
import com.ideal.common.util.PageDataUtil;
import com.ideal.common.util.batch.Batch;
import com.ideal.system.api.IUserInfo;
import com.ideal.system.common.component.config.AuthContext;
import com.ideal.system.common.component.model.CurrentUser;
import com.ideal.system.dto.UserInfoApiDto;
import com.ideal.tools.common.*;

import com.ideal.tools.config.BusinessConfig;
import com.ideal.tools.exception.*;
import com.ideal.tools.mapper.*;
import com.ideal.tools.model.bean.ToolsInfoScriptBean;
import com.ideal.tools.model.constant.ToolsMoveConstants;
import com.ideal.tools.model.dto.*;
import com.ideal.tools.model.entity.*;
import com.ideal.tools.model.enums.*;
import com.ideal.tools.model.interaction.ScriptContentDto;
import com.ideal.tools.model.interaction.UserInfoSearchDto;
import com.ideal.tools.service.*;
import com.ideal.tools.service.interaction.*;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 工具箱信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
@Service
public class ToolsInfoServiceImpl implements IToolsInfoService , Batch {

    /**
     * 日志对象
     */
    protected Logger logger = LoggerFactory.getLogger(getClass());
    private final ToolsInfoMapper toolsInfoMapper;
    private final SystemDataInteract systemDataInteract;
    private final ToolsFilesMapper toolsFilesMapper;
    private final ScriptInteract scriptInteract;
    private final ToolsAgentInfoMapper toolsAgentInfoMapper;
    private final ToolsParamMapper toolsParamMapper;
    private final IUserInfo userInfo;
    private final IToolsEverybodyService toolsEverybodyService;
    private final MqInteract mqInteract;
    private final IExecuteMonitorService executeMonitorService;
    private final IToolsProjectService toolsProjectService;
    private final StudioInteract studioInteract;
    private final AuditMapper auditMapper;
    private final IDeliveryReportService deliveryReportService;
    private final IToolsAgentInfoService toolsAgentInfoService;

    /**
     * 获取配置信息
     */
    private final BusinessConfig businessConfig;

    /**
     * 用户列表服务
     */
    private final UserInfoInteract userInfoInteract;
    /**
     * 审批服务
     */
    private IAuditService auditService;
    /**
     * 审计日志
     */
    private final ILogAuditService logAuditService;
    private final ToolsEverybodyMapper toolsEverybodyMapper;
    private final ToolsProjectMapper toolsProjectMapper;


    public ToolsInfoServiceImpl(ToolsInfoMapper toolsInfoMapper, SystemDataInteract systemDataInteract, ScriptInteract scriptInteract,
                                ToolsFilesMapper toolsFilesMapper, ToolsAgentInfoMapper toolsAgentInfoMapper, ToolsParamMapper toolsParamMapper, IUserInfo userInfo
    , IToolsEverybodyService toolsEverybodyService, IExecuteMonitorService executeMonitorService, MqInteract mqInteract, IToolsProjectService toolsProjectService
    , StudioInteract studioInteract, AuditMapper auditMapper, IDeliveryReportService deliveryReportService,UserInfoInteract userInfoInteract,
                                BusinessConfig businessConfig,IAuditService auditService,ILogAuditService logAuditService, ToolsEverybodyMapper toolsEverybodyMapper, ToolsProjectMapper toolsProjectMapper,IToolsAgentInfoService toolsAgentInfoService) {
        this.toolsInfoMapper = toolsInfoMapper;
        this.systemDataInteract=systemDataInteract;
        this.scriptInteract=scriptInteract;
        this.toolsFilesMapper=toolsFilesMapper;
        this.toolsAgentInfoMapper=toolsAgentInfoMapper;
        this.toolsParamMapper = toolsParamMapper;
        this.userInfo=userInfo;
        this.toolsEverybodyService=toolsEverybodyService;
        this.executeMonitorService=executeMonitorService;
        this.mqInteract=mqInteract;
        this.toolsProjectService=toolsProjectService;
        this.studioInteract=studioInteract;

        this.auditMapper=auditMapper;
        this.deliveryReportService = deliveryReportService;
        this.userInfoInteract = userInfoInteract;
        this.businessConfig = businessConfig;
        this.auditService = auditService;
        this.logAuditService = logAuditService;
        this.toolsEverybodyMapper = toolsEverybodyMapper;
        this.toolsProjectMapper = toolsProjectMapper;
        this.toolsAgentInfoService = toolsAgentInfoService;
    }


    /**
     * 查询工具箱信息
     *
     * @param id 工具箱信息主键
     * @return 工具箱信息
     */
    @Override
    public ToolsInfoDto selectToolsInfoById(Long id) {
        ToolsInfoEntity toolsInfo = toolsInfoMapper.selectToolsInfoById(id);
        return BeanUtils.copy(toolsInfo, ToolsInfoDto.class);
    }

    /**
     * 查询工具箱信息列表
     *
     * @param toolsInfoQueryDto 工具箱信息
     * @param pageNum 页码
     * @param pageSize 单页长度
     * @return 工具箱信息
     */
    @Override
    public PageInfo<ToolsInfoResultDto> selectToolsInfoListByUser(ToolsQueryDto toolsInfoQueryDto,String businessSystemCode, Long userId,Integer pageNum, Integer pageSize) {
    	ToolsInfoEntity query = BeanUtils.copy(toolsInfoQueryDto, ToolsInfoEntity.class);
    	Long businessSystemId=query.getBusinessSystemId();
    	if (null!=businessSystemId&&-1==businessSystemId) {
    		query.setBusinessSystemId(null);
		}
    	PageMethod.startPage(pageNum, pageSize);
    	query.setDelFlag(0);
        //添加用户权限控制
        query.setBusinessSystemIds(getBusiSysSet(userId));
        List<ToolsInfoEntity> toolsInfoList = new ArrayList<>();
        //
        if(businessConfig.isToolEvnMainSwitch() && ToolsEnvEnum.DEV_ENV.getDesc().equals(businessConfig.getToolsEvn())){
            toolsInfoList = toolsInfoMapper.selectToolsInfoListByStatus(query);
        }else{
            query.setStatus(ToolsStatusEnum.STATUS_WAITING.getCode());
            toolsInfoList = toolsInfoMapper.selectToolsInfoList(query);
        }
        systemDataInteract.addBusinessSystemIdByCode(toolsInfoList, businessSystemCode);
    	return PageDataUtil.toDtoPage(toolsInfoList, ToolsInfoResultDto.class);
    }



    /**
     * 验证参数与原有参数对比
     *
     * @param toolsDto 工具信息
     * @return 验证信息信息
     */

    @Override
    public String selectToolsInfoList(ToolsDto toolsDto) {
        ToolsInfoEditEntity query = new ToolsInfoEditEntity();
        query.setName(toolsDto.getName());

        List<ToolsInfoEditEntity> toolsInfoList = toolsInfoMapper.selectToolsInfoEditList(query);
        //如果查询数据为空不往下验证
        if(toolsInfoList.isEmpty()){
            return "";
        }
        ToolsInfoEditEntity toolsInfoEditEntity=  toolsInfoList.get(0);

        if((toolsDto.getId() == null || toolsDto.getId() <= 0)&&Boolean.TRUE.equals(!toolsInfoList.isEmpty())){
            return "checkParams Tool name already exists!";
        }
        if(toolsInfoEditEntity!=null){
            if(toolsInfoEditEntity.getStatus()!=null){
                if(toolsInfoEditEntity.getStatus().equals(ToolsStatusEnum.STATUS_AUDIT.getCode())){
                    return "Tool status is under review and cannot be edited!";
                }
            }
        }

        return "";
    }
    
    /**
     * 
     * @Description 获取用户所有busiSysId
     * @return
     * <AUTHOR>
     * @date 2025-04-14 10:34:57
     */
    public Set<Long> getBusiSysSet(Long userId){
    	if (null==userId) {
    		userId=getUser().getUserId();
		}
    	List<SystemPullDto> busIdList=systemDataInteract.getBusinessSystemList(userId);
        Set<Long> busiSysSet=new HashSet<Long>();
        for(SystemPullDto systemPullDto:busIdList) {
        	busiSysSet.add(systemPullDto.getBusinessSystemId());
        }
        return busiSysSet;
    }

    /**
     * 查询工具箱信息列表
     *
     * @param toolsInfoQueryDto 工具箱信息
     * @param pageNum 页码
     * @param pageSize 单页长度
     * @return 工具箱信息
     */
    @Override
    public PageInfo<ToolsInfoResultDto> selectToolsInfoCategoryList(ToolsQueryDto toolsInfoQueryDto, Integer pageNum, Integer pageSize) {
        ToolsInfoEditEntity query = BeanUtils.copy(toolsInfoQueryDto, ToolsInfoEditEntity.class);
        //名称和编码都是模糊查询
        query.setLikeName(toolsInfoQueryDto.getName());
        query.setName(null);
        query.setLikeCode(toolsInfoQueryDto.getCode());
        query.setCode(null);
        PageMethod.startPage(pageNum, pageSize);
        //添加用户权限控制
        query.setBusinessSystemIds(getBusiSysSet(null));
        List<ToolsInfoEditEntity> toolsInfoList = toolsInfoMapper.selectToolsInfoEditList(query);
        //处理集合数据
        List<ToolsInfoEditEntity> toolsInfoEditList =new ArrayList<>();

        for(ToolsInfoEditEntity  toolsInfoEditEntity:toolsInfoList){
            //如果审批数据不存在默认待审核
            if(toolsInfoEditEntity.getApprovalState()==null){
                toolsInfoEditEntity.setApprovalState(AuditStateEnum.AUDIT_AWAITING.getCode());
            }//如果工具信息已修改 默认待审核
            if(toolsInfoEditEntity.getStatus()!=null){
                if (toolsInfoEditEntity.getStatus().equals(ToolsStatusEnum.STATUS_MODIFIED.getCode())
                        && !toolsInfoEditEntity.getApprovalState().equals(AuditStateEnum.AUDIT_SEND_BACK.getCode())
                        && !toolsInfoEditEntity.getApprovalState().equals(AuditStateEnum.AUDIT_WITHDRAW.getCode())) {
                    toolsInfoEditEntity.setApprovalState(AuditStateEnum.AUDIT_AWAITING.getCode());
                }
            }else {
                toolsInfoEditEntity.setStatus(ToolsStatusEnum.STATUS_DRAFT.getCode());
            }
            toolsInfoEditList.add(toolsInfoEditEntity);

        }
     List<ToolsInfoResultDto>   list=   BeanUtils.copy(toolsInfoEditList,ToolsInfoResultDto.class);
        PageInfo<ToolsInfoResultDto> toolsInfoResultPageInfo=    PageDataUtil.toDtoPage(toolsInfoList, ToolsInfoResultDto.class);
        return   AutoDevPageDataUtils.getPageInfo(toolsInfoResultPageInfo, list);
    }
    /**
     * 新增工具箱信息
     *
     * @param toolsInfoDto 工具箱信息
     * @return 结果
     */
    @Override
    public int insertToolsInfo(ToolsInfoDto toolsInfoDto, UserDto userDto) {
        ToolsInfoEntity toolsInfo = BeanUtils.copy(toolsInfoDto, ToolsInfoEntity.class);
        return toolsInfoMapper.insertToolsInfo(toolsInfo);
    }

    /**
     * 修改工具箱信息
     *
     * @param toolsInfoDto 工具箱信息
     * @return 结果
     */
    @Override
    public int updateToolsInfo(ToolsInfoDto toolsInfoDto) {
        ToolsInfoEntity toolsInfo = BeanUtils.copy(toolsInfoDto, ToolsInfoEntity.class);
        return toolsInfoMapper.updateToolsInfo(toolsInfo);
    }

    /**
     * 批量删除工具箱信息
     *
     * @param ids 需要删除的工具箱信息主键
     * @return 结果
     */
    @Override
    public int deleteToolsInfoByIds(Long[] ids) {
        return toolsInfoMapper.deleteToolsInfoByIds(ids);
    }


    /**
     * 业务系统列表
     *
     * @param userDto 用户信息
     */
    @Override
    public List<SystemPullDto> selectToolsInfoCategoryList(UserDto userDto) {
        return systemDataInteract.getBusinessSystemList(userDto.getUserId());
    }

    /**
     * 获取脚本列表信息
     *
     * @param userDto 用户信息
     */
    @Override
    public List<ScriptPullDto> scriptPullList(UserDto userDto,String scriptInfo) {
        return scriptInteract.getScriptInfoList(userDto,scriptInfo);
    }


    /**
     * 获取脚本列表信息
     *
     * @param userDto 用户信息
     */
    @Override
    public ScriptContentDto  selectScriptContentInfo(UserDto userDto,
                                                     String scriptId) {
        return scriptInteract.getScriptContentInfo(userDto,scriptId);
    }

    /**
     * 查询工具箱信息分页数据根据工具ID集合
     *
     * @param ids 工具ID集合
     * @param name 工具名称
     * @param pageNum 页码
     * @param pageSize 单页长度
     * @return 工具箱信息分页数据
     */
    @Override
    public PageInfo<ToolsInfoResultDto> selectToolsInfoPageListByIdsAndName(Long[] ids,String name,Integer pageNum,Integer pageSize) {
        PageMethod.startPage(pageNum, pageSize);
        List<ToolsInfoEntity> toolsInfoList = toolsInfoMapper.selectToolsInfoListByIdsAndName(ids,name);
        return PageDataUtil.toDtoPage(toolsInfoList, ToolsInfoResultDto.class);
    }

    /**
     * 查询工具箱信息列表根据工具ID集合（用于导出）
     *
     * @param ids 工具ID集合
     * @param name 工具名称
     * @return 工具箱列表集合
     */
    @Override
    public List<ToolsInfoResultDto> selectToolsInfoListByIdsAndName(Long[] ids,String name) {
        List<ToolsInfoEntity> toolsInfoEntities = toolsInfoMapper.selectToolsInfoListByIdsAndName(ids, name);
        return BeanUtils.copy(toolsInfoEntities, ToolsInfoResultDto.class);
    }

    /**
     * 查询工具箱信息
     *
     * @param id 工具箱信息主键
     * @return 工具箱信息
     */
    @Override
    public ToolsDto getToolsCombinedInfo(Long id) {

        ToolsInfoEditEntity toolsInfo = toolsInfoMapper.selectToolsEditInfoById(id);

        if (Objects.isNull(toolsInfo)) {
            return new ToolsDto();
        }

        //工具参数对象
        ToolsParamEntity queryParam = new ToolsParamEntity();
        queryParam.setTdToolsId(id);
        List<ToolsParamEntity> toolsParamList = toolsParamMapper.selectToolsParamList(queryParam);

        //退回原因
        AuditEntity auditQueryEntity = new AuditEntity();
        auditQueryEntity.setBusinessId(toolsInfo.getId());
        auditQueryEntity.setType(AuditTypeEnum.AUDIT_TOOLS_PUBLISH_IMPORT.getCode());
        auditQueryEntity.setApprovalState(AuditStateEnum.AUDIT_WITHDRAW.getCode());
        AuditEntity auditEntity = auditMapper.selectAuditByLastOne(auditQueryEntity);
        if (Objects.nonNull(auditEntity)) {
            toolsInfo.setReturnReason(auditEntity.getAuditReturnReason());
        }

        //合并主表
        ToolsDto toolsDto =  BeanUtils.copy(toolsInfo, ToolsDto.class);

        /** 预估运行风险（1  导致CPU升高2 内存溢出3 生产大文件 4 应用中断 5 其他） */
        Long[] longArray = null;
        if(toolsInfo.getEstimateOperationalRisk()!=null && !"".equals(toolsInfo.getEstimateOperationalRisk())){

            String[] strArray = toolsInfo.getEstimateOperationalRisk().split(",");
            // 转换后的长整型数组
            longArray = new Long[strArray.length];
            // 转换过程
            for (int i = 0; i < strArray.length; i++) {
                longArray[i] = Long.parseLong(strArray[i]);
            }
        }
        toolsDto.setEstimateOperationalRisk(longArray==null? new Long[0]:longArray);
        ToolsFilesEntity queryFiles =new ToolsFilesEntity();
        queryFiles.setTdToolsId(id);
        List<ToolsFilesEntity> toolsFilesList = toolsFilesMapper.selectToolsFilesList(queryFiles);
        if (toolsDto.getType().equals(ToolsTypeEnum.TYPE_SCRIPT.getCode())){

            ToolsAgentInfoEntity toolsAgentInfo=new ToolsAgentInfoEntity();
            toolsAgentInfo.setTdToolsId(id);
            List<ToolsAgentInfoEntity> toolsAgentInfoList = toolsAgentInfoMapper.selectToolsAgentInfoList(toolsAgentInfo);
            //去掉被平台管理删除的agent
            try {
            	AgentQueryDto agentQueryDto=new AgentQueryDto();
            	agentQueryDto.setSysId(toolsInfo.getBusinessSystemId());
				toolsAgentInfoService.rmDeleteAgent(toolsAgentInfoList, agentQueryDto, getUser().getUserId());
			} catch (Exception e) {
				logger.error("去掉被平台管理删除的agent失败",e);
			}
            /** agent列表 */
            List<ToolsAgentResultDto> toolsAgentResultList =  BeanUtils.copy(toolsAgentInfoList, ToolsAgentResultDto.class);
            toolsDto.setToolsAgentResultList(toolsAgentResultList);
            /** 参数列表 */
            List<ToolsParamResultDto> toolsParamResultList =  BeanUtils.copy(toolsParamList, ToolsParamResultDto.class);

            toolsDto.setToolsParamResultList(toolsParamResultList);
            /** 脚本文件列表ids */
            List<ToolsFilesQueryDto>  scriptFilesList =  BeanUtils.copy(toolsFilesList, ToolsFilesQueryDto.class);
            toolsDto.setScriptFilesList(scriptFilesList);
        }else if(toolsDto.getType().equals(ToolsTypeEnum.TYPE_COMBINED.getCode())){


            //获取脚本信息
            ToolsProjectDto toolsProjectDto ;
            ToolsProjectInfoDto toolsProjectInfoDto =null;
            try {
                toolsProjectDto = toolsProjectService.selectToolsProjectByToolId(id);

               String workflowContent= studioInteract.getWorkflowJson(toolsProjectDto.getWorkflowId());
                toolsProjectDto.setWorkflowContent(workflowContent);
                toolsProjectInfoDto = BeanUtils.copy(toolsProjectDto, ToolsProjectInfoDto.class);
            } catch (ToolsException |StudioException e) {
               toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);

            }
            toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
        }else if(toolsDto.getType().equals(ToolsTypeEnum.TYPE_DESCRIPTION.getCode())){
            /** 描述文件附件ids */
            List<ToolsFilesQueryDto>  describeFilesList=  BeanUtils.copy(toolsFilesList, ToolsFilesQueryDto.class);
            toolsDto.setDescribeFilesList(describeFilesList);
        }

        ToolsEverybodyQueryDto toolsEverybodyQueryDto=new ToolsEverybodyQueryDto();
        toolsEverybodyQueryDto.setTdToolsId(id);
        List<ToolsEverybodyDto> toolsEverybodyList=   toolsEverybodyService.selectToolsEverybodyList(toolsEverybodyQueryDto);
        List< AuditEverybodyQueryDto> auditEverybodyDtoList=new ArrayList<>();
        /** 审核人实例 */
        if(toolsEverybodyList!=null){

            for(ToolsEverybodyDto  toolsEverybodyDto:toolsEverybodyList){
                AuditEverybodyQueryDto auditEverybodyQueryDto= BeanUtils.copy(toolsEverybodyDto, AuditEverybodyQueryDto.class);
                UserDto user =getUser();
                if(auditEverybodyQueryDto.getAuditorId().equals(user.getUserId())){

                }else {
                    auditEverybodyDtoList.add(auditEverybodyQueryDto);
                }

            }
            toolsDto.setAuditEverybodyDtoList(auditEverybodyDtoList);
        }


        return toolsDto;
    }

    public UserDto getUser(){
        UserDto userDto =null;
        try {
        CurrentUser userApiDto = AuthContext.getUser();
        if(userApiDto.getId() != null){
            userDto = new UserDto();
            userDto.setUserName(userApiDto.getFullName());
            userDto.setLoginName(userApiDto.getLoginName());
            userDto.setUserId(userApiDto.getId());
        }else {
            userDto = new UserDto();
            userDto.setUserName("admin");
            userDto.setLoginName("admin");
            userDto.setUserId(1L);
        }
        }catch (Exception e) {
            userDto = new UserDto();
            userDto.setUserName("admin");
            userDto.setLoginName("admin");
            userDto.setUserId(1L);
            return userDto;
        }
        return userDto;
    }
    /**
     * 双人复核查询工具箱信息
     *
     * @param toolsAuditDto 工具箱信息主键
     * @return 工具箱信息
     */
    @Override
    public  ToolsDto getToolsCombinedInfo( ToolsAuditDto toolsAuditDto){
        if(toolsAuditDto!=null){
            if (toolsAuditDto.getAuditId() != null && toolsAuditDto.getAuditId() != 0) {
                AuditEntity auditEntity = auditMapper.selectAuditById(toolsAuditDto.getAuditId());
                // 如果环境主开关未开启，直接获取工具信息
                if (!businessConfig.isToolEvnMainSwitch()) {
                    return getToolsCombinedInfo(auditEntity.getBusinessId());
                }
                try {
                    //判别审批类型
                    if (AuditTypeEnum.AUDIT_TOOLS_PUBLISH_IMPORT.getCode().equals(auditEntity.getType())) {
                        //根据工具发布迁移标识、审批id获取redis中工具详情信息
                        return Optional.ofNullable(RedisTemplateUtil.hGet(RedisKeyEnum.TOOLS_MIGRATE_IMPORT.getDesc(), String.valueOf(toolsAuditDto.getAuditId())))
                                .map(Object::toString)
                                .map(json -> JSONObject.parseObject(json, ToolsDto.class))
                                .orElse(null);
                    }
                    // 如果不是工具发布迁移导入类型，则获取工具信息
                    return getToolsCombinedInfo(auditEntity.getBusinessId());
                } catch (Exception e) {
                    logger.error("get tools combined info error", e);
                    return null;
                }
            } else if (toolsAuditDto.getId() != null && toolsAuditDto.getId() != 0) {
                return getToolsCombinedInfo(toolsAuditDto.getId());
            }
            return null;
        }else {
            return null;
        }

    }
    /**
     *修改一线工具描述
     *
     * @param toolsDescribeDto 工具箱信息主键
     * @return 工具箱信息
     */
    @Override
    public int updateToolsFirstDescribe(ToolsDescribeDto toolsDescribeDto,UserDto userDto) {
        ToolsInfoEntity toolsInfo = new ToolsInfoEntity();
        toolsInfo.setId(toolsDescribeDto.getId());
        toolsInfo.setFirstDescribe(toolsDescribeDto.getFirstDescribe());
        int updated = toolsInfoMapper.updateToolsInfo(toolsInfo);
        Integer operateCode = updated > 0 ? OperatResultEnum.SUCCESS.getCode() : OperatResultEnum.FAILURE.getCode();
        //计入审计日志
        logAuditService.logOperationAudit(toolsDescribeDto.getId(),OperatModuleEnum.TOOL_EDIT,OperatTypeEnum.TOOL_FIRST_DESCRIBE,operateCode, userDto,OperatTypeEnum.TOOL_FIRST_DESCRIBE.getRemark());

        return toolsInfoMapper.updateToolsInfo(toolsInfo);
    }

    /**
     * 工具交付
     *
     * @param toolsDeliveryDto 工具箱信息主键
     * @return 工具箱信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class, noRollbackFor = ToolsException.class)
    public void updateDeliveryTools(ToolsDeliveryDto toolsDeliveryDto, UserDto userDto) throws ToolsException {

        ToolsInfoEntity toolsInfo = BeanUtils.copy(toolsDeliveryDto, ToolsInfoEntity.class);
        //交付状态 0、未交付 1、待接收 2、已退回 3、已交付
        if (ToolsDeliveryStatusEnum.STATUS_RECEIVED.getCode().equals(toolsDeliveryDto.getDeliveryStatus())) {
            List<ToolsInfoEntity> toolsInfoEntities = toolsInfoMapper.selectToolsInfoListByIdsAndName(toolsDeliveryDto.getIds(), null);
            for (ToolsInfoEntity toolsInfoEntity : toolsInfoEntities) {

                if (toolsInfoEntity.getDeliveryStatus().equals(ToolsDeliveryStatusEnum.STATUS_RECEIVED.getCode())) {
                    throw new ToolsException("选择的工具'" + toolsInfoEntity.getName() + "' 为待接收 无法进行交付！请接收后再交付。 ");
                }
            }
            //插入接收报表
            DeliveryReportDto deliveryReportDto = new DeliveryReportDto();
            //循环操作
            for (Long toolId : toolsDeliveryDto.getIds()) {
                try {
                    for (ToolsInfoEntity toolsInfoEntity : toolsInfoEntities) {
                        if (toolsInfoEntity.getId().equals(toolId)) {
                            deliveryReportDto.setTdToolsId(toolId);
                            deliveryReportDto.setToolCode(toolsInfoEntity.getCode());
                            deliveryReportDto.setToolName(toolsInfoEntity.getName());
                            deliveryReportDto.setToolType(toolsInfoEntity.getType());
                            deliveryReportDto.setBusinessSystemId(toolsInfoEntity.getBusinessSystemId());
                            deliveryReportDto.setBusinessSystemName(toolsInfoEntity.getBusinessSystemName());
                            deliveryReportDto.setTransferId(toolsInfoEntity.getUpdatorId());
                            deliveryReportDto.setTransferName(toolsInfoEntity.getUpdatorName());
                            deliveryReportService.insertDeliveryReport(deliveryReportDto, userDto);
                        }
                    }
                    // 记录每个工具的交付结果审计日志
                    logAuditService.logOperationAudit(
                            toolId,
                            OperatModuleEnum.TOOL_EDIT,
                            OperatTypeEnum.TOOL_DELIVERY,
                            OperatResultEnum.SUCCESS.getCode(),
                            userDto,
                            OperatTypeEnum.TOOL_DELIVERY.getRemark()
                    );
                } catch (Exception e) {
                    logger.error("insert delivery report error", e);
                    logAuditService.logOperationAudit(
                            toolId,
                            OperatModuleEnum.TOOL_EDIT,
                            OperatTypeEnum.TOOL_DELIVERY,
                            OperatResultEnum.FAILURE.getCode(),
                            userDto,
                            OperatTypeEnum.TOOL_DELIVERY.getRemark()
                    );
                }
            }

            /**
             * 修改工具接收报表
             *
             * @param deliveryReportDto 工具接收报表实体
             * @return 结果
             */

        }
        if (ToolsDeliveryStatusEnum.STATUS_DELIVERED.getCode().equals(toolsDeliveryDto.getDeliveryStatus())) {
            /** 交付接收人 */
            toolsInfo.setDeliveryAuditorId(userDto.getUserId());
            //获取一线人员权限
            List<UserInfoApiDto> userInfoApiListFrontline = userInfo.queryUserInfoListByPermissionCode(AuditorPermissionEnum.AUDIT_PERMISSION_FRONTLINE.getValue());
            boolean hasFrontlinePermission = userInfoApiListFrontline.stream().anyMatch(user -> user.getId().equals(userDto.getUserId()));
            if (!hasFrontlinePermission) {
                throw new ToolsException("没有工具箱接收权限 !");
            }
            toolsInfo.setDeliveryReceptionTime(new Date());
            List<ToolsInfoEntity> toolsInfoEntities = toolsInfoMapper.selectToolsInfoListByIdsAndName(toolsDeliveryDto.getIds(), null);
            for (ToolsInfoEntity toolsInfoEntity : toolsInfoEntities) {
                if (toolsInfoEntity.getDeliveryStatus().equals(ToolsDeliveryStatusEnum.STATUS_NON_DELIVERY.getCode())) {
                    throw new ToolsException("选择的工具'" + toolsInfoEntity.getName() + "' 为未交付 无法进行接收！请交付后在进行接收。 ");
                }
                if (toolsInfoEntity.getDeliveryStatus().equals(ToolsDeliveryStatusEnum.STATUS_RETURNED.getCode())) {
                    throw new ToolsException("选择的工具'" + toolsInfoEntity.getName() + "'  为已回退 无法进行接收！请交付后在进行接收。 ");
                }
            }

            //插入接收报表
            DeliveryReportDto deliveryReportDto = new DeliveryReportDto();
            for (ToolsInfoEntity toolsInfoEntity : toolsInfoEntities) {
                try {
                    deliveryReportDto.setTdToolsId(toolsInfoEntity.getId());
                    deliveryReportDto.setToolType(toolsInfoEntity.getType());
                    deliveryReportDto.setReceiveId(userDto.getUserId());
                    deliveryReportDto.setReceiveName(userDto.getUserName());
                    deliveryReportService.updateDeliveryReport(deliveryReportDto);
                    // 记录每个工具的接收结果审计日志
                    logAuditService.logOperationAudit(
                            toolsInfoEntity.getId(),
                            OperatModuleEnum.TOOL_EDIT,
                            OperatTypeEnum.TOOL_RECEIVE,
                            OperatResultEnum.SUCCESS.getCode(),
                            userDto,
                            OperatTypeEnum.TOOL_RECEIVE.getRemark()
                    );
                } catch (Exception e) {
                    logger.error("insert delivery report error", e);
                    logAuditService.logOperationAudit(
                            toolsInfoEntity.getId(),
                            OperatModuleEnum.TOOL_EDIT,
                            OperatTypeEnum.TOOL_DELIVERY,
                            OperatResultEnum.FAILURE.getCode(),
                            userDto,
                            OperatTypeEnum.TOOL_DELIVERY.getRemark()
                    );
                }
            }


        }
        if (ToolsDeliveryStatusEnum.STATUS_RETURNED.getCode().equals(toolsDeliveryDto.getDeliveryStatus())) {

            /** 交付接收人 */
            toolsInfo.setDeliveryAuditorId(userDto.getUserId());
            //获取一线人员权限
            List<UserInfoApiDto> userInfoApiListFrontline = userInfo.queryUserInfoListByPermissionCode(AuditorPermissionEnum.AUDIT_PERMISSION_FRONTLINE.getValue());
            boolean hasFrontlinePermission = userInfoApiListFrontline.stream().anyMatch(user -> user.getId().equals(userDto.getUserId()));
            if (!hasFrontlinePermission) {
                throw new ToolsException("No toolbox receive permission ！");
            }
            List<ToolsInfoEntity> toolsInfoEntities = toolsInfoMapper.selectToolsInfoListByIdsAndName(toolsDeliveryDto.getIds(), null);
            for (ToolsInfoEntity toolsInfoEntity : toolsInfoEntities) {
                if (toolsInfoEntity.getDeliveryStatus().equals(ToolsDeliveryStatusEnum.STATUS_NON_DELIVERY.getCode())) {
                    throw new ToolsException("选择的工具'" + toolsInfoEntity.getName() + "'为未交付 无法进行回退！请交付后在进行回退。 ");
                }
                if (toolsInfoEntity.getDeliveryStatus().equals(ToolsDeliveryStatusEnum.STATUS_RETURNED.getCode())) {
                    throw new ToolsException("选择的工具'" + toolsInfoEntity.getName() + "'为已回退 无法进行回退！请交付后在进行回退。 ");
                }
            }

                deliveryReportService.deleteDeliveryReportByIds(toolsDeliveryDto.getIds());

                for (Long id : toolsDeliveryDto.getIds()) {
                    try {
                    // 记录每个工具的退回结果审计日志
                    logAuditService.logOperationAudit(
                            id,
                            OperatModuleEnum.TOOL_EDIT,
                            OperatTypeEnum.TOOL_RETURN_DELIVERY,
                            OperatResultEnum.SUCCESS.getCode(),
                            userDto,
                            OperatTypeEnum.TOOL_RETURN_DELIVERY.getRemark()
                    );
                    } catch (Exception e) {
                        logger.error("delete delivery report error", e);
                        logAuditService.logOperationAudit(
                                id,
                                OperatModuleEnum.TOOL_EDIT,
                                OperatTypeEnum.TOOL_RETURN_DELIVERY,
                                OperatResultEnum.FAILURE.getCode(),
                                userDto,
                                OperatTypeEnum.TOOL_RETURN_DELIVERY.getRemark()
                        );
                }
            }

        }

        List<ToolsInfoEntity> toolsInfoEntityList = new ArrayList<>();

        for (Long id : toolsDeliveryDto.getIds()) {
            ToolsInfoEntity toolsInfoDto = BeanUtils.copy(toolsInfo, ToolsInfoEntity.class);
            toolsInfoDto.setId(id);
            toolsInfoDto.setUpdatorName(userDto.getUserName());
            toolsInfoDto.setUpdatorId(userDto.getUserId());
            toolsInfoEntityList.add(toolsInfoDto);
        }


        this.batchData(toolsInfoEntityList, toolsInfoMapper::updateToolsInfo);


    }

    /**
     * 查询工具信息By工具id
     * @param id 工具箱信息主键
     * @return
     */
    @Override
    public ToolsInfoCategoryEntity selectToolsInfoCategoryById(Long id) {
        ToolsInfoCategoryEntity toolsInfo = toolsInfoMapper.selectToolsInfoCategoryById(id);
        return BeanUtils.copy(toolsInfo, ToolsInfoCategoryEntity.class);
    }


    /**
     * 批量逻辑删除工具箱信息
     *
     * @param ids 需要删除的工具箱信息主键集合
     * @return 结果
     */
    @Override
    public R<Object> logicallyDeleteTools(Long[] ids)  throws ToolsException {
        List<ToolsInfoEntity> toolsInfoList = toolsInfoMapper.selectToolsInfoListByIds(ids);

        // 光大现场  并且  为DEV环境且工具状态为草稿状态
        if (businessConfig.isToolEvnMainSwitch() && ToolsEnvEnum.DEV_ENV.getDesc().equals(businessConfig.getToolsEvn())) {
            // 检查是否存在非草稿状态的工具
            boolean hasNonDraftTools = toolsInfoList.stream()
                    .anyMatch(tool -> tool.getStatus() != null && tool.getStatus() != 0);
            if (hasNonDraftTools) {
                return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, "仅能删除状态为草稿的工具！");
            }
            // 检查工具是否被引用
            List<ToolsInfoEntity> toolsChildsList = toolsInfoMapper.selectToolsChildsByIds(ids);
            if (!CollectionUtils.isEmpty(toolsChildsList)) {
                return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, "有工具被引用，不能删除，请重新选择！");
            }
        }

        for(ToolsInfoEntity toolsInfoEntity:toolsInfoList){
            if (toolsInfoEntity.getStatus() == null) {
                continue;
            }
            if(toolsInfoEntity.getStatus().equals(ToolsStatusEnum.STATUS_AUDIT.getCode())){
                throw new ToolsException("'"+toolsInfoEntity.getName()+"'工具在审批中无法删除！");
            }
            boolean verifyRun=executeMonitorService.verifyRunMonitory(toolsInfoEntity.getId());
            if(verifyRun){
                throw new ToolsException("'"+toolsInfoEntity.getName()+"'工具在运行中无法删除！");
            }
        }

        int  deleteNumber = toolsInfoMapper.deleteByLogic(ids);
        mqInteract.getToolsChangeDtoMq(ids, SendTypeEnum.TYPE_DELETE.getCode(),null,null);
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, ConstantsEnum.REMOVE_SUCCESS.getDesc());
    }
    /**
     * 根据业务系统ID获取工具信息
     *
     * @param businessSystemIds 业务系统ID
     * @return 工具箱信息
     */
    @Override
    public List<ToolsAlarmDto> selectToolsInfoListByBusinessSystemIds(List<Long> businessSystemIds) {
        List<ToolsInfoEntity> toolsInfoEntities = toolsInfoMapper.selectToolsInfoListByBusinessSystemIds(businessSystemIds);
        return BeanUtils.copy(toolsInfoEntities, ToolsAlarmDto.class);
    }



    /**
     * 具类状态举类  0 草稿 1 已修改 2、审核中3、待启动4、运行中
     *
     * @return 工具箱信息
     */
    @Override
    public List<ToolsStatusDto> selectToolsStatus() {

        return ToolsStatusEnum.getOperatModuleEnum();
    }


    /**
     * 根据code查询工具
     *
     * @param code
     * @return
     */
    @Override
    public ToolsInfoEntity selectToolsByCode(String code) {
        ToolsInfoEntity query = new ToolsInfoEntity();
        query.setCode(code);
        List<ToolsInfoEntity> toolsInfoList = toolsInfoMapper.selectToolsInfoList(query);
        if(!toolsInfoList.isEmpty()){
            return toolsInfoList.get(0);
        }
        return null;
    }
    /**
     * 获取所有用户信息 -用于分页数据
     *
     * @param query 用户查询信息
     * @param pageNum 页码
     * @param pageSize 单页长度
     * @return 用户信息
     */
    @Override
    public PageInfo<UserInfoDto> getUserInfoPageList(UserInfoSearchDto query, Integer pageNum, Integer pageSize) {
        query.setPageNum(pageNum);
        query.setPageSize(pageSize);
        return userInfoInteract.getUserInfoByLoginNameForApi(query);
    }

    /**
     * 修改工具创建人
     * @param toolsCreateUserDto 工具创建人实体
     * @return 返回更新结果
     */
    @Override
    public int updateCreateUser(ToolsCreateUserDto toolsCreateUserDto,UserDto userDto) {
        ToolsInfoEntity toolsInfoEntity = new ToolsInfoEntity();
        toolsInfoEntity.setId(toolsCreateUserDto.getToolId());
        toolsInfoEntity.setCreatorId(toolsCreateUserDto.getCreatorId());
        toolsInfoEntity.setCreatorName(toolsCreateUserDto.getCreatorName());
        toolsInfoEntity.setUpdatorId(userDto.getUserId());
        toolsInfoEntity.setUpdatorName(userDto.getUserName());
        int updated = toolsInfoMapper.updateToolsInfo(toolsInfoEntity);
        if (updated > 0) {
            logAuditService.logOperationAudit(toolsCreateUserDto.getToolId(),OperatModuleEnum.TOOL_EDIT,OperatTypeEnum.TOOL_UPDATE_CREATOR,OperatResultEnum.SUCCESS.getCode(), userDto,OperatTypeEnum.TOOL_UPDATE_CREATOR.getRemark());
        } else {
            logAuditService.logOperationAudit(toolsCreateUserDto.getToolId(),OperatModuleEnum.TOOL_EDIT,OperatTypeEnum.TOOL_UPDATE_CREATOR,OperatResultEnum.FAILURE.getCode(), userDto,OperatTypeEnum.TOOL_UPDATE_CREATOR.getRemark());
        }
        return updated;
    }

    /**
     * 根据当前用户查询是否是审批人
     * @param toolId 工具id
     * @return boolean 返回结果
     */
    @Override
    public boolean isAuditUser(Long toolId, UserDto user) {
       Long auditorId = toolsInfoMapper.selectAuditorIdByToolId(toolId);
        if (user.getUserId() != null) {
            return user.getUserId().equals(auditorId);
        }
        return false;
    }

    /**
     * 校验工具撤回
     * @param ids 工具主键集合
     * @return 撤回结果
     */
    @Override
    public R<Object> checkWithDraw(Long[] ids, UserDto user) throws ToolsException {
        try {
            String toolPrefix = "工具:";
            String errMessage = ",无法撤回！";
            //校验参数
            validateIds(ids);
            // 循环校验工具操作
            for (Long id : ids) {
                // 工具信息查询
                ToolsInfoEntity toolsInfo = toolsInfoMapper.selectToolsInfoById(id);

                // 校验工具是否存在
                if (toolsInfo == null) {
                    throw new ToolsException(String.format("%sID: %d不存在%s", toolPrefix, id, errMessage));
                }
                String code = toolsInfo.getCode();
                // 校验工具状态
                if (!ToolsStatusEnum.STATUS_AUDIT.getCode().equals(toolsInfo.getStatus())) {
                    throw new ToolsException(String.format("%s%s的工具状态非审核中%s", toolPrefix, code, errMessage));
                }

                // 校验工具是否已下线
                if (ToolsStatusEnum.STATUS_OFFLINE.getCode().equals(toolsInfo.getStatus())) {
                    throw new ToolsException(String.format("%s%s已下线%s", toolPrefix, code, errMessage));
                }

                // 审核状态校验
                AuditEntity auditEntity = new AuditEntity();
                auditEntity.setBusinessId(id);
                auditEntity.setApprovalState(AuditStateEnum.AUDIT_UNDER_REVIEW.getCode());
                List<AuditEntity> auditEntityList = auditMapper.selectAuditList(auditEntity);
                if (auditEntityList == null || auditEntityList.isEmpty()) {
                    throw new ToolsException(String.format("%s%s没有审核中记录%s", toolPrefix, code, errMessage));
                }
                //获取审核类型
                Map<Integer, AuditEntity> auditMap = convertToAuditMap(auditEntityList);

                //工具草稿一级审批
                AuditEntity firstLevelAudit = auditMap.get(AuditTypeEnum.AUDIT_TOOLS_DRAFT_ONE.getCode());
                //工具草稿二级审批
                AuditEntity secondLevelAudit = auditMap.get(AuditTypeEnum.AUDIT_TOOLS_DRAFT_TWO.getCode());

                // 一级审批校验
                if (Objects.nonNull(firstLevelAudit)) {
                    // 校验申请人
                    String checkResult = checkApplyId(firstLevelAudit, user);
                    if (checkResult != null) {
                        throw new ToolsException(String.format("%s%s%s%s", toolPrefix, code, checkResult, errMessage));
                    }

                    // 一级审批状态校验
                    if (AuditStateEnum.AUDIT_APPROVED.getCode().equals(firstLevelAudit.getApprovalState())) {
                        // 二级审批校验
                        if (Objects.nonNull(secondLevelAudit)) {
                            // 校验申请人
                            checkResult = checkApplyId(secondLevelAudit, user);
                            if (checkResult != null) {
                                throw new ToolsException(String.format("%s%s%s%s", toolPrefix, code, checkResult, errMessage));
                            }
                            // 二级审批状态校验
                            if (!AuditStateEnum.AUDIT_UNDER_REVIEW.getCode().equals(secondLevelAudit.getApprovalState())) {
                                throw new ToolsException(String.format("%s%s%s%s", toolPrefix, code, "的二级审批状态非审核中", errMessage));
                            }
                        }
                    } else {
                        // 一级审批状态非审核中
                        if (!AuditStateEnum.AUDIT_UNDER_REVIEW.getCode().equals(firstLevelAudit.getApprovalState())) {
                            throw new ToolsException(String.format("%s%s%s%s", toolPrefix, code, "的一级审批状态非审核中", errMessage));
                        }
                    }
                } else {
                    // 工具下线审批撤回
                    AuditEntity downLineAudit = auditMap.get(AuditTypeEnum.AUDIT_TOOLS_DOWN_LINE.getCode());
                    if (Objects.nonNull(downLineAudit)) {
                        String checkResult = checkApplyId(downLineAudit, user);
                        if (checkResult != null) {
                            throw new ToolsException(String.format("%s%s%s%s", toolPrefix, code, checkResult, errMessage));
                        }
                        if (!AuditStateEnum.AUDIT_UNDER_REVIEW.getCode().equals(downLineAudit.getApprovalState())) {
                            throw new ToolsException(String.format("%s%s%s%s", toolPrefix, code, "的审批状态非审核中", errMessage));
                        }
                    } else {
                        throw new ToolsException(String.format("%s%s要撤回的审批不存在%s", toolPrefix, code, errMessage));
                    }
                }
            }
        } catch (ToolsException ex) {
            throw ex;
        } catch (Exception e) {
            logger.error("check with draw is error:", e);
            throw new ToolsException("check.fail!");
        }
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, "check.success!");
    }

    /**
     * 工具撤回
     * @param ids 工具主键集合
     * @return 返回更新结果
     */
    @Override
    public R<Object> toolsWithDraw(Long[] ids, UserDto user) throws ToolsException {
        //校验参数
        validateIds(ids);
        try {
            for (Long id : ids) {
                // 获取工具审核信息
                AuditEntity auditTypeEntity = new AuditEntity();
                auditTypeEntity.setBusinessId(id);
                auditTypeEntity.setApprovalState(AuditStateEnum.AUDIT_UNDER_REVIEW.getCode());
                List<AuditEntity> auditEntityList = auditMapper.selectAuditList(auditTypeEntity);
                if (auditEntityList == null || auditEntityList.isEmpty()) {
                    throw new ToolsException("没有审核中记录,无法撤回!");
                }
                //获取审核类型
                Map<Integer, AuditEntity> auditMap = convertToAuditMap(auditEntityList);
                //工具下线撤回
                if (auditMap.containsKey(AuditTypeEnum.AUDIT_TOOLS_DOWN_LINE.getCode())) {
                    AuditEntity downLineAudit = auditMap.get(AuditTypeEnum.AUDIT_TOOLS_DOWN_LINE.getCode());
                    //发送双人复核工具下线审核撤回及更新工具状态
                    sendWithDrawAudit(downLineAudit, id, user);
                    continue;
                }
                //工具迁移撤回
                //工具草稿一级审批
                AuditEntity firstLevelAudit = auditMap.get(AuditTypeEnum.AUDIT_TOOLS_DRAFT_ONE.getCode());
                //工具草稿二级审批
                AuditEntity secondLevelAudit = auditMap.get(AuditTypeEnum.AUDIT_TOOLS_DRAFT_TWO.getCode());
                // 根据审批类型发送双人复核工具撤回及更新工具状态
                if (Objects.nonNull(secondLevelAudit)) {//二级撤回
                    sendWithDrawAudit(secondLevelAudit, id, user);
                } else {//一级撤回
                    sendWithDrawAudit(firstLevelAudit, id, user);
                }
            }

            //多环境当前生产环境
            if (businessConfig.isToolEvnMainSwitch() && ToolsEnvEnum.PRO_ENV.getDesc().equals(businessConfig.getToolsEvn())) {
                //撤回成功！向下同步工具状态失败!
                handleSyncToolStatus(new ArrayList<>(Arrays.asList(ids)), "撤回成功！但同步工具状态失败!");
            }
            // 撤回成功
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, "撤回成功!");
        }  catch (ToolsException te) {
            throw te;
        } catch (AuditException ex) {
            throw new ToolsException("发送双人复核工具撤回失败，请联系管理员!");
        } catch (Exception e) {
            logger.error("Failed to process tools withdraw:",e);
            throw new ToolsException("withdraw.fail!");
        }
    }

    /**
     * 校验工具下线
     * @param ids 工具主键集合
     * @return 结果
     */
    @Override
    public R<Object> checkDownLine(Long[] ids, UserDto user) throws ToolsException {
        //先校验参数
        validateIds(ids);
        //校验工具操作
        checkTools(ids, user);
        //下线操作的提示信息
        //工具下线集合
        List<Long> downIds = new ArrayList<>();
        //工具删除集合
        List<Long> delIds = new ArrayList<>();
        //工具下线工具编码提示
        StringBuilder downStr = new StringBuilder();
        //描述工具下线编码提示
        StringBuilder msDownStr = new StringBuilder();
        //工具删除工具编码提示
        StringBuilder delStr = new StringBuilder();
        //工具下线标识
        boolean downFlag = false;
        //工具删除标识
        boolean delFlag = false;
        try {
            //循环构建工具下线提示信息
            for (Long id : ids) {
                // 工具信息查询
                ToolsInfoCategoryEntity toolsInfoEntity = toolsInfoMapper.selectToolsInfoCategoryById(id);
                //是否有审核通过记录
                boolean auditApproved = containsAuditApproved(toolsInfoEntity.getId());
                //工具状态为生效且工具的审核状态是审核通过、退回、撤销、空，这四种状态，点击下线时弹出提示
                //“工具编号XXX下线将影响引用此工具的组合工具执行，是否确定下线？”，否则不允许下线弹窗提示内容“选中工具不可以下线”
                boolean canDown = (ToolsStatusEnum.STATUS_WAITING.getCode().equals(toolsInfoEntity.getStatus())
                        && (AuditStateEnum.AUDIT_APPROVED.getCode().equals(toolsInfoEntity.getApprovalState())
                        || AuditStateEnum.AUDIT_SEND_BACK.getCode().equals(toolsInfoEntity.getApprovalState())
                        || AuditStateEnum.AUDIT_WITHDRAW.getCode().equals(toolsInfoEntity.getApprovalState())
                        || toolsInfoEntity.getApprovalState() == null))
                        //已修改且审核通过
                        || ToolsStatusEnum.STATUS_MODIFIED.getCode().equals(toolsInfoEntity.getStatus())
                        && AuditStateEnum.AUDIT_APPROVED.getCode().equals(toolsInfoEntity.getApprovalState())
                        //已修改且有审核通过记录
                        || (ToolsStatusEnum.STATUS_MODIFIED.getCode().equals(toolsInfoEntity.getStatus())
                        && auditApproved);

                // //工具是草稿状态且审核状态为空(待审核)时，工具下线后直接删除，点击下线提示“此工具下线后将被删除，是否确认下线？”
                boolean canDelete = (ToolsStatusEnum.STATUS_DRAFT.getCode().equals(toolsInfoEntity.getStatus())
                        && (AuditStateEnum.AUDIT_AWAITING.getCode().equals(toolsInfoEntity.getApprovalState())
                        || toolsInfoEntity.getApprovalState() == null))
                        //工具是重审状态且审核状态为退回或为撤销时时，工具下线后直接删除，点击下线提示“工具XXX下线后将被删除，是否确认下线？
                        || (ToolsStatusEnum.STATUS_RE_AUDIT.getCode().equals(toolsInfoEntity.getStatus())
                        && AuditStateEnum.AUDIT_SEND_BACK.getCode().equals(toolsInfoEntity.getApprovalState())
                        || AuditStateEnum.AUDIT_WITHDRAW.getCode().equals(toolsInfoEntity.getApprovalState()))
                        //工具状态是已修改，审批状态是空(待审核)时，工具下线后直接删除，点击下线提示“此工具下线后将被删除，是否确认下线？
                        || (ToolsStatusEnum.STATUS_MODIFIED.getCode().equals(toolsInfoEntity.getStatus())
                        && AuditStateEnum.AUDIT_AWAITING.getCode().equals(toolsInfoEntity.getApprovalState()))
                        || (ToolsStatusEnum.STATUS_MODIFIED.getCode().equals(toolsInfoEntity.getStatus())
                        && !auditApproved);
                if (canDown) {
                    if (ToolsTypeEnum.TYPE_DESCRIPTION.getCode().equals(toolsInfoEntity.getType())) {
                        msDownStr.append(toolsInfoEntity.getCode()).append(",");
                    } else {
                        downStr.append(toolsInfoEntity.getCode()).append(",");
                    }
                    downFlag = true;
                    downIds.add(id);
                }
                if (canDelete) {
                    delStr.append(toolsInfoEntity.getCode()).append(",");
                    delFlag = true;
                    delIds.add(id);
                }
                if (!canDown && !canDelete) {
                    throw new ToolsException("所选工具中不可以下线!");
                }
            }
            //下线总提示
            String downInfo  = buildDownInfo(downFlag, delFlag, downStr.toString(), msDownStr.toString(), delStr.toString());
            //返回下线校验结果
            if (StringUtils.isNotBlank(downInfo)) {
                DownLineToolsResultDto downLineToolsResultDto = buildDownLineToolList(downIds, delIds);
                return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, downLineToolsResultDto, downInfo);
            } else {
                return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, "check.fail!");
            }
        } catch (ToolsException ex) {
            throw ex;
        } catch (Exception e) {
            logger.error("check down line tools is error:", e);
            throw new ToolsException("check.fail!");
        }
    }

    /**
     * 工具下线
     * @param downLineToolsSaveDto 工具下线实体
     * @return 返回结果
     */
    @Override
    public R<Object> toolsDownLine(DownLineToolsSaveDto downLineToolsSaveDto, UserDto user) throws ToolsException {
        //工具下线集合
        List<Long> downIds = new ArrayList<>();
        //工具删除集合
        List<Long> delIds = new ArrayList<>();
        //先校验参数
        if (downLineToolsSaveDto == null || downLineToolsSaveDto.getDownLineToolsDtoList().isEmpty()) {
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, "参数不能为空，下线失败!");
        }
        //下线操作标识
        boolean isDown = false;
        //删除操作标识
        boolean isDel = false;
        try {
            //循环校验工具操作
            for (DownLineToolsDto downLineToolsDto : downLineToolsSaveDto.getDownLineToolsDtoList()) {
                //是否审核类型
                Integer type = downLineToolsDto.getType();
                //工具id
                Long toolsId = downLineToolsDto.getToolsId();

                if (type != null) {
                    if (type.equals(Integer.valueOf(2))) {
                        downIds.add(toolsId);
                    } else if (type.equals(Integer.valueOf(1))) {
                        delIds.add(toolsId);
                    }
                }
            }
            if (!downIds.isEmpty()) {
                isDown = batchDownLineTools(downIds,downLineToolsSaveDto.getAuditEverybodyDtoList(),user);
            }

            if (!delIds.isEmpty()) {
                //根据工具ID查询是否有被其他工具所引用，前端检验时查询了一遍
                // 因是删除操作双层保险，再检验一遍
                //checkToolReferences()
                isDel = batchDeleteTools(delIds);
            }

            //下线不需要向下同步状态，审批通过后才能同步状态
           /* if (Boolean.TRUE.equals(businessConfig.isToolEvnMainSwitch())) {
                if (!downIds.isEmpty() && isDown) {
                    handleSyncToolStatus(downIds, "下线成功！但同步工具状态失败!");
                }
                //直接删除的工具不需要向下同步，是因为直接在生产创建的工具
                if (!delIds.isEmpty() && isDel) {
                    handleSyncToolStatus(delIds, "删除成功！但同步工具状态失败!");
                }
            }*/

        } catch (Exception e) {
            logger.error("tools down line is error:", e);
            throw new ToolsException("downLine.fail!");
        }
        if (isDown) {
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, "工具下线已提交审批,待审批通过后完成下线操作!");
        }
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, "下线成功!");
    }

    /**
     * 查询当前组合工具下调用的 子工程 工具集合
     * @param toolId 工具id
     * @return boolean 返回结果
     */
    @Override
    public  List<ToolsInfoScriptBean>   toolsInfoEntityChildNodeList(Long toolId, UserDto user) {
        ToolsInfoEntity toolsInfoEntity = toolsInfoMapper.selectToolsInfoById(toolId);
        if(toolsInfoEntity==null ){
            return new ArrayList<>();
        }
        String childIds= toolsInfoEntity.getChildIds();

        return  toolsInfoEntityChildNodeList(childIds,user);

    }

    /**
     * 子工程 工具集合
     * @param childIds 工具集合
     * @return boolean 返回结果
     */
    @Override
    public  List<ToolsInfoScriptBean>   toolsInfoEntityChildNodeList(String childIds, UserDto user) {
        //判别childIds参数是否为空
        if (childIds == null || childIds.trim().isEmpty()) {
            return new ArrayList<>();
        }

        if (childIds.startsWith(",")) {
            StringBuilder sb = new StringBuilder(childIds);
            sb.deleteCharAt(0);
            childIds = sb.toString();
        }
        String[] childIdArr = childIds.split(",");
        Long [] ids = new Long[childIdArr.length];
        String []  uuId= new String[childIdArr.length];

        List<ToolsInfoScriptBean>  toolsInfoScriptBeanList=new ArrayList<>();

        for (int i = 0; i < childIdArr.length; i++){
            String[] toolIds = childIdArr[i].split("_");
            for (int c= 0; c< toolIds.length; c++){
                ids[i]=Long.parseLong(toolIds[0]);
                uuId[i]=toolIds[1];
            }
            // 查询工具
            ToolsInfoEntity childNodeToolsInfoEntity = toolsInfoMapper.selectToolsInfoById(ids[i]);

            ToolsInfoScriptBean toolsInfoScriptBean=BeanUtils.copy(childNodeToolsInfoEntity,ToolsInfoScriptBean.class);
            //如果是脚本工具 查询脚本工具
            if(childNodeToolsInfoEntity.getType().equals(ToolsTypeEnum.TYPE_SCRIPT.getCode())){

                ScriptContentDto scriptContentDto=    scriptInteract.getDefaultScriptInfo(user,uuId[i]);
                toolsInfoScriptBean.setScriptContentDto(scriptContentDto);
            }
            //如果是组合工具 查询组合工具
            if(childNodeToolsInfoEntity.getType().equals(ToolsTypeEnum.TYPE_COMBINED.getCode())){
                try {
                    ToolsProjectDto toolsProjectInfoDto =  toolsProjectService.selectToolsProjectByToolId(ids[i]);
                    toolsInfoScriptBean.setToolsProjectDto(toolsProjectInfoDto);
                } catch (ToolsException e) {
                    logger.info(e.getMessage());
                }
            }

            toolsInfoScriptBeanList.add(toolsInfoScriptBean);
        }


        return toolsInfoScriptBeanList;

    }

    /**
     * 发送双人复核工具撤回操作
     * @param auditEntity 审核实体
     * @param id 工具id
     * @param user 用户信息
     * @return
     */
    private void sendWithDrawAudit(AuditEntity auditEntity, Long id, UserDto user) throws AuditException {
        AuditDto auditDto = BeanUtils.copy(auditEntity, AuditDto.class);
        Long auditPush = auditService.withDrawDoubleCheck(auditDto);
        if (auditPush != -1L) {
            ToolsInfoEntity toolsInfo = new ToolsInfoEntity();
            toolsInfo.setId(id);
            //工具状态-主线为已修改
            toolsInfo.setStatus(ToolsStatusEnum.STATUS_MODIFIED.getCode());
            //多环境的工具状态更新为根据当前工具状态生效则不动，其他的则重审
            if (businessConfig.isToolEvnMainSwitch() && ToolsEnvEnum.PRO_ENV.getDesc().equals(businessConfig.getToolsEvn())) {
                ToolsInfoEntity toolsInfoEntity = toolsInfoMapper.selectToolsInfoById(id);
                if (!ToolsStatusEnum.STATUS_WITHDRAWN.getCode().equals(toolsInfoEntity.getStatus())){
                    toolsInfo.setStatus(ToolsStatusEnum.STATUS_RE_AUDIT.getCode());
                }
            }
            toolsInfo.setUpdatorId(user.getUserId());
            toolsInfo.setUpdatorName(user.getUserName());
            int updated = toolsInfoMapper.updateToolsInfo(toolsInfo);
            String withDrawResult = "撤回结果";
            //加入审计日志
            if (updated > 0) {
                logOperationAudit(id, OperatModuleEnum.TOOL_EDIT, OperatTypeEnum.TOOL_WITH_DRAW_REVIEW, OperatResultEnum.SUCCESS.getCode(), user,withDrawResult);
            } else {
                logOperationAudit(id, OperatModuleEnum.TOOL_EDIT, OperatTypeEnum.TOOL_WITH_DRAW_REVIEW, OperatResultEnum.FAILURE.getCode(), user,withDrawResult);
            }
        }
    }

    /**
     * 校验工具是否符合下线条件
     * @param ids 工具id
     * @return 结果
     */
    private void checkTools(Long[] ids, UserDto user) throws ToolsException {
        String toolPrefix = "工具:";
        String errMessage = ",无法下线！";
        for (Long id : ids) {
            // 工具信息查询
            ToolsInfoEntity toolsInfoEntity = toolsInfoMapper.selectToolsInfoById(id);
            // 校验工具是否存在
            if (toolsInfoEntity == null) {
                throw new ToolsException(String.format("%sID: %d不存在%s", toolPrefix, id, errMessage));
            }
            // 校验工具状态-审核中
            if (ToolsStatusEnum.STATUS_AUDIT.getCode().equals(toolsInfoEntity.getStatus())) {
                // 获取工具审核信息
                AuditEntity auditTypeEntity = new AuditEntity();
                auditTypeEntity.setBusinessId(id);
                auditTypeEntity.setType(AuditTypeEnum.AUDIT_TOOLS_DOWN_LINE.getCode());
                List<AuditEntity> auditEntityList = auditMapper.selectAuditList(auditTypeEntity);
                //下线审核中校验
                if (auditEntityList != null && !auditEntityList.isEmpty()) {
                    //获取审核类型
                    Map<Integer, AuditEntity> auditMap = convertToAuditMap(auditEntityList);
                    //获取工具下线审核
                    AuditEntity downLineAudit = auditMap.get(AuditTypeEnum.AUDIT_TOOLS_DOWN_LINE.getCode());
                    if (Objects.nonNull(downLineAudit) &&
                            AuditStateEnum.AUDIT_UNDER_REVIEW.getCode().equals(downLineAudit.getApprovalState())) {
                        throw new ToolsException(String.format("%s%s下线审核中%s", toolPrefix, toolsInfoEntity.getCode(), errMessage));
                    }
                } else {
                    throw new ToolsException(String.format("%s%s审核中%s", toolPrefix, toolsInfoEntity.getCode(), errMessage));
                }
            }

            //工具是否运行中
            boolean verifyRun = executeMonitorService.verifyRunMonitory(toolsInfoEntity.getId());
            if(verifyRun){
                throw new ToolsException(String.format("%s%s为运行中%s", toolPrefix, toolsInfoEntity.getCode(), errMessage));
            }

            //校验下线的工具创建人是否为当前登录账号
            if (!user.getUserId().equals(toolsInfoEntity.getCreatorId())) {
                throw new ToolsException(String.format("%s%s的创建人不是当前登录用户%s", toolPrefix, toolsInfoEntity.getCode(), errMessage));
            }

            //校验当前工具是否被其他工具所引用
            String checkResult = checkToolReferences(toolPrefix, toolsInfoEntity.getCode(), errMessage, id);
            if (StringUtils.isNotBlank(checkResult)) {
                throw new ToolsException(checkResult);
            }
        }
    }

    /**
     * 处理下线提示信息
     * @param downFlag 下线标识
     * @param delFlag  删除标识
     * @param downStr  下线提示
     * @param msDownStr 描述工具提示
     * @param delStr 删除提示
     * @return 结果
     */
    private String buildDownInfo(boolean downFlag, boolean delFlag, String downStr, String msDownStr, String delStr) {
        //下线提示
        downStr = removeLastStr(downStr);
        //描述工具提示
        msDownStr = removeLastStr(msDownStr);
        //删除提示
        delStr = removeLastStr(delStr);
        String toolPrefix = "工具:";
        //既有下线又有删除操作
        if (downFlag && delFlag) {
            String info = "下线将影响引用此工具的组合工具执行，且需要审核，工具:";
            if (StringUtils.isNotBlank(msDownStr) && StringUtils.isNotBlank(downStr)) {
                return toolPrefix + downStr + info + msDownStr + "将提交下线审核，工具:" + delStr + "下线后将被删除，是否确认下线？";
            } else if (StringUtils.isNotBlank(msDownStr)) {
                return toolPrefix + msDownStr + "将提交下线审核，工具:" + delStr + "下线后将被删除，是否确认下线？";
            } else {
                return toolPrefix + downStr + info + delStr + "下线后将被删除，是否确认下线？";
            }
        } else if (downFlag) {//只有下线
            if (StringUtils.isNotBlank(msDownStr) && StringUtils.isNotBlank(downStr)) {
                return toolPrefix + downStr + "下线将影响引用此工具的组合工具执行，且需要审核，工具:" + msDownStr + "将提交下线审核，是否确认下线？";
            } else if (StringUtils.isNotBlank(msDownStr)) {
                return toolPrefix + msDownStr + "将提交下线审核，是否确认下线？";
            } else {
                return toolPrefix + downStr + "下线将影响引用此工具的组合工具执行，且需要审核，是否确认下线？";
            }
        } else if (delFlag) {//只有删除
            return toolPrefix + delStr + "下线后将被删除，是否确认下线？";
        } else {
            return "";
        }
    }

    /**
     * 处理下线和删除工具
     * @param downIds 下线ID集合
     * @param delIds 删除ID集合
     * @return 结果
     */
    private DownLineToolsResultDto buildDownLineToolList(List<Long> downIds, List<Long> delIds) {
        DownLineToolsResultDto downLineToolsResultDto = new DownLineToolsResultDto();
        //是否需要审核标识
        boolean isAudit = false;
        List<DownLineToolsDto> downLineToolList = new ArrayList<>();
        if (downIds != null && !downIds.isEmpty()) {
            for (Long downId : downIds) {
                DownLineToolsDto downLineToolsDto = new DownLineToolsDto();
                downLineToolsDto.setToolsId(downId);
                //是否下线需审核-2 需要审核
                downLineToolsDto.setType(2);
                downLineToolList.add(downLineToolsDto);
            }
            isAudit = true;
        }
        if (delIds != null && !delIds.isEmpty()) {
            for (Long delId : delIds) {
                DownLineToolsDto downLineToolsDto = new DownLineToolsDto();
                downLineToolsDto.setToolsId(delId);
                //是否下线需审核-1 不需要审核
                downLineToolsDto.setType(1);
                downLineToolList.add(downLineToolsDto);
            }
        }
        downLineToolsResultDto.setAudit(isAudit);
        downLineToolsResultDto.setDownLineToolsDtoList(downLineToolList);
        return downLineToolsResultDto;
    }

    /**
     * 撤回同步工具状态
     * @param failureMessage 工具信息实体
     * @param failureMessage 失败提示信息
     * @return 结果
     */
    private void handleSyncToolStatus(List<Long> toolIds, String failureMessage) throws ToolsException{
        Long[] toolsIds = toolIds.toArray(new Long[0]);
        List<ToolsInfoEntity> entityList = toolsInfoMapper.selectToolsInfoListByIds(toolsIds);
        if (entityList != null && !entityList.isEmpty()) {
            boolean syncFlag = true;
            StringBuilder failureMsg = new StringBuilder("工具:");
            for (ToolsInfoEntity entity : entityList) {
                ToolsDto toolsDto = BeanUtils.copy(entity, ToolsDto.class);
                //都变成重审
                toolsDto.setStatus(ToolsStatusEnum.STATUS_RE_AUDIT.getCode());
                boolean synced = syncToolsStatus(toolsDto);
                //同步失败
                if (!synced) {
                    syncFlag = false;
                    failureMsg.append(entity.getCode()).append(",");
                }
            }
            // 同步失败提示
            if (!syncFlag) {
                String reMsg = removeLastStr(failureMsg.toString()) + failureMessage;
                throw new ToolsException(reMsg); //提示同步失败信息
            }
        }
    }

    /**
     * 同步其他环境工具状态—（除生产环境外）
     * @param toolsDto 工具信息实体
     * @return 结果
     */
    private boolean syncToolsStatus(ToolsDto toolsDto) throws ToolsException {
        boolean flag =false;
        if (toolsDto != null) {
            //生成工具状态status的json文件
            com.alibaba.fastjson2.JSONObject toolsJson = com.alibaba.fastjson2.JSONObject.from(toolsDto);
            //判别都不为空
            if (toolsDto.getCode() == null || toolsJson == null) {
                logger.error("tools code or toolsJson is null, cannot convert to file.");
                throw new ToolsException("工具编码或状态JSON为空，无法生成同步状态文件");
            }

            // 日志输出
            String statusJson = toolsJson.toJSONString();
            logger.info("Generated tools status json: {}", statusJson);

            File statusFile;
            try {
                statusFile = FileUtility.convertJsonFile(toolsDto.getCode(), toolsJson);
            } catch (IOException e) {
                logger.error("convert tools withdraw status file is failed!!");
                throw new ToolsException("工具撤回成功，同步状态文件失败");
            }
            String basePath = businessConfig.getSftpDataPath();
            if (basePath == null || basePath.isEmpty()) {
                logger.error("upload withdraw status file sftp data path is null or empty!");
                throw new ToolsException("upload withdraw status file sftp data path is required!");
            }
            List<String> sftpPathList = Arrays.stream(new String[]{
                            ToolsMoveConstants.TOOLS_MOVE_DEV_EVN,
                            ToolsMoveConstants.TOOLS_MOVE_QA_EVN,
                            ToolsMoveConstants.TOOLS_MOVE_PRE_EVN
                    }).map(env -> basePath + env + ToolsMoveConstants.TOOLS_MOVE_STATUS_PATH)
                    .collect(Collectors.toList());
            // 日志输出同步状态文件路径列表
            String statusPathsLog = Optional.ofNullable(sftpPathList)
                    .filter(list -> !list.isEmpty())
                    .map(list -> list.stream().collect(Collectors.joining(", ")))
                    .orElse("Upload to withdraw status sftp file path is null or empty");
            logger.info("Withdraw status SFTP path list for upload: {}", statusPathsLog);
            //上传status状态文件到sftp服务器上
            if (!sftpPathList.isEmpty() && statusFile != null
                    && ToolsEnvEnum.PRO_ENV.getDesc().equals(businessConfig.getToolsEvn())) {
                for (String ftpPath : sftpPathList) {
                    SFTPUtil sftp = new SFTPUtil(businessConfig.getFtpUser(), businessConfig.getFtpPw(), businessConfig.getFtpIp(),Integer.parseInt(businessConfig.getFtpPort()));
                    flag = sftp.uploadFile(ftpPath, statusFile);
                }
            }
        }
        return flag;
    }

    /**
     * 批量下线工具
     * @param toolsIds 工具ID集合
     * @param auditEverybodyDtoList 下线审批人员集合
     * @param user 用戶信息
     * @return 结果
     */
    private boolean batchDownLineTools(List<Long> toolsIds,List<AuditEverybodyQueryDto> auditEverybodyDtoList,UserDto user) {
        boolean isSuccess = false;
        try {
            for (Long toolsId : toolsIds) {
                ToolsDto   toolsDto = getToolsCombinedInfo(toolsId);
                toolsDto.setAuditEverybodyDtoList(auditEverybodyDtoList);
                AuditDto auditDto = auditService.saveAuditDoubleCheck(toolsId, toolsDto.getAuditEverybodyDtoList(), AuditTypeEnum.AUDIT_TOOLS_DOWN_LINE.getCode(), user);
                if (auditDto == null) {
                    logger.error("Error save audit double check for toolsId: {}",toolsId);
                    throw new ToolsException("保存工具下线审核失败,下线失败!");
                }
                //发送双人复核信息,回更双人复核状态
                Long auditPush = auditService.sendDoubleCheck(auditDto, toolsDto.getAuditEverybodyDtoList(), toolsDto.getName() + "_" + AuditTypeEnum.AUDIT_TOOLS_DOWN_LINE.getDesc());
                if (auditPush != null) {
                    //修改工具状态-审核中
                    ToolsInfoEntity toolsInfoEntity = new ToolsInfoEntity();
                    toolsInfoEntity.setId(toolsDto.getId());
                    if (Boolean.TRUE.equals(businessConfig.isToolEvnMainSwitch())) {//光大不改变状态，只改变审核状态
                        toolsInfoEntity.setStatus(toolsDto.getStatus());
                    }else{//主线状态审核中
                        toolsInfoEntity.setStatus(ToolsStatusEnum.STATUS_AUDIT.getCode());
                    }
                    toolsInfoEntity.setUpdatorId(user.getUserId());
                    toolsInfoEntity.setUpdatorName(user.getUserName());
                    toolsInfoMapper.updateToolsInfo(toolsInfoEntity);
                    isSuccess = true;
                }

                String downLineResult = "下线结果";
                //加入审计日志
                if (isSuccess) {
                    logOperationAudit(toolsId,OperatModuleEnum.TOOL_EDIT,OperatTypeEnum.TOOL_DOWN_LINE,OperatResultEnum.SUCCESS.getCode(), user,downLineResult);
                } else {
                    logOperationAudit(toolsId,OperatModuleEnum.TOOL_EDIT,OperatTypeEnum.TOOL_DOWN_LINE,OperatResultEnum.FAILURE.getCode(), user,downLineResult);
                }
            }
        } catch (Exception e) {
            logger.error("send double check is failed:",e);
            isSuccess = false;
        }
        return isSuccess;
    }

    /**
     * 批量删除工具
     * @param toolsIds 工具ID集合
     * @return 结果
     */
    private boolean batchDeleteTools(List<Long> toolsIds) {
        Long[] ids = toolsIds.toArray(new Long[0]);
        int deleteNumber = toolsInfoMapper.batchDelete(ids);
        return deleteNumber > 0;
    }

    /**
     * 校验工具是否被其他工具所引用
     * @param toolsId 工具ID
     * @return 校验结果
     */
    public String checkToolReferences(String toolPrefix,String code,String errMessage,Long toolsId) throws ToolsException {
        try {
            // 获取当前工具被哪些工具引用
            List<String> toolsChildRefactorList = toolsInfoMapper.getToolsChildListIdRefactorDownLine(toolsId);
            // 被哪些工具引用的集合
            if (toolsChildRefactorList != null && !toolsChildRefactorList.isEmpty()) {
                // 工具提示数量控制在5个工具以内，以免造成页面展示不全的问题
                String subStr = "";
                List<String> referencedTools;

                if (toolsChildRefactorList.size() > 5) {
                    referencedTools = toolsChildRefactorList.subList(0, 5);
                    subStr = "......";
                } else {
                    referencedTools = toolsChildRefactorList;
                }

                // 拼接提示信息
                String joinedTools = String.join("，", referencedTools) + subStr;

                if (!joinedTools.isEmpty()) {
                    // 被引用提示信息
                    String refactorMessage = String.format("被工具（%s）引用；", joinedTools);

                    if (!refactorMessage.isEmpty()) {
                        //错误信息
                        return String.format("%s%s%s%s", toolPrefix, code, refactorMessage, errMessage);
                    }
                }
            }
        } catch (Exception e) {
            logger.error("get tools child list id refactor down line is error:",e);
            throw new ToolsException("获取工具被引用失败!");
        }
        return "";
    }

    /**
     * 校验申请人
     * @param auditEntity 审核实体
     * @param user 用户信息
     * @return 结果
     */
    private String checkApplyId(AuditEntity auditEntity, UserDto user) {
        if (auditEntity == null || !user.getUserId().equals(auditEntity.getApplyId())) {
            return "的当前登录用户不是申请人";
        }
        return null;
    }

    /**
     * 处理字符串
     * @param str 字符串信息
     * @return 结果
     */
    private String removeLastStr(String str) {
        if (str != null && str.endsWith(",")) {
            return str.substring(0, str.length() - 1);
        }
        return str;
    }

    /**
     * 校验参数
     * @param ids 工具id集合
     * @return 结果
     */
    private void validateIds(Long[] ids) throws ToolsException {
        if (ids == null || ids.length == 0) {
            throw new ToolsException("请求参数为空，操作失败!");
        }
    }

    /**
     * 是否有审核通过的记录
     *
     * @param toolsId 工具ID
     * @return 结果
     */
    private boolean containsAuditApproved (Long toolsId) {
        // 获取工具审核信息
        AuditEntity auditTypeEntity = new AuditEntity();
        auditTypeEntity.setBusinessId(toolsId);
        List<AuditEntity> auditEntityList = auditMapper.selectAuditList(auditTypeEntity);
        if (CollectionUtils.isEmpty(auditEntityList)) {
            return false;
        }
        //审核成功过
        return auditEntityList.stream()
                .filter(entity -> entity.getApprovalState() != null)
                .anyMatch(entity -> AuditStateEnum.AUDIT_APPROVED.getCode().equals(entity.getApprovalState())); //是否包含审核通过记录
    }

    /**
     * 审核类型转换
     * @param auditEntityList 审核实体集合
     * @return Map<Integer, AuditEntity>
     */
    private Map<Integer, AuditEntity> convertToAuditMap(List<AuditEntity> auditEntityList) {
        return auditEntityList.stream()
                .collect(Collectors.toMap(
                        AuditEntity::getType,
                        entity -> entity,
                        (e1, e2) -> e1.getApplyTime().before(e2.getApplyTime()) ? e2 : e1
                ));
    }

    /**
     * 日志操作记录
     * @param toolsId 工具id
     * @param operateModule 操作模块枚举
     * @param operateType 操作类型枚举
     * @param userDto 用户信息
     * @return
     */
    private void logOperationAudit(Long toolsId,OperatModuleEnum operateModule,OperatTypeEnum operateType,Integer result,UserDto userDto,String msg) {
        //公共部分
        String commonRemark = operateModule.getRemark() + operateType.getRemark();
        // 构建操作内容
        String operatingContent = commonRemark + "（工具ID:" + toolsId + msg + "）";
        // 构建结果描述
        String resultDesc = commonRemark;
        // 根据 result 的值设置结果描述
        if (Integer.valueOf(1).equals(result)) {
            resultDesc += "失败";
        } else {
            resultDesc += "成功";
        }
        // 创建日志审计DTO对象
        LogAuditDto logAuditDto = new LogAuditDto(operateModule.getCode(), operateType.getCode(), operatingContent);
        logAuditDto.setUserDto(userDto);
        logAuditDto.setResult(result);
        logAuditDto.setResultDesc(resultDesc);
        // 插入日志审计信息
        logAuditService.insertLogAudit(logAuditDto);
    }


    /**
     * 批量查询工具箱信息
     *
     * @param ids 工具箱信息主键
     * @return 工具箱信息
     */
    @Override
    public HashMap<String, List<ToolsDto>> getToolsBatchCombinedInfo(List<Long> ids,List<ToolsDto> errorScriptList,List<ToolsDto> errorComList,List<ToolsDto> errorDescList) {
        HashMap<String, List<ToolsDto>> map = new HashMap<>();
        HashMap<Long, ToolsInfoEditEntity> longToolsInfoEditEntityHashMap = new HashMap<>();

        List<ToolsInfoEditEntity>  toolsInfos=toolsInfoMapper.selectToolsEditInfoByIds(ids);
        if (Objects.isNull(toolsInfos)) {
            return new HashMap<>();
        }
        //参数
        HashMap<Long, List<ToolsParamEntity>> longParamListHashMap = new HashMap<>();
        List<ToolsParamEntity> toolsParamListAll = toolsParamMapper.selectToolsParamListByIds(ids);
        toolsParamListAll.stream().forEach(x->{
            Long tdToolsId = x.getTdToolsId();
            if (!longParamListHashMap.containsKey(tdToolsId)) {
                //如果不包含
                longParamListHashMap.put(tdToolsId, new ArrayList<ToolsParamEntity>());
            }
            longParamListHashMap.get(tdToolsId).add(x);
        });
        ArrayList<ToolsDto> toolsDtos = new ArrayList<>();
        ArrayList<ToolsDto> toolsDtoLists = new ArrayList<>();
        toolsInfos.stream().forEach(x->{
            longToolsInfoEditEntityHashMap.put(x.getId(),x);
            //合并主表
            ToolsDto toolsDto =  BeanUtils.copy(x, ToolsDto.class);
            toolsDtos.add(toolsDto);
        });
        //附件
        HashMap<Long, List<ToolsFilesEntity>> longFilesListHashMap = new HashMap<>();
        List<ToolsFilesEntity> toolsFilesListAll = toolsFilesMapper.selectToolsFilesListByIds(ids);
        toolsFilesListAll.stream().forEach(x->{
            Long tdToolsId = x.getTdToolsId();
            if (!longFilesListHashMap.containsKey(tdToolsId)) {
                //如果不包含
                longFilesListHashMap.put(tdToolsId, new ArrayList<ToolsFilesEntity>());
            }
            longFilesListHashMap.get(tdToolsId).add(x);
        });

        //查询agent信息
        HashMap<Long, List<ToolsAgentInfoEntity>> longAgentListHashMap = new HashMap<>();
        List<ToolsAgentInfoEntity> toolsAgentInfoListAll = toolsAgentInfoMapper.selectToolsAgentInfoListByIds(ids);
        toolsAgentInfoListAll.stream().forEach(x->{
            Long tdToolsId = x.getTdToolsId();
            if (!longAgentListHashMap.containsKey(tdToolsId)) {
                //如果不包含
                longAgentListHashMap.put(tdToolsId, new ArrayList<ToolsAgentInfoEntity>());
            }
            longAgentListHashMap.get(tdToolsId).add(x);
        });

        //工具箱审批人查询
        HashMap<Long, List<ToolsEverybodyEntity>> longEverybodyListHashMap = new HashMap<>();
        List<ToolsEverybodyEntity> toolsEverybodyListAll = toolsEverybodyMapper.selectToolsEverybodyListByIds(ids);
        toolsEverybodyListAll.stream().forEach(x->{
            Long tdToolsId = x.getTdToolsId();
            if (!longEverybodyListHashMap.containsKey(tdToolsId)) {
                //如果不包含
                longEverybodyListHashMap.put(tdToolsId, new ArrayList<ToolsEverybodyEntity>());
            }
            longEverybodyListHashMap.get(tdToolsId).add(x);
        });
        //组合工具工程工作流
        HashMap<Long, ToolsProjectEntity> longProjectListHashMap = new HashMap<>();
        List<ToolsProjectEntity> toolsProjectListAll = toolsProjectMapper.selectToolsProjectListByIds(ids);
        toolsProjectListAll.stream().forEach(x->{
            Long tdToolsId = x.getTdToolsId();
            longProjectListHashMap.put(tdToolsId, x);
        });

        for (ToolsDto toolsDto : toolsDtos) {
            if (null==toolsDto.getOneTypeId() || null==toolsDto.getOneTypeName()){
                toolsDto.setDesc("未配置一级分类");
                extracted(errorScriptList, errorComList,errorDescList, toolsDto);
                continue;
            }
            if (null==toolsDto.getTwoTypeId() || null==toolsDto.getTwoTypeName()){
                toolsDto.setDesc("未配置二级分类");
                extracted(errorScriptList, errorComList,errorDescList, toolsDto);
                continue;
            }
            if (null==toolsDto.getBusinessSystemId() || null==toolsDto.getBusinessSystemName()){
                toolsDto.setDesc("未配置业务系统");
                extracted(errorScriptList, errorComList,errorDescList, toolsDto);
                continue;
            }
            if (null==toolsDto.getCreatorId() || null==toolsDto.getCreatorName()){
                toolsDto.setDesc("未配置用户");
                extracted(errorScriptList, errorComList,errorDescList, toolsDto);
                continue;
            }
            /** 预估运行风险（1  导致CPU升高2 内存溢出3 生产大文件 4 应用中断 5 其他） */
            Long[] longArray = null;
            ToolsInfoEditEntity toolsInfo = longToolsInfoEditEntityHashMap.get(toolsDto.getId());
            if(toolsInfo.getEstimateOperationalRisk()!=null && !"".equals(toolsInfo.getEstimateOperationalRisk())){
                String[] strArray = toolsInfo.getEstimateOperationalRisk().split(",");
                // 转换后的长整型数组
                longArray = new Long[strArray.length];
                // 转换过程
                for (int i = 0; i < strArray.length; i++) {
                    longArray[i] = Long.parseLong(strArray[i]);
                }
            }
            toolsDto.setEstimateOperationalRisk(longArray==null? new Long[0]:longArray);
            //附件对象
            List<ToolsFilesEntity> toolsFilesList = longFilesListHashMap.get(toolsDto.getId());
            if (toolsDto.getType().equals(ToolsTypeEnum.TYPE_SCRIPT.getCode())){
                if (null==toolsDto.getScriptIds()){
                    toolsDto.setDesc("未配置脚本信息");
                    extracted(errorScriptList, errorComList,errorDescList, toolsDto);
                    continue;
                }
                //参数
                List<ToolsParamEntity> toolsParamList = longParamListHashMap.get(toolsDto.getId());
                //agent信息
                List<ToolsAgentInfoEntity> toolsAgentInfoList = longAgentListHashMap.get(toolsDto.getId());
                /** agent列表 */
                List<ToolsAgentResultDto> toolsAgentResultList =  BeanUtils.copy(toolsAgentInfoList, ToolsAgentResultDto.class);
//                toolsDto.setToolsAgentResultList(toolsAgentResultList);
                /** 参数列表 */
                List<ToolsParamResultDto> toolsParamResultList =  BeanUtils.copy(toolsParamList, ToolsParamResultDto.class);

                toolsDto.setToolsParamResultList(toolsParamResultList);
                /** 脚本文件列表ids */
                List<ToolsFilesQueryDto>  scriptFilesList =  BeanUtils.copy(toolsFilesList, ToolsFilesQueryDto.class);
                toolsDto.setScriptFilesList(scriptFilesList);
            }else if(toolsDto.getType().equals(ToolsTypeEnum.TYPE_COMBINED.getCode())){
                //获取脚本信息
                ToolsProjectDto toolsProjectDto ;
                ToolsProjectInfoDto toolsProjectInfoDto =null;
                try {
                    ToolsProjectEntity toolsProjectEntitie = longProjectListHashMap.get(toolsDto.getId());
                    toolsProjectDto = BeanUtils.copy(toolsProjectEntitie, ToolsProjectDto.class);

                    String workflowContent= studioInteract.getWorkflowJson(toolsProjectDto.getWorkflowId());
                    toolsProjectDto.setWorkflowContent(workflowContent);
                    toolsProjectInfoDto = BeanUtils.copy(toolsProjectDto, ToolsProjectInfoDto.class);
                } catch (StudioException e) {
                    toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
                }
                toolsDto.setToolsProjectInfoDto(toolsProjectInfoDto);
            }else if(toolsDto.getType().equals(ToolsTypeEnum.TYPE_DESCRIPTION.getCode())){
                /** 描述文件附件ids */
                List<ToolsFilesQueryDto>  describeFilesList=  BeanUtils.copy(toolsFilesList, ToolsFilesQueryDto.class);
                toolsDto.setDescribeFilesList(describeFilesList);
            }

            List<ToolsEverybodyEntity> toolsEverybodyListEn = longEverybodyListHashMap.get(toolsDto.getId());
            List<ToolsEverybodyDto> toolsEverybodyList = BeanUtils.copy(toolsEverybodyListEn, ToolsEverybodyDto.class);
            List< AuditEverybodyQueryDto> auditEverybodyDtoList=new ArrayList<>();
            /** 审核人实例 */
            if(toolsEverybodyList!=null){
                for(ToolsEverybodyDto  toolsEverybodyDto:toolsEverybodyList){
                    AuditEverybodyQueryDto auditEverybodyQueryDto= BeanUtils.copy(toolsEverybodyDto, AuditEverybodyQueryDto.class);
                    UserDto user =getUser();
                    if(auditEverybodyQueryDto.getAuditorId().equals(user.getUserId())){

                    }else {
                        auditEverybodyDtoList.add(auditEverybodyQueryDto);
                    }

                }
//                toolsDto.setAuditEverybodyDtoList(auditEverybodyDtoList);
            }
            toolsDtoLists.add(toolsDto);
        }
        map.put("toolsDtos",toolsDtoLists);
        map.put("errorScriptList",errorScriptList);
        map.put("errorComList",errorComList);
        map.put("errorDescList",errorDescList);
        return map;
    }
    private static void extracted(List<ToolsDto> errorScriptList, List<ToolsDto> errorComList,List<ToolsDto> errorDescList, ToolsDto toolsDto) {
        if (toolsDto.getType().equals(ToolsTypeEnum.TYPE_SCRIPT.getCode())){
            errorScriptList.add(toolsDto);
        }else if(toolsDto.getType().equals(ToolsTypeEnum.TYPE_COMBINED.getCode())){
            errorComList.add(toolsDto);
        }else if(toolsDto.getType().equals(ToolsTypeEnum.TYPE_DESCRIPTION.getCode())){
            errorDescList.add(toolsDto);
        }
    }
}

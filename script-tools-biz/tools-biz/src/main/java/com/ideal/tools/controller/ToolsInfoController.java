package com.ideal.tools.controller;


import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.common.util.spring.MessageUtil;
import com.ideal.system.common.component.aop.MethodPermission;
import com.ideal.tools.common.Constants;
import com.ideal.tools.common.ExceptionDumper;
import com.ideal.tools.config.BusinessConfig;
import com.ideal.tools.core.web.BaseController;
import com.ideal.tools.dto.ToolsAgentResultApiDto;
import com.ideal.tools.dto.ToolsAgentResultQueryApiDto;
import com.ideal.tools.exception.ToolsException;
import com.ideal.tools.model.bean.ToolsInfoScriptBean;
import com.ideal.tools.model.constant.ToolsMoveConstants;
import com.ideal.tools.model.dto.*;
import com.ideal.tools.model.enums.ConstantsEnum;
import com.ideal.tools.model.enums.ToolsEnvEnum;
import com.ideal.tools.model.enums.ToolsOperateStatusEnum;
import com.ideal.tools.model.enums.ToolsTypeEnum;
import com.ideal.tools.model.interaction.ScriptContentDto;
import com.ideal.tools.model.interaction.UserInfoSearchDto;
import com.ideal.tools.service.IToolConfigurationxService;
import com.ideal.tools.service.IToolsInfoService;
import com.ideal.tools.service.IToolsOperateService;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * 工具箱信息Controller
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
@RestController
@RequestMapping("${app.script-tools-url:}/tools/info")
public class ToolsInfoController extends BaseController {


    private final IToolsInfoService toolsInfoService;
    private final IToolsOperateService toolsOperateService;
    private final IToolConfigurationxService toolConfigurationxService;
    private final BusinessConfig businessConfig;

    public ToolsInfoController(IToolsInfoService toolsInfoService, IToolsOperateService toolsOperateService,
                               IToolConfigurationxService toolConfigurationxService, BusinessConfig businessConfig) {
        this.toolsInfoService = toolsInfoService;
        this.toolsOperateService = toolsOperateService;
        this.toolConfigurationxService = toolConfigurationxService;
        this.businessConfig = businessConfig;
    }

    /**
     * 查询工具箱信息列表
     *
     * @param tableQueryDto 查询条件
     * @return 查询结果
     */
    @PostMapping("/list")
    public R<PageInfo<ToolsInfoResultDto>> list(@RequestBody TableQueryDto<ToolsQueryDto> tableQueryDto) {

        PageInfo<ToolsInfoResultDto> pages = null;
        try {
            pages = toolsInfoService.selectToolsInfoCategoryList(
                    tableQueryDto.getQueryParam(),
                    tableQueryDto.getPageNum(),
                    tableQueryDto.getPageSize()
            );
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, pages, ConstantsEnum.LIST_SUCCESS.getDesc());
        } catch (Exception e) {
            logger.error("Failed to query ToolsInfo custom categories !{}", e.getMessage(), e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, pages, ConstantsEnum.LIST_FAIL.getDesc());
        }
    }

    /**
     * 查询工具箱信息详细信息
     *
     * @param toolsAuditDto 数据唯一标识
     * @return 查询结果
     */
    @PostMapping(value = "/get")
    @MethodPermission("@dp.hasBtnPermission('actionToolRun') or @dp.hasBtnPermission('detailTool') or @dp.hasBtnPermission('editTool')")
    public R<ToolsDto> getToolsCombinedInfo(@RequestBody ToolsAuditDto toolsAuditDto) {
        ToolsDto dto = null;
        try {
            dto = toolsInfoService.getToolsCombinedInfo(toolsAuditDto);
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, dto, ConstantsEnum.LIST_SUCCESS.getDesc());
        } catch (Exception e) {
            logger.error("query getAgentInfoInfo  by id is error", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, dto, ConstantsEnum.LIST_FAIL.getDesc());
        }
    }

    /**
     * 新增保存工具箱信息
     *
     * @param toolsInfoDto 添加数据
     * @return 添加结果
     */
    @PostMapping("/save")
    @MethodPermission("@dp.hasBtnPermission('editTool')")
    public R<Object> save(HttpServletResponse response, @RequestBody ToolsDto toolsInfoDto, BindingResult bindingResult) {


        try {
            ToolsProjectInfoDto toolsProjectInfoDto = toolsOperateService.saveCommitToolsInfo(response, toolsInfoDto, getUser(), 1);
            //光大发布迁移导入,提交的返回，描述不提示，生产环境不提示
            if (businessConfig.isToolEvnMainSwitch() &&
                    toolsInfoDto.getOperateStatus().equals(ToolsOperateStatusEnum.OPERATE_STATUS_SUBMIT.getCode()) &&
                    !ToolsEnvEnum.PRO_ENV.getDesc().equals(businessConfig.getToolsEvn()) &&
                    !ToolsTypeEnum.TYPE_DESCRIPTION.getCode().equals(toolsInfoDto.getType())) {
                //获取需要导出到的环境，qa可以选择生产还是验证
                Integer env = toolsInfoDto.getEnv() == null ? ToolsEnvEnum.getFromDesc(businessConfig.getToolsEvn()).getCode() : toolsInfoDto.getEnv();
                MoveResultDto tipDetails = getTipDetails(env, businessConfig.getToolsEvn());
                return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, toolsProjectInfoDto, tipDetails.toString());
            }
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, toolsProjectInfoDto, ConstantsEnum.SAVE_SUCCESS.getDesc());
        } catch (Throwable e) {
            // 使用 ExceptionDumper.dump 获取异常链中的所有异常
            Throwable throwable1 = ExceptionDumper.get();
            // 初始化错误消息
            String errMsg = "";
            // 如果没有找到 StudioException，使用默认的错误消息
            if (throwable1 != null) {
                errMsg = MessageUtil.message(throwable1.getMessage());
            } else {
                errMsg = MessageUtil.message(e.getMessage());
            }
            // 返回结果
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, "", errMsg);
        }

    }

    /**
     * 查询应用系统下拉列表
     */
    @GetMapping("/system")
    @MethodPermission("@dp.hasBtnPermission('scene-tools') or @dp.hasBtnPermission('execution-history') " +
            "or @dp.hasBtnPermission('tool-monitoring') or @dp.hasBtnPermission('tool-run') " +
            "or @dp.hasBtnPermission('tool-execution')")
    public R<List<SystemPullDto>> getSystemList() {
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, toolsInfoService.selectToolsInfoCategoryList(getUser()), "查询成功");
    }


    /**
     * 查询所有脚本列表信息 下拉
     *
     * @return 查询结果
     */
    @GetMapping("/scriptList")
    @MethodPermission("@dp.hasBtnPermission('detailTool') or @dp.hasBtnPermission('editTool') or @dp.hasBtnPermission('scriptTool')")
    public R<List<ScriptPullDto>> scriptList() {

        List<ScriptPullDto> scriptPullList = null;
        try {
            scriptPullList = toolsInfoService.scriptPullList(getUser(), "");
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, scriptPullList, ConstantsEnum.LIST_SUCCESS.getDesc());
        } catch (Exception e) {
            logger.error("Failed to query Toolbox custom categories !{}", e.getMessage(), e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, scriptPullList, ConstantsEnum.LIST_FAIL.getDesc());
        }
    }


    /**
     * 获取脚本详情
     *
     * @return 查询结果
     */
    @GetMapping("/getScript")
    @MethodPermission("@dp.hasBtnPermission('scriptTool')")
    public R<ScriptContentDto> getScript(@RequestParam(value = "scriptIds") String scriptIds) {

        ScriptContentDto scriptContentDto = null;
        try {
            scriptContentDto = toolsInfoService.selectScriptContentInfo(getUser(), scriptIds);
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, scriptContentDto, ConstantsEnum.LIST_SUCCESS.getDesc());
        } catch (Exception e) {
            logger.error("Failed to query Toolbox custom categories !{}", e.getMessage(), e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, scriptContentDto, ConstantsEnum.LIST_FAIL.getDesc());
        }
    }


    /**
     * 修改一线工具描述
     *
     * @param toolsDescribeDto 添加数据
     * @return 添加结果
     */
    @PostMapping("/updateToolsFirstDescribe")
    @MethodPermission("@dp.hasBtnPermission('descriptionTool')")
    public R<Object> updateToolsFirstDescribe(@RequestBody ToolsDescribeDto toolsDescribeDto, BindingResult bindingResult) {


        try {

            toolsInfoService.updateToolsFirstDescribe(toolsDescribeDto, getUser());
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, "", ConstantsEnum.SAVE_SUCCESS.getDesc());
        } catch (Exception e) {
            logger.error("New updateToolsFirstDescribe customization failed!", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, "", e.getMessage());
        }

    }

    /**
     * 工具交付
     *
     * @param toolsDescribeDto 添加数据
     * @return 添加结果
     */
    @PostMapping("/updateToolsDelivery")
    public R<Object> updateToolsDelivery(@RequestBody ToolsDeliveryDto toolsDescribeDto, BindingResult bindingResult) {

        try {

            toolsOperateService.saveDeliveryTools(toolsDescribeDto, getUser());
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, "", ConstantsEnum.SAVE_SUCCESS.getDesc());
        } catch (ToolsException e) {

            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, "", e.getMessage());
        } catch (Exception e) {
            logger.error("New updateToolsDelivery customization failed!", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, "", e.getMessage());
        }

    }

    /**
     * 逻辑删除工具
     *
     * @param ids 主键列表
     * @return 删除结果
     */
    @PostMapping("/remove")
    @MethodPermission("@dp.hasBtnPermission('tool-execution')")
    public R<Object> remove(@RequestBody Long[] ids) {

        try {
            return toolsInfoService.logicallyDeleteTools(ids);
        } catch (ToolsException e) {

            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, e.getMessage());
        }
    }

    /**
     * 获取工具状态
     *
     * @return 查询结果
     */
    @GetMapping(value = "/toolsStatus")
    @MethodPermission("@dp.hasBtnPermission('tool-execution')")
    public R<Object> getToolsStatusList() {

        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, toolsInfoService.selectToolsStatus(), ConstantsEnum.LIST_SUCCESS.getDesc());
    }

    /**
     * 查询用户分页信息列表
     *
     * @param tableQueryDto 用户查询入参
     * @return 返回用户集合带分页
     */
    @PostMapping("/getUserInfoPageList")
    @MethodPermission("@dp.hasBtnPermission('editCreater')")
    public R<PageInfo<UserInfoDto>> getUserInfoPageList(@RequestBody TableQueryDto<UserInfoSearchDto> tableQueryDto){
        PageInfo<UserInfoDto> pages =null;
        try{
            pages = toolsInfoService.getUserInfoPageList(
                    tableQueryDto.getQueryParam(),
                    tableQueryDto.getPageNum(),
                    tableQueryDto.getPageSize()
            );
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, pages, ConstantsEnum.LIST_SUCCESS.getDesc());
        } catch (Exception e) {
            logger.error("Failed to query userInfo page list!{}", e.getMessage(), e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, pages, ConstantsEnum.LIST_FAIL.getDesc());
        }
    }

    /**
     * 修改工具创建人
     *
     * @param toolsCreateUserDto 工具创建人实体
     * @return 返回更新结果
     */
    @PostMapping("/updateCreateUser")
    public R<Object> updateCreateUser(@RequestBody ToolsCreateUserDto toolsCreateUserDto, BindingResult bindingResult) {
        try {
            toolsInfoService.updateCreateUser(toolsCreateUserDto, getUser());
        } catch (Exception e) {
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, "", "update.error");
        }
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, "", "update.success");
    }

    /**
     * 根据当前用户查询是否是审批人           沒用
     * @param toolId 工具id
     * @return boolean 返回结果
     */
    @GetMapping("/isAuditUser")
    public boolean isAuditUser(@RequestParam(value = "toolId") Long toolId) {
        return toolsInfoService.isAuditUser(toolId, getUser());
    }

    /**
     * 工具撤回
     *
     * @param ids 工具主键集合
     * @return 撤回结果
     */
    @PostMapping("/toolsWithDraw")
    public R<Object> toolsWithDraw(@RequestBody Long[] ids) {
        try {
            return toolsInfoService.toolsWithDraw(ids, getUser());
        } catch (ToolsException e) {
            // 处理返回失败响应
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, e.getMessage());
        }
    }

    /**
     * 校验工具撤回
     *
     * @param ids 工具主键集合
     * @return 撤回结果
     */
    @PostMapping("/checkWithDraw")
    @MethodPermission("@dp.hasBtnPermission('toolWithdrawal')")
    public R<Object> checkWithDraw(@RequestBody Long[] ids) {
        try {
            return toolsInfoService.checkWithDraw(ids, getUser());
        } catch (ToolsException e) {
            // 处理返回失败响应
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, e.getMessage());
        }
    }

    /**
     * 校验工具下线
     *
     * @param ids 工具主键集合
     * @return 结果
     */
    @PostMapping("/checkDownLine")
    @MethodPermission("@dp.hasBtnPermission('toolOffline')")
    public R<Object> checkDownLine(@RequestBody Long[] ids) {
        try {
            return toolsInfoService.checkDownLine(ids, getUser());
        } catch (ToolsException e) {
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, e.getMessage());
        }
    }

    /**
     * 工具下线
     *
     * @param downLineToolsSaveDto 工具下线实体
     * @return 下线结果
     */
    @PostMapping("/toolsDownLine")
    public R<Object> toolsDownLine(@RequestBody DownLineToolsSaveDto downLineToolsSaveDto) {
        try {
            return toolsInfoService.toolsDownLine(downLineToolsSaveDto, getUser());
        } catch (ToolsException e) {
            // 处理返回失败响应
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, e.getMessage());
        }
    }

    /**
     * 查询用户分页信息列表               沒用
     * @param toolId 工具id
     * @return 返回用户集合带分页
     */
    @GetMapping(value = "/getToolsInfoEntityChildNodeList")
    public R<List<ToolsInfoScriptBean>> getToolsInfoEntityChildNodeList(@RequestParam(value = "toolId") Long toolId) {
        List<ToolsInfoScriptBean> dtoList = null;
        try {
            dtoList = toolsInfoService.toolsInfoEntityChildNodeList(
                    toolId,
                    getUser()
            );
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, dtoList, ConstantsEnum.LIST_SUCCESS.getDesc());
        } catch (Exception e) {
            logger.error("Failed to query userInfo page list!{}", e.getMessage(), e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, dtoList, ConstantsEnum.LIST_FAIL.getDesc());
        }
    }


    /**
     * 获取工具agent信息
     *
     * @param tableQueryDto 脚本信息查询条件
     * @return 返回绑定或未绑定agent信息
     */
    @PostMapping("/selectToolAgentLists")
    public R<PageInfo<ToolsAgentResultApiDto>> selectToolAgentLists(@RequestBody TableQueryDto<ToolsAgentResultQueryApiDto> tableQueryDto) {
        PageInfo<ToolsAgentResultApiDto> pages = null;
        try {
            pages = toolConfigurationxService.selectToolAgentLists(tableQueryDto.getQueryParam(), tableQueryDto.getPageNum(), tableQueryDto.getPageSize());
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, pages, ConstantsEnum.LIST_SUCCESS.getDesc());
        } catch (Exception e) {
            logger.error("Failed to query ToolsInfo agentList !{}", e.getMessage(), e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, pages, ConstantsEnum.LIST_FAIL.getDesc());
        }
    }


    /**
     * 各环境导出迁移工具提示信息
     *
     * @param evn      导出环境
     * @param toolsEvn 当前环境
     */
    public MoveResultDto getTipDetails(Integer evn, String toolsEvn) throws ToolsException {
        // 根据环境代码获取当前环境类型枚举
        ToolsEnvEnum envEnum = ToolsEnvEnum.getFromCode(evn);
        // 环境提示信息
        String tipEvn;
        // 环境校验
        switch (envEnum) {
            case DEV_ENV:
                tipEvn = ToolsEnvEnum.QA_ENV.getDesc();
                break;
            case QA_ENV:
                tipEvn = ToolsEnvEnum.PRE_ENV.getDesc();
                break;
            case PRE_ENV:
                if (evn.equals(ToolsEnvEnum.getCodeByDesc(toolsEvn))) {
                    tipEvn = ToolsEnvEnum.PRO_ENV.getDesc();
                } else {
                    tipEvn = ToolsEnvEnum.PRE_ENV.getDesc();
                }
                break;
            case PRO_ENV:
                tipEvn = ToolsEnvEnum.PRO_ENV.getDesc();
                break;
            default:
                throw new ToolsException("当前环境不匹配,请检查环境配置信息! 当前环境: " + envEnum);
        }

        // 创建结果对象并设置提示信息
        MoveResultDto moveResultDto = new MoveResultDto();
        moveResultDto.setTitle(toolsEvn + ToolsMoveConstants.TOOLS_MOVE_TIPS);
        String tipDetails = String.format(ToolsMoveConstants.TOOLS_MOVE_TIP_DETAILS, tipEvn);
        moveResultDto.setTipDetails(tipDetails);

        return moveResultDto;
    }
}

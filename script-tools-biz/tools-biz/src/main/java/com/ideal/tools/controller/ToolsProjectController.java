package com.ideal.tools.controller;


import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.system.common.component.aop.MethodPermission;
import com.ideal.tools.common.Constants;
import com.ideal.tools.core.web.BaseController;

import com.ideal.tools.model.dto.ToolsProjectInfoDto;
import com.ideal.tools.model.enums.ConstantsEnum;
import org.springframework.web.bind.annotation.*;
import com.ideal.tools.model.dto.ToolsProjectDto;
import com.ideal.tools.model.dto.ToolsProjectQueryDto;
import com.ideal.tools.service.IToolsProjectService;

import java.util.List;


/**
 * 组合工具工程工作流Controller
 *
 * <AUTHOR>
 * @date 2024-07-19
 */
@RestController
@RequestMapping("${app.script-tools-url:}/tools/project")
@MethodPermission("@dp.hasBtnPermission('tool-execution')")
public class ToolsProjectController  extends BaseController {


    private final IToolsProjectService toolsProjectService;

    public ToolsProjectController(IToolsProjectService toolsProjectService) {
        this.toolsProjectService = toolsProjectService;
    }

    /**
     * 查询组合工具工程工作流列表            沒用
     *
     * @param tableQueryDto 查询条件
     * @return 查询结果
     */
    @GetMapping("/list")
    public R<PageInfo<ToolsProjectDto>> list(TableQueryDto<ToolsProjectQueryDto> tableQueryDto) {
        PageInfo<ToolsProjectDto> list = toolsProjectService.selectToolsProjectList(
                tableQueryDto.getQueryParam(),
                tableQueryDto.getPageNum(),
                tableQueryDto.getPageSize()
        );
        return R.ok(list);
    }
    /**
     * 组合工具 流程 下拉
     * @return 查询结果
     */
    @GetMapping("/projectList")
    public R<List<ToolsProjectInfoDto>> projectList(@RequestParam(value = "sysId")Long sysId) {

        List<ToolsProjectInfoDto> scriptPullList =null;
        try{
            scriptPullList = toolsProjectService.getStudioProjectList(sysId,getUser());
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, scriptPullList, ConstantsEnum.LIST_SUCCESS.getDesc());
        }catch (Exception e){
            logger.error("Failed to query Project custom categories !{}",e.getMessage(), e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE,scriptPullList , ConstantsEnum.LIST_FAIL.getDesc());
        }
    }
    /**
     * 查询组合工具工程工作流详细信息          沒用
     *
     * @param id 数据唯一标识
     * @return 查询结果
     */
    @GetMapping(value = "/get")
    public R<ToolsProjectDto> getAgentInfoInfo(@RequestParam(value = "id")Long id) {
        return R.ok(toolsProjectService.selectToolsProjectById(id));
    }

    /**
     * 新增保存组合工具工程工作流        沒用
     *
     * @param toolsProjectDto 添加数据
     * @return 添加结果
     */
    @PostMapping("/save")
    public R<Void> save(@RequestBody ToolsProjectDto toolsProjectDto) {
        if (toolsProjectService.insertToolsProject(toolsProjectDto) > 0) {
            return R.ok();
        }
        return R.fail();
    }

    /**
     * 修改保存组合工具工程工作流            沒用
     *
     * @param toolsProjectDto 更新数据
     * @return 更新结果
     */
    @PostMapping("/update")
    public R<Void> update(@RequestBody ToolsProjectDto toolsProjectDto) {
        toolsProjectService.updateToolsProject(toolsProjectDto);
        return R.ok();
    }


    /**
     * 删除组合工具工程工作流              沒用
     *
     * @param ids 主键列表
     * @return 删除结果
     */
    @PostMapping("/remove")
    public R<Void> remove(@RequestParam(value = "ids") Long[] ids) {
        toolsProjectService.deleteToolsProjectByIds(ids);
        return R.ok();
    }
}

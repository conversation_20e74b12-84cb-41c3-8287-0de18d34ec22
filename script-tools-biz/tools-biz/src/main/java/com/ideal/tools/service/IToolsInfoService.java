package com.ideal.tools.service;

import com.ideal.common.dto.R;
import com.ideal.system.common.component.model.CurrentUser;
import com.ideal.tools.exception.ToolsException;
import com.ideal.tools.model.bean.ToolsInfoScriptBean;
import com.ideal.tools.model.dto.*;

import com.github.pagehelper.PageInfo;
import com.ideal.tools.model.entity.ToolsInfoCategoryEntity;
import com.ideal.tools.model.entity.ToolsInfoEntity;
import com.ideal.tools.model.interaction.ScriptContentDto;
import com.ideal.tools.model.interaction.UserInfoSearchDto;

import java.util.HashMap;
import java.util.List;

/**
 * 工具箱信息Service接口
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
public interface IToolsInfoService {
    /**
     * 查询工具箱信息
     *
     * @param id 工具箱信息主键
     * @return 工具箱信息
     */
    ToolsInfoDto selectToolsInfoById(Long id);

    /**
     * 查询工具箱信息列表
     *
     * @param toolsInfoQueryDto 工具箱信息
     * @param pageNum 页码
     * @param pageSize 单页长度
     * @return 工具箱信息集合
     */
    PageInfo<ToolsInfoResultDto> selectToolsInfoListByUser(ToolsQueryDto toolsInfoQueryDto,String businessSystemCode, Long user,Integer pageNum, Integer pageSize);
    /**
     * 工具名称是否存在
     *
     * @param toolsDto 工具信息
     * @return true 重名   false  不重名
     */
    String selectToolsInfoList(ToolsDto toolsDto );
    /**
     * 查询工具箱信息列表
     *
     * @param toolsInfoQueryDto 工具箱信息
     * @param pageNum 页码
     * @param pageSize 单页长度
     * @return 工具箱信息集合
     */
    PageInfo<ToolsInfoResultDto> selectToolsInfoCategoryList(ToolsQueryDto toolsInfoQueryDto, Integer pageNum, Integer pageSize);
    /**
     * 新增工具箱信息
     *
     * @param toolsInfoDto 工具箱信息
     * @param userDto 用户信息
     * @return 结果
     */
    int insertToolsInfo(ToolsInfoDto toolsInfoDto, UserDto userDto);

    /**
     * 修改工具箱信息
     *
     * @param toolsInfoDto 工具箱信息
     * @return 结果
     */
    int updateToolsInfo(ToolsInfoDto toolsInfoDto);

    /**
     * 批量删除工具箱信息
     *
     * @param ids 需要删除的工具箱信息主键集合
     * @return 结果
     */
    int deleteToolsInfoByIds(Long[] ids);

    /**
     * 业务系统列表
     *
     * @param userDto 用户信息
     */
    List<SystemPullDto> selectToolsInfoCategoryList(UserDto userDto);


    /**
     * 获取脚本列表信息
     *
     * @param userDto 用户信息
     */
    List<ScriptPullDto> scriptPullList(UserDto userDto,String scriptInfo);



    /**
     * 单个脚本信息
     *
     * @param userDto 用户信息
     */
    ScriptContentDto  selectScriptContentInfo(UserDto userDto,
                                              String scriptId) ;
    /**
     * 查询工具箱信息分页数据根据工具ID集合
     *
     * @param ids 工具ID集合
     * @param name 工具名称
     * @param pageNum 页码
     * @param pageSize 单页长度
     * @return 工具箱信息分页数据
     */
    PageInfo<ToolsInfoResultDto> selectToolsInfoPageListByIdsAndName(Long[] ids,String name,Integer pageNum,Integer pageSize);

    /**
     * 查询工具箱信息列表根据工具ID集合（用于导出）
     *
     *
     * @param ids 工具ID集合
     * @param name 工具名称
     * @return 工具箱列表集合
     */
    List<ToolsInfoResultDto> selectToolsInfoListByIdsAndName(Long[] ids,String name);

    /**
     * 查询工具箱信息
     *
     * @param id 工具箱信息主键
     * @return 工具箱信息
     */
    ToolsDto getToolsCombinedInfo(Long id);
    /**
     * 双人复核工具箱详情
     *
     * @param toolsAuditDto 工具箱信息主键
     * @return 工具箱信息
     */
    ToolsDto getToolsCombinedInfo( ToolsAuditDto toolsAuditDto);
    /**
     *修改一线工具描述
     *
     * @param toolsDescribeDto 工具箱信息主键
     * @return 工具箱信息
     */
    int updateToolsFirstDescribe(ToolsDescribeDto toolsDescribeDto,UserDto userDto);
    /**
     * 工具交付
     *
     * @param toolsDeliveryDto 工具箱信息主键
     * @return 工具箱信息
     */
     void updateDeliveryTools(ToolsDeliveryDto toolsDeliveryDto,UserDto userDto)  throws ToolsException;

    /**
     * 查询工具箱信息全信息
     *
     * @param id 工具箱信息主键
     * @return 工具箱信息
     */
    ToolsInfoCategoryEntity selectToolsInfoCategoryById(Long id);



    /**
     * 逻辑删除
     *
     * @param ids 工具箱信息主键
     * @return 工具箱信息
     */
    R<Object> logicallyDeleteTools(Long[] ids)  throws ToolsException;
    /**
     * 根据业务系统ID获取工具信息
     *
     * @param businessSystemIds 业务系统ID
     * @return 工具箱信息
     */
    List<ToolsAlarmDto> selectToolsInfoListByBusinessSystemIds(List<Long> businessSystemIds);



    /**
     * 工具类状态下拉
     * @return 工具箱信息
     */
    List<ToolsStatusDto> selectToolsStatus();

    /**
     * @param code
     * @return
     */
    ToolsInfoEntity selectToolsByCode(String code);

    /**
     * 获取所有用户信息 -用于分页数据
     *
     * @param query 用户查询信息
     * @param pageNum 页码
     * @param pageSize 单页长度
     * @return 用户信息
     */
    PageInfo<UserInfoDto> getUserInfoPageList(UserInfoSearchDto query, Integer pageNum, Integer pageSize);
    /**
     * 修改工具创建人
     * @param toolsCreateUserDto 工具创建人实体
     * @param userDto 用户信息
     * @return 返回更新结果
     */
    int updateCreateUser(ToolsCreateUserDto toolsCreateUserDto,UserDto userDto);

    /**
     * 根据当前用户查询是否是审批人
     * @param toolId 工具id
     * @return boolean 返回结果
     */
    boolean isAuditUser(Long toolId, UserDto user);

    /**
     * 校验工具撤回
     * @param ids 工具主键集合
     * @return 返回撤回结果
     */
    R<Object> checkWithDraw(Long[] ids, UserDto user) throws ToolsException;

    /**
     * 工具撤回
     * @param ids 工具主键集合
     * @return 返回撤回结果
     */
    R<Object> toolsWithDraw(Long[] ids, UserDto user) throws ToolsException;

    /**
     * 校验工具下线
     * @param ids 工具主键集合
     * @return 结果
     */
    R<Object> checkDownLine(Long[] ids, UserDto user) throws ToolsException;

    /**
     * 工具下线
     * @param downLineToolsSaveDto 工具下线实体
     * @return 返回下线结果
     */
    R<Object> toolsDownLine(DownLineToolsSaveDto downLineToolsSaveDto, UserDto user) throws ToolsException;

    /**
     * 查询当前组合工具下调用的 子工程集合
     * @param toolId 工具id
     * @return boolean 返回结果
     */
    List<ToolsInfoScriptBean>   toolsInfoEntityChildNodeList(Long toolId, UserDto user);

    /**
     * 子工程 工具集合
     * @param childIds 子工程集合 工具字段
     * @return boolean 返回结果
     */
    List<ToolsInfoScriptBean>   toolsInfoEntityChildNodeList(String childIds, UserDto user);

    /**
     * 批量查询工具箱信息
     *
     * @param id 工具箱信息主键
     * @return 工具箱信息
     */
    HashMap<String, List<ToolsDto>> getToolsBatchCombinedInfo(List<Long> id, List<ToolsDto> errorScriptList, List<ToolsDto> errorComList,List<ToolsDto> errorDescList);
}

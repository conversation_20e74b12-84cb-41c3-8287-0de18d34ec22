package com.ideal.tools.common;

import java.util.Base64;

/**
 * Base64 编码解码
 *
 * <AUTHOR>
 */
public class BaseProcessingUtil {



    /**
     * 解密字段  lastLine  最后的线
     */
    public static final String LAST_LINE = "lastLine";
    /**
     * 解密字段  stdout
     */
    public static final String STD_OUT = "stdout";

    /**
     * 解密字段  stderr
     */
    public static final String STD_DERR = "stderr";

    public static final String RET = "ret";

    public static final String IS_TIMEOUT = "isTimeout";

    public static void main(String[] args) {
        System.out.println(getFromBASE64("6ZqL5Z2P5ouJ5LiJbWxrdmFzam5sYXN2c2FzZD9TQURWOj5FeyI+I0AkIj4hQCJFPiJCPkRWQ1ha"));
    }

    /**
     * base64解码
     * @param s
     * @return
     */
    public static String getFromBASE64 (String s)
    {
        if (s == null){
            return null;
        }
        Base64.Decoder decoder = Base64.getDecoder();
        try
        {
            byte[] b = decoder.decode(s);
            return new String(b);
        } catch (Exception e)
        {
            return null;
        }
    }
}

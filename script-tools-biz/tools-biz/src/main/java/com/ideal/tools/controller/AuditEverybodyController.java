package com.ideal.tools.controller;


import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.system.common.component.aop.MethodPermission;
import org.springframework.web.bind.annotation.*;
import com.ideal.tools.model.dto.AuditEverybodyDto;
import com.ideal.tools.model.dto.AuditEverybodyQueryDto;
import com.ideal.tools.service.IAuditEverybodyService;


/**
 * 双人复核审批人多个关系Controller
 *
 * <AUTHOR>
 * @date 2024-06-26
 */
@RestController
@RequestMapping("${app.script-tools-url:}/tools/audit/everybody")
@MethodPermission("@dp.hasBtnPermission('tool-execution')")
public class AuditEverybodyController {


    private final IAuditEverybodyService auditEverybodyService;

    public AuditEverybodyController(IAuditEverybodyService auditEverybodyService) {
        this.auditEverybodyService = auditEverybodyService;
    }

    /**
     * 查询双人复核审批人多个关系列表    沒用
     *
     * @param tableQueryDto 查询条件
     * @return 查询结果
     */
    @GetMapping("/list")
    public R<PageInfo<AuditEverybodyDto>> list(TableQueryDto<AuditEverybodyQueryDto> tableQueryDto) {
        PageInfo<AuditEverybodyDto> list = auditEverybodyService.selectAuditEverybodyList(
                tableQueryDto.getQueryParam(),
                tableQueryDto.getPageNum(),
                tableQueryDto.getPageSize()
        );
        return R.ok(list);
    }

    /**
     * 查询双人复核审批人多个关系详细信息  沒用
     *
     * @param id 数据唯一标识
     * @return 查询结果
     */
    @GetMapping(value = "/get")
    public R<AuditEverybodyDto> getAgentInfoInfo(@RequestParam(value = "id")Long id) {
        return R.ok(auditEverybodyService.selectAuditEverybodyById(id));
    }

    /**
     * 新增保存双人复核审批人多个关系      沒用
     *
     * @param auditEverybodyDto 添加数据
     * @return 添加结果
     */
    @PostMapping("/save")
    public R<Void> save(@RequestBody AuditEverybodyDto auditEverybodyDto) {
        if (auditEverybodyService.insertAuditEverybody(auditEverybodyDto) > 0) {
            return R.ok();
        }
        return R.fail();
    }

    /**
     * 修改保存双人复核审批人多个关系      沒用
     *
     * @param auditEverybodyDto 更新数据
     * @return 更新结果
     */
    @PostMapping("/update")
    public R<Void> update(@RequestBody AuditEverybodyDto auditEverybodyDto) {
        auditEverybodyService.updateAuditEverybody(auditEverybodyDto);
        return R.ok();
    }


    /**
     * 删除双人复核审批人多个关系        沒用
     *
     * @param ids 主键列表
     * @return 删除结果
     */
    @PostMapping("/remove")
    public R<Void> remove(@RequestParam(value = "ids") Long[] ids) {
        auditEverybodyService.deleteAuditEverybodyByIds(ids);
        return R.ok();
    }
}

package com.ideal.tools.controller;


import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.system.common.component.aop.MethodPermission;
import com.ideal.tools.common.Constants;
import com.ideal.tools.common.ControlsTimeUtil;
import com.ideal.tools.common.EasyExcelUtil;
import com.ideal.tools.core.web.BaseController;
import com.ideal.tools.model.bean.OperaMaintenReportExcle;
import com.ideal.tools.model.dto.OperaAndMaintenResultDto;
import com.ideal.tools.model.dto.OperaMaintenReportQueryDto;
import com.ideal.tools.model.dto.UserDto;
import com.ideal.tools.model.enums.ConstantsEnum;
import com.ideal.tools.service.IOperaMaintenReportService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;


/**
 * 运维报表自定义分类Controller
 *
 * <AUTHOR>
 * @date 2024-07-11
 */
@RestController
@RequestMapping("${app.script-tools-url:}/tools/operaMaintenReport")
@MethodPermission("@dp.hasBtnPermission('statistical-report')")
public class OperaMaintenReportController extends BaseController {

    private final IOperaMaintenReportService operaMaintenReportService;

    public OperaMaintenReportController(IOperaMaintenReportService operaMaintenReportService) {
        this.operaMaintenReportService = operaMaintenReportService;
    }

    /**
     * 查询运维报表自定义分类列表
     *
     * @param tableQueryDto 查询条件
     * @return 查询结果
     */
    @PostMapping("/list")
    public R<PageInfo<OperaAndMaintenResultDto>> list(@RequestBody TableQueryDto<OperaMaintenReportQueryDto> tableQueryDto) {
        PageInfo<OperaAndMaintenResultDto> pages =null;
        try{
            UserDto user = getUser();
            pages = operaMaintenReportService.selectOperaMaintenReportList(
                    tableQueryDto.getQueryParam(),
                    tableQueryDto.getPageNum(),
                    tableQueryDto.getPageSize(),user
            );
            logger.info("success to query Toolbox operaMainten reports ");
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, pages, ConstantsEnum.LIST_SUCCESS.getDesc());
        }catch (Exception e){
            logger.error("Failed to query Toolbox operaMainten reports !{}",e.getMessage(), e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE,pages , ConstantsEnum.LIST_FAIL.getDesc());
        }
    }
    

    /**
     * 运维报表下载，最多下载3000行
     *
     * @param queryDto 下载条件
     * @return 查询结果
     */
    @PostMapping("/exportOperaMaintenReportData")
    public void exportOperaMaintenReportData(HttpServletResponse response, @RequestBody OperaMaintenReportQueryDto queryDto)  {
        try{
            List<OperaMaintenReportExcle> list = operaMaintenReportService.exportOperaMaintenReportData(queryDto,getUser());
            // 使用 EasyExcel 将数据写入响应输出流
            EasyExcelUtil.writeExcel(response, list, "月统计报表" + ControlsTimeUtil.formatDateByFormat(new Date(), "YYYY-MM"), "月统计报表报表1", OperaMaintenReportExcle.class);
            logger.info("exportoperaMaintenReportData success");
        } catch (Exception e) {
            logger.error("exportoperaMaintenReportData error", e);
        }
    }

}


package com.ideal.tools.service.impl;

import com.ideal.common.util.BeanUtils;
import com.ideal.tools.common.ExceptionDumper;
import com.ideal.tools.config.BusinessConfig;
import com.ideal.tools.exception.ToolsException;
import com.ideal.tools.mapper.ToolsInfoMapper;
import com.ideal.tools.mapper.ToolsProjectMapper;
import com.ideal.tools.model.dto.*;
import com.ideal.tools.model.entity.ToolsInfoEntity;
import com.ideal.tools.model.entity.ToolsProjectEntity;
import com.ideal.tools.model.enums.ToolsDeliveryStatusEnum;
import com.ideal.tools.model.enums.ToolsEnvEnum;
import com.ideal.tools.model.enums.ToolsStatusEnum;
import com.ideal.tools.model.interaction.ScriptContentDto;
import com.ideal.tools.service.*;
import com.ideal.tools.service.producter.ICommonToolsAgentInfoService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 工具箱操作类
 * <AUTHOR>
 */
@Service
public class ToolsOperateBaseServiceImpl implements IToolsOperateBaseService {
    private static final Logger logger = LoggerFactory.getLogger(ToolsOperateBaseServiceImpl.class);
    private final IToolsFilesService toolsFilesService;
    private final ToolsInfoMapper toolsInfoMapper;
    private final ToolsProjectMapper toolsProjectMapper;
    private final IToolsParamService toolsParamService;
    private final IToolsInfoService toolsInfoService;
    private final IToolsAgentInfoService toolsAgentInfoService;
    private final ICommonToolsAgentInfoService commonToolsAgentInfoService;
    private final BusinessConfig businessConfig;

    public ToolsOperateBaseServiceImpl(ToolsInfoMapper toolsInfoMapper,IToolsFilesService toolsFilesService,
                                       IToolsParamService toolsParamService,IToolsAgentInfoService toolsAgentInfoService,IToolsInfoService toolsInfoService,
                                       ICommonToolsAgentInfoService commonToolsAgentInfoService,ToolsProjectMapper toolsProjectMapper,BusinessConfig businessConfig) {
        this.toolsInfoMapper = toolsInfoMapper;
        this.toolsFilesService=toolsFilesService;
        this.toolsParamService=toolsParamService;
        this.toolsAgentInfoService=toolsAgentInfoService;
        this.commonToolsAgentInfoService=commonToolsAgentInfoService;
        this.toolsInfoService=toolsInfoService;
        this.toolsProjectMapper=toolsProjectMapper;
        this.businessConfig=businessConfig;

    }
    /**
     * 工具参数检查
     * @param toolsDto 前台传递实体安参数
     * @param userDto 当前登录用户
     * @return 错误信息，空时标识通过
     */
    @Override
    public String checkParams(ToolsDto toolsDto, UserDto userDto) {
        if (userDto==null){

            return "checkParams userDto is null";
        }
        if(StringUtils.isBlank(userDto.getUserName())){
            return "checkParams login user name is null";
        }
        if(userDto.getUserId() == null){
            return "checkParams login user name is null";
        }
        if (toolsDto==null){

            return "checkParams ToolsDto is null";
        }
        if (toolsDto.getName().length()>40){

            return "checkParams ToolsName greater than 40";
        }
        return checkParamsUpdate(toolsDto);
    }

    /**
     * 工具主体参数验证
     * @param toolsDto 前台传递实体安参数
     * @return 错误信息，空时标识通过
     */

    public String checkParamsUpdate(ToolsDto toolsDto){

        if(toolsDto.getType()==null){
            return "checkParams tools Type is null!";
        }
        if(StringUtils.isBlank(toolsDto.getName())){
            return "checkParams tools Name is null!";
        }



        if(toolsDto.getBusinessSystemId()==null||StringUtils.isBlank(toolsDto.getBusinessSystemName())){
            return "checkParams tools The application system cannot be empty!";
        }

        if(toolsDto.getHighRisk()==null){
            return "checkParams tools High Risk cannot be empty!";
        }
        if (!businessConfig.isToolEvnMainSwitch()){//主线逻辑不变
            if(toolsDto.getAuditEverybodyDtoList()==null||toolsDto.getAuditEverybodyDtoList().isEmpty()){
                return "checkParams tools approval is empty!";
            }
        }else{
            if( ToolsEnvEnum.PRO_ENV.getDesc().equals(businessConfig.getToolsEvn())){//光大走向，生产校验审批人
                if(toolsDto.getAuditEverybodyDtoList()==null||toolsDto.getAuditEverybodyDtoList().isEmpty()){
                    return "checkParams tools approval is empty!";
                }
            }
        }

        return toolsInfoService.selectToolsInfoList(toolsDto);
    }
    /**
     * 保存工具基本信息
     * @param toolsDto 前台传递实体安参数
     * @param userDto 当前登录用户
     * @return 工具id
     */
    @Transactional(rollbackFor = {ToolsException.class })
    @Override
    public Long saveToolsBaseInfo(ToolsDto toolsDto, UserDto userDto) throws ToolsException {
        Long toolsId;
        Integer status = toolsDto.getStatus();
        try{
            ToolsInfoEntity toolsInfo = getToolsInfo(toolsDto);
            if(status == null){  //新增如果是空的化为草稿状态
                status = toolsInfo.getStatus();
            }
            if (businessConfig.isToolEvnMainSwitch()){//光大走向，主线逻辑不变
                //校验光大开关，dev:待审批、待测试、待验证  qa、pre、pro：所有状态不能变
                String currentEvn = businessConfig.getToolsEvn();
                //修改工具状态
                toolsInfo.setId(toolsDto.getId());
                //根据环境判断
                if (ToolsEnvEnum.DEV_ENV.getDesc().equals(currentEvn)){
                    //判断是否是待审批，待测试，待验证状态不变
                    if (status.equals(ToolsStatusEnum.STATUS_AUDIT.getCode())
                            ||status.equals(ToolsStatusEnum.STATUS_UNTESTED.getCode())
                            ||status.equals(ToolsStatusEnum.STATUS_UNVERIFIED.getCode())){
                        toolsInfo.setStatus(status);
                    }
                }else if (ToolsEnvEnum.QA_ENV.getDesc().equals(currentEvn)){//所有状态不能变
                    toolsInfo.setStatus(status);
                }else if (ToolsEnvEnum.PRE_ENV.getDesc().equals(currentEvn)){//所有状态不能变
                    toolsInfo.setStatus(status);
                }else if (ToolsEnvEnum.PRO_ENV.getDesc().equals(currentEvn)){//所有状态不能变
                    toolsInfo.setStatus(status);
                }
            }
            //组织用户信息
            toolsInfo.setCreatorName(userDto.getUserName());
            toolsInfo.setCreatorId(userDto.getUserId());
            toolsInfoMapper.insertToolsInfo(toolsInfo);
            toolsId = toolsInfo.getId();
            String taskMessage="insert Tools："+toolsId;
            logger.info(taskMessage);
        }catch (Exception e){
            throw new ToolsException(e);
        }

        return toolsId;
    }

    /**
     * 工具箱组织基础工具数据
     * @param toolsDto 工具箱创建基础信息
     * @return  组合后信息
     */
    private ToolsInfoEntity  getToolsInfo(ToolsDto toolsDto){
        ToolsInfoEntity toolsInfoEntity =  BeanUtils.copy(toolsDto, ToolsInfoEntity.class);

        if(toolsDto.getEstimateOperationalRisk()!=null){
            //组织转换 预估运行风险 复选框
            Long[] risk= toolsDto.getEstimateOperationalRisk();
            if(risk.length!=0){

            StringBuilder code= new StringBuilder();
            for(Long estimate: risk){
                if(estimate!=null){
                 code.append(estimate).append(",");
                }
            }
            code.deleteCharAt(code.lastIndexOf(","));
            String str = code.toString();
            toolsInfoEntity.setEstimateOperationalRisk(str);
            }
        }

        //草稿状态
        if(toolsDto.getId() == null || toolsDto.getId() <= 0){
            toolsDto.setStatus(ToolsStatusEnum.STATUS_DRAFT.getCode());
            //初始化草稿状态
            toolsInfoEntity.setStatus(toolsDto.getStatus());
            //初始化工具编码
            if(StringUtils.isBlank(toolsDto.getCode())){
                toolsDto.setCode("TOOLS_"+System.currentTimeMillis());
                toolsInfoEntity.setCode(toolsDto.getCode());

            }else {
                toolsInfoEntity.setCode(toolsDto.getCode());
            }

            //初始化未交付
            toolsInfoEntity.setDeliveryStatus(ToolsDeliveryStatusEnum.STATUS_NON_DELIVERY.getCode());
        }else {
            toolsDto.setStatus(ToolsStatusEnum.STATUS_MODIFIED.getCode());
            //修改 工具状态至成已修改
            toolsInfoEntity.setStatus( toolsDto.getStatus());
        }

        return toolsInfoEntity;
    }

    /**
     * 保存工具基本信息
     * @param toolsDto 前台传递实体安参数
     * @param userDto 当前登录用户
     * @return 工具id
     */
    @Transactional(rollbackFor = {ToolsException.class })
    @Override
    public Long updateToolsBaseInfo(ToolsDto toolsDto, UserDto userDto) throws ToolsException {
        Long toolsId;
        Integer status = toolsDto.getStatus();
        try{
            ToolsInfoEntity toolsInfo = getToolsInfo(toolsDto);
            //如果第一次新增的时候不存在，则状态为  草稿
            if (status==null){
                status = toolsInfo.getStatus();
            }
            if (businessConfig.isToolEvnMainSwitch()){//光大走向，主线逻辑不变
                //校验光大开关，dev:待审批、待测试、待验证  qa、pre、pro：所有状态不能变
                String currentEvn = businessConfig.getToolsEvn();
                //修改工具状态
                toolsInfo.setId(toolsDto.getId());
                //根据环境判断
                if (ToolsEnvEnum.DEV_ENV.getDesc().equals(currentEvn)){
                    //判断是否是待审批，待测试，待验证状态不变
                    if (status.equals(ToolsStatusEnum.STATUS_AUDIT.getCode())
                            ||status.equals(ToolsStatusEnum.STATUS_UNTESTED.getCode())
                            ||status.equals(ToolsStatusEnum.STATUS_UNVERIFIED.getCode())){
                        toolsInfo.setStatus(status);
                    }
                }else if (ToolsEnvEnum.QA_ENV.getDesc().equals(currentEvn)){//所有状态不能变
                    toolsInfo.setStatus(status);
                }else if (ToolsEnvEnum.PRE_ENV.getDesc().equals(currentEvn)){//所有状态不能变
                    toolsInfo.setStatus(status);
                }else if (ToolsEnvEnum.PRO_ENV.getDesc().equals(currentEvn)){//所有状态不能变
                    toolsInfo.setStatus(status);
                }
            }
            toolsInfo.setUpdatorId(userDto.getUserId());
            toolsInfo.setUpdatorName(userDto.getUserName());

            toolsInfoMapper.updateToolsInfo(toolsInfo);

            toolsId = toolsInfo.getId();
            String taskMessage="update Tools："+toolsId;
            logger.info(taskMessage);
        }catch (Exception e){
            ExceptionDumper.wrap(e);
            throw new ToolsException(e);
        }

        return toolsId;
    }


    /**
     * 保存描述工具 附件 如果空不添加
     * @param toolsDto 前台传递实体安参数
     * @param userDto 当前登录用户
     * @return 工具id
     */
    @Transactional(rollbackFor = {ToolsException.class })
    @Override
    public Long saveToolsFilesBaseInfo(Long toolsId,ToolsDto toolsDto, UserDto userDto) throws ToolsException {
        List <ToolsFilesQueryDto> filesList;
        //附近ids 如果不传不添加
        if(toolsDto.getDescribeFilesList()==null && toolsDto.getScriptFilesList()==null ){
            return null;
        }else {
            if(toolsDto.getDescribeFilesList()!=null){
                filesList=toolsDto.getDescribeFilesList();
            }else if(toolsDto.getScriptFilesList()!=null){
                filesList=toolsDto.getScriptFilesList();
            }else {
                return null;
            }
            try{
                List <ToolsFilesDto> toolsFilesList=getToolsFiles(toolsId,filesList);

                toolsFilesService.batchUpdateToolsFiles(toolsId,toolsFilesList);

                String taskMessage="insert Tools Files id："+toolsId;
                logger.info(taskMessage);
            }catch (Exception e){
                ExceptionDumper.wrap(e);
                throw new ToolsException(e);
            }

        }

        return toolsId;
    }

    /**
     * 工具箱组织基础工具附件 数据
     * @param toolsId 工具箱创建基础信息
     * @param filesList 附近结果集
     * @return  组合后信息
     */
    private List <ToolsFilesDto>  getToolsFiles(Long toolsId,
                                                List <ToolsFilesQueryDto> filesList){
        List <ToolsFilesDto> toolsFilesDto=new ArrayList<>();


       for (ToolsFilesQueryDto files:filesList){
           ToolsFilesDto toolsFiles=BeanUtils.copy(files, ToolsFilesDto.class);
           toolsFiles.setTdToolsId(toolsId);
           toolsFilesDto.add(toolsFiles);
       }

        return toolsFilesDto;
    }

    /**
     * 保存描述工具 参数 如果空不添加
     * @param toolsParamResultList 参数列表
     * @param userDto 当前登录用户
     * @return 工具id
     */
    @Transactional(rollbackFor = {ToolsException.class })
    @Override
    public void  saveToolsParamBaseInfo(Long toolsId, List<ToolsParamResultDto> toolsParamResultList, UserDto userDto) throws ToolsException{
        List<ToolsParamDto> auditEverybodyDtoList=BeanUtils.copy(toolsParamResultList,ToolsParamDto.class);
        toolsParamService.batchInsertToolsParam(toolsId, auditEverybodyDtoList,userDto);

    }

    /**
     * 保存Agent 设备
     * @param toolsParamResultList 参数列表
     * @param userDto 当前登录用户
     * @return 工具id
     */
    @Transactional(rollbackFor = {ToolsException.class })
    @Override
    public void  saveToolsAgentBaseInfo(Long toolsId, List<ToolsAgentResultDto> toolsParamResultList, UserDto userDto) throws ToolsException{
        List<ToolsAgentInfoDto> toolsAgentInfoList=BeanUtils.copy(toolsParamResultList,ToolsAgentInfoDto.class);
        toolsAgentInfoService.batchInsertToolsAgent(toolsId, toolsAgentInfoList);

    }


    /**
     * 删除工具下所有 脚本信息、脚本信息、添加附件
     *
     * @param toolsId 任务ID
     */
    @Transactional(rollbackFor = {ToolsException.class })
    @Override
    public void deleteToolsScriptAnnexedTable(Long toolsId) throws ToolsException{
        //脚本信息 参数
        toolsParamService.deleteParamToolId(toolsId);
        //脚本信息 设备
        toolsAgentInfoService.deleteAgentToolId(toolsId);

        String taskMessage="delete Tools Script Annexed Table ："+toolsId;
        logger.info(taskMessage);
    }
    /**
     * 发布迁移导入删除工具下所有 脚本信息、脚本信息
     *
     * @param toolsId 工具id
     */
    @Transactional(rollbackFor = {ToolsException.class })
    @Override
    public void deleteToolsScriptAnnexedTableForMove(Long toolsId) throws ToolsException {
        //dev,qa,验证-只删除参数，不删除设备
        //脚本信息 参数
        toolsParamService.deleteParamToolId(toolsId);
        String taskMessage="delete Tools Script Annexed Table ："+toolsId;
        logger.info(taskMessage);
    }

    /**
     * 删除多余附件
     *
     * @param toolsDto 工具信息
     */
    @Transactional(rollbackFor = {ToolsException.class })
    @Override
    public void deleteToolsFilesTable(ToolsDto toolsDto) throws ToolsException {


        ToolsFilesQueryDto filesDto= new  ToolsFilesQueryDto();
        filesDto.setTdToolsId(toolsDto.getId());
        List<ToolsFilesDto> toolsFilesDtoList=   toolsFilesService.selectToolsFilesList( filesDto);


        Long[]  ids=  getToolsFilesQueryDtoList(toolsDto.getScriptFilesList() ,toolsDto.getDescribeFilesList(),toolsFilesDtoList) ;

        if(ids.length==0){
            return;
        }
        toolsFilesService.deleteToolsFilesByIds(ids);
        String taskMessage="delete Tools Script Annexed Table ："+toolsDto.getId();
        logger.info(taskMessage);
    }

    /**
     * 合并附件集合
     * @param scriptFilesList 脚本附件
     * @param describeFilesList 描述附件
     * @param toolsFilesDtoList 之前的附件
     * @return ids 刪除的id
     */
    public Long[]  getToolsFilesQueryDtoList( List<ToolsFilesQueryDto> scriptFilesList,List<ToolsFilesQueryDto> describeFilesList,  List<ToolsFilesDto> toolsFilesDtoList) {

        Long[] ids = new Long[0];
        List<ToolsFilesQueryDto> toolsFilesList=BeanUtils.copy(toolsFilesDtoList,ToolsFilesQueryDto.class);

        if(toolsFilesDtoList==null){
            return ids;
        }

            List<ToolsFilesQueryDto> getScriptList =new ArrayList<>();
            //取差集删除
            if(scriptFilesList!=null){

                getScriptList = toolsFilesList.stream()
                        .filter(s1 -> !scriptFilesList.stream().anyMatch(s2 -> s2.getId().equals(s1.getId())))
                        .collect(Collectors.toList());

            }

            if(describeFilesList!=null){

                getScriptList = toolsFilesList.stream()
                        .filter(s1 -> !describeFilesList.stream().anyMatch(s2 -> s2.getId().equals(s1.getId())))
                        .collect(Collectors.toList());

            }


            if(!getScriptList.isEmpty()){
                ids= new Long[getScriptList.size()];
                int i=0;
                for (ToolsFilesQueryDto files:getScriptList){
                    ids[i]= files.getId();
                    i++;
                }
            }




        return ids;
    }

    /**
     * 验证该版本脚本是草稿 还是已发布
     *
     * @param scriptId 脚本uuid
     * @return 返回值false就新增脚本  true 就修改脚本
     *
     */
    @Override
    public ScriptContentDto verificationScript( String scriptId, UserDto userDto) {
        ScriptContentDto scriptContentDto = new ScriptContentDto();
        Boolean script=false;
        if(scriptId!=null&&!"".equals(scriptId)){
             scriptContentDto  = toolsInfoService.selectScriptContentInfo(userDto,scriptId);

            if(scriptContentDto.getVersionNumber()==null&&scriptContentDto.getScriptIds()==null){
                script=false;
            }else if(scriptContentDto.getVersionNumber()==null&&scriptContentDto.getScriptIds()!=null){
               script=true;
            }else {
                script =false;
            }


        }
        scriptContentDto.setScriptBoolean(script);
        return scriptContentDto;
    }

    /**
     * 保存工具基本信息
     * @param toolsInfo 前台传递实体安参数
     * @param userDto 当前登录用户
     * @return 工具id
     */
    @Transactional(rollbackFor = {ToolsException.class })
    @Override
    public Long updateToolsSatus(ToolsInfoEntity toolsInfo, UserDto userDto) throws ToolsException {
        Long toolsId;
        try{

            toolsInfo.setUpdatorId(userDto.getUserId());
            toolsInfo.setUpdatorName(userDto.getUserName());
            toolsInfoMapper.updateToolsInfo(toolsInfo);
            toolsId = toolsInfo.getId();
            String taskMessage="update Tools S："+toolsId;
            logger.info(taskMessage);
        }catch (Exception e){
            ExceptionDumper.wrap(e);
            throw new ToolsException(e);
        }

        return toolsId;
    }

    /**
     * 添加组合工具流程编排信息
     * @param toolsProjectInfoDto 前台传递实体安参数
     * @param userDto 当前登录用户
     * @return 工具id
     */
    @Transactional(rollbackFor = {ToolsException.class })
    @Override
    public Long insertUpdateToolsProject(Long toolsId,ToolsProjectInfoDto  toolsProjectInfoDto, UserDto userDto) throws ToolsException {

        Long toolsProjectId;
        try{
           ToolsProjectEntity toolsProject= BeanUtils.copy(toolsProjectInfoDto,ToolsProjectEntity.class);
            //组织用户信息
           toolsProject.setCreatorName(userDto.getUserName());
           toolsProject.setCreatorId(userDto.getUserId());
           toolsProject.setTdToolsId(toolsId);
            toolsProject.setId(null);
           toolsProjectMapper.insertToolsProject(toolsProject);
            toolsProjectId = toolsProject.getId();
           String taskMessage="insert Tools Project："+toolsProjectId;
           logger.info(taskMessage);
        }catch (Exception e){
            ExceptionDumper.wrap(e);
            throw new ToolsException(e);
        }

        return toolsProjectId;
    }
    /**
     * 删除组合工具流程编排信息
     *
     * @param toolsId 工具id
     */
    @Transactional(rollbackFor = {ToolsException.class })
    @Override
    public void deleteToolsProject(Long toolsId) throws ToolsException {
        try{
            toolsProjectMapper.deleteToolsProjectByToolsId(toolsId);
            String taskMessage="delete Composition tool process orchestration information toolsId ："+toolsId;
            logger.info(taskMessage);
        }catch (Exception e){
            logger.error("deleteToolsProject delete tools id :{} is error",toolsId);
            ExceptionDumper.wrap(e);
            throw new ToolsException(e);
        }

    }

    /**
     * 检查工具节点是否绑定agent信息
     * @param toolsId 工具id
     * @param type 工具类型
     * @return 返回节点是否绑定agent标志
     */
    @Override
    public boolean checkToolsAgent(Long toolsId, Integer type) {
        // 第一层：工具本身的判断，由于工具本身判断在脚本工具处的查询逻辑与子节点不同，因此不能放入递归中。
        if (type == 2) {
            // 组合工具
            // 查询工具信息
            ToolsInfoDto toolsInfoDto = toolsInfoService.selectToolsInfoById(toolsId);
            if (toolsInfoDto == null || toolsInfoDto.getChildIds() == null || "".equals(toolsInfoDto.getChildIds()) || !toolsInfoDto.getChildIds().startsWith(",")) {
                return false;
            }
            // 解析子节点id
            String childId = toolsInfoDto.getChildIds();
            String[] childIds = childId.split("_");
            String childToolsId = childIds[0].substring(1, childIds[0].length());
            String[] childToolsIds = childToolsId.split(",");
            for (String cid : childToolsIds) {
                // 查询子节点工具信息
                ToolsInfoDto childToolsInfoDto = toolsInfoService.selectToolsInfoById(Long.parseLong(cid));
                // 子节点递归调用，参数为工具本身id：toolsId及子节点工具信息dto
                if (!checkToolsAgentByDTO(toolsId, childToolsInfoDto)) {
                    return false;
                }
            }
        } else if (type == 3) {
            // 脚本工具
            // 脚本工具本身查询逻辑：查询ieai_tb_tools_agent_info表，看脚本工具是否绑定了agent
            Integer scriptToolsCount = toolsAgentInfoService.selectAgentCountByToolId(toolsId);
            if (scriptToolsCount == null || scriptToolsCount <= 0) {
                return false;
            }
        }
        return true;
    }


    /**
     * 检查工具子节点是否绑定agent信息
     * @param parentToolsId 父节点工具id
     * @param toolsInfoDto 子节点工具信息
     * @return 返回节点是否绑定agent标志
     */
    private boolean checkToolsAgentByDTO(Long parentToolsId, ToolsInfoDto toolsInfoDto) {
        if (toolsInfoDto == null) {
            return false;
        }
        if (toolsInfoDto.getType() == 2) {
            // 组合工具
            if (toolsInfoDto == null || toolsInfoDto.getChildIds() == null || "".equals(toolsInfoDto.getChildIds()) || !toolsInfoDto.getChildIds().startsWith(",")) {
                return false;
            }
            // 解析子节点的子节点id
            String childId = toolsInfoDto.getChildIds();
            String[] childIds = childId.split("_");
            String childToolsId = childIds[0].substring(1, childIds[0].length());
            String[] childToolsIds = childToolsId.split(",");
            for (String cid : childToolsIds) {
                // 查询子节点的子节点工具信息
                ToolsInfoDto childToolsInfoDto = toolsInfoService.selectToolsInfoById(Long.parseLong(cid));
                // 子节点的子节点递归调用，参数为子节点工具本身id：toolsInfoDto.getId()及子节点的子节点工具信息dto
                if (!checkToolsAgentByDTO(toolsInfoDto.getId(), childToolsInfoDto)) {
                    return false;
                }
            }
        } else if (toolsInfoDto.getType() == 3) {
            // 脚本工具
            // 子节点脚本工具信息查询逻辑：查询ieai_tb_tools_commonagent_info表，看脚本工具是否绑定了agent，参数为父节点组合工具id：parentToolsId，子节点脚本工具id：toolsInfoDto.getId()
            Integer scriptToolsCount = commonToolsAgentInfoService.selectToolsAgentCountByCommonToolsIdAndScriptToolsId(parentToolsId, toolsInfoDto.getId());
            if (scriptToolsCount == null || scriptToolsCount <= 0) {
                return false;
            }
        }
        return true;
    }

}

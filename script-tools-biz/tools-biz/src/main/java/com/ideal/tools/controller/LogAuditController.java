package com.ideal.tools.controller;


import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.system.common.component.aop.MethodPermission;
import com.ideal.tools.common.Constants;
import com.ideal.tools.core.web.BaseController;
import com.ideal.tools.model.dto.*;
import com.ideal.tools.model.enums.ConstantsEnum;
import com.ideal.tools.model.enums.OperatResultEnum;
import com.ideal.tools.service.ILogAuditService;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 审计日志自定义分类Controller
 *
 * <AUTHOR>
 * @date 2024-06-17
 */
@RestController
@RequestMapping("${app.script-tools-url:}/tools/logAudit")
public class LogAuditController extends BaseController {

    private final ILogAuditService logAuditService;

    public LogAuditController(ILogAuditService logAuditService) {
        this.logAuditService = logAuditService;
    }

    /**
     * 查询日志审计自定义分类列表
     *
     * @param tableQueryDto 查询条件
     * @return 查询结果
     */
    @PostMapping("/list")
    public R<PageInfo<LogAuditResultDto>> list(@RequestBody TableQueryDto<LogAuditQueryDto> tableQueryDto) {
        PageInfo<LogAuditResultDto> pages =null;
        try{
            pages = logAuditService.selectLogAuditList(
                    tableQueryDto.getQueryParam(),
                    tableQueryDto.getPageNum(),
                    tableQueryDto.getPageSize()
            );
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, pages, ConstantsEnum.LIST_SUCCESS.getDesc());
        }catch (Exception e){
            logger.error("Failed to query Toolbox custom categories !{}",e.getMessage(), e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE,pages , ConstantsEnum.LIST_FAIL.getDesc());
        }
    }

    /**
     * 查询操作模块、类型 自定义分类下拉框二级联动     沒用
     *
     * @return 查询结果
     */
    @GetMapping(value = "/getModuleAndType")
    public R<List<LogModuleAndTypeDto>> getModuleAndType() {
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, logAuditService.selectModuleAndType(), ConstantsEnum.LIST_SUCCESS.getDesc());
    }
    /**
     * 查询操作模块自定义分类下拉框
     *
     * @return 查询结果
     */
    @GetMapping(value = "/getModuleData")
    public R<List<LogModuleAndTypeDto>> getModuleData() {
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, logAuditService.selectModule(), ConstantsEnum.LIST_SUCCESS.getDesc());
    }
    /**
     * 查询操作类型 自定义分类下拉框
     *
     * @return 查询结果
     */
    @GetMapping(value = "/getTypeDataBymoduleId")
    public R<List<LogModuleAndTypeDto>> getTypeData(@RequestParam(value = "moduleId")Integer moduleCode) {
        return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, logAuditService.selectType(moduleCode), ConstantsEnum.LIST_SUCCESS.getDesc());
    }

    /**
     * 审计日志插入自定义        沒用
     *
     * @param logAuditDto 日志审计自定义分类
     */
    @PostMapping("/save")
    public R<Object> insertLogAudit(@RequestBody LogAuditDto logAuditDto) {
        try {
            UserDto user = getUser();
            logAuditDto.setUserDto(user);
            logAuditService.insertLogAudit(logAuditDto);
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, OperatResultEnum.SUCCESS.getDesc(), ConstantsEnum.SAVE_SUCCESS.getDesc());
        }catch (Exception e) {
            logger.error("insertLogAudit failed!", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, OperatResultEnum.FAILURE.getDesc(),ConstantsEnum.SAVE_FAIL.getDesc() );
        }
    }
}


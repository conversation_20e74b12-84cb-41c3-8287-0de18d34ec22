package com.ideal.tools.service.interaction;

import com.github.pagehelper.PageInfo;
import com.ideal.common.util.BeanUtils;
import com.ideal.system.api.IBusinessSystem;
import com.ideal.system.api.IUserInfo;
import com.ideal.system.dto.BusinessSystemApiDto;
import com.ideal.system.dto.BusinessSystemQueryDto;
import com.ideal.system.dto.UserInfoApiDto;
import com.ideal.system.dto.UserInfoQueryDto;
import com.ideal.tools.model.dto.BusinessSystemDto;
import com.ideal.tools.model.dto.SystemPullDto;
import com.ideal.tools.model.dto.UserPullDto;
import com.ideal.tools.model.entity.ToolsInfoEntity;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;


/**
 * 与平台管理服务对接交互集中处理类
 * <AUTHOR>
 *
 */
@Component
public class SystemDataInteract {


    private static final Logger logger = LoggerFactory.getLogger(SystemDataInteract.class);


    private final IBusinessSystem businessSystem;
    private final IUserInfo userInfo;



    public SystemDataInteract( IBusinessSystem businessSystem,IUserInfo userInfo) {

        this.businessSystem = businessSystem;
        this.userInfo = userInfo;

    }




    /**
     * 获取权限用户下的业务系统Id列表
     * @param userId 当前登录用户Id
     * @return 获取到的业务系统Id列表
     */
    public List<SystemPullDto> getBusinessSystemList(Long userId){
        List<BusinessSystemApiDto> businessSystemApiDtoList =businessSystem.getBusinessSystemInfoByUserIdForApi(userId);
        BusinessSystemApiDto businessSystemA=new BusinessSystemApiDto();
        businessSystemA.setId(-1L);
        businessSystemA.setName("公共");
        businessSystemA.setCode("toolsPub");
        businessSystemApiDtoList.add(0, businessSystemA);
        if(CollectionUtils.isEmpty(businessSystemApiDtoList)){
            logger.info("query businessSystemId list from system service ,result is null,param is {},find size is {}",userId,0);
            return Collections.emptyList();
        }
        logger.info("query businessSystemId list from system service ,param is {},find size is {}",userId,businessSystemApiDtoList.size());
        List<SystemPullDto> systemPull = new ArrayList<>();
        for(BusinessSystemApiDto businessSystemApiDto:  businessSystemApiDtoList){
            SystemPullDto systemPullDto = new SystemPullDto();


            systemPullDto.setBusinessSystemId(businessSystemApiDto.getId());
            systemPullDto.setBusinessSystemCode(businessSystemApiDto.getCode());
            systemPullDto.setBusinessSystemName(businessSystemApiDto.getName());

            systemPull.add(systemPullDto);

        }
        return systemPull;
    }

    /**
     * 根据系统业务名称获取业务系统信息
     * @param businessSystemName 业务系统名称
     * @return 获取平台业务系统信息
     */
    public SystemPullDto queryBusinessSystemBySysName(String businessSystemName){
        if(businessSystemName.equals("公共")){
            SystemPullDto systemPullDto = new SystemPullDto();
            systemPullDto.setBusinessSystemId(-1L);
            systemPullDto.setBusinessSystemName("公共");
            systemPullDto.setBusinessSystemCode("toolsPub");
            return systemPullDto;
        }
        List<BusinessSystemApiDto> businessSystemApiDto = businessSystem.queryBusinessSystemListByName(businessSystemName);
        if (businessSystemApiDto.isEmpty()){
            logger.info("query businessSystemApiDto from system service ,result is null,param is {}",businessSystemName);
            return new SystemPullDto();
        }
        if (businessSystemApiDto.size() != 1){//平台管理会返回多个内容，没有内容，返回空的内容
            logger.info("query businessSystemApiDto from system service ,result multiple data,param is {},result is {}"
                    ,businessSystemName,businessSystemApiDto.toString());
            return new SystemPullDto();
        }
        logger.info("query businessSystemApiDto from system service ,param is {},result is {}",businessSystemName,businessSystemApiDto.toString());
        SystemPullDto systemPullDto = new SystemPullDto();
        systemPullDto.setBusinessSystemId(businessSystemApiDto.get(0).getId());
        systemPullDto.setBusinessSystemCode(businessSystemApiDto.get(0).getCode());
        systemPullDto.setBusinessSystemName(businessSystemApiDto.get(0).getName());
        return systemPullDto;
    }

    public List<UserPullDto> getUserList() {
        UserInfoQueryDto userInfoQueryDto = new UserInfoQueryDto();
        UserInfoApiDto queryParam = new UserInfoApiDto();
        queryParam.setLoginName(null);
        userInfoQueryDto.setQueryParam(queryParam);
        userInfoQueryDto.setPageNum(1);
        userInfoQueryDto.setPageSize(1000);
        PageInfo<UserInfoApiDto> userInfoByLoginNameForApi = userInfo.getUserInfoByLoginNameForApi(userInfoQueryDto);
        List<UserInfoApiDto> list = userInfoByLoginNameForApi.getList();
        if(CollectionUtils.isEmpty(list)){
            logger.info("query user list from system service ,result is null,find size is {}",0);
            return Collections.emptyList();
        }
        logger.info("query user list from system service ,find size is {}",list.size());
        List<UserPullDto> userPullDtos = new ArrayList<>();
        for(UserInfoApiDto userInfoApiDto:  list){
            UserPullDto userPullDto = new UserPullDto();


            userPullDto.setUserId(userInfoApiDto.getId());
            userPullDto.setUserName(userInfoApiDto.getFullName());

            userPullDtos.add(userPullDto);

        }
        return userPullDtos;
    }

    /**
     * 获取业务系统详细信息
     * @return 业务系统信息List
     */
    public List<BusinessSystemDto> getBusinessSystemList(){
        BusinessSystemQueryDto businessSystemQueryDto = new BusinessSystemQueryDto();
        businessSystemQueryDto.setPageNum(1);
        businessSystemQueryDto.setPageSize(20000);
        PageInfo<BusinessSystemApiDto> businessSystemApiDtoList =businessSystem.selectBusinessSystemList(businessSystemQueryDto);
        if(CollectionUtils.isEmpty(businessSystemApiDtoList.getList())){
            logger.info("query businessSystemId list from system service ,result is null,find size is {}",0);
            return Collections.emptyList();
        }
        logger.info("query businessSystemId list from system service ,find size is {}",businessSystemApiDtoList.getList().size());
        List<BusinessSystemDto> businessSystemDtoList = new ArrayList<>();
        for(BusinessSystemApiDto businessSystemApiDto:  businessSystemApiDtoList.getList()){
            BusinessSystemDto businessSystem = BeanUtils.copy(businessSystemApiDto, BusinessSystemDto.class);
            businessSystemDtoList.add(businessSystem);
        }
        return businessSystemDtoList;
    }
    /**
     * 根据业务系统id 获取业务系统是否为重要全局系统
     * @param busSystemId 业务系统id
     * @return true 是重要全局，false 不是重要全局
     */
    public Boolean getBusinessSystemInfoForType(Long busSystemId){
        boolean result  = false;
        List<BusinessSystemDto> businessSystemDtos = getBusinessSystemList();
        for (BusinessSystemDto businessSystemDto : businessSystemDtos) {
            if (businessSystemDto.getId().equals(busSystemId)) {
                logger.info("业务系统id{},业务系统typeName{}",busSystemId,businessSystemDto.getTypeName());
                logger.info("业务系统实体类{}",businessSystemDto.toString());
                    if(StringUtils.isNotEmpty(businessSystemDto.getTypeName())){
                        switch (businessSystemDto.getTypeName()){
                            case "重要系统":
                                result = true;
                                break;
                            case "全局系统":
                                result = true;
                                break;
                            default:
                                result = false;
                                break;
                        }
                    break;
                }
            }
        }
        return result;
    }

    /**
     * 根据业务系统code查询业务系统信息
     * @param systemCode
     * @return
     */
    public List<com.ideal.tools.dto.SystemPullDto> getBusinessSystemListByCode(String systemCode) {
        List<BusinessSystemApiDto> businessSystemApiDtoList =businessSystem.queryBusinessSystemListByCode(systemCode);
        if(CollectionUtils.isEmpty(businessSystemApiDtoList)){
            logger.info("query businessSystemId list from system service ,result is null,param is {},find size is {}",systemCode,0);
            return Collections.emptyList();
        }
        logger.info("query businessSystemId list from system service ,param is {},find size is {}",systemCode,businessSystemApiDtoList.size());
        List<com.ideal.tools.dto.SystemPullDto> systemPull = new ArrayList<>();
        for(BusinessSystemApiDto businessSystemApiDto:  businessSystemApiDtoList){
            com.ideal.tools.dto.SystemPullDto systemPullDto = new com.ideal.tools.dto.SystemPullDto();


            systemPullDto.setBusinessSystemId(businessSystemApiDto.getId());
            systemPullDto.setBusinessSystemCode(businessSystemApiDto.getCode());
            systemPullDto.setBusinessSystemName(businessSystemApiDto.getName());

            systemPull.add(systemPullDto);

        }
        return systemPull;
    }

    /**
     * 获取权限用户下的业务系统Id列表
     * @param userId 当前登录用户Id
     * @return 获取到的业务系统Id列表
     */
    public List<com.ideal.tools.dto.SystemPullDto> getBusinessSystemListStudio(Long userId){
        List<BusinessSystemApiDto> businessSystemApiDtoList =businessSystem.getBusinessSystemInfoByUserIdForApi(userId);
        BusinessSystemApiDto businessSystemA=new BusinessSystemApiDto();
        businessSystemA.setId(-1L);
        businessSystemA.setName("公共");
        businessSystemA.setCode("toolsPub");
        businessSystemApiDtoList.add(0, businessSystemA);
        if(CollectionUtils.isEmpty(businessSystemApiDtoList)){
            logger.info("query businessSystemId list from system service ,result is null,param is {},find size is {}",userId,0);
            return Collections.emptyList();
        }
        logger.info("query businessSystemId list from system service ,param is {},find size is {}",userId,businessSystemApiDtoList.size());
        List<com.ideal.tools.dto.SystemPullDto> systemPull = new ArrayList<>();
        for(BusinessSystemApiDto businessSystemApiDto:  businessSystemApiDtoList){
            com.ideal.tools.dto.SystemPullDto systemPullDto = new com.ideal.tools.dto.SystemPullDto();


            systemPullDto.setBusinessSystemId(businessSystemApiDto.getId());
            systemPullDto.setBusinessSystemCode(businessSystemApiDto.getCode());
            systemPullDto.setBusinessSystemName(businessSystemApiDto.getName());

            systemPull.add(systemPullDto);

        }
        return systemPull;
    }
    
    
    /**
     * 获取权限用户下的业务系统Id列表
     * @param userId 当前登录用户Id
     * @return 获取到的业务系统Id列表
     */
    public Long getBusinessSystemIdByCode(String code){
    	List<BusinessSystemApiDto> businessSystemApiDtoList =businessSystem.queryBusinessSystemListByCode(code);
    	if (null==businessSystemApiDtoList||businessSystemApiDtoList.size()==0) {
			return null;
		}else {
			return businessSystemApiDtoList.get(0).getId();
		}
    }
    /**
     * 给实体实体添加业务code
     */
    public void addBusinessSystemIdByCode(List<ToolsInfoEntity> toolsInfoList,String businessSystemCode){
    	List<Long> reqList=new LinkedList<Long>();
    	if (null==toolsInfoList||toolsInfoList.size()==0) {
			return;
		}
    	boolean isPub=true;
    	if (null!=businessSystemCode&&!"toolsPub".equals(businessSystemCode)) {
    		isPub=false;
    	}
    	Map<Long,String> busiIdCodeMap=null;
    	if (isPub) {
    		for (ToolsInfoEntity toolsInfoEntity:toolsInfoList) {
        		reqList.add(toolsInfoEntity.getBusinessSystemId());			
    		}
        	List<BusinessSystemApiDto> businessSystemApiDtoList =businessSystem.getBusinessSystemInfoByBusSystemIdForApi(reqList);
        	busiIdCodeMap=new HashMap<Long,String>();
        	for (BusinessSystemApiDto businessSystemApiDto:businessSystemApiDtoList) {
        		busiIdCodeMap.put(businessSystemApiDto.getId(), businessSystemApiDto.getCode());
    		}
		}
		for (ToolsInfoEntity toolsInfoEntityIt:toolsInfoList) {
			String busiCodeIt=businessSystemCode;
			if (isPub) {
				busiCodeIt=busiIdCodeMap.get(toolsInfoEntityIt.getBusinessSystemId());
			}
			toolsInfoEntityIt.setBusinessSystemCode(busiCodeIt);		
		}
    }
}

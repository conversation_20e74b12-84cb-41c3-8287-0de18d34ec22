package com.ideal.tools.controller;

import java.io.IOException;
import java.net.URLEncoder;
import java.util.Date;
import java.util.List;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.system.common.component.aop.MethodPermission;
import com.ideal.tools.common.Constants;
import com.ideal.tools.common.ControlsTimeUtil;
import com.ideal.tools.common.EasyExcelUtil;
import com.ideal.tools.core.web.BaseController;
import com.ideal.tools.model.dto.ExecuteHistoryDto;
import com.ideal.tools.model.dto.ExecuteHistoryExportDto;
import com.ideal.tools.model.dto.ExecuteHistoryFilesDto;
import com.ideal.tools.model.dto.ExecuteHistoryQueryListDto;
import com.ideal.tools.model.dto.ExecuteHistoryResultListDto;
import com.ideal.tools.model.dto.LogAuditDto;
import com.ideal.tools.model.enums.ConstantsEnum;
import com.ideal.tools.model.enums.OperatModuleEnum;
import com.ideal.tools.model.enums.OperatResultEnum;
import com.ideal.tools.model.enums.OperatTypeEnum;
import com.ideal.tools.service.IExecuteHistoryService;
import com.ideal.tools.service.ILogAuditService;
/**
 * 执行历史
 */
@RestController
@RequestMapping("${app.script-tools-url:}/tools/history")
@MethodPermission("@dp.hasBtnPermission('execution-history')")
public class ExecuteHistoryController extends BaseController {
    private final IExecuteHistoryService executeHistoryService;

    private final ILogAuditService logAuditService;
    public ExecuteHistoryController(IExecuteHistoryService executeHistoryService,ILogAuditService logAuditService){
        this.executeHistoryService = executeHistoryService;
        this.logAuditService =  logAuditService;
    }
    /**
     * 查询工具执行历史列表
     *
     * @param tableQueryDto 查询条件
     * @return 查询结果
     */
    @PostMapping("/list")
    public R<PageInfo<ExecuteHistoryResultListDto>> list(@RequestBody TableQueryDto<ExecuteHistoryQueryListDto> tableQueryDto) {
        PageInfo<ExecuteHistoryResultListDto> pages =null;
        try{
            pages = executeHistoryService.selectExecuteHistoryList(
                    tableQueryDto.getQueryParam(),
                    tableQueryDto.getPageNum(),
                    tableQueryDto.getPageSize()
            );
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, pages, ConstantsEnum.LIST_SUCCESS.getDesc());
        }catch (Exception e){
            logger.error("Failed to query Toolbox execute History !{}",e.getMessage(), e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE,pages , ConstantsEnum.LIST_FAIL.getDesc());
        }
    }

    /**
     * 查询工具执行历史详细信息
     *
     * @param id 数据唯一标识
     * @return 查询结果
     */
    @GetMapping(value = "/detail")
    @MethodPermission("@dp.hasBtnPermission('detailToolHistory')")
    public R<ExecuteHistoryDto> getAgentInfoInfo(@RequestParam(value = "id")Long id) {
        try {
            ExecuteHistoryDto executeHistory = executeHistoryService.selectExecuteHistoryById(id, getUser());
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, executeHistory, ConstantsEnum.GAIN_SUCCESS.getDesc());
        }catch (Exception e) {
            logger.error("toolbox.History customization for detail failed!", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, null, ConstantsEnum.GAIN_FAIL.getDesc() );
        }
    }

    /**
     * 工具执行历史导出
     * @param response 响应信息
     * @param dto 入参
     */
    @PostMapping("/export")
    @MethodPermission("@dp.hasBtnPermission('exportToolHistory')")
    public void exportHistoryList(HttpServletResponse response, @RequestBody ExecuteHistoryQueryListDto dto) {
        List<ExecuteHistoryExportDto> list = executeHistoryService.exportHistoryList(dto);
        // 使用 EasyExcel 将数据写入响应输出流
        EasyExcelUtil.writeExcel(response, list, "工具执行历史" + ControlsTimeUtil.formatDateByFormat(new Date(), "YYYY-MM-dd hh_mm_ss"), "工具执行历史", ExecuteHistoryExportDto.class);
        String operatingContent = "导出全部数据到本地";
        String resultDesc = OperatModuleEnum.TOOL_EXECUTE_HISTORY.getRemark() + OperatTypeEnum.TOOL_EXECUTE_HISTORY_EXPORT.getRemark();
        LogAuditDto logAuditDto = new LogAuditDto(OperatModuleEnum.TOOL_EXECUTE_HISTORY.getCode(), OperatTypeEnum.TOOL_EXECUTE_HISTORY_EXPORT.getCode(), operatingContent);
        logAuditDto.setResult(OperatResultEnum.SUCCESS.getCode());
        logAuditDto.setResultDesc(resultDesc + OperatResultEnum.SUCCESS.getDesc());
        logAuditService.insertLogAudit(logAuditDto);
    }

    /**
     * 下载工具执行历史中的文件
     * @param id   工具id
     * @param response
     */
    @PostMapping("/downloadHistoryFiles")
    @MethodPermission("@dp.hasBtnPermission('detailToolHistory')")
    public void downloadFiles(@RequestBody Long id, HttpServletResponse response) {
        ExecuteHistoryFilesDto executeHistoryFilesDto = executeHistoryService.selectExecuteHistoryFilesById(id);
        response.reset();
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            response.setContentType("application/octet-stream");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-disposition","attachment; filename=" + URLEncoder.encode(executeHistoryFilesDto.getName(), "UTF-8"));
            outputStream.write(executeHistoryFilesDto.getFiles());
        } catch (IOException e) {
            logger.error("tools history downloadFiles error:" ,e);
            response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
        }
    }

}

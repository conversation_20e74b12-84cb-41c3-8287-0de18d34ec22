package com.ideal.tools.controller;


import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.common.dto.ValidateError;
import com.ideal.common.util.spring.MessageUtil;
import com.ideal.system.common.component.aop.MethodPermission;
import com.ideal.tools.common.Constants;
import com.ideal.tools.core.web.BaseController;
import com.ideal.tools.model.dto.*;
import com.ideal.tools.model.enums.ConstantsEnum;
import com.ideal.tools.service.ITbCategoryService;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 工具箱自定义分类Controller
 *
 * <AUTHOR>
 * @date 2024-06-06
 */
@RestController
@RequestMapping("${app.script-tools-url:}/tools/category")
public class TbCategoryController extends BaseController {



    private final ITbCategoryService categoryService;

    public TbCategoryController(ITbCategoryService categoryService) {
        this.categoryService = categoryService;
    }

    /**
     * 查询工具箱自定义分类列表
     *
     * @param tableQueryDto 查询条件
     * @return 查询结果
     */
    @PostMapping("/list")
    public R<PageInfo<CategoryResultDto>> list(@RequestBody TableQueryDto<CategoryQueryDto> tableQueryDto) {
        PageInfo<CategoryResultDto> pages =null;
        try{
            pages = categoryService.selectCategoryTreeList(
                    tableQueryDto.getQueryParam(),
                    tableQueryDto.getPageNum(),
                    tableQueryDto.getPageSize()
            );
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, pages, ConstantsEnum.LIST_SUCCESS.getDesc());
        }catch (Exception e){
            logger.error("Failed to query Category custom categories !{}",e.getMessage(), e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE,pages , ConstantsEnum.LIST_FAIL.getDesc());
        }
    }


    /**
     * 查询工具箱自定义分类详细信息       沒用
     *
     * @param id 数据唯一标识
     * @return 查询结果
     */
    @GetMapping(value = "/get")
    public R<CategoryDto> getAgentInfoInfo(@RequestParam(value = "id")Long id) {
        return R.ok(categoryService.selectCategoryById(id));
    }

    /**
     * 新增保存工具箱自定义分类
     *
     * @param categoryDto 添加数据
     * @return 添加结果
     */
    @PostMapping("/save")
    @MethodPermission("@dp.hasBtnPermission('addToolclass')")
    public R<Object> save(@RequestBody CategoryDto categoryDto, BindingResult bindingResult) {
            if (bindingResult.hasErrors()) {
                List<ValidateError> validateErrorList = bindingResult.getFieldErrors().stream()
                        .map(item -> new ValidateError(item.getField(), MessageUtil.message(item.getDefaultMessage())))
                        .collect(Collectors.toList());
                Map<String, List<ValidateError>> validateError = new HashMap<>(1);
                validateError.put("validateError", validateErrorList);
                return R.fail(Constants.REPONSE_STATUS_VALIDATA_CODE, validateError, "validate.error");
            }

            try {
                categoryService.insertTreeCategory(categoryDto, getUser());
                return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, "", ConstantsEnum.SAVE_SUCCESS.getDesc());
            }catch (RuntimeException e) {
                logger.error("insert toolbox custom classification failed!", e);
                return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, e.getMessage());
            }
            catch (Exception e) {
                logger.error("New toolbox customization failed!", e);
                return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, null,ConstantsEnum.SAVE_FAIL.getDesc() );
            }

        }

    /**
     * 修改保存工具箱自定义分类
     *
     * @param categoryDto 更新数据
     * @return 更新结果
     */
    @PostMapping("/update")
    @MethodPermission("@dp.hasBtnPermission('editList')")
    public R<Object> update(@RequestBody CategoryDto categoryDto, BindingResult bindingResult) {

        if (bindingResult.hasErrors()) {
            List<ValidateError> validateErrorList = bindingResult.getFieldErrors().stream()
                    .map(item -> new ValidateError(item.getField(), MessageUtil.message(item.getDefaultMessage())))
                    .collect(Collectors.toList());
            Map<String, List<ValidateError>> validateError = new HashMap<>(1);
            validateError.put("validateError", validateErrorList);
            return R.fail(Constants.REPONSE_STATUS_VALIDATA_CODE, validateError,"validate.error");
        }

        try {
            categoryService.updateCategory(categoryDto,getUser());
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, "", ConstantsEnum.UPDATE_SUCCESS.getDesc());
        }catch (Exception e) {
            logger.error("Modifying toolbox custom classification failed!", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, "", ConstantsEnum.UPDATE_FAIL.getDesc());
        }
    }


    /**
     * 删除工具箱自定义分类
     *
     * @param ids 主键列表
     * @return 删除结果
     */
    @PostMapping("/remove")
    @MethodPermission("@dp.hasBtnPermission('deleteList')")
    public R<Void> remove(@RequestBody Long[] ids) {
        try{
            categoryService.deleteCategoryByIds(ids,getUser());
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, ConstantsEnum.REMOVE_SUCCESS.getDesc());
        }catch (RuntimeException e) {
            logger.error("Delete toolbox custom classification failed!", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, e.getMessage());
        }
        catch (Exception e) {
            logger.error("Delete toolbox custom classification failed!", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, ConstantsEnum.REMOVE_FAIL.getDesc());
        }
    }


    /**
     * 查询工具箱自定义分类下拉列表
     */
    @PostMapping("/dropdownList")
    @MethodPermission("@dp.hasBtnPermission('tool-class') or @dp.hasBtnPermission('execution-history') " +
            "or @dp.hasBtnPermission('tool-monitoring') or @dp.hasBtnPermission('tool-run') or @dp.hasBtnPermission('actionToolRun') " +
            "or @dp.hasBtnPermission('scene-tools') or @dp.hasBtnPermission('tool-execution') or @dp.hasBtnPermission('detailTool') " +
            "or @dp.hasBtnPermission('editTool') or @dp.hasBtnPermission('scriptTool') or @dp.hasBtnPermission('combinationTool')")
    public  R<List<CategoryDropdownDto>> saveTaskTerminating(@RequestBody CategoryDropdownQueryDto categoryDropdownQueryDto)
    {
        List<CategoryDropdownDto> lists = null;
        try {
            lists =  categoryService.selectCategoryLists(categoryDropdownQueryDto);
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE,lists, ConstantsEnum.LIST_SUCCESS.getDesc());

        }catch (Exception e) {
            logger.error("Failed to query the toolbox custom classification dropdown list!", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE,lists, ConstantsEnum.LIST_FAIL.getDesc());
        }
    }

    /**
     * 工具箱自定义分类 查询所有一级二级 分类
     */
    @PostMapping("/superiorList")
    @MethodPermission("@dp.hasBtnPermission('descTool')")
    public  R<List<CategorySuperiorDto>> saveTaskTerminatingSuperior(@RequestBody CategoryDropdownQueryDto categoryDropdownQueryDto)
    {
        List<CategorySuperiorDto> lists = null;
        try {

            lists =  categoryService.selectCategorySuperiorList(categoryDropdownQueryDto);
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE,lists, ConstantsEnum.LIST_SUCCESS.getDesc());

        }catch (Exception e) {
            logger.error("Failed to query the toolbox custom classification dropdown list!", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE,lists, ConstantsEnum.LIST_FAIL.getDesc());
        }
    }
}


package com.ideal.tools.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 工具箱信息对象 (用于工具执行列表)
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
public class ToolsInfoResultDto implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 主键ID
     */
    private Long id;
    /**
     * 工具编码
     */
    private String code;
    /**
     * 工具名称
     */
    private String name;
    /** 业务系统id */
    private Long businessSystemId;
    /** 业务系统名称 */
    private String businessSystemName;
    /** 业务系统 Code */
    private String businessSystemCode;
    /**
     * 工具状态（0 草稿 1 已修改 2、审核中 3、待启动 4、运行中）
     */
    private Integer status;
    /**
     * 工具类型（1描述工具，2组合工具，3脚本工具……）
     */
    private Integer type;
    /**
     * 交付状态 0、未交付 1、待接收 2、已退回 3、已交付
     */
    private Integer deliveryStatus;
    /**
     * 交付接收时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date deliveryReceptionTime;
    /**
     * 审批状态，0待审核、1审核中、2已通过、3退回、4归档
     */
    private Integer approvalState;
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    /**
     * 创建人
     */
    private String creatorName;
    /** 创建人ID */
    private Long creatorId;
    /**
     * 一级分类名称
     */
    private String oneTypeName;
    /**
     * 二级分类名称
     */
    private String twoTypeName;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
     * 工具脚本名称
     */
    private String scriptName;
    /**
     * 是否高危 - 0:否 1:是
     */
    private Integer highRisk;
    /** 工具分类（1.预案、2.查询诊断、3.变更处置、4.告警自愈） */
    private Integer classification;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setBusinessSystemId(Long businessSystemId){
        this.businessSystemId = businessSystemId;
    }

    public Long getBusinessSystemId(){
        return businessSystemId;
    }

    public void setBusinessSystemName(String businessSystemName){
        this.businessSystemName = businessSystemName;
    }

    public String getBusinessSystemName(){
        return businessSystemName;
    }


    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getDeliveryStatus() {
        return deliveryStatus;
    }

    public void setDeliveryStatus(Integer deliveryStatus) {
        this.deliveryStatus = deliveryStatus;
    }

    public Date getDeliveryReceptionTime() {
        return deliveryReceptionTime;
    }

    public void setDeliveryReceptionTime(Date deliveryReceptionTime) {
        this.deliveryReceptionTime = deliveryReceptionTime;
    }

    public Integer getApprovalState() {
        return approvalState;
    }

    public void setApprovalState(Integer approvalState) {
        this.approvalState = approvalState;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public String getOneTypeName() {
        return oneTypeName;
    }

    public void setOneTypeName(String oneTypeName) {
        this.oneTypeName = oneTypeName;
    }

    public String getTwoTypeName() {
        return twoTypeName;
    }

    public void setTwoTypeName(String twoTypeName) {
        this.twoTypeName = twoTypeName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getScriptName() {
        return scriptName;
    }

    public void setScriptName(String scriptName) {
        this.scriptName = scriptName;
    }

    public Integer getHighRisk() {
        return highRisk;
    }

    public void setHighRisk(Integer highRisk) {
        this.highRisk = highRisk;
    }

    public Integer getClassification() {
        return classification;
    }

    public void setClassification(Integer classification) {
        this.classification = classification;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public String getBusinessSystemCode() {
		return businessSystemCode;
	}

	public void setBusinessSystemCode(String businessSystemCode) {
		this.businessSystemCode = businessSystemCode;
	}

	@Override
    public String toString() {
        return "ToolsInfoResultDto{" +
                "id=" + id +
                ", code='" + code + '\'' +
                ", name='" + name + '\'' +
                ", businessSystemId=" + businessSystemId +
                ", businessSystemName='" + businessSystemName + '\'' +
                ", status=" + status +
                ", type=" + type +
                ", deliveryStatus=" + deliveryStatus +
                ", deliveryReceptionTime=" + deliveryReceptionTime +
                ", approvalState=" + approvalState +
                ", updateTime=" + updateTime +
                ", creatorName='" + creatorName + '\'' +
                ", creatorId=" + creatorId +
                ", oneTypeName='" + oneTypeName + '\'' +
                ", twoTypeName='" + twoTypeName + '\'' +
                ", createTime=" + createTime +
                ", scriptName='" + scriptName + '\'' +
                ", highRisk=" + highRisk +
                ", classification=" + classification +
                '}';
    }
}

package com.ideal.tools.service.impl;


import com.ideal.common.util.BeanUtils;
import com.ideal.common.util.PageDataUtil;
import com.ideal.tools.common.ExceptionDumper;
import com.ideal.tools.exception.AuditException;

import com.ideal.tools.mapper.AuditEverybodyMapper;
import com.ideal.tools.model.constant.AuditTypeConstants;
import com.ideal.tools.model.dto.*;
import com.ideal.tools.model.entity.AuditEverybodyEntity;
import com.ideal.tools.model.enums.AuditStateEnum;

import com.ideal.tools.model.enums.AuditTypeEnum;
import com.ideal.tools.model.interaction.DoubleCheckTrackDto;
import com.ideal.tools.model.interaction.TrackAuditorsDto;
import com.ideal.tools.service.IAuditEverybodyService;
import com.ideal.tools.service.interaction.AuditInteract;
import org.springframework.stereotype.Service;
import com.ideal.tools.mapper.AuditMapper;
import com.ideal.tools.model.entity.AuditEntity;
import com.ideal.tools.service.IAuditService;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Date;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

/**
 * 双人复核服务关系Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-25
 */
@Service("toolsAuditService")
public class AuditServiceImpl implements IAuditService {
    private final Logger logger = LoggerFactory.getLogger(AuditServiceImpl.class);

    private final AuditMapper auditMapper;
    private final AuditInteract auditInteract;
    private final IAuditEverybodyService auditEverybodyService;
    private final AuditEverybodyMapper auditEverybodyMapper;


    public AuditServiceImpl(AuditMapper auditMapper,IAuditEverybodyService auditEverybodyService,AuditInteract auditInteract,AuditEverybodyMapper auditEverybodyMapper) {
        this.auditMapper = auditMapper;
        this.auditInteract=auditInteract;
        this.auditEverybodyService=auditEverybodyService;
        this.auditEverybodyMapper = auditEverybodyMapper;
    }

    /**
     * 查询双人复核服务关系
     *
     * @param id 双人复核服务关系主键
     * @return 双人复核服务关系
     */
    @Override
    public AuditDto selectAuditById(Long id) {
        AuditEntity audit = auditMapper.selectAuditById(id);
        return BeanUtils.copy(audit, AuditDto.class);
    }

    /**
     * 查询双人复核服务关系列表
     *
     * @param auditQueryDto 双人复核服务关系
     * @param pageNum 页码
     * @param pageSize 单页长度
     * @return 双人复核服务关系
     */
    @Override
    public PageInfo<AuditDto> selectAuditList(AuditQueryDto auditQueryDto, Integer pageNum, Integer pageSize) {
        AuditEntity query = BeanUtils.copy(auditQueryDto, AuditEntity.class);
        PageMethod.startPage(pageNum, pageSize);
        List<AuditEntity> auditList = auditMapper.selectAuditList(query);
        return PageDataUtil.toDtoPage(auditList, AuditDto.class);
    }

    /**
     * 查询双人复核服务关系列表
     *
     * @param auditQueryDto 双人复核服务关系
     * @return 双人复核服务关系
     */
    @Override
    public List<AuditDto> selectAuditList(AuditQueryDto auditQueryDto) {
        AuditEntity query = BeanUtils.copy(auditQueryDto, AuditEntity.class);

        List<AuditEntity> auditList = auditMapper.selectAuditList(query);
        return BeanUtils.copy(auditList,AuditDto.class) ;
    }
    /**
     * 新增双人复核服务关系
     *
     * @param auditDto 双人复核服务关系
     * @return 结果
     */
    @Override
    public int insertAudit(AuditDto auditDto) {
        AuditEntity audit = BeanUtils.copy(auditDto, AuditEntity.class);
        return auditMapper.insertAudit(audit);
    }

    /**
     * 修改双人复核服务关系
     *
     * @param auditDto 双人复核服务关系
     * @return 结果
     */
    @Override
    public int updateAudit(AuditDto auditDto) {
        AuditEntity audit = BeanUtils.copy(auditDto, AuditEntity.class);
        return auditMapper.updateAudit(audit);
    }

    /**
     * 批量删除双人复核服务关系
     *
     * @param ids 需要删除的双人复核服务关系主键
     * @return 结果
     */
    @Override
    public int deleteAuditByIds(Long[] ids) {
        return auditMapper.deleteAuditByIds(ids);
    }
    /**
     * 保存任务审核信息
     * @param businessId 业务ID
     * @param auditEverybodyDtoList 审核人集合
     * @param type 审核类型
     * @param user 当前登录用户
     * @return AuditDto 返回双人复核表对象
     */
    @Transactional(rollbackFor = {AuditException.class })
    @Override
    public AuditDto saveAuditDoubleCheck(Long businessId, List<AuditEverybodyQueryDto> auditEverybodyDtoList, Integer type, UserDto user) throws AuditException {
        AuditDto dto ;
        try{
            AuditEntity auditEntityDto =new AuditEntity();
            auditEntityDto.setType(Math.toIntExact(type));
            auditEntityDto.setApplyName(user.getUserName());
            auditEntityDto.setApplyId(user.getUserId());
            auditEntityDto.setBusinessId(businessId);
            auditEntityDto.setAprvWorkitemId(-1L);
            auditEntityDto.setApprovalState(Math.toIntExact(AuditStateEnum.AUDIT_AWAITING.getCode()));
            auditEntityDto.setApplyTime(new Date(System.currentTimeMillis()));
            auditMapper.insertAudit(auditEntityDto);

            auditEverybodyService.batchInsertAuditEverybody(auditEntityDto.getId(),auditEverybodyDtoList);
            dto = com.ideal.common.util.BeanUtils.copy(auditEntityDto,AuditDto.class);

            logger.info("insert Audit Id：{}",dto.getId());
        }catch (Exception e){
            logger.error("Audit save is error");
            ExceptionDumper.wrap( new AuditException(e));
            throw new AuditException(e);
        }
        return dto;

    }
    /**
     * 发送双人复核信息
     * @param auditDto 发送双人复核主要信息
     * @param taskName 任务主题
     * @return SaveTaskResDto 任务ID和双人复核Id对象
     */
    @Transactional(rollbackFor = {AuditException.class })
    @Override
    public Long sendDoubleCheck(AuditDto auditDto,List<AuditEverybodyQueryDto> auditEverybodyDtoList,String taskName) throws AuditException {
        Long auditPush=-1L;
        //发送双人复核信息
        if(auditDto == null){
            logger.error("Double review push approval params is null !");
            return auditPush;
        }

        AuditDto dto = com.ideal.common.util.BeanUtils.copy(auditDto,AuditDto.class);
        //审批人集合
        List <AuditEverybodyDto> auditEverybodyDtoList1=new ArrayList<>();
        for(AuditEverybodyQueryDto auditEverybodyQueryDto1: auditEverybodyDtoList){
            AuditEverybodyDto auditEverybodyDto=new AuditEverybodyDto();
            auditEverybodyDto.setAuditorId(auditEverybodyQueryDto1.getAuditorId());
            auditEverybodyDto.setAuditorName(auditEverybodyQueryDto1.getAuditorName());
            auditEverybodyDtoList1.add(auditEverybodyDto);
        }

        auditPush = auditInteract.auditPush(dto,auditEverybodyDtoList1,taskName);

        logger.info("send audit {} to double service end",auditDto.getId());


        //回更双人复核状态
        if(auditPush!=-1L){
            logger.info("update audit approvalState is {} start", AuditStateEnum.AUDIT_UNDER_REVIEW.getCode());
            dto.setAprvWorkitemId(auditPush);
            dto.setApprovalState(Math.toIntExact(AuditStateEnum.AUDIT_UNDER_REVIEW.getCode()));
            //暂时使用服务器时间，后续需要使用数据库时间
            Date data = new Date(System.currentTimeMillis());
            dto.setAuditTime(data);

            AuditEntity auditEntity = new AuditEntity();
            org.springframework.beans.BeanUtils.copyProperties(dto, auditEntity);
            auditMapper.updateAudit(auditEntity);

            logger.info("update audit approvalState is {} end",AuditStateEnum.AUDIT_UNDER_REVIEW.getCode());
        }


        return auditPush;
    }


    /**
     * 查询双人复核审核人
     *
     * @param toolsId 双人复核服务关系主键
     * @return 双人复核服务关系
     */
    @Override
    public List<AuditEverybodyEntity> selectAuditToolsId(Long toolsId) {

        List<AuditEverybodyEntity> auditEverybodyList =null;
                AuditEntity audit=new AuditEntity();
        audit.setBusinessId(toolsId);
        List<AuditEntity> auditList = auditMapper.selectAuditList(audit);
        if(auditList!=null && !auditList.isEmpty()) {
            AuditEntity auditEntity=    auditList.get(0);
            AuditEverybodyEntity auditEverybody=new AuditEverybodyEntity();
            auditEverybody.setAuditId(auditEntity.getId());
           auditEverybodyList = auditEverybodyMapper.selectAuditEverybodyList(auditEverybody);
        }


        return auditEverybodyList;
    }



    /**
     * 根据运行任务ID 根据审批状态修改状态
     *
     * @param  auditDto 双人复核服务关系
     * @return 结果
     */
    @Transactional(rollbackFor = {AuditException.class })
    @Override
    public int updateAuditMultipleState(AuditDto auditDto)
    {
        AuditEntity auditEntity = new AuditEntity();
        org.springframework.beans.BeanUtils.copyProperties(auditDto, auditEntity);
        if(auditEntity.getApprovalState().equals(AuditStateEnum.AUDIT_APPROVED.getCode())){
            logger.info("BusinessId :{},Double check status!audit approach {} ", auditEntity.getId(), auditEntity.getApprovalState());
            return auditMapper.updateAuditUnderReviewState(auditEntity);

        }else if(auditEntity.getApprovalState().equals(AuditStateEnum.AUDIT_SEND_BACK.getCode())){
            logger.info("BusinessId :{},Double check status!audit send {} ", auditEntity.getId(), auditEntity.getApprovalState());

            return auditMapper.updateAuditUnderReviewState(auditEntity);

        }else if(auditEntity.getApprovalState().equals(AuditStateEnum.AUDIT_UNDER_REVIEW.getCode())){
            logger.info("BusinessId :{},Double check status!audit under {} ", auditEntity.getId(), auditEntity.getApprovalState());

            return auditMapper.updateAuditSendBackState(auditEntity);

        }
        logger.info("BusinessId :{},Double check status!ApprovalState {} ", auditEntity.getId(), auditEntity.getApprovalState());
        return auditMapper.updateAuditState(auditEntity);
    }

    /**
     * 删除审核信息
     * @param businessId 业务ID
     * @param type 审核类型
     * @param user 当前登录用户
     * @return AuditDto 返回双人复核表对象
     */
    @Transactional(rollbackFor = {AuditException.class })
    @Override
    public void deleteAuditDoubleCheck(Long businessId,  Integer type, UserDto user) throws AuditException {

        AuditEntity query = new AuditEntity();
        query.setBusinessId(businessId);
        query.setType(type);
        try {
            List<AuditEntity> auditList = auditMapper.selectAuditList(query);
            if(!auditList.isEmpty()){
                auditMapper.deleteAuditById( auditList.get(0).getId());
                auditEverybodyMapper.deleteAuditEverybodyAuditId(auditList.get(0).getId());
            }
        }catch (Exception e){
            logger.error("Audit save is error");
            throw new AuditException(e);
        }

    }

    /**
     * 获取审批最新一条数据
     * @param auditQueryDto
     * @return
     */
    @Override
    public AuditDto selectAuditByLastOne(AuditQueryDto auditQueryDto) {
        AuditEntity query = BeanUtils.copy(auditQueryDto, AuditEntity.class);
        AuditEntity auditList = auditMapper.selectAuditByLastOne(query);
        return BeanUtils.copy(auditList,AuditDto.class) ;
    }
    /**
     * 获取双人复核任务审批轨迹信息
     * @param businessId
     * @return
     */
    @Override
    public TrackDto listDoubleCheckTrack(Long businessId) {

        List<DoubleCheckTrackDto>  deleteAuditDoubleCheckList=new ArrayList<>();
        AuditQueryDto auditQueryDto= new AuditQueryDto();
        auditQueryDto.setBusinessId(businessId);
        List<AuditDto> auditDtoList  = selectAuditList(auditQueryDto);
        for(AuditDto auditDto: auditDtoList){
            List<DoubleCheckTrackDto> deleteAuditDoubleList = auditInteract.listDoubleCheckTrack(auditDto.getAprvWorkitemId());
            for (DoubleCheckTrackDto doubleCheckTrackDto:deleteAuditDoubleList){
                doubleCheckTrackDto.setAuditType(auditDto.getType());
                deleteAuditDoubleCheckList.add(doubleCheckTrackDto);
            }
        }

        return   approvalTrackData(deleteAuditDoubleCheckList);
    }


    /**
     * 组织多条 审批数据
     * @param deleteAuditDoubleCheckList  双人复核任务审批轨迹信息
     * @return 更改结果
     */
    public TrackDto  approvalTrackData(List<DoubleCheckTrackDto>  deleteAuditDoubleCheckList) {
        TrackDto trackDto = new TrackDto();
        List<ApprovalTrackDto> approvalTrackList =new ArrayList<>();
        for(DoubleCheckTrackDto doubleCheckTrackDto:deleteAuditDoubleCheckList){
            //1添加标题
            trackDto.setTaskSubject(AuditTypeConstants.AUDIT_PROCESS_RECORD);
            //审批记录明细
            for(com.ideal.tools.model.interaction.TrackDto track: doubleCheckTrackDto.getTrackApiDtos()){
                ApprovalTrackDto approvalTrackDto = new ApprovalTrackDto();
                /**
                 * 发起人
                 */
                approvalTrackDto.setOriginatorName(doubleCheckTrackDto.getOriginatorName()) ;

                /**
                 * 审批类型
                 */
                if (AuditTypeEnum.AUDIT_TOOLS_DRAFT_ONE.getCode().equals(doubleCheckTrackDto.getAuditType())) {
                    approvalTrackDto.setAuditType(AuditTypeConstants.TOOL_PUBLISH_AUDIT);
                } else {
                    approvalTrackDto.setAuditType(AuditTypeEnum.getDescByCode(doubleCheckTrackDto.getAuditType()));
                }

                /**
                 * 发起时间
                 */
                approvalTrackDto.setOriginateTime(doubleCheckTrackDto.getOriginateTime());
                /**
                 * 审批节点阶段名
                 */
                approvalTrackDto.setNodeName(track.getNodeName());

                approvalTrackDto.setActionByUserName(track.getActionByUserName());
                /**
                 * 审核状态
                 */
                approvalTrackDto.setApprovalStateForDisplay(track.getApprovalStateForDisplay());


                approvalTrackDto.setAuditTime(track.getAuditTime());
                StringBuilder auditorName = new StringBuilder();
                if(null!=track.getTrackAuditorsApiDtos()){
                    for (TrackAuditorsDto trackAuditorsDto: track.getTrackAuditorsApiDtos()) {

                        auditorName.append(",").append(trackAuditorsDto.getAuditorName());

                    }

                    StringBuilder sb = new StringBuilder(auditorName.toString());
                    int index = sb.indexOf(","); // 找到第一个逗号的索引
                    if (index != -1) {
                        sb.deleteCharAt(index); // 删除第一个逗号
                    }
                    auditorName = new StringBuilder(sb.toString());

                }

                /**
                 * 审核人
                 */
                approvalTrackDto.setAuditorName(auditorName.toString());
                approvalTrackList.add(approvalTrackDto);
            }
        }
        //按发起时间升序排序
        sortApprovalTrackList(approvalTrackList);
        trackDto.setApprovalTrackList(approvalTrackList);
        return trackDto;
    }

    @Transactional(rollbackFor = {AuditException.class })
    @Override
    public Long withDrawDoubleCheck(AuditDto auditDto) throws AuditException {
        Long auditPush=-1L;
        //发送双人复核撤回
        if(auditDto == null){
            logger.error("Double withdraw push approval params is null !");
            return auditPush;
        }
        AuditDto dto = com.ideal.common.util.BeanUtils.copy(auditDto,AuditDto.class);
        auditPush = auditInteract.withdrawPush(auditDto.getApplyName(),auditDto.getAprvWorkitemId());

        logger.info("send withDraw {} to double service end",auditDto.getId());

        //回更双人复核状态
        if(auditPush!=-1L){
            logger.info("update audit withDraw approvalState is {} start", AuditStateEnum.AUDIT_WITHDRAW.getCode());
            dto.setAprvWorkitemId(auditPush);
            dto.setApprovalState(Math.toIntExact(AuditStateEnum.AUDIT_WITHDRAW.getCode()));
            //暂时使用服务器时间，后续需要使用数据库时间
            Date data = new Date(System.currentTimeMillis());
            dto.setAuditTime(data);

            AuditEntity auditEntity = new AuditEntity();
            org.springframework.beans.BeanUtils.copyProperties(dto, auditEntity);
            auditMapper.updateAudit(auditEntity);

            logger.info("update audit withDraw approvalState is {} end",AuditStateEnum.AUDIT_WITHDRAW.getCode());
        }
        return auditPush;
    }
    /**
     * 审批轨迹信息按发起时间升序排序
     * @param approvalTrackList  双人复核任务审批轨迹信息
     * @return
     */
    private void sortApprovalTrackList(List<ApprovalTrackDto> approvalTrackList) {
        // 定义排序规则-按发起时间升序排序，并将null值放在最后
        Comparator<ApprovalTrackDto> comparator = Comparator
                .comparing(ApprovalTrackDto::getOriginateTime, Comparator.nullsLast(Comparator.naturalOrder()))
                .thenComparing(dto -> dto.getOriginateTime() == null ? extractNodeNumber(dto.getNodeName()) : null, Comparator.nullsFirst(Integer::compareTo));
        approvalTrackList.sort(comparator);
    }
    /**
     * 审批节点阶段名-按审批节点排序
     * @param nodeName  审批节点阶段名
     * @return 结果
     */
    public Integer extractNodeNumber(String nodeName) {
        if (nodeName == null || !nodeName.contains("第")) {
            return null;
        }
        String numberPart = nodeName.replaceAll("[^0-9]", "");
        return numberPart.isEmpty() ? null : Integer.parseInt(numberPart);
    }
}

package com.ideal.tools.service.interaction;

import com.github.pagehelper.PageInfo;
import com.ideal.common.util.BeanUtils;
import com.ideal.system.api.IUserInfo;


import com.ideal.system.dto.UserInfoApiDto;
import com.ideal.system.dto.UserInfoQueryDto;
import com.ideal.tools.model.dto.UserInfoDto;
import com.ideal.tools.model.interaction.UserInfoSearchDto;
import org.springframework.stereotype.Component;

import java.util.List;

import static com.ideal.tools.common.AutoDevPageDataUtils.getPageInfo;


/**
 * 与平台管理服务对接审批人列表
 */
@Component
public class UserInfoInteract {
    private final IUserInfo userInfo;
    public UserInfoInteract(IUserInfo userInfo) {
        this.userInfo = userInfo;
    }

    /**
     * 根据权限编码获取人员信息集合
     * @param permissionCode 权限编码
     * @return 获取到的业务系统Id列表
     */
    public List<UserInfoDto> queryUserInfoListByPermissionCode(String permissionCode){
        List<UserInfoApiDto> userInfoApiList = userInfo.queryUserInfoListByPermissionCode(permissionCode);
        return BeanUtils.copy(userInfoApiList, UserInfoDto.class);
    }

    /**
     * 获取用户人员信息集合
     * @param userInfoSearchDto 用户查询实体
     * @return 用户人员信息集合
     */
    public PageInfo<UserInfoDto> getUserInfoByLoginNameForApi(UserInfoSearchDto userInfoSearchDto){
        UserInfoApiDto userInfoApiDto = new UserInfoApiDto();
        if (userInfoSearchDto.getLoginName() != null
                || userInfoSearchDto.getFullName() != null) {
            userInfoApiDto = new UserInfoApiDto();
            userInfoApiDto.setLoginName(userInfoSearchDto.getLoginName());
            userInfoApiDto.setFullName(userInfoSearchDto.getFullName());
        }
        UserInfoQueryDto userInfoQueryDto = BeanUtils.copy(userInfoSearchDto, UserInfoQueryDto.class);
        userInfoQueryDto.setQueryParam(userInfoApiDto);
        PageInfo<UserInfoApiDto> userInfoApiDtoPageInfo = userInfo.getUserInfoByLoginNameForApi(userInfoQueryDto);
        List<UserInfoApiDto> userInfoApiDtoList = userInfoApiDtoPageInfo.getList();
        List<UserInfoDto> userInfoDtoList = BeanUtils.copy(userInfoApiDtoList, UserInfoDto.class);
        return getPageInfo(userInfoApiDtoPageInfo,userInfoDtoList);
    }

}

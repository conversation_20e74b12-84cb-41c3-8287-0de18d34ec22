package com.ideal.tools.controller;

import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.system.common.component.aop.MethodPermission;
import com.ideal.tools.common.Constants;
import com.ideal.tools.common.ControlsTimeUtil;
import com.ideal.tools.common.EasyExcelUtil;
import com.ideal.tools.core.web.BaseController;
import com.ideal.tools.model.bean.DevelopmentReportExcle;
import com.ideal.tools.model.dto.*;
import com.ideal.tools.model.enums.*;
import com.ideal.tools.service.IDevelopmentReportService;
import com.ideal.tools.service.ILogAuditService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;

/**
 * 工具开发报表Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("${app.script-tools-url:}/tools/developmentReport")
public class DevelopmentReportController extends BaseController {
    private final IDevelopmentReportService developmentReportService;
    private final ILogAuditService logAuditService;

    public DevelopmentReportController(IDevelopmentReportService developmentReportService,ILogAuditService logAuditService) {
        this.developmentReportService = developmentReportService;
        this.logAuditService =  logAuditService;
    }

    /**
     * 查询工具开发报表列表
     *
     * @param tableQueryDto 查询条件
     * @return 查询结果
     */
    @PostMapping("/list")
    public R<PageInfo<DevelopmentReportResultDto>> list(@RequestBody TableQueryDto<DevelopmentReportQueryDto> tableQueryDto) {
        PageInfo<DevelopmentReportResultDto> pages =null;
        try{
            pages = developmentReportService.selectDevelopmentReportList(
                    tableQueryDto.getQueryParam(),
                    tableQueryDto.getPageNum(),
                    tableQueryDto.getPageSize()
            );
            logger.info("success to query Toolbox development reports ");
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, pages, ConstantsEnum.LIST_SUCCESS.getDesc());
        }catch (Exception e){
            logger.error("Failed to query Toolbox development reports !{}",e.getMessage(), e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE,pages , ConstantsEnum.LIST_FAIL.getDesc());
        }
    }

    /**
     * 新增保存工具开发报表       沒用
     *
     * @param developmentReportDto 添加数据
     * @return 添加结果
     */
    @PostMapping("/save")
    public R<Object> save(@RequestBody DevelopmentReportDto developmentReportDto) {
        try {
            developmentReportService.insertDevelopmentReport(developmentReportDto);
            logger.info("insert developmentReport success!");
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, "成功", ConstantsEnum.SAVE_SUCCESS.getDesc());
        }catch (Exception e) {
            logger.error("insert developmentReport failed!", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, "失败", ConstantsEnum.SAVE_FAIL.getDesc() );
        }
    }

    /**
     * 修改保存工具开发报表      沒用
     *
     * @param developmentReportDto 更新数据
     * @return 更新结果
     */
    @PostMapping("/update")
    public R<Object> update(@RequestBody DevelopmentReportDto developmentReportDto) {
        try {
            developmentReportService.updateDevelopmentReport(developmentReportDto);
            logger.info("update developmentReport success!");
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, "成功", ConstantsEnum.UPDATE_SUCCESS.getDesc());
        }catch (Exception e) {
            logger.error("update developmentReport failed!", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, "失败", ConstantsEnum.UPDATE_FAIL.getDesc() );
        }
    }
    /**
     * 工具开发报表导出
     *
     * @param queryDto 导出检索条件
     * @return 结果
     */
    @PostMapping("/exportDevelopmentReportData")
    @MethodPermission("@dp.hasBtnPermission('exportDevelopmentReport')")
    public void exportDevelopmentReportData(HttpServletResponse response, @RequestBody DevelopmentReportQueryDto queryDto) {
        try{
            List<DevelopmentReportExcle> list = developmentReportService.exportDevelopmentReportData(queryDto);
            // 使用 EasyExcel 将数据写入响应输出流
            String fileName = OperatModuleEnum.TOOL_DEVELOPMENT_REPORT.getRemark() + ControlsTimeUtil.formatDateByFormat(new Date(), "YYYY-MM-dd hh_mm_ss");
            EasyExcelUtil.writeExcel(response, list, fileName, OperatModuleEnum.TOOL_DEVELOPMENT_REPORT.getRemark(), DevelopmentReportExcle.class);
            String operatingContent = CommonTypeEnum.EXPORT.getName() + fileName + "到本地";
            String resultDesc = OperatModuleEnum.TOOL_DEVELOPMENT_REPORT.getRemark() + OperatTypeEnum.DEVELOPMENT_REPORT_EXPORT.getRemark();
            LogAuditDto logAuditDto = new LogAuditDto(OperatModuleEnum.TOOL_DEVELOPMENT_REPORT.getCode(), OperatTypeEnum.DEVELOPMENT_REPORT_EXPORT.getCode(), operatingContent);
            logAuditDto.setResult(OperatResultEnum.SUCCESS.getCode());
            logAuditDto.setResultDesc(resultDesc + OperatResultEnum.SUCCESS.getDesc());
            logAuditService.insertLogAudit(logAuditDto);
            logger.info("exportDevelopmentReportData success");
        } catch (Exception e) {
            logger.error("exportDevelopmentReportData error", e);
        }
    }
}

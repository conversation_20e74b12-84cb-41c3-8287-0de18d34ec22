package com.ideal.tools.controller;


import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.system.common.component.aop.MethodPermission;
import com.ideal.tools.common.Constants;
import com.ideal.tools.core.web.BaseController;
import com.ideal.tools.exception.ToolsException;
import com.ideal.tools.model.enums.ConstantsEnum;
import org.springframework.web.bind.annotation.*;
import com.ideal.tools.model.dto.ToolsFilesDto;
import com.ideal.tools.model.dto.ToolsFilesQueryDto;
import com.ideal.tools.service.IToolsFilesService;

import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;

/**
 * 描述工具保存附件Controller
 *
 * <AUTHOR>
 * @date 2024-06-27
 */
@RestController
@RequestMapping("${app.script-tools-url:}/tools/files")
@MethodPermission("@dp.hasBtnPermission('tool-execution')")
public class ToolsFilesController extends BaseController {


    private final IToolsFilesService toolsFilesService;

    public ToolsFilesController(IToolsFilesService toolsFilesService) {
        this.toolsFilesService = toolsFilesService;
    }

    /**
     * 查询描述工具保存附件列表             沒用
     *
     * @param tableQueryDto 查询条件
     * @return 查询结果
     */
    @GetMapping("/list")
    public R<PageInfo<ToolsFilesDto>> list(TableQueryDto<ToolsFilesQueryDto> tableQueryDto) {
        PageInfo<ToolsFilesDto> list = toolsFilesService.selectToolsFilesList(
                tableQueryDto.getQueryParam(),
                tableQueryDto.getPageNum(),
                tableQueryDto.getPageSize()
        );
        return R.ok(list);
    }

    /**
     * 查询描述工具保存附件详细信息           沒用
     *
     * @param id 数据唯一标识
     * @return 查询结果
     */
    @GetMapping(value = "/get")
    public R<ToolsFilesDto> getAgentInfoInfo(@RequestParam(value = "id")Long id) {
        return R.ok(toolsFilesService.selectToolsFilesById(id));
    }

    /**
     * 新增保存描述工具保存附件
     *
     * @param file 添加数据
     * @return 添加结果
     */
    @PostMapping("/save")
    public R<ToolsFilesDto> save(@RequestParam("file") MultipartFile file,@RequestParam("type") Long type, HttpServletResponse response) {
        try {

          return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, toolsFilesService.saveToolsFiles(file,type ,getUser()),ConstantsEnum.SAVE_SUCCESS.getDesc());
        }catch (IOException | ToolsException e) {
            logger.error("New save file failed!", e);

            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE,e.getMessage());
        }
    }

    /**
     * 下载附件
     * @param id 附件id
     */
    @PostMapping("/downloadAttachment")
    public void downloadAttachment(@RequestBody Long id, HttpServletResponse response) {
        ToolsFilesDto toolsFilesDto = toolsFilesService.selectToolsFilesById(id);
        response.reset();
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            response.setContentType("application/octet-stream");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-disposition","attachment; filename=" + URLEncoder.encode(toolsFilesDto.getName(), "UTF-8"));
            outputStream.write(toolsFilesDto.getFiles());
        } catch (IOException e) {
            logger.error("tools downloadAttachment error:" ,e);
            response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
        }
    }
    /**
     * 修改保存描述工具保存附件         沒用
     *
     * @param toolsFilesDto 更新数据
     * @return 更新结果
     */
    @PostMapping("/update")
    public R<Void> update(@RequestBody ToolsFilesDto toolsFilesDto) {
        toolsFilesService.updateToolsFiles(toolsFilesDto);
        return R.ok();
    }



}

package com.ideal.tools.controller;


import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.system.common.component.aop.MethodPermission;
import org.springframework.web.bind.annotation.*;
import com.ideal.tools.model.dto.ToolsParamDto;
import com.ideal.tools.model.dto.ToolsParamQueryDto;
import com.ideal.tools.service.IToolsParamService;

/**
 * 工具参数Controller
 *
 * <AUTHOR>
 * @date 2024-06-26
 */
@RestController
@RequestMapping("${app.script-tools-url:}/tools/param")
@MethodPermission("@dp.hasBtnPermission('tool-execution')")
public class ToolsParamController {

    private final IToolsParamService toolsParamService;

    public ToolsParamController(IToolsParamService toolsParamService) {
        this.toolsParamService = toolsParamService;
    }

    /**
     * 查询工具参数列表         沒用
     *
     * @param tableQueryDto 查询条件
     * @return 查询结果
     */
    @GetMapping("/list")
    public R<PageInfo<ToolsParamDto>> list(TableQueryDto<ToolsParamQueryDto> tableQueryDto) {
        PageInfo<ToolsParamDto> list = toolsParamService.selectToolsParamList(
                tableQueryDto.getQueryParam(),
                tableQueryDto.getPageNum(),
                tableQueryDto.getPageSize()
        );
        return R.ok(list);
    }

    /**
     * 查询工具参数详细信息           沒用
     *
     * @param id 数据唯一标识
     * @return 查询结果
     */
    @GetMapping(value = "/get")
    public R<ToolsParamDto> getAgentInfoInfo(@RequestParam(value = "id")Long id) {
        return R.ok(toolsParamService.selectToolsParamById(id));
    }

    /**
     * 新增保存工具参数         沒用
     *
     * @param toolsParamDto 添加数据
     * @return 添加结果
     */
    @PostMapping("/save")
    public R<Void> save(@RequestBody ToolsParamDto toolsParamDto) {
        if (toolsParamService.insertToolsParam(toolsParamDto) > 0) {
            return R.ok();
        }
        return R.fail();
    }

    /**
     * 修改保存工具参数             沒用
     *
     * @param toolsParamDto 更新数据
     * @return 更新结果
     */
    @PostMapping("/update")
    public R<Void> update(@RequestBody ToolsParamDto toolsParamDto) {
        toolsParamService.updateToolsParam(toolsParamDto);
        return R.ok();
    }


    /**
     * 删除工具参数           沒用
     *
     * @param ids 主键列表
     * @return 删除结果
     */
    @PostMapping("/remove")
    public R<Void> remove(@RequestParam(value = "ids") Long[] ids) {
        toolsParamService.deleteToolsParamByIds(ids);
        return R.ok();
    }
}

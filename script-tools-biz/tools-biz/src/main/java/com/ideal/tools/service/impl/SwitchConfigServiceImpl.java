package com.ideal.tools.service.impl;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.ideal.common.util.BeanUtils;
import com.ideal.tools.common.AutoDevPageDataUtils;
import com.ideal.tools.mapper.SwitchConfigMapper;
import com.ideal.tools.model.dto.*;
import com.ideal.tools.model.entity.SwitchConfigEntity;
import com.ideal.tools.model.enums.SwitchConfigEnum;
import com.ideal.tools.model.enums.SwitchConfigValueEnum;
import com.ideal.tools.service.ISwitchConfigService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 工具接收报表自定义分类Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
@Service
public class SwitchConfigServiceImpl implements ISwitchConfigService {

    /**
     * 日志对象
     */
    protected Logger logger = LoggerFactory.getLogger(getClass());

    private final SwitchConfigMapper switchConfigMapper;

    public SwitchConfigServiceImpl(SwitchConfigMapper switchConfigMapper) {
        this.switchConfigMapper = switchConfigMapper;
    }

    /**
     * 查询开关配置自定义分类树结构列表
     *
     * @param query 开关配置自定义分类
     * @param pageNum 页码
     * @param pageSize 单页长度
     * @return 开关配置自定义分类
     */
    @Override
    public PageInfo<SwitchConfigResultDto> selectSwitchConfigList(SwitchConfigQueryDto query, Integer pageNum, Integer pageSize) {
        logger.info("SwitchConfig:Get all data");
        List<SwitchConfigResultDto> switchConfigQueryDtoList;
        PageMethod.startPage(pageNum, pageSize);
        List<SwitchConfigEntity> entity = switchConfigMapper.selectSwitchConfigList(query);
        switchConfigQueryDtoList = BeanUtils.copy(entity,SwitchConfigResultDto.class);
        return AutoDevPageDataUtils.getPageInfo(switchConfigQueryDtoList,pageNum,pageSize);
    }

    /**
     * 查询开关配置自定义分类下拉框
     *
     * @return 查询结果
     */
    @Override
    public List<SwitchConfigValueDto> selectSwitchConfigData() {
        return SwitchConfigValueEnum.getSwitchConfigEnum();
    }

    /**
     * 查询开关配置value自定义分类下拉框
     *
     * @return 查询结果
     */
    @Override
    public List<SwitchConfigValueDto> selectSwitchConfigValueData(Integer code) {
        return SwitchConfigValueEnum.getSwitchConfigValue(code);
    }

    /**
     * 开关配置插入自定义
     *
     * @param switchConfigFormDto 开关配置自定义分类
     */
    @Override
    public void insertSwitchConfig(SwitchConfigFormDto switchConfigFormDto) {
        SwitchConfigDto switchConfigDto = new SwitchConfigDto();
        switchConfigDto.setName(SwitchConfigEnum.SWITCH_IMPORTANT.getDesc());
        switchConfigDto.setKey(SwitchConfigEnum.SWITCH_IMPORTANT.getName());
        switchConfigDto.setValue(switchConfigFormDto.getImportantValue());
        SwitchConfigEntity entity = BeanUtils.copy(switchConfigDto, SwitchConfigEntity.class);
        switchConfigMapper.insertSwitchConfig(entity);
        SwitchConfigDto switchConfigDto1 = new SwitchConfigDto();
        switchConfigDto1.setName(SwitchConfigEnum.SWITCH_VIEW.getDesc());
        switchConfigDto1.setKey(SwitchConfigEnum.SWITCH_VIEW.getName());
        switchConfigDto1.setValue(switchConfigFormDto.getViewValue());
        logger.info("switchConfig:Insert switchConfig Report Data");
        SwitchConfigEntity entity1 = BeanUtils.copy(switchConfigDto1, SwitchConfigEntity.class);
        switchConfigMapper.insertSwitchConfig(entity1);
    }

    /**
     * 修改开关配置
     *
     * @param switchConfigFormDto 更新数据
     * @return 更新结果
     */
    @Override
    public int updateSwitchConfig(SwitchConfigFormDto switchConfigFormDto) {
        int i = 0;
        i = insertOrUpdate(i, SwitchConfigEnum.SWITCH_IMPORTANT, switchConfigFormDto.getImportantValue());
        i = insertOrUpdate(i, SwitchConfigEnum.SWITCH_VIEW, switchConfigFormDto.getViewValue());
        i = insertOrUpdate(i, SwitchConfigEnum.BATCH_PATH, switchConfigFormDto.getBatchPath());
        return i;
    }

    private int insertOrUpdate(int i, SwitchConfigEnum switchImportant, String importantValue) {
        SwitchConfigDto switchConfigDto = new SwitchConfigDto();
        switchConfigDto.setName(switchImportant.getDesc());
        switchConfigDto.setKey(switchImportant.getName());
        switchConfigDto.setValue(importantValue);
        SwitchConfigEntity switchConfig = BeanUtils.copy(switchConfigDto, SwitchConfigEntity.class);
        SwitchConfigQueryDto switchConfigQueryDto = new SwitchConfigQueryDto();
        switchConfigQueryDto.setKey(switchImportant.getName());
        List<SwitchConfigEntity> switchConfigEntities = switchConfigMapper.selectSwitchConfigList(switchConfigQueryDto);
        if (switchConfigEntities.isEmpty()) {
            //新增
            switchConfigMapper.insertSwitchConfig(switchConfig);
        } else {
            i = switchConfigMapper.updateSwitchConfigByKey(switchConfig);
        }
        return i++;
    }

    /**
     * 根据ids删除开关配置
     *
     * @param ids 开关配置id数组
     * @return 删除结果
     */
    @Override
    public int deleteSwitchConfigByIds(Long[] ids) {
        return switchConfigMapper.deleteSwitchConfigByIds(ids);
    }

    /**
     * 查询开关配置value根据key
     *
     * @return 查询结果
     */
    @Override
    public Object selectSwitchConfigValueByKey(String key) {
        Object result = new Object();
        SwitchConfigQueryDto query = new SwitchConfigQueryDto();
        query.setKey(key);
        List<SwitchConfigEntity> switchConfigEntities = switchConfigMapper.selectSwitchConfigList(query);
        if (!switchConfigEntities.isEmpty()) {
            SwitchConfigEntity switchConfig = switchConfigEntities.get(0);
            if (key.equals(SwitchConfigEnum.SWITCH_IMPORTANT.toString())){
                //存在，根据value找code
                String value = switchConfig.getValue();
                result = SwitchConfigValueEnum.getCodeByName(value);
            }
            if (key.equals(SwitchConfigEnum.SWITCH_VIEW.toString())){
                String value = switchConfig.getValue();
                boolean contains = value.contains(",");
                result = new ArrayList<String>();
                if (contains) {
                    result = Arrays.asList(value.split(","));
                }else {
                    ((ArrayList<String>) result).add(value);
                }
            }
            if (key.equals(SwitchConfigEnum.BATCH_PATH.toString())){
                String value = switchConfig.getValue();
                boolean contains = value.contains(",");
                result = new ArrayList<String>();
                if (contains) {
                    result = Arrays.asList(value.split(","));
                }else {
                    ((ArrayList<String>) result).add(value);
                }
            }
        }else {
            if (key.equals(SwitchConfigEnum.SWITCH_IMPORTANT.toString())){
                result = 0;
            }
            if (key.equals(SwitchConfigEnum.SWITCH_VIEW.toString())){
                result=new ArrayList<>();
            }
            if (key.equals(SwitchConfigEnum.BATCH_PATH.toString())){
                result=new ArrayList<>();
            }
        }
        return result;
    }


    /**
     * 开关配置插入自定义form表单
     *
     * @param switchConfigListDto 开关配置自定义分类
     */
    @Override
    public void insertSwitchConfigList(SwitchConfigListDto switchConfigListDto) {
        for (SwitchConfigDto switchConfigDto : switchConfigListDto.getList()) {
            logger.info("switchConfig:Insert switchConfig Report Data");
            if (switchConfigDto.getName().equals(SwitchConfigEnum.SWITCH_VIEW.getDesc())){
                switchConfigDto.setKey(SwitchConfigEnum.SWITCH_VIEW.getName());
            }
            if (switchConfigDto.getName().equals(SwitchConfigEnum.SWITCH_IMPORTANT.getDesc())){
                switchConfigDto.setKey(SwitchConfigEnum.SWITCH_IMPORTANT.getName());
            }
            SwitchConfigEntity entity = BeanUtils.copy(switchConfigDto, SwitchConfigEntity.class);
            switchConfigMapper.insertSwitchConfig(entity);
        }
    }

    @Override
    public SwitchConfigFormDto listValue() {
        SwitchConfigFormDto switchConfigFormDto = new SwitchConfigFormDto();
        List<SwitchConfigEntity> entity = switchConfigMapper.selectSwitchConfigList(new SwitchConfigQueryDto());
        if (!entity.isEmpty()) {
            for (SwitchConfigEntity switchConfig : entity) {
                if (switchConfig.getKey().equals(SwitchConfigEnum.SWITCH_IMPORTANT.getName())) {
                    switchConfigFormDto.setImportantValue(switchConfig.getValue());
                }
                if (switchConfig.getKey().equals(SwitchConfigEnum.SWITCH_VIEW.getName())) {
                    switchConfigFormDto.setViewValue(switchConfig.getValue());
                }
                if (switchConfig.getKey().equals(SwitchConfigEnum.BATCH_PATH.getName())) {
                    switchConfigFormDto.setBatchPath(switchConfig.getValue());
                }
            }
        }
        return switchConfigFormDto;
    }
}

package com.ideal.tools.service;

import com.ideal.tools.exception.InteractServiceException;
import com.ideal.tools.exception.ToolsException;
import com.ideal.tools.model.dto.*;
import com.github.pagehelper.PageInfo;
import com.ideal.tools.model.entity.ToolsAgentInfoEntity;

import java.util.List;

/**
 * 设备Service接口
 *
 * <AUTHOR>
 * @date 2024-06-27
 */
public interface IToolsAgentInfoService {
    /**
     * 查询设备
     *
     * @param id 设备主键
     * @return 设备
     */
    ToolsAgentInfoDto selectToolsAgentInfoById(Long id);

    /**
     * 查询设备列表
     *
     * @param toolsAgentInfoQueryDto 设备
     * @param pageNum 页码
     * @param pageSize 单页长度
     * @return 设备集合
     */
    PageInfo<ToolsAgentInfoDto> selectToolsAgentInfoList(ToolsAgentInfoQueryDto toolsAgentInfoQueryDto, Integer pageNum, Integer pageSize);

    /**
     * 新增设备
     *
     * @param toolsAgentInfoDto 设备
     * @return 结果
     */
    int insertToolsAgentInfo(ToolsAgentInfoDto toolsAgentInfoDto);

    /**
     * 修改设备
     *
     * @param toolsAgentInfoDto 设备
     * @return 结果
     */
    int updateToolsAgentInfo(ToolsAgentInfoDto toolsAgentInfoDto);

    /**
     * 批量删除设备
     *
     * @param ids 需要删除的设备主键集合
     * @return 结果
     */
    int deleteToolsAgentInfoByIds(Long[] ids);
    /**
     *
     * agent列表(分页)
     *
     * @param agentQueryDto  查询参数
     * @return 结果
     */
    PageInfo<ToolsAgentResultDto> selectStandardTaskComputerIdLists(AgentQueryDto agentQueryDto,Long userId, Integer pageNum, Integer pageSize)throws InteractServiceException;
    /**
     *
     * agent列表(分页)
     *
     * @param agentQueryDto  查询参数
     * @return 结果
     */
    PageInfo<ToolsAgentResultDto> selectCurrentToolIdBingComputerIdLists(AgentQueryDto agentQueryDto,Long userId, Integer pageNum, Integer pageSize)throws InteractServiceException;
    /**
     * 新增脚本agent多个关系
     * @param toolsId 工具id
     * @param toolsAgentInfoList 双人复核审批人多个关系
     * @return 结果
     */
    void batchInsertToolsAgent(Long toolsId, List<ToolsAgentInfoDto> toolsAgentInfoList)  throws ToolsException;



    /**
     * 批量删除设备
     *
     * @param tdToolsId 需要删除的设备工具主键
     * @return 结果
     */
    int deleteAgentToolId(Long tdToolsId)  throws ToolsException;

    /**
     * 根据工具ID、agentIp查询设备IP数据
     *
     * @param toolsAgentInfo 设备信息
     * @return 结果
     */
    List<String> selectAgentIpByToolIdAndAgentIp(ToolsAgentInfoEntity toolsAgentInfo);

    /**
     * 根据工具ID查询绑定的agent数量
     *
     * @param tdToolsId 工具id
     * @return 工具ID对应绑定的agent数量
     */
    Integer selectAgentCountByToolId(Long tdToolsId);

    /**
     * 查询工具绑定的agent信息
     * @param agentQueryDto  tdToolsId 工具id，computerIp 设备IP ， 结束IP  agentIpEnd  开始IP agentIpStart
     * @return
     * @throws InteractServiceException
     */
    List<ToolsAgentInfoEntity> selectToolsAgentInfoList(AgentQueryDto agentQueryDto) throws InteractServiceException;
    /**
     * 获取组合调脚本/组合 绑定agent信息
     * @param agentQueryDto  tdToolsId 工具id，computerIp 设备IP 
     * @return
     * @throws InteractServiceException
     */
    List<ToolsAgentInfoEntity> selectToolsAgentInfoListb(AgentQueryDto agentQueryDto) throws InteractServiceException;

	/**
	 * 
	 * @Description 去掉平台管理中删除的agent
	 * @param toolsAgentInfoList
	 * @param agentQueryDto
	 * @param userDto
	 * @return
	 * @throws InteractServiceException
	 * <AUTHOR>
	 * @date 2025-04-10 07:03:53
	 */
	List<ToolsAgentInfoEntity> rmDeleteAgent(List<ToolsAgentInfoEntity> toolsAgentInfoList, AgentQueryDto agentQueryDto,
			Long userId) throws InteractServiceException;
}

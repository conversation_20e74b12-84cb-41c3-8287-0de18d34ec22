package com.ideal.tools.mapper;

import com.ideal.tools.model.entity.AuditEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

/**
 * 双人复核服务关系Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-25
 */
@Repository("toolsAuditMapper")
public interface AuditMapper {
    /**
     * 查询双人复核服务关系
     *
     * @param id 双人复核服务关系主键
     * @return 双人复核服务关系
     */
    AuditEntity selectAuditById(Long id);

    /**
     * 查询双人复核服务关系列表
     *
     * @param audit 双人复核服务关系
     * @return 双人复核服务关系集合
     */
    List<AuditEntity> selectAuditList(AuditEntity audit);

    /**
     * 新增双人复核服务关系
     *
     * @param audit 双人复核服务关系
     * @return 结果
     */
    int insertAudit(AuditEntity audit);

    /**
     * 修改双人复核服务关系
     *
     * @param audit 双人复核服务关系
     * @return 结果
     */
    int updateAudit(AuditEntity audit);

    /**
     * 删除双人复核服务关系
     *
     * @param id 双人复核服务关系主键
     * @return 结果
     */
    int deleteAuditById(Long id);

    /**
     * 批量删除双人复核服务关系
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteAuditByIds(Long[] ids);


    /**
     * 修改双人复核服务关系
     *
     * @param auditEntity 双人复核服务关系
     * @return 结果
     */
    public int updateAuditState(AuditEntity auditEntity);


    /**
     * 修改运行中的双人复核服务关系
     *
     * @param auditEntity 双人复核服务关系
     * @return 结果
     */
    public int updateAuditUnderReviewState(AuditEntity auditEntity);


    /**
     * 修改回退的双人复核服务关系
     *
     * @param auditEntity 双人复核服务关系
     * @return 结果
     */
    public int updateAuditSendBackState(AuditEntity auditEntity);

    /**
     * 获取审批最新一条数据
     * @param query
     * @return
     */
    AuditEntity selectAuditByLastOne(AuditEntity query);

    int updateStateByToolids(@Param("state")int state, @Param("toolIds")ArrayList<Long> auditLists);

}

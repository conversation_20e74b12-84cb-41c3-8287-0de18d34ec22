package com.ideal.tools.model.dto;

import java.util.List;

public class ExecuteMonitorProjectDto {
    /**
     * 主键id
     */
    private Long id;
    /**
     * 子流程
     */
    private List<ExecuteMonitorProjectDto> children;
    /**
     * 父节点
     */
    private Long parentId;
    /**
     * 节点名称
     */
    private String nodeName;
    /**
     * 节点名称
     */
    private String actName;
    /**
     * 节点主键id
     */
    private Long nodeId;
    /**
     * 节点状态
     * 根节点：0 运行中 1已完成 2未运行 3异常 4 终止
     */
    private Long nodeStatus;
    /**
     * 节点状态(活动表原值状态)
     */
    private String actStatus;
    /**
     * 流程id
     */
    private Long projectId;
    /**
     * 流程名称
     */
    private String projectName;
    /**
     * 节点工具类型：1 描述工具，2 组合工具，3 脚本工具
     */
    private Long toolType;
    /**
     * 工作流ID(查询流程图)
     */
    private Long workflowId;
    /**
     * 工作流ID（查询子节点）
     */
    private Long flowId;
    /**
     * 工作流名称
     */
    private String workflowName;
    /** 当前节点对应的工具id */
    private Long toolId;

    /**
     * 流程图xml
     */
    private String xml;

    public String getActName() {
        return actName;
    }

    public void setActName(String actName) {
        this.actName = actName;
    }

    public String getActStatus() {
        return actStatus;
    }

    public void setActStatus(String actStatus) {
        this.actStatus = actStatus;
    }

    public Long getNodeId() {
        return nodeId;
    }

    public void setNodeId(Long nodeId) {
        this.nodeId = nodeId;
    }

    public Long getFlowId() {
        return flowId;
    }

    public void setFlowId(Long flowId) {
        this.flowId = flowId;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public List<ExecuteMonitorProjectDto> getChildren() { return children; }
    public void setChildren(List<ExecuteMonitorProjectDto> value) { this.children = value; }
    public Long getToolId() {
        return toolId;
    }

    public void setToolId(Long toolId) {
        this.toolId = toolId;
    }
    public String getNodeName() { return nodeName; }
    public void setNodeName(String value) { this.nodeName = value; }

    public Long getNodeStatus() { return nodeStatus; }
    public void setNodeStatus(Long value) { this.nodeStatus = value; }
    public Long getProjectId() { return projectId; }
    public void setProjectId(Long value) { this.projectId = value; }

    public String getProjectName() { return projectName; }
    public void setProjectName(String value) { this.projectName = value; }
    public Long getToolType() { return toolType; }
    public void setToolType(Long value) { this.toolType = value; }

    public Long getWorkflowId() { return workflowId; }
    public void setWorkflowId(Long value) { this.workflowId = value; }

    public String getWorkflowName() { return workflowName; }
    public void setWorkflowName(String value) { this.workflowName = value; }

    public String getXml() { return xml; }
    public void setXml(String value) { this.xml = value; }
}

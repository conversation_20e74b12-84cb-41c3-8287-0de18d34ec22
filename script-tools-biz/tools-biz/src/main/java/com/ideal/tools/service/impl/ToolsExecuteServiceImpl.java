package com.ideal.tools.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.ideal.common.dto.R;
import com.ideal.common.util.BeanUtils;
import com.ideal.common.util.PageDataUtil;
import com.ideal.script.dto.ScriptTaskApplyAgentApiDto;
import com.ideal.snowflake.util.SnowflakeIdWorker;
import com.ideal.tools.common.Constants;
import com.ideal.tools.common.ControlsTimeUtil;
import com.ideal.tools.config.BusinessConfig;
import com.ideal.tools.exception.*;
import com.ideal.tools.exception.AuditException;
import com.ideal.tools.exception.ExecuteMonitorException;
import com.ideal.tools.exception.ScriptToolsException;
import com.ideal.tools.exception.ToolsException;
import com.ideal.tools.mapper.AuditExecutAgentMapper;
import com.ideal.tools.mapper.CommonToolsAgentInfoMapper;
import com.ideal.tools.mapper.ToolsExecuteMapper;
import com.ideal.tools.mapper.ToolsInfoMapper;
import com.ideal.tools.model.bean.ExecuteAuditBean;
import com.ideal.tools.model.dto.*;
import com.ideal.tools.model.entity.*;
import com.ideal.tools.model.enums.*;
import com.ideal.tools.model.interaction.ScriptExecuteDto;
import com.ideal.tools.service.*;
import com.ideal.tools.service.interaction.StudioInteract;
import com.ideal.tools.service.interaction.SystemDataInteract;
import com.ideal.tools.service.interaction.UserInfoInteract;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

import static com.ideal.tools.common.AutoDevPageDataUtils.getPageInfo;
import static com.ideal.tools.model.enums.AuditTypeEnum.getByCode;

/**
 * 工具执行前期Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-04
 */
@Service
public class ToolsExecuteServiceImpl implements IToolsExecuteService {
    /**
     * 日志对象
     */
    protected Logger logger = LoggerFactory.getLogger(getClass());

    private final ToolsExecuteMapper toolsExecuteMapper;
    //工具信息
    private final IToolsInfoService toolsInfoService;
    //高峰时期
    private final ITopTimeService topTimeService;
    //ecc白名单
    private final IEccWhiteService eccWhiteService;
    //执行agent
    private final IAuditExecutAgentService auditExecutAgentService;
    //执行参数
    private final IAuditExecutParamService auditExecutParamService;
    //开关配置
    private final ISwitchConfigService switchConfigService;
    //执行监控
    private final IExecuteMonitorService executeMonitorService;
    //配置获取
    private final BusinessConfig businessConfig;
    //工具信息
    private final ToolsInfoMapper toolsInfoMapper;
    //流程编排
    private final StudioInteract studioInteract;
    //工具操作校验
    private final IToolsOperateBaseService toolsOperateBaseService;
    /**
     * 审批执行agent
     */
    private final AuditExecutAgentMapper auditExecutAgentMapper;
    /**
     * 用户列表服务
     */
    private final UserInfoInteract userInfoInteract;
    /**
     * 业务系统服务
     */
    private final SystemDataInteract systemDataInteract;
    /**
     * 脚本服务
     */
    private final IToolsOperateService toolsOperateService;
    /**
     * 提交审核
     */
    private final IAuditService auditService;
    /**
     * 使用报表
     */
    private final IUsageReportService usageReportService;
    /**
     * 工具绑定agent
     */
    private final IToolsAgentInfoService toolsAgentInfoService;

    /**
     * 审计日志
     */
    private final ILogAuditService logAuditService;
    private final CommonToolsAgentInfoMapper commonToolsAgentInfoMapper;

    public ToolsExecuteServiceImpl(ToolsExecuteMapper toolsExecuteMapper
            , IToolsInfoService toolsInfoService, ITopTimeService topTimeService
            , IEccWhiteService eccWhiteService, UserInfoInteract userInfoInteract
            , SystemDataInteract systemDataInteract, IAuditService auditService
            , IAuditExecutAgentService auditExecutAgentService, IAuditExecutParamService auditExecutParamService
            , ISwitchConfigService switchConfigService, IExecuteMonitorService executeMonitorService
            , IToolsOperateService toolsOperateService, BusinessConfig businessConfig,
                                   ILogAuditService logAuditService, AuditExecutAgentMapper auditExecutAgentMapper
            ,IUsageReportService usageReportService,ToolsInfoMapper toolsInfoMapper, StudioInteract studioInteract
            ,IToolsOperateBaseService toolsOperateBaseService,IToolsAgentInfoService toolsAgentInfoService,CommonToolsAgentInfoMapper commonToolsAgentInfoMapper) {
        this.toolsExecuteMapper = toolsExecuteMapper;
        this.toolsInfoService = toolsInfoService;
        this.topTimeService = topTimeService;
        this.eccWhiteService = eccWhiteService;
        this.userInfoInteract = userInfoInteract;
        this.systemDataInteract = systemDataInteract;
        this.auditService = auditService;
        this.auditExecutAgentService = auditExecutAgentService;
        this.auditExecutParamService = auditExecutParamService;
        this.switchConfigService = switchConfigService;
        this.executeMonitorService = executeMonitorService;
        this.toolsOperateService = toolsOperateService;
        this.businessConfig = businessConfig;
        this.logAuditService = logAuditService;
        this.auditExecutAgentMapper = auditExecutAgentMapper;
        this.usageReportService = usageReportService;
        this.toolsInfoMapper = toolsInfoMapper;
        this.studioInteract = studioInteract;
        this.toolsOperateBaseService = toolsOperateBaseService;
        this.toolsAgentInfoService = toolsAgentInfoService;
        this.commonToolsAgentInfoMapper = commonToolsAgentInfoMapper;
    }


    /**
     * 获取展示执行展示按钮
     *
     * @param executAuditQueryDto toolsid  工具id， execFrom 来源   4.场景工具 5.告警诊断 7.工具执行 ，userId 用户id
     *                            ,currentIp 当前ip ,auditId 审批id
     * @return 0.不允许执行 1.直接执行，2.弹窗倒计时10秒冷静期，3.双人复合，4.弹框确认5秒执行，5.审核中,6.审批失败
     */
    @Override
    public R<ExecuteAuditResultDto> getExecutButtonStatus(ExecutAuditQueryDto executAuditQueryDto) {
        ExecuteAuditResultDto executeAuditResultDto = new ExecuteAuditResultDto();
        //获取工具信息
        ToolsInfoCategoryEntity toolsInfo = toolsInfoService.selectToolsInfoCategoryById(executAuditQueryDto.getToolsId());
        //没有获取当前工具
        if (toolsInfo == null || toolsInfo.getId() == null) {
            executeAuditResultDto.setButtenType(ToolsExecuteButtonEnum.NOT_EXECUTABLE.getCode());
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, executeAuditResultDto, ConstantsEnum.TOOL_NOT_EXIST.getDesc());
        }
        //是否开启工具执行校验，未开启可以直接执行
        boolean toolsExecuteCheckSwitch = businessConfig.isToolsExecuteCheck();//判断是否为空数据
        if(!toolsExecuteCheckSwitch || toolsInfo.getType().equals(ToolsTypeEnum.TYPE_DESCRIPTION.getCode())){
            executeAuditResultDto.setButtenType(ToolsExecuteButtonEnum.DIRECTLY_EXECUTE.getCode());
            return R.ok(executeAuditResultDto);
        }
        //主线需要区分工具状态
        //光大dev什么情况下都可以执行，qa什么情况下都可以执行，dev什么情况下都可以执行，生产，新增待审批不能执行，
        if(!businessConfig.isToolEvnMainSwitch()){//主线
            //区分来源，校验 场景 ：工具状态，审批状态    工具执行 ：工具状态，审批状态  诊断：工具状态，审批状态，交付状态
            Boolean check = isToolStatusCheck(toolsInfo, executAuditQueryDto.getExecFrom());
            if (Boolean.TRUE.equals(check)) {
                executeAuditResultDto.setButtenType(ToolsExecuteButtonEnum.NOT_EXECUTABLE.getCode());
                return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, executeAuditResultDto, ConstantsEnum.EXECUTE_STATE_INCONSISTENT.getDesc());
            }
        }else if(businessConfig.isToolEvnMainSwitch() && ToolsEnvEnum.PRO_ENV.getDesc().equals(businessConfig.getToolsEvn())){//光大生产
            if(toolsInfo.getStatus().equals(ToolsStatusEnum.STATUS_AUDIT.getCode())){//生产，新增待审批不能执行
                executeAuditResultDto.setButtenType(ToolsExecuteButtonEnum.NOT_EXECUTABLE.getCode());
                return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, executeAuditResultDto, ConstantsEnum.EXECUTE_STATE_INCONSISTENT.getDesc());
            }
        }
        //是否开启工具执行校验，未开启则可以直接执行
        boolean toolsExecuteSwitch = businessConfig.isToolsExecuteCheck();//判断是否为空数据
        if (!toolsExecuteSwitch || toolsInfo.getType().equals(ToolsTypeEnum.TYPE_DESCRIPTION.getCode())) {
            executeAuditResultDto.setButtenType(ToolsExecuteButtonEnum.DIRECTLY_EXECUTE.getCode());
            return R.ok(executeAuditResultDto);
        }
        //没有获取到当前ip
        if (StringUtils.isEmpty(executAuditQueryDto.getCurrentIp())) {
            executeAuditResultDto.setButtenType(ToolsExecuteButtonEnum.NOT_EXECUTABLE.getCode());
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, executeAuditResultDto, ConstantsEnum.GET_IP_FAIL.getDesc());
        }

        //获取执行最新一条记录审批记录，根据工具id，来源，用户id
        ExecuteAuditBean executeAuditDto = toolsExecuteMapper.getExecuteAudit(BeanUtils.copy(executAuditQueryDto, ExecuteAuditEntity.class));
        ExecuteAuditResultDto executeAuditResultAudit = BeanUtils.copy(executeAuditDto, ExecuteAuditResultDto.class);
        //判断是否存在审批记录
        if (executeAuditDto != null) {
            //获取执行agent
            AuditExecutAgentEntity auditExecutAgentEntity = new AuditExecutAgentEntity();
            auditExecutAgentEntity.setAuditId(executeAuditDto.getAuditDto().getId());
            List<AuditExecutAgentEntity> auditExecutAgentEntities = auditExecutAgentMapper.selectAuditExecutAgentList(auditExecutAgentEntity);
            executeAuditResultAudit.setAgentIps(BeanUtils.copy(auditExecutAgentEntities, AuditExecutAgentDto.class));
            if(executeAuditDto.getParams()!=null){
                List<ToolsParamResultDto> toolsParamResultDtos = JSONArray.parseArray(executeAuditDto.getParams().getParamJson(), ToolsParamResultDto.class);
                executeAuditResultAudit.setToolsParamResultList(toolsParamResultDtos);
            }
            //判断审批记录是否已经审批通过
            if (executeAuditDto.getAuditDto().getApprovalState().equals(AuditStateEnum.AUDIT_APPROVED.getCode())) {
                //判断旭哥哪里是是否存在执行记录
                boolean executRecord = executeMonitorService.matchAuditId(executeAuditDto.getAuditDto().getId());
                if (!executRecord) { //不存在执行记录,已经审批过了可以进行执行
                    executeAuditResultAudit.setButtenType(ToolsExecuteButtonEnum.DIRECTLY_EXECUTE.getCode());
                    executeAuditResultAudit.setAuditId(executeAuditResultAudit.getAuditDto().getId());
                    return R.ok(executeAuditResultAudit);
                }
            } else if (executeAuditDto.getAuditDto().getApprovalState().equals(AuditStateEnum.AUDIT_UNDER_REVIEW.getCode())
                    || executeAuditDto.getAuditDto().getApprovalState().equals(AuditStateEnum.AUDIT_AWAITING.getCode())) {//还在审批中
                executeAuditResultAudit.setButtenType(ToolsExecuteButtonEnum.UNDER_REVIEW.getCode());
                executeAuditResultAudit.setAuditId(executeAuditResultAudit.getAuditDto().getId());
                return R.ok(executeAuditResultAudit);
            }
        }
        //存在不审批记录
        //获取当前时间是否是高峰时期
        boolean isHighTime = topTimeService.checkHighTime(ControlsTimeUtil.getCurrentDayOfWeek()
                , ControlsTimeUtil.getStringNowDate("HH:mm"));
        //根据ip获取请求方式
        int sourceType = eccWhiteService.selectLoginUrlByIp(executAuditQueryDto.getCurrentIp());
        int executeButton = getExecuteButton(isHighTime, sourceType, toolsInfo, executAuditQueryDto.getUserId());
        executeAuditResultDto.setButtenType(executeButton);
        return R.ok(executeAuditResultDto);
    }

    /**
     * 保存执行审批
     *
     * @param executeAuditSaveDto 提交审批工具信息
     * @param userDto
     * @param executeAuditSaveDto
     * @return
     */
    @Transactional(rollbackFor = {ToolsException.class, AuditException.class})
    @Override
    public ExecuteAuditResultDto saveExecutToolsAuditStatus(ExecuteAuditSaveDto executeAuditSaveDto, UserDto userDto) throws AuditException {
        LogAuditDto logAuditDto = null;
        ExecuteAuditResultDto executeAuditResultDto = new ExecuteAuditResultDto();
        //根据工具id 获取工具脚本信息
        ToolsInfoDto toolsInfoDto = toolsInfoService.selectToolsInfoById(executeAuditSaveDto.getToolsInfoDto().getId());
        String operatingContent = " 执行工具“" + toolsInfoDto.getCode() + "”,提交执行复核人(" + executeAuditSaveDto.getAuditorId() + "-" + executeAuditSaveDto.getAuditorName() + ")";
        //封装审计日志参数
        if (executeAuditSaveDto.getExecFrom().equals(AuditTypeEnum.AUDIT_TOOLS_EXECUTE_FAVORITES.getCode())) {
            logAuditDto = createLogAudit(operatingContent, OperatModuleEnum.TOOL_FAVORITES.getRemark(), OperatTypeEnum.TOOL_FAVORITES_REVIEW.getRemark(),
                    OperatModuleEnum.TOOL_FAVORITES.getCode(), OperatTypeEnum.TOOL_FAVORITES_REVIEW.getCode(), userDto);
        }else if (executeAuditSaveDto.getExecFrom().equals(AuditTypeEnum.AUDIT_TOOLS_EXECUTE_TOOLS_EXECUTE.getCode())) {//工具执行
            logAuditDto = createLogAudit(operatingContent, OperatModuleEnum.TOOL_EXECUTE.getRemark(), OperatTypeEnum.TOOL_EXECUTE_REVIEW.getRemark(),
                    OperatModuleEnum.TOOL_EXECUTE.getCode(), OperatTypeEnum.TOOL_EXECUTE_REVIEW.getCode(), userDto);
        } else {
            logAuditDto = createLogAudit(operatingContent, OperatModuleEnum.ALARM_MANAGE.getRemark(), OperatTypeEnum.ALARM_MANAGE4.getRemark(),
                    OperatModuleEnum.ALARM_MANAGE.getCode(), OperatTypeEnum.ALARM_MANAGE4.getCode(), userDto);
        }
        if (toolsInfoDto.getType().equals(ToolsTypeEnum.TYPE_SCRIPT.getCode())) {//脚本参数封装
            //如果没有参数内容
            if (executeAuditSaveDto.getParams() != null) {
                executeAuditSaveDto.getParams().setScriptContent(toolsInfoDto.getScriptEditing());
                executeAuditSaveDto.getParams().setScriptIds(toolsInfoDto.getScriptIds());
            } else {
                AuditExecutParamDto auditExecutParamDto = new AuditExecutParamDto();
                auditExecutParamDto.setScriptContent(toolsInfoDto.getScriptEditing());
                auditExecutParamDto.setScriptIds(toolsInfoDto.getScriptIds());
                executeAuditSaveDto.setParams(auditExecutParamDto);
            }
        }
        //插入审批表
        List<AuditEverybodyQueryDto> auditorList = new ArrayList<>();
        AuditDto auditDto = assembleSaveAudit(executeAuditSaveDto, auditorList, userDto);
        if (auditDto == null) {
            logAuditDto.setResult(1);
            logAuditDto.setResultDesc(logAuditDto.getResultDesc() + "创建审批失败");
            logAuditService.insertLogAudit(logAuditDto);
            throw new AuditException("Tool execution approval insertion double review failed ! ");
        }
        //发送双人复核信息,回更双人复核状态
        Long auditNum = auditService.sendDoubleCheck(auditDto, auditorList, toolsInfoDto.getName());
        //审核提交成功后，返回
        if (auditNum != -1) {
            if (toolsInfoDto.getType().equals(ToolsTypeEnum.TYPE_SCRIPT.getCode())) {//脚本参数插入
                //插入脚本参数，选中ip内容
                auditExecutAgentService.batchInsertAuditExecutAgent(executeAuditSaveDto.getAgentIps(), auditDto.getId());
                if (executeAuditSaveDto.getParams() != null) {
                    executeAuditSaveDto.getParams().setAuditId(auditDto.getId());
                    auditExecutParamService.insertAuditExecutParam(executeAuditSaveDto.getParams());
                }
            }
            //返回按钮为审批中的按钮
            executeAuditResultDto.setButtenType(ToolsExecuteButtonEnum.UNDER_REVIEW.getCode());
        }
        //审计日志插入
        logAuditDto.setResult(0);
        logAuditDto.setResultDesc(logAuditDto.getResultDesc() + "成功");
        logAuditService.insertLogAudit(logAuditDto);
        return executeAuditResultDto;
    }

    /**
     * 双人复核获取任务详细信息
     *
     * @param executAuditQueryDto 审批id
     */
    @Override
    public ExecuteAuditResultDto getAuditDetails(ExecutAuditQueryDto executAuditQueryDto) {
        //获取审批记录
        ExecuteAuditBean executeAuditBean = toolsExecuteMapper.getExecuteAudit(BeanUtils.copy(executAuditQueryDto, ExecuteAuditEntity.class));
        //获取工具信息
//        ToolsDto toolsDto = toolsInfoService.getToolsCombinedInfo(executeAuditBean.getAuditDto().getBusinessId());
        ExecuteAuditResultDto executeAuditResultDto = BeanUtils.copy(executeAuditBean, ExecuteAuditResultDto.class);
        if(executeAuditBean.getParams() != null && StringUtils.isNotEmpty(executeAuditBean.getParams().getParamJson())){
            List<ToolsParamResultDto> toolsParamResultDtos = JSONArray.parseArray(executeAuditBean.getParams().getParamJson(), ToolsParamResultDto.class);
            executeAuditResultDto.setToolsParamResultList(toolsParamResultDtos);
        }
        //获取执行agent
        AuditExecutAgentEntity auditExecutAgentEntity = new AuditExecutAgentEntity();
        auditExecutAgentEntity.setAuditId(executeAuditResultDto.getAuditDto().getId());
        List<AuditExecutAgentEntity> auditExecutAgentEntities = auditExecutAgentMapper.selectAuditExecutAgentList(auditExecutAgentEntity);
        executeAuditResultDto.setAgentIps(BeanUtils.copy(auditExecutAgentEntities, AuditExecutAgentDto.class));
        //参数封装
//        executeAuditResultDto.setToolsInfoCategoryEntity(toolsDto);
        executeAuditResultDto.setExecFromName(getByCode(executeAuditBean.getAuditDto().getType()).getDesc());
        return executeAuditResultDto;
    }

    @Override
    public ToolsDto getToolsCombinedInfoAll(ToolsAuditDto toolsAuditDto, UserDto user, String ipAddress) {
        //获取工具所有信息
        ToolsDto toolsCombinedInfo = toolsInfoService.getToolsCombinedInfo(toolsAuditDto);
        if(toolsCombinedInfo!=null){
            //获取双人复核详情
            if(toolsAuditDto.getAuditId()!=null&&toolsAuditDto.getAuditId()!=0){
                ExecutAuditQueryDto executAuditQueryDto = new ExecutAuditQueryDto();
                executAuditQueryDto.setAuditId(toolsAuditDto.getAuditId());
                ExecuteAuditResultDto auditDetails = getAuditDetails(executAuditQueryDto);
                if(auditDetails.getToolsParamResultList()!=null){//存在参数内容
                    toolsCombinedInfo.setToolsParamResultList(auditDetails.getToolsParamResultList());
                }
                if(auditDetails.getAgentIps()!=null){//存在agent内容
                    List<AuditExecutAgentDto> agentIps = auditDetails.getAgentIps() ;
                    List<ToolsAgentResultDto> toolsAgentResult = BeanUtils.copy(agentIps, ToolsAgentResultDto.class);
                    toolsCombinedInfo.setToolsAgentResultList(toolsAgentResult);
                }
                return toolsCombinedInfo;
            }else if(toolsAuditDto.getId()!=null&&toolsAuditDto.getId()!=0){//获取执行详情
                ExecutAuditQueryDto executAuditQueryDto = new ExecutAuditQueryDto();
                executAuditQueryDto.setToolsId(toolsCombinedInfo.getId());
                executAuditQueryDto.setCurrentIp(ipAddress);
                executAuditQueryDto.setExecFrom(toolsAuditDto.getExecFrom());
                R<ExecuteAuditResultDto> executButtonStatus = getExecutButtonStatus(executAuditQueryDto);
                if(executButtonStatus.getData().getToolsParamResultList()!=null){//存在审批参数内容
                    toolsCombinedInfo.setToolsParamResultList(executButtonStatus.getData().getToolsParamResultList());
                }
                if(executButtonStatus.getData().getAgentIps()!=null){ //存在审批agent内容
                    List<AuditExecutAgentDto> agentIps = executButtonStatus.getData().getAgentIps() ;
                    List<ToolsAgentResultDto> toolsAgentResult = BeanUtils.copy(agentIps, ToolsAgentResultDto.class);
                    toolsCombinedInfo.setToolsAgentResultList(toolsAgentResult);
                }else{
                    toolsCombinedInfo.setToolsAgentResultList(null);
                }
                return toolsCombinedInfo;
            }
            return null;
        }else {
            return null;
        }
    }

    /**
     * 撤回工具执行审批  执行撤回，审批撤回
     *
     * @param executAuditQueryDto toolsid  工具id， execFrom 来源   4.场景工具 5.告警诊断 7.工具执行  ，userId 用户id
     *                            ，auditState 审批状态 ,auditId  审批id
     * @return
     */
    @Override
    public R<Object> revokeExecutToolsAudit(ExecutAuditQueryDto executAuditQueryDto) {
        //封装查询参数
        AuditQueryDto auditQueryDto = new AuditQueryDto();
        auditQueryDto.setBusinessId(executAuditQueryDto.getToolsId());
        auditQueryDto.setType(executAuditQueryDto.getExecFrom());
        auditQueryDto.setApplyId(executAuditQueryDto.getUserId());
        auditQueryDto.setId(executAuditQueryDto.getAuditId());
        //获取当前工具审批状态
        AuditDto auditDto = auditService.selectAuditByLastOne(auditQueryDto);
        //查询当前审批是否已经审批完，返回执行，审批状态审批通过
        //判断当前是 审批撤回
        if (executAuditQueryDto.getAuditState().equals(AuditStateEnum.AUDIT_AWAITING.getCode())) {
            if (!(auditDto.getApprovalState().equals(AuditStateEnum.AUDIT_AWAITING.getCode())
                    || auditDto.getApprovalState().equals(AuditStateEnum.AUDIT_UNDER_REVIEW.getCode()))) {//当前状态不是审批中，或者待审批
                return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, ConstantsEnum.REFRESH_CURRENT_STATE.getDesc());
            }
            auditDto.setApprovalState(AuditStateEnum.AUDIT_WITHDRAW.getCode());//撤销
        } else if (executAuditQueryDto.getAuditState().equals(AuditStateEnum.AUDIT_APPROVED.getCode())) {//执行撤回
            //调用旭哥是否已经执行
            boolean executRecord = executeMonitorService.matchAuditId(auditDto.getId());
            if (executRecord) { //审批通过且已经执行
                return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, ConstantsEnum.EXECUTE_AUDIT_APPROVED_AND_EXECUTE.getDesc());
            }
            //设置审核状态为执行撤回
            auditDto.setApprovalState(AuditStateEnum.AUDIT_EXECUTE_BACK.getCode());//审批通过执行撤回
        } else {//状态不对
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, ConstantsEnum.REFRESH_CURRENT_STATE.getDesc());
        }
        //修改撤回状态
        int i = auditService.updateAudit(auditDto);
        if (i == 1) {
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, ConstantsEnum.RECALL_SUCCESS.getDesc());
        } else {
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, ConstantsEnum.RECALL_FAIL.getDesc());
        }
    }

    /**
     * 工具执行
     *
     * @param executeToolsDto 审批id
     * @param user
     * @return
     */
    @Override
    public R<Object> executeTools(ExecuteToolsDto executeToolsDto, UserDto user) throws ToolsException {
        LogAuditDto logAuditDto = null;
        //获取工具内容 ToolsDto getToolsCombinedInfo
        ToolsDto toolsInfoDto = toolsInfoService.getToolsCombinedInfo(executeToolsDto.getToolsId());
        if (toolsInfoDto.getId() == null) {//工具不存在
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, ConstantsEnum.TOOL_NOT_EXIST.getDesc());
        }
        if (executeToolsDto.getAuditId() == null || executeToolsDto.getAuditId() == 0) {//直接执行
            String operatingContent = " 执行" + ToolsTypeEnum.getDescByCode(toolsInfoDto.getType()) + "工具“" + toolsInfoDto.getCode() + "”,执行用户:" + user.getUserName() + ")";
            //封装审计日志参数
            if (executeToolsDto.getExecFrom().equals(AuditTypeEnum.AUDIT_TOOLS_EXECUTE_FAVORITES.getCode())) {//场景工具
                logAuditDto = createLogAudit(operatingContent, OperatModuleEnum.TOOL_FAVORITES.getRemark(), OperatTypeEnum.TOOL_EXECUTE_FAVORITES.getRemark(),
                        OperatModuleEnum.TOOL_FAVORITES.getCode(), OperatTypeEnum.TOOL_EXECUTE_FAVORITES.getCode(), user);
            }else if (executeToolsDto.getExecFrom().equals(AuditTypeEnum.AUDIT_TOOLS_EXECUTE_TOOLS_EXECUTE.getCode())) {//工具执行
                logAuditDto = createLogAudit(operatingContent, OperatModuleEnum.TOOL_EXECUTE.getRemark(), OperatTypeEnum.TOOL_EXECUTE.getRemark(),
                        OperatModuleEnum.TOOL_EXECUTE.getCode(), OperatTypeEnum.TOOL_EXECUTE.getCode(), user);
            } else {//告警管理
                logAuditDto = createLogAudit(operatingContent, OperatModuleEnum.ALARM_MANAGE.getRemark(), OperatTypeEnum.ALARM_MANAGE3.getRemark(),
                        OperatModuleEnum.ALARM_MANAGE.getCode(), OperatTypeEnum.ALARM_MANAGE3.getCode(), user);
            }
            //封装请求执行内容
            List<ToolsParamDto> toolsParamDtoList = new ArrayList<>();
            ScriptExecuteDto scriptExecuteDto = makeScriptExecute(executeToolsDto, toolsInfoDto, toolsParamDtoList, user);
            //调用执行方法
            try {
                Long execute = toolsOperateService.execute(scriptExecuteDto, toolsInfoDto, null,
                        executeToolsDto.getExecFrom(), toolsParamDtoList, user,executeToolsDto.getEventNum());
                //插入使用报表
                createUsageReport(toolsInfoDto,execute,user);
                logAuditDto.setResult(0);
                logAuditDto.setResultDesc(logAuditDto.getResultDesc() + OperatResultEnum.SUCCESS.getDesc());
                logAuditService.insertLogAudit(logAuditDto);
                return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, ConstantsEnum.EXECUTE_SUCCESS.getDesc());
            } catch (ScriptToolsException | ExecuteMonitorException | MqOperateServiceException | ToolsException |
                     StudioException | ExecuteHistoryException e) {
                logAuditDto.setResult(1);
                logAuditDto.setResultDesc(logAuditDto.getResultDesc() + OperatResultEnum.FAILURE.getDesc());
                logAuditService.insertLogAudit(logAuditDto);
                throw new ToolsException(e);
            }
        } else {//审批执行
            //封装审计日志参数
            String operatingContent = " 审批执行" + ToolsTypeEnum.getDescByCode(toolsInfoDto.getType()) + "工具“" + toolsInfoDto.getCode() + "”,执行用户:" + user.getUserName() + ")";
            if (executeToolsDto.getExecFrom().equals(AuditTypeEnum.AUDIT_TOOLS_EXECUTE_FAVORITES.getCode())) {
                logAuditDto = createLogAudit(operatingContent, OperatModuleEnum.TOOL_FAVORITES.getRemark(), OperatTypeEnum.TOOL_EXECUTE_FAVORITES.getRemark()
                        , OperatModuleEnum.TOOL_FAVORITES.getCode(), OperatTypeEnum.TOOL_EXECUTE_FAVORITES.getCode(), user);
            }else if (executeToolsDto.getExecFrom().equals(AuditTypeEnum.AUDIT_TOOLS_EXECUTE_TOOLS_EXECUTE.getCode())) {//工具执行
                logAuditDto = createLogAudit(operatingContent, OperatModuleEnum.TOOL_EXECUTE.getRemark(), OperatTypeEnum.TOOL_EXECUTE.getRemark(),
                        OperatModuleEnum.TOOL_EXECUTE.getCode(), OperatTypeEnum.TOOL_EXECUTE.getCode(), user);
            } else {
                logAuditDto = createLogAudit(operatingContent, OperatModuleEnum.ALARM_MANAGE.getRemark(), OperatTypeEnum.ALARM_MANAGE3.getRemark()
                        , OperatModuleEnum.ALARM_MANAGE.getCode(), OperatTypeEnum.ALARM_MANAGE3.getCode(), user);
            }
            ExecuteAuditEntity executeAuditEntity = new ExecuteAuditEntity();
            executeAuditEntity.setAuditId(executeToolsDto.getAuditId());
            //获取审批所有内容
            ExecuteAuditBean executeAuditDto = toolsExecuteMapper.getExecuteAudit(executeAuditEntity);
            //获取执行agent
            AuditExecutAgentEntity auditExecutAgentEntity = new AuditExecutAgentEntity();
            auditExecutAgentEntity.setAuditId(executeAuditDto.getAuditDto().getId());
            List<AuditExecutAgentEntity> auditExecutAgentEntities = auditExecutAgentMapper.selectAuditExecutAgentList(auditExecutAgentEntity);
            executeAuditDto.setAgentIps(BeanUtils.copy(auditExecutAgentEntities, AuditExecutAgentDto.class));
            //校验审批结果是否通过
            if (!executeAuditDto.getAuditDto().getApprovalState().equals(AuditStateEnum.AUDIT_APPROVED.getCode())) {
                logAuditDto.setResult(1);
                logAuditDto.setResultDesc(logAuditDto.getResultDesc() + "审批未通过，失败");
                logAuditService.insertLogAudit(logAuditDto);
                return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, ConstantsEnum.APPROVAL_NOT_PASSED.getDesc());
            }
            //调用旭哥是否已经执行
            boolean executRecord = executeMonitorService.matchAuditId(executeAuditDto.getAuditDto().getId());
            if (executRecord) { //审批通过且已经执行
                logAuditDto.setResult(1);
                logAuditDto.setResultDesc(logAuditDto.getResultDesc() + "本次审批已经执行，失败");
                logAuditService.insertLogAudit(logAuditDto);
                return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, ConstantsEnum.EXECUTE_AUDIT_APPROVED_AND_EXECUTE.getDesc());
            }
            //封装请求执行内容
            List<ToolsParamDto> toolsParamDtoList = new ArrayList<>();
            ScriptExecuteDto scriptExecuteDto = makeAuditScriptExecute(executeAuditDto, toolsInfoDto, toolsParamDtoList, user);
            //调用执行方法
            try {
                Long execute = toolsOperateService.execute(scriptExecuteDto, toolsInfoDto, executeAuditDto.getAuditDto().getId()
                        , executeToolsDto.getExecFrom(), toolsParamDtoList, user,executeToolsDto.getEventNum());
                //插入使用报表
                createUsageReport(toolsInfoDto,execute,user);
                logAuditDto.setResult(0);
                logAuditDto.setResultDesc(logAuditDto.getResultDesc() + "成功");
                logAuditService.insertLogAudit(logAuditDto);
                return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, ConstantsEnum.EXECUTE_SUCCESS.getDesc());
            } catch (ScriptToolsException | ExecuteMonitorException | MqOperateServiceException | ToolsException |
                     StudioException | ExecuteHistoryException e) {
                logAuditDto.setResult(1);
                logAuditDto.setResultDesc(logAuditDto.getResultDesc() + "失败");
                logAuditService.insertLogAudit(logAuditDto);
                throw new ToolsException(e);
            }
        }
    }

    /**
     * 工具执行页面获取列表
     *
     * @param toolsInfoQueryDto 查询条件  工具编码,工具名称,工具类型,应用系统,一级分类,二级分类,工具类型,脚本名称,是否高危
     * @param pageNum
     * @param pageSize
     * @param userdto           用户
     * @return
     */
    @Override
    public PageInfo<ToolsInfoDto> selectExecuteList(ToolsQueryDto toolsInfoQueryDto, Integer pageNum, Integer pageSize, UserDto userdto) {
        // 获取用户的业务系统权限
        List<SystemPullDto> businessSystemList = systemDataInteract.getBusinessSystemList(userdto.getUserId());
        if(businessSystemList.isEmpty())
        {
            return new PageInfo<>(new ArrayList<>());
        }
        PageMethod.startPage(pageNum, pageSize);
        List<ToolsInfoCategoryEntity> filteredToolsInfoList = PageMethod.<ToolsInfoCategoryEntity>startPage(pageNum, pageSize)
                .doSelectPage(() -> this.filterToolsInfoByPermissions(toolsInfoQueryDto, businessSystemList));
        // 如果过滤后的工具信息列表为空，则返回空的分页结果
        if (filteredToolsInfoList.isEmpty()) {
            return new PageInfo<>(new ArrayList<>());
        }
        PageInfo<ToolsInfoCategoryEntity> pageInfo = PageDataUtil.toDtoPage(filteredToolsInfoList, ToolsInfoCategoryEntity.class);
        // 将实体列表转换为DTO列表
        List<ToolsInfoDto> dtoList = convertToDtoList(filteredToolsInfoList);
        return getPageInfo(pageInfo, dtoList);
    }

    /**
     * 自定义组合工具执行
     * @param toolsDto
     * @param user
     * @param execFrom  执行来源 7.工具执行
     * 前端传入当前组合工具的基础信息和新的流程xml+当前组合工具流程信息。 修改工程名相同,流程名+后缀.调用流程编排新的发布接口（组合工具专用）
     * 进行工具执行
     * @return
     */
    @Override
    public R<Object> customComToolExecute(Integer execFrom,ToolsDto toolsDto, UserDto user) throws ToolsException, StudioException {
        logger.info("Toolbox Info Custom Combined start! name :{}、userName：{}、",toolsDto.getName(),user.getUserName());
        //校验是否是脚本工具
        if (toolsDto.getType().equals(ToolsTypeEnum.TYPE_SCRIPT.getCode())){
            logger.info("saveCustomComToolsInfo,checkType：Script Forbid Custom");
            throw new ToolsException("Script Forbid Custom");
        }
        //参数检查
        String checkMessage =  toolsOperateBaseService.checkParams(toolsDto, user);
        if(StringUtils.isNotBlank(checkMessage)){
            logger.info("saveCustomComToolsInfo,checkParams： {}",checkMessage);
            throw new ToolsException(checkMessage);
        }
        Long tbToolsProject = SnowflakeIdWorker.generateId();
        toolsDto.getToolsProjectInfoDto().setId(tbToolsProject);
        toolsDto.getToolsProjectInfoDto().setProjectName(toolsDto.getToolsProjectInfoDto().getProjectName()+"_Com");
        toolsDto.getToolsProjectInfoDto().setWorkflowName(toolsDto.getToolsProjectInfoDto().getWorkflowName());

        //组合工具发布
        logger.info("Composite tool release begins!tools Name：{}",toolsDto.getName());
        //调用自定义组合工具发布
        studioInteract.externalReleaseInterface(toolsDto.getToolsProjectInfoDto(),user);
        String operatingContent = " 执行自定义" + ToolsTypeEnum.getDescByCode(toolsDto.getType()) + "工具“" + toolsDto.getCode() + "”,执行用户:" + user.getUserName() + ")";
        //封装审计日志参数
        LogAuditDto logAuditDto = createLogAudit(operatingContent, OperatModuleEnum.TOOL_EXECUTE.getRemark(), OperatTypeEnum.TOOL_EXECUTE.getRemark(),
                OperatModuleEnum.TOOL_EXECUTE.getCode(), OperatTypeEnum.TOOL_EXECUTE.getCode(), user);
        //调用执行方法
        try {
            ToolsProjectDto toolsProjectDto = BeanUtils.copy(toolsDto.getToolsProjectInfoDto(), ToolsProjectDto.class);
            Long execute = toolsOperateService.customComToolExecute(toolsDto,
                    execFrom, user,toolsProjectDto);
            //插入使用报表
            createUsageReport(toolsDto,execute,user);
            logAuditDto.setResult(0);
            logAuditDto.setResultDesc(logAuditDto.getResultDesc() + "成功");
            logAuditService.insertLogAudit(logAuditDto);
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, ConstantsEnum.EXECUTE_SUCCESS.getDesc());
        } catch ( ExecuteMonitorException | MqOperateServiceException  |
                 StudioException e) {
            logAuditDto.setResult(1);
            logAuditDto.setResultDesc(logAuditDto.getResultDesc() + "失败");
            logAuditService.insertLogAudit(logAuditDto);
            throw new ToolsException(e);
        }
    }

    /**
     * 获取工具是否绑定agent
     * @param toolsInfoQueryDto id 工具id
     * @return  true  ， false
     */
    @Override
    public R<Object> checkToolsExecuteBondAgent(ToolsInfoQueryDto toolsInfoQueryDto,Long userId) throws InteractServiceException {
		AgentQueryDto agentQueryDto = new AgentQueryDto();
		// 根据工具id查询工具信息
		ToolsInfoDto toolsInfoDto = toolsInfoService.selectToolsInfoById(toolsInfoQueryDto.getId());
		if (ToolsTypeEnum.TYPE_SCRIPT.getCode().equals(toolsInfoDto.getType())) {// 脚本工具
			if (-1L == toolsInfoDto.getBusinessSystemId()) {
				// 脚本工具公共不需要校验
				return R.ok();
			}
			agentQueryDto.setTdToolsId(toolsInfoDto.getId());
			// 根据工具id查询当前工具是否已经绑定了agent
			List<ToolsAgentInfoEntity> toolsAgentInfoList = toolsAgentInfoService
					.selectToolsAgentInfoList(agentQueryDto);
			toolsAgentInfoList = toolsAgentInfoService.rmDeleteAgent(toolsAgentInfoList, agentQueryDto, userId);
			if (toolsAgentInfoList.isEmpty()) {
				return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, ConstantsEnum.TOOLS_NOT_BOUND_AGENT.getDesc());
			} else {
				return R.ok();
			}
		} else if (ToolsTypeEnum.TYPE_COMBINED.getCode().equals(toolsInfoDto.getType())) {// 组合工具
			if (toolsInfoDto.getChildIds() != null) {
				String checkResult = checkToolsChildBoundAgent(toolsInfoQueryDto.getId(),toolsInfoDto.getChildIds(), null, userId);
				if (StringUtils.isNotEmpty(checkResult)) {
					return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, "该组合工具中引用的工具(" + checkResult + ")未配置agent！");
				} else {
					return R.ok();
				}
			} else {
				return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, ConstantsEnum.COMBINED_TOOLS_NOT_CHILDS.getDesc());
			}
		} else if (ToolsTypeEnum.TYPE_DESCRIPTION.getCode().equals(toolsInfoDto.getType())) {// 描述工具不做任何操作
			return R.ok();
		} else {
			return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, "当前工具类型错误请联系管理员！");
		}
	}

    public List<ToolsInfoDto> convertToDtoList(List<ToolsInfoCategoryEntity> entityList) {
        // 这里需要实现具体的转换逻辑，将entityList转换为dtoList
        return entityList.stream().map(toolsInfoEntity -> {
            ToolsInfoDto dto = new ToolsInfoDto();
            org.springframework.beans.BeanUtils.copyProperties(toolsInfoEntity, dto);
            return dto;
        }).collect(Collectors.toList());
    }

    /**
     * @param toolsInfoQueryDto  查询条件  工具编码,工具名称,工具类型,应用系统,一级分类,二级分类,工具类型,脚本名称,是否高危
     * @param businessSystemList   用户包含的业务系统业务系统
     * @return
     */
    public List<ToolsInfoCategoryEntity> filterToolsInfoByPermissions(ToolsQueryDto toolsInfoQueryDto, List<SystemPullDto> businessSystemList) {
        // 创建查询对象
        ToolsInfoCategoryEntity query = BeanUtils.copy(toolsInfoQueryDto, ToolsInfoCategoryEntity.class);
        //主线，光大生产环境所有人都可以看到工具审批通过数据
        query.setPermissionLevel(FavoritesPermissionEnum.PERMISSION_SUPPORTPE.getCode());
        //光大校验生产环境只能看到审批通过数据，dev/qa/pre能看到所有数据
        if(businessConfig.isToolEvnMainSwitch() && !ToolsEnvEnum.PRO_ENV.getDesc().equals(businessConfig.getToolsEvn())){
            query.setPermissionLevel(null);
        }
        query.setLikeCode(query.getCode());
        query.setCode(null);
        // 根据权限和状态过滤工具列表
        List<ToolsInfoCategoryEntity> toolsInfoList = toolsInfoMapper.selectToolsInfoCategoryList(query);
        // 根据业务系统权限进一步过滤工具列表
        return toolsInfoList.stream()
                .filter(tool -> tool.getBusinessSystemId() != null && (
                        Long.valueOf(-1).equals(tool.getBusinessSystemId()) ||
                                businessSystemList.stream().anyMatch(system -> system.getBusinessSystemId().equals(tool.getBusinessSystemId()))
                ))
                .collect(Collectors.toList());
    }

    //-------------------------------执行区分-------------------------------------------------------//

    /**
     * 下发审批参数封装  审批执行
     *
     * @param executeAuditDto   审批执行dto
     * @param toolsInfoDto      工具箱基础信息
     * @param toolsParamDtoList 封装参数
     * @param user              用户信息
     * @return
     */
    public ScriptExecuteDto makeAuditScriptExecute(ExecuteAuditBean executeAuditDto
            , ToolsDto toolsInfoDto, List<ToolsParamDto> toolsParamDtoList, UserDto user) {
        ScriptExecuteDto scriptExecuteDto = new ScriptExecuteDto();
        List<String> params = new ArrayList<>();
        String paramJson = "";
        List<ScriptTaskApplyAgentApiDto> scriptTaskApplyAgentApiDtos = new ArrayList<>();
        scriptExecuteDto.setExecuser(toolsInfoDto.getScriptOperatingUser());
        if (toolsInfoDto.getType().equals(ToolsTypeEnum.TYPE_DESCRIPTION.getCode())
                || toolsInfoDto.getType().equals(ToolsTypeEnum.TYPE_COMBINED.getCode())) {//描述，组合
            scriptExecuteDto.setScriptUuid("");
        } else {//脚本
            scriptExecuteDto.setScriptUuid(executeAuditDto.getParams().getScriptIds());
            paramJson = executeAuditDto.getParams().getParamJson();
            for (AuditExecutAgentDto auditExecutAgentDto : executeAuditDto.getAgentIps()) {
                ScriptTaskApplyAgentApiDto agentDto = new ScriptTaskApplyAgentApiDto();
                agentDto.setAgentId(auditExecutAgentDto.getSysAgentInfoId().toString());
                agentDto.setAgentIp(auditExecutAgentDto.getAgentIp());
                agentDto.setAgentPort(auditExecutAgentDto.getAgentPort().toString());
                scriptTaskApplyAgentApiDtos.add(agentDto);
            }
        }
        if (StringUtils.isNotEmpty(paramJson)) {
            JSONArray jsonArray = JSONArray.parseArray(paramJson);
            List<JSONObject> sortedList = jsonArray.toJavaList(JSONObject.class)
                    .stream()
                    .sorted(Comparator.comparingInt(o -> o.getIntValue("sort")))
                    .collect(Collectors.toList());
            for (JSONObject obj : sortedList) {
                if (!businessConfig.isToolEvnMainSwitch()){
                    params.add(obj.getString("value"));
                }else{
                    params.add(obj.getString("name") + "=" +obj.getString("value"));
                }
                ToolsParamDto toolsParamDto = new ToolsParamDto();
                toolsParamDto.setDescription(obj.getString("description"));
                toolsParamDto.setValue(obj.getString("value"));
                toolsParamDto.setType(obj.getString("type"));
                toolsParamDto.setName(obj.getString("name"));
                toolsParamDto.setSort(obj.getLong("sort"));
                toolsParamDto.setId(obj.getLong("id"));
                toolsParamDto.setTdToolsId(toolsInfoDto.getId());
                toolsParamDtoList.add(toolsParamDto);
            }

        }
        scriptExecuteDto.setParams(params);
        scriptExecuteDto.setTaskName(toolsInfoDto.getName() + System.currentTimeMillis());
        scriptExecuteDto.setChosedAgentUsers(scriptTaskApplyAgentApiDtos);
        scriptExecuteDto.setExecuser(scriptExecuteDto.getExecuser());
        return scriptExecuteDto;
    }

    /**
     * 下发参数封装  直接执行
     *
     * @param executeToolsDto   执行dto
     * @param toolsInfoDto      工具箱基础信息
     * @param toolsParamDtoList 封装参数
     * @param user              用户信息
     * @return
     */
    public ScriptExecuteDto makeScriptExecute(ExecuteToolsDto executeToolsDto
            , ToolsDto toolsInfoDto, List<ToolsParamDto> toolsParamDtoList, UserDto user) {
        List<String> params = new ArrayList<>();
        String paramJson = "";
        List<ScriptTaskApplyAgentApiDto> scriptTaskApplyAgentApiDtos = new ArrayList<>();
        ScriptExecuteDto scriptExecuteDto = new ScriptExecuteDto();
        if (toolsInfoDto.getType().equals(ToolsTypeEnum.TYPE_DESCRIPTION.getCode())
                || toolsInfoDto.getType().equals(ToolsTypeEnum.TYPE_COMBINED.getCode())) {
            scriptExecuteDto.setScriptUuid("");
        } else {
            scriptExecuteDto.setScriptUuid(executeToolsDto.getParams().getScriptIds());
            paramJson = executeToolsDto.getParams().getParamJson();
            for (AuditExecutAgentDto auditExecutAgentDto : executeToolsDto.getAgentIps()) {
                ScriptTaskApplyAgentApiDto agentDto = new ScriptTaskApplyAgentApiDto();
                agentDto.setAgentId(auditExecutAgentDto.getSysAgentInfoId().toString());
                agentDto.setAgentIp(auditExecutAgentDto.getAgentIp());
                agentDto.setAgentPort(auditExecutAgentDto.getAgentPort().toString());
                scriptTaskApplyAgentApiDtos.add(agentDto);
            }
        }
        if (StringUtils.isNotEmpty(paramJson)) {
            JSONArray jsonArray = JSONArray.parseArray(paramJson);
            List<JSONObject> sortedList = jsonArray.toJavaList(JSONObject.class)
                    .stream()
                    .sorted(Comparator.comparingInt(o -> o.getIntValue("sort")))
                    .collect(Collectors.toList());
            for (JSONObject obj : sortedList) {
                if (!businessConfig.isToolEvnMainSwitch()){
                    params.add(obj.getString("value"));
                }else{
                    params.add(obj.getString("name") + "=" +obj.getString("value"));
                }

                ToolsParamDto toolsParamDto = new ToolsParamDto();
                toolsParamDto.setDescription(obj.getString("description"));
                toolsParamDto.setValue(obj.getString("value"));
                toolsParamDto.setType(obj.getString("type"));
                toolsParamDto.setName(obj.getString("name"));
                toolsParamDto.setSort(obj.getLong("sort"));
                toolsParamDto.setId(obj.getLong("id"));
                toolsParamDto.setTdToolsId(toolsInfoDto.getId());
                toolsParamDtoList.add(toolsParamDto);
            }

        }
        scriptExecuteDto.setParams(params);
        scriptExecuteDto.setTaskName(toolsInfoDto.getScriptName() + System.currentTimeMillis());
        scriptExecuteDto.setChosedAgentUsers(scriptTaskApplyAgentApiDtos);
        scriptExecuteDto.setExecuser(toolsInfoDto.getScriptOperatingUser());
        return scriptExecuteDto;
    }


    /**
     * 审计日志实例化
     *
     * @param operatModuleRemark 操作模块描述
     * @param operatTypeRemark   操作类型描述
     * @param operatModuleCode   操作模块code
     * @param operatTypeCode     操作类型描code
     * @param user               用户信息
     * @return LogAuditDto 审计日志实例
     */
    public LogAuditDto createLogAudit(String operatingContent, String operatModuleRemark, String operatTypeRemark
            , Integer operatModuleCode, Integer operatTypeCode, UserDto user) {
        LogAuditDto logAuditDto = new LogAuditDto(operatModuleCode, operatTypeCode, operatingContent);
        logAuditDto.setResultDesc(operatModuleRemark + operatTypeRemark);
        logAuditDto.setUserDto(user);
        operatingContent = operatModuleRemark + operatTypeRemark + operatingContent;
        logAuditDto.setOperatingContent(operatingContent);
        logAuditDto.setResultDesc(operatModuleRemark + operatTypeRemark);
        return logAuditDto;
    }

    /**
     * 封装工具使用报表参数,进行插入
     * @param toolsInfoDto
     */
    private UsageReportDto createUsageReport(ToolsDto toolsInfoDto,Long executeUUid, UserDto user) {
        ToolsDto toolsCombinedInfo = toolsInfoService.getToolsCombinedInfo(toolsInfoDto.getId());
        UsageReportDto usageReportDto =  new UsageReportDto();
        try {
            usageReportDto.setTdToolsId(toolsCombinedInfo.getId());
            usageReportDto.setToolCode(toolsCombinedInfo.getCode());
            usageReportDto.setToolName(toolsCombinedInfo.getName());
            usageReportDto.setBusinessSystemId(toolsCombinedInfo.getBusinessSystemId());
            usageReportDto.setBusinessSystemName(toolsCombinedInfo.getBusinessSystemName());
            //描述工具已完成
            if(toolsInfoDto.getType().equals(ToolsTypeEnum.TYPE_DESCRIPTION.getCode())){
                usageReportDto.setExecuteStatus(MonitorRunStatusEnum.COMPLETED.getCode().intValue());
            }else{
                //脚本组合运行中
                usageReportDto.setExecuteStatus(MonitorRunStatusEnum.RUNNING.getCode().intValue());
            }
            usageReportDto.setToolType(toolsInfoDto.getType());
            //组合，描述
            if(toolsInfoDto.getType().equals(ToolsTypeEnum.TYPE_DESCRIPTION.getCode())
                    ||toolsInfoDto.getType().equals(ToolsTypeEnum.TYPE_COMBINED.getCode())){
                usageReportDto.setScriptExecutorId(0L);
                usageReportDto.setScriptExecutorName("");
                usageReportDto.setTdScriptId(0L);
                usageReportDto.setScriptName("");
            }else{//脚本
                usageReportDto.setScriptExecutorId(0L);
                usageReportDto.setScriptExecutorName(toolsCombinedInfo.getScriptOperatingUser());
                usageReportDto.setTdScriptId(0L);
                usageReportDto.setScriptName(toolsCombinedInfo.getScriptName());
            }
            usageReportDto.setScriptCreateId(0L);
            usageReportDto.setScriptCreateName("");
            if(executeUUid!=null){
                usageReportDto.setUniqueUuid(executeUUid.toString());
            }else{
                usageReportDto.setUniqueUuid("");
            }
            usageReportService.insertUsageReport(usageReportDto,user);
        }catch (Exception e) {
            logger.error("Processing encapsulation tool using report parameters error!!", e);
        }

        return usageReportDto;
    }
    //-------------------------------end-------------------------------------------------------//
    //--------------------------------校验规则------------------------------------------------------//

    /**
     * 校验工具执行是否符合要求  区分来源，校验 场景 ：工具状态，审批状态   诊断：工具状态，审批状态，交付状态
     *
     * @param toolsInfo
     * @param execFrom
     * @return true 校验成功，false 校验失败
     */
    private Boolean isToolStatusCheck(ToolsInfoCategoryEntity toolsInfo, Integer execFrom) {
        if (execFrom.equals(AuditTypeEnum.AUDIT_TOOLS_EXECUTE_FAVORITES.getCode())||execFrom.equals(AuditTypeEnum.AUDIT_TOOLS_EXECUTE_TOOLS_EXECUTE.getCode())) {//场景工具//工具执行
            if (!toolsInfo.getStatus().equals(ToolsStatusEnum.STATUS_WAITING.getCode())
                    ||toolsInfo.getApprovalState() ==null || !toolsInfo.getApprovalState().equals(AuditStateEnum.AUDIT_APPROVED.getCode())) {
                return true;
            }
        } else if (execFrom.equals(AuditTypeEnum.AUDIT_TOOLS_EXECUTE_ALARM.getCode())) {
            if (!toolsInfo.getStatus().equals(ToolsStatusEnum.STATUS_WAITING.getCode())
                    || !toolsInfo.getApprovalState().equals(AuditStateEnum.AUDIT_APPROVED.getCode()) ||
                    !toolsInfo.getDeliveryStatus().equals(ToolsDeliveryStatusEnum.STATUS_DELIVERED.getCode())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取执行按钮类型
     * 校验高峰时期，执行ip，二级分类
     *
     * @param isHighTime 高峰时期
     * @param sourceType 请求地点类型  1.ecc 2.生产变更桌面 3.生产查询桌面
     * @param toolsInfo  工具信息
     * @return 0.不允许执行 1.直接执行，2.弹窗倒计时10秒冷静期，3.双人复合，4.弹框确认执行，5.审核中
     */
    private int getExecuteButton(boolean isHighTime, int sourceType, ToolsInfoCategoryEntity toolsInfo, Long userId) {
        int btnStatus = 0;
        int ecc = ConstantsLoginUrlEnum.ECC_DEFAULT.getIndex();//ecc桌面
        int change = ConstantsLoginUrlEnum.ECC_CHANGE.getIndex();//生产变更桌面
        if (isHighTime) {//高峰时期
            if (sourceType == ecc) { //ecc桌面
                btnStatus = checkEccExecute(toolsInfo, userId);
            } else if (sourceType == change) {//生产变更桌面
                btnStatus = checkChangeExecute(toolsInfo);
            } else { //其他桌面
                btnStatus = checkQueryExecute(toolsInfo);
            }
        } else {//非高峰时期
            if (sourceType == ecc) { //ecc桌面
                btnStatus = checkNotTopTimeEccExecute(toolsInfo, userId);
            } else if (sourceType == change) {//生产变更桌面
                btnStatus = checkNotTopTmeChangeExecute(toolsInfo);
            } else {
                btnStatus = checkNotTopTimeQueryExecute(toolsInfo);
            }
        }
        return btnStatus;
    }


    /**
     * ecc 校验逻辑
     *
     * @param toolsInfo 工具信息
     * @return 0.不可执行，1.直接执行，3双人复核，4.弹框确认执行
     */
    private int checkEccExecute(ToolsInfoCategoryEntity toolsInfo, Long userId) {
        int btnStatus = ToolsExecuteButtonEnum.NOT_EXECUTABLE.getCode();
        //校验工具分类类型
        if (toolsInfo.getClassification().equals(ToolsClassEnum.PLAN_CLASS.getCode())) {
            btnStatus = ToolsExecuteButtonEnum.DIRECTLY_EXECUTE.getCode();
        } else if (toolsInfo.getClassification().equals(ToolsClassEnum.QUERY_CLASS.getCode())) {
            if (isViewOrSyste(toolsInfo)) {
                btnStatus = ToolsExecuteButtonEnum.DIRECTLY_EXECUTE.getCode();
            } else {
                btnStatus = ToolsExecuteButtonEnum.TWO_PERSON_COMPOUND.getCode();
            }
        } else if (toolsInfo.getClassification().equals(ToolsClassEnum.CHANGE_CLASS.getCode())
                || toolsInfo.getClassification().equals(ToolsClassEnum.ALARM_CLASS.getCode())) {
            if (getExecUserGroup(userId)) {
                btnStatus = ToolsExecuteButtonEnum.TWO_PERSON_COMPOUND.getCode();
            } else {
                btnStatus = ToolsExecuteButtonEnum.POP_UP_CONFIRMATION.getCode();
            }
        }
        return btnStatus;
    }

    /**
     * 生产变更桌面 校验逻辑
     *
     * @param toolsInfo 工具信息
     * @return 0.不可执行，1.直接执行，2.冷静期10秒，3双人复核
     */
    private int checkChangeExecute(ToolsInfoCategoryEntity toolsInfo) {
        int btnStatus = ToolsExecuteButtonEnum.NOT_EXECUTABLE.getCode();
        //校验工具分类类型
        if (toolsInfo.getClassification().equals(ToolsClassEnum.PLAN_CLASS.getCode())) {
            btnStatus = ToolsExecuteButtonEnum.DIRECTLY_EXECUTE.getCode();
        } else if (toolsInfo.getClassification().equals(ToolsClassEnum.QUERY_CLASS.getCode())) {
            if (isViewOrSyste(toolsInfo)) {
                btnStatus = ToolsExecuteButtonEnum.CHILL.getCode();
            } else {
                btnStatus = ToolsExecuteButtonEnum.TWO_PERSON_COMPOUND.getCode();
            }
        } else if (toolsInfo.getClassification().equals(ToolsClassEnum.CHANGE_CLASS.getCode())
                || toolsInfo.getClassification().equals(ToolsClassEnum.ALARM_CLASS.getCode())) {
            btnStatus = ToolsExecuteButtonEnum.TWO_PERSON_COMPOUND.getCode();
        }
        return btnStatus;
    }

    /**
     * 生产查询桌面 校验逻辑
     *
     * @param toolsInfo 工具信息
     * @return 0.不可执行，1.直接执行，3.双人复核
     */
    private int checkQueryExecute(ToolsInfoCategoryEntity toolsInfo) {
        int btnStatus = ToolsExecuteButtonEnum.NOT_EXECUTABLE.getCode();
        //校验工具分类类型
        if (toolsInfo.getClassification().equals(ToolsClassEnum.PLAN_CLASS.getCode())) {
            btnStatus = ToolsExecuteButtonEnum.DIRECTLY_EXECUTE.getCode();
        } else if (toolsInfo.getClassification().equals(ToolsClassEnum.QUERY_CLASS.getCode())) {
            btnStatus = ToolsExecuteButtonEnum.TWO_PERSON_COMPOUND.getCode();
        } else if (toolsInfo.getClassification().equals(ToolsClassEnum.CHANGE_CLASS.getCode())
                || toolsInfo.getClassification().equals(ToolsClassEnum.ALARM_CLASS.getCode())) {
            btnStatus = ToolsExecuteButtonEnum.NOT_EXECUTABLE.getCode();
        }
        return btnStatus;
    }

    /**
     * ecc非高峰时期 校验逻辑
     *
     * @param toolsInfo 工具信息
     * @return 0.不可执行，1.直接执行，3.双人复核，4.弹框确认执行
     */
    private int checkNotTopTimeEccExecute(ToolsInfoCategoryEntity toolsInfo, Long userId) {
        int btnStatus = ToolsExecuteButtonEnum.NOT_EXECUTABLE.getCode();
        //校验工具分类类型
        if (toolsInfo.getClassification().equals(ToolsClassEnum.PLAN_CLASS.getCode())) {
            btnStatus = ToolsExecuteButtonEnum.DIRECTLY_EXECUTE.getCode();
        } else if (toolsInfo.getClassification().equals(ToolsClassEnum.QUERY_CLASS.getCode())) {
            if (isViewOrSyste(toolsInfo)) {
                btnStatus = ToolsExecuteButtonEnum.DIRECTLY_EXECUTE.getCode();
            } else {
                btnStatus = ToolsExecuteButtonEnum.TWO_PERSON_COMPOUND.getCode();
            }
        } else if (toolsInfo.getClassification().equals(ToolsClassEnum.CHANGE_CLASS.getCode())
                || toolsInfo.getClassification().equals(ToolsClassEnum.ALARM_CLASS.getCode())) {
            if (getExecUserGroup(userId)) {
                btnStatus = ToolsExecuteButtonEnum.TWO_PERSON_COMPOUND.getCode();
            } else {
                btnStatus = ToolsExecuteButtonEnum.POP_UP_CONFIRMATION.getCode();
            }
        }
        return btnStatus;
    }

    /**
     * 生产变更桌面 非高峰时期校验逻辑
     *
     * @param toolsInfo
     * @return 0.不可执行，1.直接执行，3双人复核
     */
    private int checkNotTopTmeChangeExecute(ToolsInfoCategoryEntity toolsInfo) {
        int btnStatus = ToolsExecuteButtonEnum.NOT_EXECUTABLE.getCode();
        //校验工具分类类型
        if (toolsInfo.getClassification().equals(ToolsClassEnum.PLAN_CLASS.getCode())) {
            btnStatus = ToolsExecuteButtonEnum.DIRECTLY_EXECUTE.getCode();
        } else if (toolsInfo.getClassification().equals(ToolsClassEnum.QUERY_CLASS.getCode())) {
            if (isViewOrSyste(toolsInfo)) {
                btnStatus = ToolsExecuteButtonEnum.DIRECTLY_EXECUTE.getCode();
            } else {
                btnStatus = ToolsExecuteButtonEnum.TWO_PERSON_COMPOUND.getCode();
            }
        } else if (toolsInfo.getClassification().equals(ToolsClassEnum.CHANGE_CLASS.getCode())
                || toolsInfo.getClassification().equals(ToolsClassEnum.ALARM_CLASS.getCode())) {
            btnStatus = ToolsExecuteButtonEnum.TWO_PERSON_COMPOUND.getCode();
        }
        return btnStatus;
    }

    /**
     * 生产查询桌面 非高峰时期校验逻辑
     *
     * @param toolsInfo 工具信息
     * @return 0.不可执行，1.直接执行，3.双人复核
     */
    private int checkNotTopTimeQueryExecute(ToolsInfoCategoryEntity toolsInfo) {
        int btnStatus = ToolsExecuteButtonEnum.NOT_EXECUTABLE.getCode();
        //校验工具分类类型
        if (toolsInfo.getClassification().equals(ToolsClassEnum.PLAN_CLASS.getCode())) {
            btnStatus = ToolsExecuteButtonEnum.DIRECTLY_EXECUTE.getCode();
        } else if (toolsInfo.getClassification().equals(ToolsClassEnum.QUERY_CLASS.getCode())) {
            if (isViewOrSyste(toolsInfo)) {//校验是否是*View和重要系统
                btnStatus = ToolsExecuteButtonEnum.DIRECTLY_EXECUTE.getCode();
            } else {
                btnStatus = ToolsExecuteButtonEnum.TWO_PERSON_COMPOUND.getCode();
            }
        } else if (toolsInfo.getClassification().equals(ToolsClassEnum.CHANGE_CLASS.getCode())
                || toolsInfo.getClassification().equals(ToolsClassEnum.ALARM_CLASS.getCode())) {
            btnStatus = ToolsExecuteButtonEnum.NOT_EXECUTABLE.getCode();
        }
        return btnStatus;
    }

    /**
     * 判断工具的执行用户是否为appview,重要系统
     * <p>
     * 高峰时期：
     *
     * @return ecc ：true *view用户，非重要系统，开关关闭->可直接执行   false 全部系统，重要系统->双人复合
     * @return 生产变更桌面 ：true *view用户，非重要系统，开关关闭->冷静期10秒   false 全部系统，重要系统->双人复合
     * 非高峰时期：
     * @return ecc ：true *view用户，非重要系统，开关关闭->可直接执行   false 全部系统，重要系统->双人复合
     * @return 生产变更桌面 ：true *view用户，非重要系统，开关关闭->可直接执行   false 全部系统，重要系统->双人复合
     * @return 生产查询桌面 ：true *view用户，非重要系统，开关关闭->可直接执行   false 全部系统，重要系统->双人复合
     */
    private boolean isViewOrSyste(ToolsInfoCategoryEntity toolsInfo) {
        boolean result = true;
        //获取执行用户判断执行用户是否为*view用户或配置的其他用户，如果不是配置的用户则走审批流程
        List<String> view = (List<String>) switchConfigService.selectSwitchConfigValueByKey(SwitchConfigEnum.SWITCH_VIEW.getName());
        if (view.isEmpty()) {
            return false;
        }
        //获取工具类型
        Integer toolType = toolsInfo.getType();
        if (ToolsTypeEnum.TYPE_COMBINED.getCode().equals(toolType)) {//组合工具
            //组合工具校验子工具*view的是否复核
            String childIds = toolsInfo.getChildIds();
            result = extracted(childIds, view);
        } else {//脚本工具,校验操作用户是否包含在*view配置中
            if (!view.contains(toolsInfo.getScriptOperatingUser()) && !getArbitrarilyView(view, toolsInfo.getScriptOperatingUser())) {
                result = false;
            }
        }
        if (!result) {//如果不是*view，继续校验重要系统
            //获取重要系统，如果没有配置则按照开关关闭的状态
            int sysSwitch = (int) switchConfigService.selectSwitchConfigValueByKey(SwitchConfigEnum.SWITCH_IMPORTANT.getName());
            if (sysSwitch == SwitchConfigValueEnum.SWITCH_IMPORTANT2.getCode()) {//全部系统开关
                result = false;
            } else if (sysSwitch == SwitchConfigValueEnum.SWITCH_IMPORTANT1.getCode()) {//重要系统开关
                //增加重要系统判断逻辑，如果是重要系统，则直接返回false（提交双人复核）
                if (isImportantSystem(toolsInfo.getBusinessSystemId())) {
                    result = false;
                } else {
                    result = true;
                }
            } else { //开关关闭
                result = true;
            }
        }
        return result;
    }

    //组合工具中的多个原子工具，只要工具的执行用户 是开关配置中的用户，则返回true,否则返回false
    private boolean extracted(String childIds, List<String> view) {
        boolean result = true;
        if (null!=childIds && childIds.length()>0) {
            childIds = childIds.substring(1);
            List<Long> ids = new ArrayList<>();
            if (childIds.contains(",")) {
                //多个原子工具
                List<String> childIdList = Arrays.asList(childIds.split(","));
                for (String s : childIdList) {
                    String substring = s.substring(0, s.indexOf("_"));
                    ids.add(Long.valueOf(substring));
                }
            }else {
                //一个原子工具
                String substring = childIds.substring(0, childIds.indexOf("_"));
                ids.add(Long.valueOf(substring));
            }
            if (!ids.isEmpty()) {
                List<ToolsInfoEntity> toolsInfoEntities = toolsInfoMapper.selectToolsInfoIsHaveDescTool(ids);
                for (ToolsInfoEntity toolsInfoEntity : toolsInfoEntities) {
                    if (ToolsTypeEnum.TYPE_COMBINED.getCode().equals(toolsInfoEntity.getType())) {//组合工具
                        //组合工具校验子工具*view的是否复核
                        result = extracted(toolsInfoEntity.getChildIds(), view);
                        if (!result){
                            break;
                        }
                    }else {
                        //验操作用户是否包含在*view配置中
                        if (!view.contains(toolsInfoEntity.getScriptOperatingUser()) && !getArbitrarilyView(view, toolsInfoEntity.getScriptOperatingUser())) {
                            result = false;
                            return result;
                        }
                    }
                }
            }
        }
        return result;
    }
    //组合工具中的多个原子工具，是否已经绑定了agent
    //id 是组合工具id
    private String checkToolsChildBoundAgent(Long id,String childIds,String toolsName,Long userId) throws InteractServiceException {
        String result = "";
        if (null!=childIds && childIds.length()>0) {
            childIds = childIds.substring(1);
            List<Long> ids = new ArrayList<>();
            if (childIds.contains(",")) {
                //多个原子工具
                List<String> childIdList = Arrays.asList(childIds.split(","));
                for (String s : childIdList) {
                    String substring = s.substring(0, s.indexOf("_"));
                    ids.add(Long.valueOf(substring));
                }
            }else {
                //一个原子工具
                String substring = childIds.substring(0, childIds.indexOf("_"));
                ids.add(Long.valueOf(substring));
            }
            if (!ids.isEmpty()) {
                //获取组合下的子工具
                List<ToolsInfoEntity> toolsInfoEntities = toolsInfoMapper.selectToolsInfoIsHaveDescTool(ids);
                for (ToolsInfoEntity toolsInfoEntity : toolsInfoEntities) {
                    if (ToolsTypeEnum.TYPE_COMBINED.getCode().equals(toolsInfoEntity.getType())) {//组合工具
                        result = checkToolsChildBoundAgent(toolsInfoEntity.getId(),toolsInfoEntity.getChildIds(),toolsInfoEntity.getName(),userId);
                        if (StringUtils.isEmpty(result)){
                            break;
                        }else {
                            result+= toolsInfoEntity.getName()+"->"+toolsInfoEntity.getName()+",";
                        }
                    }else {//脚本工具
                        AgentQueryDto agentQueryDto = new AgentQueryDto();
                        agentQueryDto.setSysId(toolsInfoEntity.getBusinessSystemId());
                        List<ToolsAgentInfoEntity> toolsAgentInfoList = new ArrayList<>();
                        List<CommonToolsAgentInfoEntity> commonToolsAgentInfoEntities = commonToolsAgentInfoMapper.selectCommonToolsAgentInfoListByCommonToolsIdAndScriptToolsId(id, toolsInfoEntity.getId());
                        for (CommonToolsAgentInfoEntity entity : commonToolsAgentInfoEntities) {
                            ToolsAgentInfoEntity entity1 = new ToolsAgentInfoEntity();
                            entity1.setAgentIp(entity.getAgentIp());
                            entity1.setAgentPort(entity.getAgentPort());
                            toolsAgentInfoList.add(entity1);
                        }
                        toolsAgentInfoList=toolsAgentInfoService.rmDeleteAgent(toolsAgentInfoList, agentQueryDto, userId);
                        if(toolsAgentInfoList.isEmpty()){
                            result += toolsInfoEntity.getName()+",";
                        }
                    }
                }
            }
        }
        return result;
    }

    /**
     * 匹配*view   存在*view ，rootView ....
     *
     * @param switchInfo          开关值,分割
     * @param scriptOperatingUser 操作用户
     * @return
     */
    public static boolean getArbitrarilyView(List<String> switchInfo, String scriptOperatingUser) {
        boolean result = false;
        if ((switchInfo.contains("*view") || switchInfo.contains("*View") || switchInfo.contains("*VIEW"))
                && scriptOperatingUser.length() > 3) {
            scriptOperatingUser = scriptOperatingUser.toLowerCase();
            if (scriptOperatingUser.substring(scriptOperatingUser.length() - 4).equals("view")) {
                result = true;
            }
        }
        return result;
    }

    /**
     * 判断是否为重要系统
     *
     * @param sysId 业务系统id
     * @return true  重要系统，false 非重要系统
     */
    public boolean isImportantSystem(Long sysId) {
        //调用平台管理系统是否是重要系统
        return systemDataInteract.getBusinessSystemInfoForType(sysId);
    }

    /**
     * 判断工具执行用户是否为开关配置中的审批组内成员，是true，否false
     *
     * @return true 一线人员   false 非一线人员
     */
    private boolean getExecUserGroup(Long execUserId) {
        boolean result = false;
        //调用平台管理获取当前人是否为一线人员  通过服务权限码code 获取接收人        服务权限名： 一线人员人员
        List<UserInfoDto> userInfoDtos = userInfoInteract.
                queryUserInfoListByPermissionCode(AuditorPermissionEnum.AUDIT_PERMISSION_FRONTLINE.getValue());
        for (UserInfoDto userInfoDto : userInfoDtos) {
            if (userInfoDto.getId().equals(execUserId)) {
                result = true;
                break;
            }
        }
        return result;
    }

    /**
     * 插入审批表处理
     *
     * @param executeAuditSaveDto 插入实体内容
     * @param auditorList
     * @param userDto             用户信息
     */
    private AuditDto assembleSaveAudit(ExecuteAuditSaveDto executeAuditSaveDto, List<AuditEverybodyQueryDto> auditorList,
                                       UserDto userDto) throws AuditException {
        AuditEverybodyQueryDto audit = new AuditEverybodyQueryDto();
        audit.setAuditorId(executeAuditSaveDto.getAuditorId());
        audit.setAuditorName(executeAuditSaveDto.getAuditorName());
        auditorList.add(audit);
        //拼接审核人信息
        AuditDto auditDto = auditService.saveAuditDoubleCheck(executeAuditSaveDto.getToolsInfoDto().getId()
                , auditorList,
                executeAuditSaveDto.getExecFrom(), userDto);
        return auditDto;
    }

    //--------------------------------end------------------------------------------------------//
}

package com.ideal.tools.controller;


import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.system.common.component.aop.MethodPermission;
import org.springframework.web.bind.annotation.*;
import com.ideal.tools.model.dto.ToolsEverybodyDto;
import com.ideal.tools.model.dto.ToolsEverybodyQueryDto;
import com.ideal.tools.service.IToolsEverybodyService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 工具箱配置审批人多个关系Controller
 *
 * <AUTHOR>
 * @date 2024-07-10
 */
@RestController
@RequestMapping("${app.script-tools-url:}/tools/everybody")
@MethodPermission("@dp.hasBtnPermission('tool-execution')")
public class ToolsEverybodyController {


    private final IToolsEverybodyService toolsEverybodyService;

    public ToolsEverybodyController(IToolsEverybodyService toolsEverybodyService) {
        this.toolsEverybodyService = toolsEverybodyService;
    }

    /**
     * 查询工具箱配置审批人多个关系列表         沒用
     *
     * @param tableQueryDto 查询条件
     * @return 查询结果
     */
    @GetMapping("/list")
    public R<PageInfo<ToolsEverybodyDto>> list(TableQueryDto<ToolsEverybodyQueryDto> tableQueryDto) {
        PageInfo<ToolsEverybodyDto> list = toolsEverybodyService.selectToolsEverybodyList(
                tableQueryDto.getQueryParam(),
                tableQueryDto.getPageNum(),
                tableQueryDto.getPageSize()
        );
        return R.ok(list);
    }

    /**
     * 查询工具箱配置审批人多个关系详细信息           沒用
     *
     * @param id 数据唯一标识
     * @return 查询结果
     */
    @GetMapping(value = "/get")
    public R<ToolsEverybodyDto> getAgentInfoInfo(@RequestParam(value = "id")Long id) {
        return R.ok(toolsEverybodyService.selectToolsEverybodyById(id));
    }

    /**
     * 新增保存工具箱配置审批人多个关系         沒用
     *
     * @param toolsEverybodyDto 添加数据
     * @return 添加结果
     */
    @PostMapping("/save")
    public R<Void> save(@RequestBody ToolsEverybodyDto toolsEverybodyDto) {
        if (toolsEverybodyService.insertToolsEverybody(toolsEverybodyDto) > 0) {
            return R.ok();
        }
        return R.fail();
    }

    /**
     * 修改保存工具箱配置审批人多个关系         沒用
     *
     * @param toolsEverybodyDto 更新数据
     * @return 更新结果
     */
    @PostMapping("/update")
    public R<Void> update(@RequestBody ToolsEverybodyDto toolsEverybodyDto) {
        toolsEverybodyService.updateToolsEverybody(toolsEverybodyDto);
        return R.ok();
    }


    /**
     * 删除工具箱配置审批人多个关系               沒用
     *
     * @param ids 主键列表
     * @return 删除结果
     */
    @PostMapping("/remove")
    public R<Void> remove(@RequestParam(value = "ids") Long[] ids) {
        toolsEverybodyService.deleteToolsEverybodyByIds(ids);
        return R.ok();
    }
}

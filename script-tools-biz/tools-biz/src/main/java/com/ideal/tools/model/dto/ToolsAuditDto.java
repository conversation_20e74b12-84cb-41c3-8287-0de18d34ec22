package com.ideal.tools.model.dto;
/**
 * 工具详情查询dto
 *
 * <AUTHOR>
 */
public class ToolsAuditDto {


    /**
     * 工具id
     */
    private Long  id;

    /**
     * 工具审批id
     */
    private Long  auditId;

    /**
     * 执行来源
     */
    private Integer execFrom;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getAuditId() {
        return auditId;
    }

    public void setAuditId(Long auditId) {
        this.auditId = auditId;
    }

    public Integer getExecFrom() {
        return execFrom;
    }

    public void setExecFrom(Integer execFrom) {
        this.execFrom = execFrom;
    }

    @Override
    public String toString() {
        return "ToolsAuditDto{" +
                "id=" + id +
                ", auditId=" + auditId +
                ", execFrom=" + execFrom +
                '}';
    }
}

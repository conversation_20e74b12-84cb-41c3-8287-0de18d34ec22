package com.ideal.tools.service.impl;

import com.ideal.common.util.BeanUtils;
import com.ideal.common.util.PageDataUtil;
import com.ideal.common.util.batch.Batch;
import com.ideal.tools.common.AutoDevPageDataUtils;
import com.ideal.tools.common.ExceptionDumper;
import com.ideal.tools.exception.InteractServiceException;
import com.ideal.tools.exception.ToolsException;
import com.ideal.tools.model.dto.*;
import com.ideal.tools.model.interaction.AgentListQueryDto;
import com.ideal.tools.model.interaction.AgentListResultDto;

import com.ideal.tools.service.IToolsAgentInfoService;

import com.ideal.tools.service.interaction.SystemComputerInteract;
import org.springframework.stereotype.Service;
import com.ideal.tools.mapper.ToolsAgentInfoMapper;
import com.ideal.tools.model.entity.ToolsAgentInfoEntity;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;

import java.util.List;

/**
 * 设备Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-27
 */
@Service
public class ToolsAgentInfoServiceImpl implements IToolsAgentInfoService, Batch {
    private final Logger logger = LoggerFactory.getLogger(ToolsAgentInfoServiceImpl.class);

    private final ToolsAgentInfoMapper toolsAgentInfoMapper;

    private final SystemComputerInteract systemComputerInteract;

    public ToolsAgentInfoServiceImpl(ToolsAgentInfoMapper toolsAgentInfoMapper, SystemComputerInteract systemComputerInteract) {
        this.toolsAgentInfoMapper = toolsAgentInfoMapper;
        this.systemComputerInteract = systemComputerInteract;
    }

    /**
     * 查询设备
     *
     * @param id 设备主键
     * @return 设备
     */
    @Override
    public ToolsAgentInfoDto selectToolsAgentInfoById(Long id) {
        ToolsAgentInfoEntity toolsAgentInfo = toolsAgentInfoMapper.selectToolsAgentInfoById(id);
        return BeanUtils.copy(toolsAgentInfo, ToolsAgentInfoDto.class);
    }

    /**
     * 查询设备列表
     *
     * @param toolsAgentInfoQueryDto 设备
     * @param pageNum                页码
     * @param pageSize               单页长度
     * @return 设备
     */
    @Override
    public PageInfo<ToolsAgentInfoDto> selectToolsAgentInfoList(ToolsAgentInfoQueryDto toolsAgentInfoQueryDto, Integer pageNum, Integer pageSize) {
        ToolsAgentInfoEntity query = BeanUtils.copy(toolsAgentInfoQueryDto, ToolsAgentInfoEntity.class);
        PageMethod.startPage(pageNum, pageSize);
        List<ToolsAgentInfoEntity> toolsAgentInfoList = toolsAgentInfoMapper.selectToolsAgentInfoList(query);
        return PageDataUtil.toDtoPage(toolsAgentInfoList, ToolsAgentInfoDto.class);
    }

    /**
     * 新增设备
     *
     * @param toolsAgentInfoDto 设备
     * @return 结果
     */
    @Override
    public int insertToolsAgentInfo(ToolsAgentInfoDto toolsAgentInfoDto) {
        ToolsAgentInfoEntity toolsAgentInfo = BeanUtils.copy(toolsAgentInfoDto, ToolsAgentInfoEntity.class);
        return toolsAgentInfoMapper.insertToolsAgentInfo(toolsAgentInfo);
    }

    /**
     * 修改设备
     *
     * @param toolsAgentInfoDto 设备
     * @return 结果
     */
    @Override
    public int updateToolsAgentInfo(ToolsAgentInfoDto toolsAgentInfoDto) {
        ToolsAgentInfoEntity toolsAgentInfo = BeanUtils.copy(toolsAgentInfoDto, ToolsAgentInfoEntity.class);
        return toolsAgentInfoMapper.updateToolsAgentInfo(toolsAgentInfo);
    }

    /**
     * 批量删除设备
     *
     * @param ids 需要删除的设备主键
     * @return 结果
     */
    @Override
    public int deleteToolsAgentInfoByIds(Long[] ids) {
        return toolsAgentInfoMapper.deleteToolsAgentInfoByIds(ids);
    }

    /**
     * agent列表(分页)
     * 备注：工具执行与 脚本工具编辑  都是用的此方法
     * 工具执行（公共）：如果是公共系统，查询当前登陆人下所有系统绑定的agent
     * 工具执行（不是公共）：如果不是公共系统，查询当前工具下绑定的agent
     * 脚本工具编辑（不是公共）：如果不是公共系统，查询该系统下绑定的agent
     * @param agentQueryDto 参数列表
     * @return 结果
     */
    @Override
    public PageInfo<ToolsAgentResultDto> selectStandardTaskComputerIdLists(AgentQueryDto agentQueryDto, Long userId, Integer pageNum, Integer pageSize) throws InteractServiceException {

        if (agentQueryDto == null) {
            agentQueryDto = new AgentQueryDto();
        }
        if(agentQueryDto.getSysId()== -1L){
            agentQueryDto.setTdToolsId(null);
        }

        //是否查询工具设备
        if (agentQueryDto.getTdToolsId() != null) {
            if (agentQueryDto.getSelected() != null) {
                if (agentQueryDto.getSelected() == 1 && agentQueryDto.getAgentList() == null || agentQueryDto.getSelected() == 1 && agentQueryDto.getAgentList().isEmpty()) {
                    return new PageInfo<>();
                }
            }
            AgentQueryDto agent = new AgentQueryDto();
            agent.setTdToolsId(agentQueryDto.getTdToolsId());
            //查询本地是否存储工具设备
            List<ToolsAgentInfoEntity> toolsAgentInfoList = selectToolsAgentInfoList(agent);
            if (toolsAgentInfoList.isEmpty()) {
                //如果没有配置设备 取所有设备
                return standardAgent(agentQueryDto, userId, pageNum, pageSize);
            } else {
                //获取预存的 agent   设备集合
                return selectAgentLists(agentQueryDto, pageNum, pageSize,userId);
            }


        }

        return standardAgent(agentQueryDto, userId, pageNum, pageSize);
    }

    /**
     * agent列表(分页)
     * 流程编排那边  组合引用的原子  下面  选择agent
     * 如果是公共系统，查询当前登陆人下所有系统绑定的agent
     * 如果不是公共系统，查询当前工具下绑定的agent
     * @param agentQueryDto 参数列表
     * @return 结果
     */
    @Override
    public PageInfo<ToolsAgentResultDto> selectCurrentToolIdBingComputerIdLists(AgentQueryDto agentQueryDto, Long userId, Integer pageNum, Integer pageSize) throws InteractServiceException {

        if (agentQueryDto == null) {
            agentQueryDto = new AgentQueryDto();
        }
		Integer selected=agentQueryDto.getSelected();
		if ((selected!=null&&selected!=1)&&agentQueryDto.getSysId() == -1L){
            return standardAgent(agentQueryDto, userId, pageNum, pageSize);
        }else {
        	//是否查询工具设备
            if (agentQueryDto.getTdToolsId() != null) {
                if (agentQueryDto.getSelected() != null) {
                    if (agentQueryDto.getSelected() == 1 && agentQueryDto.getAgentList() == null || agentQueryDto.getSelected() == 1 && agentQueryDto.getAgentList().isEmpty()) {
                        return new PageInfo<>();
                    }
                }
                AgentQueryDto agent = new AgentQueryDto();
                agent.setTdToolsId(agentQueryDto.getTdToolsId());
                return selectAgentLists(agentQueryDto, pageNum, pageSize,userId);
            }
            return new PageInfo<>();
        }
    }

    /**
     * 获取所有 agent信息
     *
     * @param agentQueryDto  tdToolsId 工具id，computerIp 设备IP , agentIpEnd 结束IP agentIpStart 开始IP
     * @return
     * @throws InteractServiceException
     */
    @Override
    public List<ToolsAgentInfoEntity> selectToolsAgentInfoList(AgentQueryDto agentQueryDto) throws InteractServiceException {

        ToolsAgentInfoEntity query = BeanUtils.copy(agentQueryDto, ToolsAgentInfoEntity.class);
        query.setTdToolsId(agentQueryDto.getTdToolsId());
        query.setAgentIp(agentQueryDto.getComputerIp());
        query.setAgentIpEnd(ipToLong(agentQueryDto.getAgentIpEnd()));
        query.setAgentIpStart(ipToLong(agentQueryDto.getAgentIpStart()));
        return toolsAgentInfoMapper.selectToolsAgentInfoList(query);
    }
    
    /**
     * 获取组合调脚本/组合 绑定agent信息
     *
     * @param agentQueryDto  tdToolsId 工具id，computerIp 设备IP , agentIpEnd 结束IP agentIpStart 开始IP
     * @return
     * @throws InteractServiceException
     */
    @Override
    public List<ToolsAgentInfoEntity> selectToolsAgentInfoListb(AgentQueryDto agentQueryDto) throws InteractServiceException {
        ToolsAgentInfoEntity query = BeanUtils.copy(agentQueryDto, ToolsAgentInfoEntity.class);
        query.setTdToolsId(agentQueryDto.getTdToolsId());
        query.setAgentIp(agentQueryDto.getComputerIp());
        return toolsAgentInfoMapper.selectToolsAgentInfoListb(query);
    }

    /**
     * 获取预存的 agent   设备
     *
     * @param agentQueryDto
     * @param pageNum
     * @param pageSize
     * @param userDto
     * @return
     * @throws InteractServiceException
     */
    public PageInfo<ToolsAgentResultDto> selectAgentLists(AgentQueryDto agentQueryDto, Integer pageNum, Integer pageSize, Long userId) throws InteractServiceException {
    	logger.info("查询哪些agent在工具绑定agent中存在 :{} ", agentQueryDto);
    	List<ToolsAgentInfoEntity> toolsAgentInfoList = selectToolsAgentInfoList(agentQueryDto);
    	toolsAgentInfoList = rmDeleteAgent(toolsAgentInfoList,agentQueryDto,userId);
    	if (agentQueryDto.getSelected() == null) {
            List<ToolsAgentResultDto> toolsAgentResultList = BeanUtils.copy(toolsAgentInfoList, ToolsAgentResultDto.class);
            return AutoDevPageDataUtils.getPageInfo(toolsAgentResultList, pageNum, pageSize);
        }
        List<ToolsAgentInfoEntity> agentInfoList = getAgemtInfoEntityList(toolsAgentInfoList, agentQueryDto.getAgentList(), agentQueryDto.getSelected());
        List<ToolsAgentResultDto> toolsAgentResultList = BeanUtils.copy(agentInfoList, ToolsAgentResultDto.class);
        return AutoDevPageDataUtils.getPageInfo(toolsAgentResultList, pageNum, pageSize);
    }
    
    /**
     * 
     * @Description 去掉平台管理中删除的agent
     * @param toolsAgentInfoList
     * @param agentQueryDto
     * @param userDto
     * @return
     * @throws InteractServiceException
     * <AUTHOR>
     * @date 2025-04-10 07:03:53
     */
    @Override
    public List<ToolsAgentInfoEntity> rmDeleteAgent(List<ToolsAgentInfoEntity> toolsAgentInfoList,AgentQueryDto agentQueryDto, Long userId) throws InteractServiceException {
    	//查询平台管理当前业务系统底下所有的agent信息
        //进行比对，如果不存则进行删除
        //查询所有的agent
        List<AgentListResultDto> agentAllList = systemComputerInteract.getAgentAllListBySysId(agentQueryDto,userId);
        //校验工具箱查询出来的agent是否在平台管理存在，不存在则不进行插入
        // 创建一个集合来存储需要删除的ToolsAgentInfoEntity对象
        List<ToolsAgentInfoEntity> toRemove = new ArrayList<>();
        // 遍历工具箱的Agent信息列表
        for (ToolsAgentInfoEntity toolsAgentInfoEntity : toolsAgentInfoList) {
            boolean found = false;
            // 遍历平台管理的Agent列表
            for (AgentListResultDto agentListResultDto : agentAllList) {
                // 比对agentName, agentIp, agentPort
                if (toolsAgentInfoEntity.getAgentIp().equals(agentListResultDto.getAgentIp()) &&
                        toolsAgentInfoEntity.getAgentPort().equals(agentListResultDto.getPort())) {
                    found = true;
                    break;
                }
            }
            // 如果没有找到匹配的Agent，则标记为需要删除
            if (!found) {
                toRemove.add(toolsAgentInfoEntity);
            }
        }
        // 从toolsAgentInfoList中删除标记的Agent
        toolsAgentInfoList.removeAll(toRemove);
        return toolsAgentInfoList;
    }

    /**
     * 组织 取出  排除  设备
     *
     * @param toolsAgentInfoList 所有设备
     * @param agentList          取出  排除设备
     * @param selected           取出 1 排除 0
     * @return
     * @throws InteractServiceException
     */
    public List<ToolsAgentInfoEntity> getAgemtInfoEntityList(List<ToolsAgentInfoEntity> toolsAgentInfoList, List<AgentSelectedDto> agentList, Integer selected) {

        List<ToolsAgentInfoEntity> agentInfoList = new ArrayList<>();

        for (ToolsAgentInfoEntity toolsAgentInfoEntity : toolsAgentInfoList) {

            if (agentList == null || agentList.isEmpty()) {

                agentInfoList.add(toolsAgentInfoEntity);
            } else {
                int selectedAgent = 1;
                for (AgentSelectedDto agentSelectedDto : agentList) {
                    ToolsAgentInfoEntity agentInfo = getAgemtInfoEntitySelectedList(agentSelectedDto, toolsAgentInfoEntity, selected);
                    if (agentInfo != null && selected == 1) {
                        selectedAgent = 2;
                    }
                    if (selected == 0) {
                        if (agentSelectedDto.getAgentPort().equals(toolsAgentInfoEntity.getAgentPort())
                                && agentSelectedDto.getAgentIp().equals(toolsAgentInfoEntity.getAgentIp())) {
                            selectedAgent = 2;
                        }
                    }
                }
                if (selectedAgent == 2 && selected == 1) {
                    agentInfoList.add(toolsAgentInfoEntity);
                } else if (selectedAgent == 1 && selected == 0) {
                    agentInfoList.add(toolsAgentInfoEntity);
                }
            }
        }
        return agentInfoList;
    }

    /**
     * 组织 取出  排除  设备
     *
     * @param agentSelectedDto     Agent 排除 查询dto
     * @param toolsAgentInfoEntity 计划存储 Agent  所有列表
     * @param selected             取出 1 排除 0
     * @return
     * @throws InteractServiceException
     */
    public ToolsAgentInfoEntity getAgemtInfoEntitySelectedList(AgentSelectedDto agentSelectedDto, ToolsAgentInfoEntity toolsAgentInfoEntity, Integer selected) {

        ToolsAgentInfoEntity agentInfo = null;

        if (agentSelectedDto == null
                || toolsAgentInfoEntity == null) {
            return agentInfo;
        }

        if (agentSelectedDto.getAgentIp() == null || agentSelectedDto.getAgentPort() == null
                || toolsAgentInfoEntity.getAgentIp() == null || toolsAgentInfoEntity.getAgentPort() == null) {
            return agentInfo;
        }
        if (agentSelectedDto.getAgentPort().equals(toolsAgentInfoEntity.getAgentPort())
                && agentSelectedDto.getAgentIp().equals(toolsAgentInfoEntity.getAgentIp())) {
            if (selected == 1) {
                agentInfo = toolsAgentInfoEntity;
            }
        }
        return agentInfo;
    }

    /**
     * 获取业务系统下所有设备
     *
     * @param agentQueryDto 参数列表
     * @return 结果
     */
    public PageInfo<ToolsAgentResultDto> standardAgent(AgentQueryDto agentQueryDto, Long userId, Integer pageNum, Integer pageSize) throws InteractServiceException {
        PageInfo<AgentListResultDto> agentRespPage;
        AgentListQueryDto agentReqDto = BeanUtils.copy(agentQueryDto, AgentListQueryDto.class);


        agentReqDto.setSysId(agentQueryDto.getSysId());
        /**
         * 设备IP起始
         */
        agentReqDto.setStartIp(agentQueryDto.getAgentIpStart());

        /**
         * 设备IP结束
         */
        agentReqDto.setEndIp(agentQueryDto.getAgentIpEnd());
        if (agentQueryDto.getSelected() != null && agentQueryDto.getAgentList() != null) {
            List<AgentSelectedDto> agentList = agentQueryDto.getAgentList();
            List<com.ideal.tools.model.interaction.AgentInfoDto> excludeAgentList = new ArrayList<>();
            for (AgentSelectedDto agentSelectedDto : agentList) {
                com.ideal.tools.model.interaction.AgentInfoDto agentInfoDto = new com.ideal.tools.model.interaction.AgentInfoDto();
                agentInfoDto.setIp(agentSelectedDto.getAgentIp());
                agentInfoDto.setPort(agentSelectedDto.getAgentPort());
                excludeAgentList.add(agentInfoDto);
            }
            agentReqDto.setExcludeAgentList(excludeAgentList);

            Boolean selected = false;
            if (agentQueryDto.getSelected() == 0) {
                selected = true;
            }

            agentReqDto.setAgentParamIsExclude(selected);
        } else {
            agentReqDto.setAgentParamIsExclude(true);
        }
        try {
            logger.info("batchInsertToolsAgent Obtain agent service query information :{} ", agentReqDto);
            agentRespPage = systemComputerInteract.getAgentExcludeList(agentReqDto,userId,pageNum, pageSize);
        } catch (Exception e) {
            logger.error("batchInsertToolsAgent save tools id :{} is error", agentReqDto);
            throw new InteractServiceException(e);
        }
        List<ToolsAgentResultDto> toolsAgent = new ArrayList<>();
        if (agentRespPage != null) {
            for (AgentListResultDto agentResp : agentRespPage.getList()) {
                ToolsAgentResultDto agent = new ToolsAgentResultDto();

                /** 设备操作系统 */

                /** agent名称 */
                agent.setAgentName(agentResp.getName());
                /** IP */
                agent.setAgentIp(agentResp.getAgentIp());
                /** 端口 */
                agent.setAgentPort(agentResp.getPort());
                /** 状态 */
                agent.setAgentState(agentResp.getRegisterState());

                agent.setSysAgentInfoId(agentResp.getAgentId());


                toolsAgent.add(agent);
            }
        }
        return AutoDevPageDataUtils.getPageInfo(agentRespPage, toolsAgent);
    }

    /**
     * 新增脚本agent多个关系
     *
     * @param toolsId            工具id
     * @param toolsAgentInfoList 双人复核审批人多个关系
     * @return 结果
     */
    @Override
    public void batchInsertToolsAgent(Long toolsId, List<ToolsAgentInfoDto> toolsAgentInfoList) throws ToolsException {
        try {
            List<ToolsAgentInfoEntity> auditEverybodyList = new ArrayList<>();

            for (ToolsAgentInfoDto toolsAgentDto : toolsAgentInfoList) {
                ToolsAgentInfoEntity toolsAgent = BeanUtils.copy(toolsAgentDto, ToolsAgentInfoEntity.class);
                toolsAgent.setId(null);
                toolsAgent.setTdToolsId(toolsId);
                toolsAgent.setIpLong(ipToLong(toolsAgentDto.getAgentIp()));
                auditEverybodyList.add(toolsAgent);
            }

            this.batchData(auditEverybodyList, toolsAgentInfoMapper::insertToolsAgentInfo);
        } catch (Exception e) {
            logger.error("batchInsertToolsAgent save tools id :{} is error", toolsId);
            ExceptionDumper.wrap(e);
            throw new ToolsException(e);
        }
    }

    /**
     * 批量删除设备
     *
     * @param tdToolsId 需要删除的设备工具主键
     * @return 结果
     */
    @Override
    public int deleteAgentToolId(Long tdToolsId) {
        return toolsAgentInfoMapper.deleteAgentToolId(tdToolsId);
    }

    @Override
    public List<String> selectAgentIpByToolIdAndAgentIp(ToolsAgentInfoEntity toolsAgentInfo) {
        return toolsAgentInfoMapper.selectAgentIpByToolIdAndAgentIp(toolsAgentInfo);
    }

    /**
     * 根据工具ID查询绑定的agent数量
     *
     * @param tdToolsId 工具id
     * @return 工具ID对应绑定的agent数量
     */
    @Override
    public Integer selectAgentCountByToolId(Long tdToolsId) {
        return toolsAgentInfoMapper.selectAgentCountByToolId(tdToolsId);
    }


    /**
     * IP192.168.1.1 as long: 3232235777
     *
     * @param ip
     * @return
     */
    public static Long ipToLong(String ip) throws InteractServiceException {
        if (ip == null || "".equals(ip)) {
            return null;
        }
        if (!isValidIp(ip)) {
            throw new InteractServiceException("The format of the search condition ip address is incorrect!");
        }
        String[] parts = ip.split("\\.");
        long result = 0;
        for (int i = 0; i < parts.length; i++) {
            int octet = Integer.parseInt(parts[i]);
            // 左移8位，加上当前段的值
            result = (result << 8) + octet;
        }

        return result;
    }

    public static boolean isValidIp(String ip) {
        String[] parts = ip.split("\\.");
        if (parts.length != 4) {
            return false;
        }
        for (String part : parts) {
            if (part.isEmpty() || part.matches("[^\\d]")) {
                return false;
            }
            int num = Integer.parseInt(part);
            if (num < 0 || num > 255) {
                return false;
            }
        }
        return true;
    }


}

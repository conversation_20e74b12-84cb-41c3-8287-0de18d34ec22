package com.ideal.tools.service;

import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.tools.exception.AuditException;
import com.ideal.tools.exception.InteractServiceException;
import com.ideal.tools.exception.StudioException;
import com.ideal.tools.exception.ToolsException;
import com.ideal.tools.model.dto.*;

public interface IToolsExecuteService {
    /**
     * 获取执行按钮状态
     */
    R<ExecuteAuditResultDto> getExecutButtonStatus(ExecutAuditQueryDto executAuditQueryDto);

    /**
     * 保存执行审批
     *
     * @param executeAuditSaveDto 提交审批工具信息
     * @param user
     * @return
     */
    ExecuteAuditResultDto saveExecutToolsAuditStatus(ExecuteAuditSaveDto executeAuditSaveDto, UserDto user) throws AuditException;

    /**
     * 双人复核查看任务详细信息
     * @param executAuditQueryDto
     * @return
     */
    ExecuteAuditResultDto getAuditDetails(ExecutAuditQueryDto executAuditQueryDto);

    /**
     * 撤回工具执行审批
     * @param executAuditQueryDto  toolsid  工具id， execFrom 来源   4.场景工具 5.告警诊断 7.工具执行 用户id
     * @return
     */
    R<Object> revokeExecutToolsAudit(ExecutAuditQueryDto executAuditQueryDto);

    /**
     * 工具执行审批
     *
     * @param executeToolsDto
     * @param user
     * @return
     */
    R<Object> executeTools(ExecuteToolsDto executeToolsDto, UserDto user) throws ToolsException;
    /**
     * 查询工具执行详情信息
     *
     * @param toolsAuditDto
     * @param user
     * @param ipAddress
     * @return
     */
    ToolsDto getToolsCombinedInfoAll(ToolsAuditDto toolsAuditDto, UserDto user, String ipAddress);

    /**
     * 工具执行页面列表展示
     *
     * @param queryParam 查询条件  工具编码,工具名称,工具类型,应用系统,一级分类,二级分类,工具类型,脚本名称,是否高危
     * @param pageNum
     * @param pageSize
     * @param user
     * @return
     */
    PageInfo<ToolsInfoDto> selectExecuteList(ToolsQueryDto queryParam, Integer pageNum, Integer pageSize, UserDto user);

    /**
     * 自定义组合该工具执行
     *
     * @param execFrom
     * @param toolsDto
     * @param user
     * @return
     */
    R<Object> customComToolExecute(Integer execFrom, ToolsDto toolsDto, UserDto user) throws ToolsException, StudioException;

    /**
     * 获取工具是否绑定agent
     * @param toolsInfoQueryDto
     * @return
     */
    R<Object> checkToolsExecuteBondAgent(ToolsInfoQueryDto toolsInfoQueryDto,Long userId) throws InteractServiceException;
}

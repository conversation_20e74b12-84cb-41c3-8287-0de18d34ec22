package com.ideal.tools.controller;


import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;

import com.ideal.system.common.component.aop.MethodPermission;
import com.ideal.tools.model.dto.TrackDto;

import org.springframework.web.bind.annotation.*;
import com.ideal.tools.model.dto.AuditDto;
import com.ideal.tools.model.dto.AuditQueryDto;
import com.ideal.tools.service.IAuditService;



/**
 * 双人复核服务关系Controller
 *
 * <AUTHOR>
 * @date 2024-06-25
 */
@RestController
@RequestMapping("${app.script-tools-url:}/tools/audit")
@MethodPermission("@dp.hasBtnPermission('tool-execution')")
public class AuditController {


    private final IAuditService auditService;

    public AuditController(IAuditService auditService) {
        this.auditService = auditService;
    }

    /**
     * 查询双人复核服务关系列表
     *
     * @param tableQueryDto 查询条件
     * @return 查询结果
     */
    @GetMapping("/list")
    public R<PageInfo<AuditDto>> list(TableQueryDto<AuditQueryDto> tableQueryDto) {
        PageInfo<AuditDto> list = auditService.selectAuditList(
                tableQueryDto.getQueryParam(),
                tableQueryDto.getPageNum(),
                tableQueryDto.getPageSize()
        );
        return R.ok(list);
    }

    /**
     * 查询双人复核服务关系详细信息    沒用
     *
     * @param id 数据唯一标识
     * @return 查询结果
     */
    @GetMapping(value = "/get")
    public R<AuditDto> getAgentInfoInfo(@RequestParam(value = "id")Long id) {
        return R.ok(auditService.selectAuditById(id));
    }

    /**
     * 新增保存双人复核服务关系
     *
     * @param auditDto 添加数据    沒用
     * @return 添加结果
     */
    @PostMapping("/save")
    public R<Void> save(@RequestBody AuditDto auditDto) {
        if (auditService.insertAudit(auditDto) > 0) {
            return R.ok();
        }
        return R.fail();
    }

    /**
     * 修改保存双人复核服务关系
     *
     * @param auditDto 更新数据    沒用
     * @return 更新结果
     */
    @PostMapping("/update")
    public R<Void> update(@RequestBody AuditDto auditDto) {
        auditService.updateAudit(auditDto);
        return R.ok();
    }


    /**
     * 删除双人复核服务关系
     *
     * @param ids 主键列表     沒用
     * @return 删除结果
     */
    @PostMapping("/remove")
    public R<Void> remove(@RequestParam(value = "ids") Long[] ids) {
        auditService.deleteAuditByIds(ids);
        return R.ok();
    }



    /**
     * 获取双人复核任务审批轨迹信息
     *
     * @param toolId 工具id
     * @return 查询结果
     */
    @GetMapping("/listDoubleCheckTrack")
    @MethodPermission("@dp.hasBtnPermission('tool-execution') or @dp.hasBtnPermission('approvalProcess')")
    public R<TrackDto>  listDoubleCheckTrack(@RequestParam(value = "toolId")Long toolId) {

        TrackDto trackDto = auditService.listDoubleCheckTrack(toolId);
        return R.ok(trackDto);
    }
}

package com.ideal.tools.model.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ideal.snowflake.annotion.IdGenerator;

import javax.xml.crypto.Data;
import java.io.Serializable;
import java.util.Date;

/**
 * 审计日志自定义对象 ieai_tb_log_auditing
 *
 * <AUTHOR>
 * @date 2024-06-17
 */
public class LogAuditEntity implements Serializable {
  private static final long serialVersionUID=1L;
  /** 主键 */
  @IdGenerator
  private Long id;
  /** 操作时间 */
  @JsonFormat(pattern = "yyyy-MM-dd hh:mm:ss")
  private Date operatingTime;
  /** 操作结束时间 */
  @JsonFormat(pattern = "yyyy-MM-dd hh:mm:ss")
  private Date operatingEndTime;

  /** IP地址 */
  private String ip;

  /** 操作人id */
  private Long operatorId;

  /** 操作人 */
  private String operatorName;

  /** 操作模块id */
  private Integer moduleId;

  /** 操作类别id */
  private Integer typeId;

  /** 操作内容 */
  private String operatingContent;

  /** 操作结果 0：成功 1：失败 */
  private Integer result;

  /** 结果描述 */
  private String resultDesc;

  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public static long getSerialVersionUID() {
    return serialVersionUID;
  }

  public Date getOperatingTime() {
    return operatingTime;
  }

  public void setOperatingTime(Date operatingTime) {
    this.operatingTime = operatingTime;
  }

  public Date getOperatingEndTime() {
    return operatingEndTime;
  }

  public void setOperatingEndTime(Date operatingEndTime) {
    this.operatingEndTime = operatingEndTime;
  }

  public String getIp() {
    return ip;
  }

  public void setIp(String ip) {
    this.ip = ip;
  }

  public Long getOperatorId() {
    return operatorId;
  }

  public void setOperatorId(Long operatorId) {
    this.operatorId = operatorId;
  }

  public String getOperatorName() {
    return operatorName;
  }

  public void setOperatorName(String operatorName) {
    this.operatorName = operatorName;
  }

  public Integer getModuleId() {
    return moduleId;
  }

  public void setModuleId(Integer moduleId) {
    this.moduleId = moduleId;
  }

  public Integer getTypeId() {
    return typeId;
  }

  public void setTypeId(Integer typeId) {
    this.typeId = typeId;
  }

  public String getOperatingContent() {
    return operatingContent;
  }

  public void setOperatingContent(String operatingContent) {
    this.operatingContent = operatingContent;
  }

  public Integer getResult() {
    return result;
  }

  public void setResult(Integer result) {
    this.result = result;
  }

  public String getResultDesc() {
    return resultDesc;
  }

  public void setResultDesc(String resultDesc) {
    this.resultDesc = resultDesc;
  }

  @Override
  public String toString() {
    return "LogAuditEntity{" +
            "id=" + id +
            ", operatingTime='" + operatingTime + '\'' +
            ", ip='" + ip + '\'' +
            ", operatorId='" + operatorId + '\'' +
            ", operatorName='" + operatorName + '\'' +
            ", moduleId='" + moduleId + '\'' +
            ", typeId='" + typeId + '\'' +
            ", operatingContent='" + operatingContent + '\'' +
            ", result=" + result +
            ", resultDesc='" + resultDesc + '\'' +
            '}';
  }
}


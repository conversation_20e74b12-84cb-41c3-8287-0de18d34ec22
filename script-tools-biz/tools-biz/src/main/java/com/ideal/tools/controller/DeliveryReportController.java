package com.ideal.tools.controller;


import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.system.common.component.aop.MethodPermission;
import com.ideal.tools.common.Constants;
import com.ideal.tools.common.ControlsTimeUtil;
import com.ideal.tools.common.EasyExcelUtil;
import com.ideal.tools.core.web.BaseController;
import com.ideal.tools.model.bean.DeliveryReportExcle;
import com.ideal.tools.model.dto.*;
import com.ideal.tools.model.enums.*;
import com.ideal.tools.service.IDeliveryReportService;
import com.ideal.tools.service.ILogAuditService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.util.List;


/**
 * 工具接收报表自定义分类Controller
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
@RestController
@RequestMapping("${app.script-tools-url:}/tools/deliveryReport")
public class DeliveryReportController extends BaseController {

    private final IDeliveryReportService deliveryReportService;
    private final ILogAuditService logAuditService;

    public DeliveryReportController(IDeliveryReportService deliveryReportService,ILogAuditService logAuditService) {
        this.deliveryReportService = deliveryReportService;
        this.logAuditService = logAuditService;
    }

    /**
     * 查询工具接收报表自定义分类列表
     *
     * @param tableQueryDto 查询条件
     * @return 查询结果
     */
    @PostMapping("/list")
    public R<PageInfo<DeliveryReportResultDto>> list(@RequestBody TableQueryDto<DeliveryReportQueryDto> tableQueryDto) {
        PageInfo<DeliveryReportResultDto> pages =null;
        try{
            pages = deliveryReportService.selectDeliveryReportList(
                    tableQueryDto.getQueryParam(),
                    tableQueryDto.getPageNum(),
                    tableQueryDto.getPageSize()
            );
            logger.info("success to query Toolbox delivery reports ");
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, pages, ConstantsEnum.LIST_SUCCESS.getDesc());
        }catch (Exception e){
            logger.error("Failed to query Toolbox delivery reports !{}",e.getMessage(), e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE,pages , ConstantsEnum.LIST_FAIL.getDesc());
        }
    }

    /**
     * 工具接收报表插入自定义     沒用
     *
     * @param deliveryReportDto 工具接收报表自定义分类
     */
    @PostMapping("/save")
    public R<Object> insertDeliveryReport(@RequestBody DeliveryReportDto deliveryReportDto) {
        try {
            deliveryReportService.insertDeliveryReport(deliveryReportDto,getUser());
            logger.info("insert deliveryReport success!");
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, "成功", ConstantsEnum.SAVE_SUCCESS.getDesc());
        }catch (Exception e) {
            logger.error("insert deliveryReport failed!", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, "失败", ConstantsEnum.SAVE_FAIL.getDesc() );
        }
    }

    /**
     * 修改保存工具接收报表   根据工具id、移交人、工具名称、接收人     沒用
     *
     * @param deliveryReportDto 更新数据
     * @return 更新结果
     */
    @PostMapping("/update")
    public R<Object> update(@RequestBody DeliveryReportDto deliveryReportDto) {
        try {
            deliveryReportService.updateDeliveryReport(deliveryReportDto);
            logger.info("update deliveryReport success!");
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, "成功", ConstantsEnum.UPDATE_SUCCESS.getDesc());
        }catch (Exception e) {
            logger.error("update deliveryReport failed!", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, "失败", ConstantsEnum.UPDATE_FAIL.getDesc() );
        }
    }


    /**
     * 工具接收报表下载，最多下载3000行
     *
     * @param queryDto 下载条件
     * @return 查询结果
     */
    @PostMapping("/exportDeliveryReportData")
    @MethodPermission("@dp.hasBtnPermission('exportReceivingReports')")
    public void exportDeliveryReportData(HttpServletResponse response, @RequestBody DeliveryReportQueryDto queryDto) throws UnsupportedEncodingException {
        try{
            List<DeliveryReportExcle> list = deliveryReportService.exportDeliveryReportData(queryDto);
            // 使用 EasyExcel 将数据写入响应输出流
            String fileName = OperatModuleEnum.TOOL_DELIVERY_REPORT.getRemark() + ControlsTimeUtil.formatDateByFormat(new Date(), "YYYY-MM-dd hh_mm_ss");
            EasyExcelUtil.writeExcel(response, list, fileName, OperatModuleEnum.TOOL_DELIVERY_REPORT.getRemark(), DeliveryReportExcle.class);
            String operateContent = CommonTypeEnum.EXPORT.getName() + fileName + "到本地";
            String resultDesc = OperatModuleEnum.TOOL_DELIVERY_REPORT.getRemark() + OperatTypeEnum.DELIVERY_REPORT_EXPORT.getRemark();
            LogAuditDto logAuditDto = new LogAuditDto(OperatModuleEnum.TOOL_DELIVERY_REPORT.getCode(), OperatTypeEnum.DELIVERY_REPORT_EXPORT.getCode(), operateContent);
            logAuditDto.setResult(OperatResultEnum.SUCCESS.getCode());
            logAuditDto.setResultDesc(resultDesc + OperatResultEnum.SUCCESS.getDesc());
            logAuditService.insertLogAudit(logAuditDto);
            logger.info("exportDeliveryReportData success");
        } catch (Exception e) {
            logger.error("exportDeliveryReportData error", e);
        }
    }

}


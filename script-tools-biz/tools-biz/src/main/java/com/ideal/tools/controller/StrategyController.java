package com.ideal.tools.controller;

import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.common.dto.ValidateError;
import com.ideal.common.util.spring.MessageUtil;
import com.ideal.system.common.component.aop.MethodPermission;
import com.ideal.tools.common.Constants;
import com.ideal.tools.core.web.BaseController;
import com.ideal.tools.model.dto.*;
import com.ideal.tools.model.enums.ConstantsEnum;
import com.ideal.tools.model.interaction.AgentListQueryDto;
import com.ideal.tools.model.interaction.AgentListResultDto;
import com.ideal.tools.service.IStrategyService;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("${app.script-tools-url:}/tools/strategy")
public class StrategyController extends BaseController {

    private final IStrategyService strategyService;

    public StrategyController(IStrategyService strategyService){
        this.strategyService = strategyService;
    }

    /**
     * 保存自愈策略模板信息
     * @param strategyDto 自愈模板参数
     * @param bindingResult 验证传参
     * @return 结果
     */
    @PostMapping("/save")
    @MethodPermission("@dp.hasBtnPermission('addSelfHealing')")
    public R<Object> save(@Valid @RequestBody StrategyDto strategyDto, BindingResult bindingResult){
        if (bindingResult.hasErrors()) {
            List<ValidateError> validateErrorList = bindingResult.getFieldErrors().stream()
                    .map(item -> new ValidateError(item.getField(), MessageUtil.message(item.getDefaultMessage())))
                    .collect(Collectors.toList());
            Map<String, List<ValidateError>> validateError = new HashMap<>(1);
            validateError.put("validateError", validateErrorList);
            return R.fail(Constants.REPONSE_STATUS_VALIDATA_CODE, validateError, "validate.error");
        }

        try {
            return strategyService.insertStrategy(strategyDto, getUser());
        }catch (Exception e) {
            logger.error("toolbox.strategy for save customization failed!", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, null,ConstantsEnum.SAVE_FAIL.getDesc() );
        }
    }

    /**
     * 根据ids删除自愈策略模板
     * @param ids 模板id数组
     * @return 删除条数
     */
    @PostMapping("/delete")
    public R<Object> deleteStrategyByIds(@RequestBody Long[] ids){
        try{
            int num = strategyService.deleteStrategyByIds(ids, getUser());
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, num, ConstantsEnum.REMOVE_SUCCESS.getDesc());
        }catch (Exception e) {
            logger.error("Delete toolbox custom classification failed!", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, null, ConstantsEnum.REMOVE_FAIL.getDesc());
        }
    }

    /**
     * 修改自愈策略模板参数
     * @param strategyDto 策略模板参数
     * @param bindingResult 验参
     * @return 修改成功的条数
     */
    @PostMapping("/update")
    public R<Object> updateStrategyById(@Valid @RequestBody StrategyDto strategyDto, BindingResult bindingResult){
        if (bindingResult.hasErrors()) {
            List<ValidateError> validateErrorList = bindingResult.getFieldErrors().stream()
                    .map(item -> new ValidateError(item.getField(), MessageUtil.message(item.getDefaultMessage())))
                    .collect(Collectors.toList());
            Map<String, List<ValidateError>> validateError = new HashMap<>(1);
            validateError.put("validateError", validateErrorList);
            return R.fail(Constants.REPONSE_STATUS_VALIDATA_CODE, validateError, "validate.error");
        }

        try {
            return strategyService.updateStrategyById(strategyDto, getUser());
        }catch (Exception e) {
            logger.error("toolbox for StrategyController.updateStrategyById is failed!", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, null,ConstantsEnum.UPDATE_FAIL.getDesc() );
        }
    }

    /**
     * 查询自愈策略模板列表
     * @param tableQueryDto 模板查询入参
     * @return 返回模板集合带分页
     */
    @PostMapping("/list")
    public R<PageInfo<StrategyResultDto>> getList(@RequestBody TableQueryDto<StrategyQueryDto> tableQueryDto){
        PageInfo<StrategyResultDto> pages =null;
        try{
            pages = strategyService.selectStrategyList(
                    tableQueryDto.getQueryParam(),
                    tableQueryDto.getPageNum(),
                    tableQueryDto.getPageSize()
            );
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, pages, ConstantsEnum.LIST_SUCCESS.getDesc());
        }catch (Exception e){
            logger.error("Failed to query Toolbox custom strategy !{}",e.getMessage(), e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE,pages , ConstantsEnum.LIST_FAIL.getDesc());
        }
    }

    /**
     * 获取自愈模板策略状态    沒用
     * @return 结果
     */
    @GetMapping("/getStatus")
    public R<Object> getStatus(){
        List<CommonStatusDto> listStatus;
        try {
            listStatus = strategyService.getStatus();
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, listStatus, ConstantsEnum.GAIN_SUCCESS.getDesc());
        }catch (Exception e) {
            logger.error("toolbox customization for StrategyController.getStatus is failed!", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, null,ConstantsEnum.GAIN_FAIL.getDesc() );
        }
    }

    /**
     * 获取agent列表信息
     * @param tableQueryDto 查询参数
     * @return agent列表
     */
    @PostMapping("/getAgent")
    public R<PageInfo<AgentListResultDto>> getAgent(@RequestBody TableQueryDto<AgentListQueryDto> tableQueryDto){
        PageInfo<AgentListResultDto> agentList;
        try {
            AgentListQueryDto params = tableQueryDto.getQueryParam();
            params.setUserId(getUser().getUserId());
            agentList = strategyService.getAgentList(params, tableQueryDto.getPageNum(), tableQueryDto.getPageSize());
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, agentList, ConstantsEnum.LIST_SUCCESS.getDesc());
        }catch (Exception e) {
            logger.error("toolbox customization for StrategyController.getAgent is failed!", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, null,ConstantsEnum.LIST_FAIL.getDesc() );
        }
    }

    /**
     * 报警管理，诊断执行匹配自愈查询
     * @param strategyAlarmQueryDto 策略查询入参
     * @return 自愈策略集合
     */
    @PostMapping("/searchStrategyData")
    public R<Object> searchStrategyData(@RequestBody StrategyAlarmQueryDto strategyAlarmQueryDto){
        List<StrategyResultDto> strategyList;
        try {
            strategyList = strategyService.searchStrategyData(strategyAlarmQueryDto);
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, strategyList, ConstantsEnum.GAIN_SUCCESS.getDesc());
        }catch (Exception e) {
            logger.error("toolbox customization for StrategyController.getAgent is failed!", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, null,ConstantsEnum.GAIN_FAIL.getDesc() );
        }
    }

    /**
     * 获取审批人列表
     * @return 结果
     */
    @GetMapping("/getAuditUser")
    @MethodPermission("@dp.hasBtnPermission('execution-history') or @dp.hasBtnPermission('tool-monitoring') " +
            "or @dp.hasBtnPermission('tool-run') or @dp.hasBtnPermission('scene-tools') or @dp.hasBtnPermission('self-healing') " +
            "or @dp.hasBtnPermission('tool-execution')")
    public R<Object> getAuditUser(){
        try {
            List<UserInfoDto> userInfoList = strategyService.getAuditUser(getUser());
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, userInfoList, ConstantsEnum.LIST_SUCCESS.getDesc());
        }catch (Exception e) {
            logger.error("toolbox customization for StrategyController.getStatus is failed!", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, null,ConstantsEnum.LIST_FAIL.getDesc() );
        }
    }

    /**
     * 获取自愈模板关联工具
     * @return 结果
     */
    @GetMapping("/getTools")
    public R<Object> getTools(@RequestParam(value = "sysId")Long sysId){
        try {
            List<StrategyToolsResultDto> tools = strategyService.getTools(sysId);
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, tools, ConstantsEnum.LIST_SUCCESS.getDesc());
        }catch (Exception e) {
            logger.error("toolbox customization for StrategyController.getTools is failed!", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, null,ConstantsEnum.LIST_FAIL.getDesc() );
        }
    }
    /**
     * 查询自愈策略模板详情
     *
     * @param auditId 数据唯一标识
     * @return 查询结果
     */
    @GetMapping(value = "/getStrategyInfo")
    @MethodPermission("@dp.hasBtnPermission('detailSelfHealing')")
    public R<StrategyResultDto> getStrategyInfo(@RequestParam(value = "auditId")Long auditId) {
        StrategyResultDto dto = null;
        try{
            dto = strategyService.getStrategyInfo(auditId);
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, dto, ConstantsEnum.GAIN_SUCCESS.getDesc());
        }catch (Exception e){
            logger.error("query getAgentInfoInfo  by id is error", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE,dto, ConstantsEnum.GAIN_FAIL.getDesc());
        }
    }
}

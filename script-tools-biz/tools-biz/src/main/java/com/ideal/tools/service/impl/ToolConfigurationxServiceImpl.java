package com.ideal.tools.service.impl;

import com.github.pagehelper.PageInfo;
import com.ideal.common.util.BeanUtils;
import com.ideal.system.common.component.model.CurrentUser;
import com.ideal.system.dto.BusinessSystemApiDto;
import com.ideal.tools.common.AutoDevPageDataUtils;
import com.ideal.tools.dto.*;
import com.ideal.tools.dto.SystemPullDto;
import com.ideal.tools.exception.InteractServiceException;
import com.ideal.tools.model.dto.*;
import com.ideal.tools.model.dto.UserDto;
import com.ideal.tools.model.enums.ToolsOSEnum;
import com.ideal.tools.model.enums.ToolsTypeEnum;
import com.ideal.tools.service.IToolConfigurationxService;
import com.ideal.tools.service.IToolsAgentInfoService;
import com.ideal.tools.service.IToolsInfoService;
import com.ideal.tools.service.interaction.SystemDataInteract;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;

/**
 * 工具箱工具配置信息
 *
 * <AUTHOR>
 */
@Service
public class ToolConfigurationxServiceImpl implements IToolConfigurationxService {
    private final Logger logger = LoggerFactory.getLogger(ToolConfigurationxServiceImpl.class);
    private final IToolsInfoService toolsInfoService;
    private final IToolsAgentInfoService toolsAgentInfoService;
    private final SystemDataInteract systemDataInteract;
    public ToolConfigurationxServiceImpl(IToolsInfoService toolsInfoService,IToolsAgentInfoService toolsAgentInfoService,SystemDataInteract systemDataInteract) {
        this.toolsInfoService=toolsInfoService;
        this.toolsAgentInfoService=toolsAgentInfoService;
        this.systemDataInteract=systemDataInteract;
    }

    /**
     * 工具列表（流程编排 活动详情）
     *
     * @param toolsInfoResultQueryApiDto 脚本信息查询条件
     * @return ScriptContentApiDto
     * <AUTHOR>
     */
    @Override
    public PageInfo<ToolsInfoResultApiDto> compositeScriptToolList(ToolsInfoResultQueryApiDto toolsInfoResultQueryApiDto, Integer pageNum, Integer pageSize)  {
    	ToolsQueryDto toolsInfoQueryDto= BeanUtils.copy(toolsInfoResultQueryApiDto,ToolsQueryDto.class);
    	String businessSystemCode=toolsInfoResultQueryApiDto.getBusinessSystemCode();
    	if (null!=businessSystemCode&&!"toolsPub".equals(businessSystemCode)) {
    		Long sysId= systemDataInteract.getBusinessSystemIdByCode(businessSystemCode);
    		if (null==sysId) {
    			return AutoDevPageDataUtils.getPageInfo(new PageInfo<ToolsInfoResultDto>(), new LinkedList<ToolsInfoResultApiDto>());
			}
    		toolsInfoQueryDto.setBusinessSystemId(sysId);
		}
    	PageInfo<ToolsInfoResultDto> toolsInfoResultDtoPageInfo = toolsInfoService.selectToolsInfoListByUser(toolsInfoQueryDto,businessSystemCode,toolsInfoResultQueryApiDto.getUserId(),pageNum,pageSize);
    	List <ToolsInfoResultApiDto> tToolsInfoResultApiDtoList=BeanUtils.copy(toolsInfoResultDtoPageInfo.getList(), ToolsInfoResultApiDto.class);
    	return AutoDevPageDataUtils.getPageInfo(toolsInfoResultDtoPageInfo, tToolsInfoResultApiDtoList);
    
    }
    /**
     * 工具详情（流程编排 活动详情）
     *
     * @param tdToolsId 脚本id
     * @return ScriptContentApiDto
     * <AUTHOR>
     */
    @Override
    public ToolsApiDto getToolsCombinedApiInfo(Long tdToolsId)  {

        ToolsDto toolsDto = toolsInfoService.getToolsCombinedInfo(tdToolsId);
        ToolsApiDto tolsApiDto=BeanUtils.copy(toolsDto, ToolsApiDto.class);
        tolsApiDto.setOsTypeName(ToolsOSEnum.getByCode(toolsDto.getOsType()));
        tolsApiDto.setTypeName(ToolsTypeEnum.getDescByCode(toolsDto.getType()));
        tolsApiDto.setToolsProjectInfoDto(BeanUtils.copy(toolsDto.getToolsProjectInfoDto(), ToolsProjectInfoApiDto.class));
        tolsApiDto.setScriptFilesList(BeanUtils.copy(toolsDto.getScriptFilesList(), ToolsFilesQueryApiDto.class));
        tolsApiDto.setAuditEverybodyDtoList(BeanUtils.copy(toolsDto.getAuditEverybodyDtoList(), AuditEverybodyQueryApiDto.class));
        tolsApiDto.setToolsParamResultList(BeanUtils.copy(toolsDto.getToolsParamResultList(), ToolsParamResultApiDto.class));
       return tolsApiDto;
    }

    /**
     * Agent列表（流程编排 活动详情）
     *
     * @param toolsInfoResultQueryApiDto 脚本信息查询条件
     * @return ScriptContentApiDto
     * <AUTHOR>
     */
    @Override
    public PageInfo<ToolsAgentResultApiDto> selectToolAgentLists(ToolsAgentResultQueryApiDto toolsInfoResultQueryApiDto,
                                                                      Integer pageNum,
                                                                      Integer pageSize){
        if(toolsInfoResultQueryApiDto==null){
            return new PageInfo<>();
        }
        logger.info("toolsInfoResultQueryApiDto:{},pageNum: {} pageSize:{}",toolsInfoResultQueryApiDto,pageNum,pageSize);
        AgentQueryDto agentQueryDto=BeanUtils.copy(toolsInfoResultQueryApiDto,AgentQueryDto.class);
        List<AgentSelectedApiDto> agentList=  toolsInfoResultQueryApiDto.getAgentList();

        agentQueryDto.setAgentList(BeanUtils.copy(agentList, AgentSelectedDto.class));
        PageInfo<ToolsAgentResultDto> toolsAgentResultDtoPageInfo=new PageInfo<>();
        try {
        	String businessSystemCode=toolsInfoResultQueryApiDto.getBusinessSystemCode();
        	if (null==businessSystemCode||"toolsPub".equals(businessSystemCode)) {
        		agentQueryDto.setSysId(-1L);
			}else {
				Long sysId =systemDataInteract.getBusinessSystemIdByCode(businessSystemCode);
				if (null==sysId) {
					logger.error("根据businessSystemCode没有查到id,businessSystemCode是:"+businessSystemCode);
					return new PageInfo<>();
				}else {
					agentQueryDto.setSysId(sysId);
				}
			}
            //查询当前工具下绑定的agent
        toolsAgentResultDtoPageInfo=toolsAgentInfoService.selectCurrentToolIdBingComputerIdLists(agentQueryDto,toolsInfoResultQueryApiDto.getUserId(),pageNum,pageSize);
        } catch (InteractServiceException e) {
            logger.error("selectToolAgentLists:selectStandardTaskComputerIdLists ",e);
        }
        List <ToolsAgentResultApiDto> toolsAgentResultApiDtoList=BeanUtils.copy(toolsAgentResultDtoPageInfo.getList(), ToolsAgentResultApiDto.class);
        logger.info("toolsAgentResultApiDtoList:{}",toolsAgentResultApiDtoList.size());
        return AutoDevPageDataUtils.getPageInfo(toolsAgentResultDtoPageInfo, toolsAgentResultApiDtoList);
    }

    /**
     * 查看应用系统下拉列表
     * 组合工具选择原子工具时，业务系统为下拉框（流程编排调用）
     * 如果systemCode=toolsPub  为公共系统，查询当前登录人所有权限的系统+公共系统（放在第一位）
     *                          不是公共系统，仅返回当前组合工具 系统本身
     * @param systemCode
     * @param userDto
     * @return
     */
    @Override
    public List<SystemPullDto> getSystemstudio(String systemCode, com.ideal.tools.dto.UserDto userDto) {
        List<SystemPullDto> list = new ArrayList<>();
        if ("toolsPub".equals(systemCode)){
            //公共系统，查询当前登录人所有权限的系统+公共系统（放在第一位）
            list = systemDataInteract.getBusinessSystemListStudio(userDto.getUserId());
        }else {
            //不是公共，仅返回当前组合工具 系统本身
            list = systemDataInteract.getBusinessSystemListByCode(systemCode);
        }
        return list;
    }
}

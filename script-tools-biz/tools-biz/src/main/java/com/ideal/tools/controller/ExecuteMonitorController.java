package com.ideal.tools.controller;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.common.dto.ValidateError;
import com.ideal.common.util.spring.MessageUtil;
import com.ideal.system.common.component.aop.MethodPermission;
import com.ideal.tools.common.Constants;
import com.ideal.tools.common.ControlsTimeUtil;
import com.ideal.tools.common.EasyExcelUtil;
import com.ideal.tools.core.web.BaseController;
import com.ideal.tools.exception.ExecuteMonitorException;
import com.ideal.tools.model.dto.CommonStatusDto;
import com.ideal.tools.model.dto.ExecuteMonitorDto;
import com.ideal.tools.model.dto.ExecuteMonitorExportDto;
import com.ideal.tools.model.dto.ExecuteMonitorProjectDto;
import com.ideal.tools.model.dto.ExecuteMonitorQueryListDto;
import com.ideal.tools.model.dto.ExecuteMonitorResultListDto;
import com.ideal.tools.model.dto.LogAuditDto;
import com.ideal.tools.model.dto.MonitorFlowActiveDto;
import com.ideal.tools.model.dto.MonitorFlowActiveQueryDto;
import com.ideal.tools.model.enums.ConstantsEnum;
import com.ideal.tools.model.enums.OperatModuleEnum;
import com.ideal.tools.model.enums.OperatResultEnum;
import com.ideal.tools.model.enums.OperatTypeEnum;
import com.ideal.tools.model.interaction.ActivityOutPutResultDto;
import com.ideal.tools.model.interaction.TaskResultDto;
import com.ideal.tools.service.IExecuteMonitorService;
import com.ideal.tools.service.ILogAuditService;

/**
 * 执行历史监控
 */
@RestController
@RequestMapping("${app.script-tools-url:}/tools/monitor")
public class ExecuteMonitorController extends BaseController {

    private final IExecuteMonitorService executeMonitorService;
    private final ILogAuditService logAuditService;

    public ExecuteMonitorController(IExecuteMonitorService executeMonitorService,ILogAuditService logAuditService){
        this.executeMonitorService = executeMonitorService;
        this.logAuditService =  logAuditService;
    }
    
    /**
     * 新增保存工具执行监控   沒用
     *
     * @param executeMonitorDto 添加数据
     * @return 添加结果
     */
    @RequestMapping("/getExecuteTimeParam/{toolsId}")
    public R<Object> getExecuteTimeParam(@PathVariable("toolsId")Long toolsId,@RequestBody Map reqMap){
    	if (null==reqMap) {
			reqMap=new HashMap();
		}
    	reqMap.put("toolsId", toolsId);
    	try {
			return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, executeMonitorService.getExecuteTimeParam(reqMap), ConstantsEnum.SAVE_SUCCESS.getDesc());
		} catch (ExecuteMonitorException e) {
			logger.error("查询数据失败", e);
			return R.fail(Constants.REPONSE_STATUS_VALIDATA_CODE, "查询数据失败", "validate.error");
		}
    }

    /**
     * 新增保存工具执行监控       沒用
     *
     * @param executeMonitorDto 添加数据
     * @return 添加结果
     */
    @PostMapping("/save")
    public R<Object> save(@Valid @RequestBody ExecuteMonitorDto executeMonitorDto, BindingResult bindingResult){
        if (bindingResult.hasErrors()) {
            List<ValidateError> validateErrorList = bindingResult.getFieldErrors().stream()
                    .map(item -> new ValidateError(item.getField(), MessageUtil.message(item.getDefaultMessage())))
                    .collect(Collectors.toList());
            Map<String, List<ValidateError>> validateError = new HashMap<>(1);
            validateError.put("validateError", validateErrorList);
            return R.fail(Constants.REPONSE_STATUS_VALIDATA_CODE, validateError, "validate.error");
        }

        try {
            Long monitorId = executeMonitorService.insertMonitor(executeMonitorDto);
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, monitorId, ConstantsEnum.SAVE_SUCCESS.getDesc());
        }catch (Exception e) {
            logger.error("toolbox.monitor for save customization failed!", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, null, ConstantsEnum.SAVE_FAIL.getDesc() );
        }
    }

    /**
     * 查询工具执行监控列表
     *
     * @param tableQueryDto 查询条件
     * @return 查询结果
     */
    @PostMapping("/list")
    public R<PageInfo<ExecuteMonitorResultListDto>> list(@RequestBody TableQueryDto<ExecuteMonitorQueryListDto> tableQueryDto) {
        PageInfo<ExecuteMonitorResultListDto> pages =null;
        try{
            pages = executeMonitorService.selectExecuteMonitorList(
                    tableQueryDto.getQueryParam(),
                    tableQueryDto.getPageNum(),
                    tableQueryDto.getPageSize()
            );
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, pages, ConstantsEnum.LIST_SUCCESS.getDesc());
        }catch (Exception e){
            logger.error("Failed to query Toolbox execute monitory !{}",e.getMessage(), e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE,pages , ConstantsEnum.LIST_FAIL.getDesc());
        }
    }

    /**
     * 查询工具执行监控详细信息
     *
     * @param id 数据唯一标识
     * @return 查询结果
     */

    @GetMapping(value = "/detail")
    @MethodPermission("@dp.hasBtnPermission('detailToolMonitoring') or @dp.hasBtnPermission('tool-monitoring')")
    public R<ExecuteMonitorDto> getDetail(@RequestParam(value = "id")Long id) {
        try {
            ExecuteMonitorDto executeMonitor = executeMonitorService.selectExecuteMonitorById(id, getUser());
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, executeMonitor, ConstantsEnum.REMOVE_SUCCESS.getDesc());
        }catch (Exception e) {
            logger.error("toolbox.monitor for detail is failed!", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, null, ConstantsEnum.REMOVE_FAIL.getDesc() );
        }
    }

    /**
     * 查询工具运行状态     沒用
     * @return 查询结果
     */
    @GetMapping(value = "/getRunStatus")
    public R<Object> getRunStatus() {
        List<CommonStatusDto> listStatus;
        try {
            listStatus = executeMonitorService.getRunStatus();
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, listStatus, ConstantsEnum.GAIN_SUCCESS.getDesc());
        }catch (Exception e) {
            logger.error("toolbox for ExecuteMonitorController.getRunStatus is failed!", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, null,ConstantsEnum.GAIN_FAIL.getDesc() );
        }
    }

    /**
     * 修改工具运行状态     沒用
     * @return 查询结果
     */
    @GetMapping(value = "/updateStatus")
    public R<Object> updateStatus(@RequestBody ExecuteMonitorDto monitor) {
        try {
            boolean updateResult = executeMonitorService.updateStatusSelf(monitor);
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, updateResult, ConstantsEnum.UPDATE_SUCCESS.getDesc());
        }catch (Exception e) {
            logger.error("toolbox customization for ExecuteMonitorController.updateStatus is failed!", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, null,ConstantsEnum.UPDATE_FAIL.getDesc() );
        }
    }

    /**
     * 工具执行监控导出
     * @param response 响应信息
     * @param dto 入参
     */
    @PostMapping("/export")
    @MethodPermission("@dp.hasBtnPermission('exportToolMonitoring')")
    public void exportMonitorList(HttpServletResponse response, @RequestBody ExecuteMonitorQueryListDto dto) {
        List<ExecuteMonitorExportDto> list = executeMonitorService.exportMonitorList(dto);
        // 使用 EasyExcel 将数据写入响应输出流
        EasyExcelUtil.writeExcel(response, list, "工具执行监控" + ControlsTimeUtil.formatDateByFormat(new Date(), "YYYY-MM-dd hh_mm_ss"), "工具执行监控", ExecuteMonitorExportDto.class);
        String operatingContent = "导出全部数据到本地";
        String resultDesc = OperatModuleEnum.TOOL_EXECUTE_MONITOR.getRemark() + OperatTypeEnum.TOOL_EXECUTE_MONITOR2.getRemark();
        LogAuditDto logAuditDto = new LogAuditDto(OperatModuleEnum.TOOL_EXECUTE_MONITOR.getCode(), OperatTypeEnum.TOOL_EXECUTE_MONITOR2.getCode(), operatingContent);
        logAuditDto.setResult(OperatResultEnum.SUCCESS.getCode());
        logAuditDto.setResultDesc(resultDesc + OperatResultEnum.SUCCESS.getDesc());
        logAuditService.insertLogAudit(logAuditDto);
    }

    /**
     * 工具执行监控、历史根据审核id查询匹配数据    沒用
     * @param auditId 双人复核表主键
     */
    @GetMapping("/matchAuditId")
    public boolean matchAuditId(@RequestParam(value = "auditId")Long auditId) {
        return executeMonitorService.matchAuditId(auditId);
    }
    /**
     * 根据工具id查看执行内容
     * @param monitor 入参
     */
    @PostMapping("/getExecuteDetail")
    @MethodPermission("@dp.hasBtnPermission('tool-monitoring') or @dp.hasBtnPermission('resultToolRun') " +
            "or @dp.hasBtnPermission('resultScenceTools')")
    public R<ExecuteMonitorDto> getExecuteDetailByToolsId(@RequestBody ExecuteMonitorDto monitor) {
        try {
            ExecuteMonitorDto executeMonitor = executeMonitorService.getExecuteDetailByToolsId(monitor.getExecFrom(), monitor.getToolId(), getUser());
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, executeMonitor, ConstantsEnum.GAIN_SUCCESS.getDesc());
        }catch (Exception e) {
            logger.error("toolbox.monitor customization for detail is failed!", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, null, ConstantsEnum.GAIN_FAIL.getDesc() );
        }
    }
    /**
     * 根据工具id查询是否有运行中得数据    沒用
     * @param toolId 工具id
     */
    @GetMapping("/verifyRunMonitory")
    public boolean verifyRunMonitory(@RequestParam(value = "toolId")Long toolId) {
        return executeMonitorService.verifyRunMonitory(toolId);
    }
    /**
     * 脚本工具-根据实例id获取本次任务的agent
     */
    @PostMapping("/getTaskList")
    @MethodPermission("@dp.hasBtnPermission('detailToolHistory') or @dp.hasBtnPermission('detailToolMonitoring') " +
            "or @dp.hasBtnPermission('resultToolRun') or @dp.hasBtnPermission('resultScenceTools')")
    public R<PageInfo<TaskResultDto>> getTaskList(@RequestBody TableQueryDto<Long> tableQueryDto) {
        PageInfo<TaskResultDto> pages = null;
        try{
            pages = executeMonitorService.getTaskList(
                    tableQueryDto.getQueryParam(),
                    tableQueryDto.getPageNum(),
                    tableQueryDto.getPageSize()
            );
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, pages, ConstantsEnum.LIST_SUCCESS.getDesc());
        }catch (Exception e){
            logger.error("Failed to query for get script task data !{}",e.getMessage(), e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, pages, ConstantsEnum.LIST_FAIL.getDesc());
        }
    }

    /**
     * 脚本工具-获取agent执行内容
     * @param taskRuntimeId agent运行实例id
     * @return 返回运行结果
     */
    @GetMapping("/getOutPutMessage")
    @MethodPermission("@dp.hasBtnPermission('detailToolHistory') or @dp.hasBtnPermission('detailToolMonitoring')" +
            " or @dp.hasBtnPermission('resultScenceTools')")
    public R<Object> getOutPutMessage(@RequestParam(value = "taskRuntimeId")Long taskRuntimeId){
        try {
            String agentOutPutMessage = executeMonitorService.getOutPutMessage(taskRuntimeId);
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, agentOutPutMessage, ConstantsEnum.GAIN_SUCCESS.getDesc());
        }catch (Exception e) {
            logger.error("toolbox.monitor customization for detail failed!", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, null, ConstantsEnum.GAIN_FAIL.getDesc() );
        }
    }

    /**
     * 工具-终止
     * @param runningId 运行id
     * @return 返回终止状态
     */
    @PostMapping("/killTask")
    public R<Object> killTask(@RequestBody List<Long> runningId){
        try {
            Object killTaskStatus = executeMonitorService.killTask(runningId, getUser());
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, killTaskStatus, ConstantsEnum.UPDATE_SUCCESS.getDesc());
        }catch (Exception e) {
            logger.error("toolbox.monitor customization for kill task failed!", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, null, ConstantsEnum.UPDATE_FAIL.getDesc() );
        }
    }

    /**
     * 流程图节点详细信息查询      沒用
     * @return 查询结果
     */
    @GetMapping(value = "/getNodeDetail")
    public R<Object> getNodeDetail(@RequestParam(value = "name")String name, @RequestParam(value = "flowId")Long flowId, @RequestParam(value = "reqId")String reqId) {
        List <ActivityOutPutResultDto> act;
        try {
            act = executeMonitorService.getNodeDetail(name, flowId, reqId);
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, act, ConstantsEnum.GAIN_SUCCESS.getDesc());
        }catch (Exception e) {
            logger.error("ExecuteMonitorController.getRunStatus is failed!", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, null,ConstantsEnum.GAIN_FAIL.getDesc() );
        }
    }

    /**
     * 组合工具-查询原子工具为脚本工具得agent列表
     * @return 查询结果
     */
    @PostMapping(value = "/getScriptAgentList")
    @MethodPermission("@dp.hasBtnPermission('detailToolMonitoring')")
    public R<PageInfo<MonitorFlowActiveDto>> getScriptAgentList(@RequestBody TableQueryDto<MonitorFlowActiveQueryDto> tableQueryDto) {
        PageInfo<MonitorFlowActiveDto> pages = null;
        try {
            pages = executeMonitorService.getScriptAgentList11(
                    tableQueryDto.getQueryParam(),
                    tableQueryDto.getPageNum(),
                    tableQueryDto.getPageSize(), getUser());
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, pages, ConstantsEnum.GAIN_SUCCESS.getDesc());
        }catch (Exception e) {
            logger.error("toolbox customization for ExecuteMonitorController.getRunStatus is failed!", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, pages,ConstantsEnum.GAIN_FAIL.getDesc() );
        }
    }

    /**
     * 查询组合工具节点信息
     * @param flowId 流程id
     * @return 工程信息
     */
    @GetMapping(value = "/getComToolNodes")
    public R<Object> getComToolNodes(@RequestParam(value = "flowId") Long flowId, @RequestParam(value = "workFlowId") Long workFlowId) {
        ExecuteMonitorProjectDto result = null;
        try {
            result = executeMonitorService.getComToolNodes(flowId, workFlowId, getUser());
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, result, ConstantsEnum.GAIN_SUCCESS.getDesc());
        }catch (Exception e) {
            logger.error("toolbox customization for ExecuteMonitorController.getRunStatus is failed!", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, result,ConstantsEnum.GAIN_FAIL.getDesc() );
        }
    }
}

package com.ideal.tools.mapper;

import com.ideal.tools.model.entity.AlarmInfoEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * 告警信息Mapper接口
 *
 * <AUTHOR>
 */
@Component
public interface AlarmInfoMapper {
    /**
     * 查询告警信息
     *
     * @param id 告警信息主键
     * @return 告警信息
     */
    AlarmInfoEntity selectAlarmInfoById(Long id);

    /**
     * 查询告警信息列表
     *
     * @param alarmInfo 告警信息
     * @return 告警信息集合
     */
    List<AlarmInfoEntity> selectAlarmInfoList(@Param("alarmInfo") AlarmInfoEntity alarmInfo, @Param("startTime") Date startTime, @Param("endTime")Date endTime);

    /**
     * 新增告警信息
     *
     * @param alarmInfo 告警信息
     * @return 结果
     */
    int insertAlarmInfo(AlarmInfoEntity alarmInfo);

    /**
     * 修改告警信息
     *
     * @param alarmInfo 告警信息
     * @return 结果
     */
    int updateAlarmInfo(AlarmInfoEntity alarmInfo);

    /**
     * 删除告警信息
     *
     * @param id 告警信息主键
     * @return 结果
     */
    int deleteAlarmInfoById(Long id);

    /**
     * 批量删除告警信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteAlarmInfoByIds(Long[] ids);
    /**
     * 批量修改 告警处理，忽略
     *
     * @param ids 需要删除的数据主键集合
     * @param userId  用户id
     * @param userName  用户名
     * @return 结果
     */
    int updateAlarmStatus(Long[] ids, Integer code, Long userId, String userName);
    /**
     * 根据主键ID更新工具个数
     *
     * @param toolCount 工具个数
     * @param id  主键ID
     * @return 结果
     */
    int updateToolsCount(Integer toolCount, Long id);

    /**
     * 获取导出数据
     * @param alarmInfoEntity
     * @return
     */
    List<AlarmInfoEntity> selectExportAlarmList(AlarmInfoEntity alarmInfoEntity);

    /**
     * 批量修改告警信息by告警事件id
     * @param alarmInfoEntity
     * @return
     */
    int updateAlarmInfoByEventId(AlarmInfoEntity alarmInfoEntity);
    /**
     * 批量更新告警信息关联工具个数自减
     * @param alarmIds 主键集合
     * @return
     */
    int updateToolsCountDecrementByIds(Long[] alarmIds);

    /**
     * 批量更新告警信息关联工具个数自增
     * @param alarmIds 主键集合
     * @return
     */
    int updateToolsCountIncrementByIds(Long[] alarmIds);
    /**
     * 根据业务系统ID获取告警信息
     *
     * @param sysId 业务系统ID
     * @return 告警信息集合
     */
    List<AlarmInfoEntity> selectAlarmInfoListBySysId(Long sysId);

    /**
     * 根据事件单号更新自愈状态
     *
     * @param eventId 工具ID集合
     * @param healingStatus 自愈状态
     * @return boolean
     */
    int updateHealingStatus(String eventId, Integer healingStatus);
}

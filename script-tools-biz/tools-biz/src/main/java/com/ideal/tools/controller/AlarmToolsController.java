package com.ideal.tools.controller;

import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.system.common.component.aop.MethodPermission;
import org.springframework.web.bind.annotation.*;
import com.ideal.tools.model.dto.AlarmToolsDto;
import com.ideal.tools.model.dto.AlarmToolsQueryDto;
import com.ideal.tools.service.IAlarmToolsService;

/**
 * 报警关联关联工具中间Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("${app.script-tools-url:}/tools/alarmTool")
@MethodPermission("@dp.hasBtnPermission('alarm-management')")
public class AlarmToolsController {
    private final IAlarmToolsService alarmToolsService;

    public AlarmToolsController(IAlarmToolsService alarmToolsService) {
        this.alarmToolsService = alarmToolsService;
    }

    /**
     * 查询报警关联关联工具中间列表   沒用
     *
     * @param tableQueryDto 查询条件
     * @return 查询结果
     */
    @GetMapping("/list")
    public R<PageInfo<AlarmToolsDto>> list(@RequestBody TableQueryDto<AlarmToolsQueryDto> tableQueryDto) {
        PageInfo<AlarmToolsDto> list = alarmToolsService.selectAlarmToolsList(
                tableQueryDto.getQueryParam(),
                tableQueryDto.getPageNum(),
                tableQueryDto.getPageSize()
        );
        return R.ok(list);
    }

    /**
     * 查询报警关联关联工具中间详细信息   沒用
     *
     * @param id 数据唯一标识
     * @return 查询结果
     */
    @GetMapping(value = "/get")
    public R<AlarmToolsDto> getAgentInfoInfo(@RequestParam(value = "id")Long id) {
        return R.ok(alarmToolsService.selectAlarmToolsById(id));
    }

    /**
     * 新增保存报警关联关联工具中间
     *
     * @param alarmToolsDto 添加数据   沒用
     * @return 添加结果
     */
    @PostMapping("/save")
    public R<Void> save(@RequestBody AlarmToolsDto alarmToolsDto) {
        if (alarmToolsService.insertAlarmTools(alarmToolsDto) > 0) {
            return R.ok();
        }
        return R.fail();
    }

    /**
     * 修改保存报警关联关联工具中间   沒用
     *
     * @param alarmToolsDto 更新数据
     * @return 更新结果
     */
    @PostMapping("/update")
    public R<Void> update(@RequestBody AlarmToolsDto alarmToolsDto) {
        alarmToolsService.updateAlarmTools(alarmToolsDto);
        return R.ok();
    }


    /**
     * 删除报警关联关联工具中间   沒用
     *
     * @param ids 主键列表
     * @return 删除结果
     */
    @PostMapping("/remove")
    public R<Void> remove(@RequestParam(value = "ids") Long[] ids) {
        alarmToolsService.deleteAlarmToolsByIds(ids);
        return R.ok();
    }
}

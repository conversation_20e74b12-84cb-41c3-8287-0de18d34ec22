package com.ideal.tools.controller;
import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.common.dto.ValidateError;
import com.ideal.common.util.spring.MessageUtil;
import com.ideal.system.common.component.aop.MethodPermission;
import com.ideal.tools.common.Constants;
import com.ideal.tools.core.web.BaseController;
import com.ideal.tools.model.dto.EccWhiteDto;
import com.ideal.tools.model.dto.EccWhiteQueryDto;
import com.ideal.tools.model.dto.EccWhiteResultDto;
import com.ideal.tools.model.dto.LoginUrlDropdownDto;
import com.ideal.tools.model.enums.ConstantsEnum;
import com.ideal.tools.service.IEccWhiteService;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 工具箱Ecc白名单Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("${app.script-tools-url:}/tools/eccWhite")
public class EccWhiteController extends BaseController {
    private final IEccWhiteService eccWhiteService;

    public EccWhiteController(IEccWhiteService eccWhiteService) {
        this.eccWhiteService = eccWhiteService;
    }

    /**
     * 查询工具箱Ecc白名单列表
     *
     * @param tableQueryDto 查询条件
     * @return 查询结果
     */
    @PostMapping("/list")
    public R<PageInfo<EccWhiteResultDto>> list(@RequestBody TableQueryDto<EccWhiteQueryDto> tableQueryDto) {
        PageInfo<EccWhiteResultDto> pages =null;
        try{
            pages = eccWhiteService.selectEccWhiteList(
                    tableQueryDto.getQueryParam(),
                    tableQueryDto.getPageNum(),
                    tableQueryDto.getPageSize()
            );
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, pages, ConstantsEnum.LIST_SUCCESS.getDesc());
        }catch (Exception e){
            logger.error("Failed to query Toolbox ecc white !{}",e.getMessage(), e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE,pages , ConstantsEnum.LIST_FAIL.getDesc());
        }
    }


    /**
     * 查询工具箱Ecc白名单详细信息    沒用
     *
     * @param id 数据唯一标识
     * @return 查询结果
     */
    @GetMapping(value = "/get")
    public R<EccWhiteDto> getAgentInfoInfo(@RequestParam(value = "id")Long id) {
        return R.ok(eccWhiteService.selectEccWhiteById(id));
    }

    /**
     * 查询Ecc白名单访问途径下拉数据
     *
     * @return 查询结果
     */
    @GetMapping(value = "/loginUrlDropdownList")
    public R<List<LoginUrlDropdownDto>> getLoginUrlDropdownList() {
        List<LoginUrlDropdownDto> lists = null;
        try {
            lists =  eccWhiteService.selectLoginUrlLists();
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE,lists, ConstantsEnum.LIST_SUCCESS.getDesc());

        }catch (Exception e) {
            logger.error("Failed to query the toolbox ecc white dropdown list!", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE,lists, ConstantsEnum.LIST_FAIL.getDesc());
        }
    }


    /**
     * 新增保存工具箱Ecc白名单
     *
     * @param eccWhiteDto 添加数据
     * @return 添加结果
     */
    @PostMapping("/save")
    @MethodPermission("@dp.hasBtnPermission('addECC')")
    public R<Object> save(@RequestBody EccWhiteDto eccWhiteDto, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            List<ValidateError> validateErrorList = bindingResult.getFieldErrors().stream()
                    .map(item -> new ValidateError(item.getField(), MessageUtil.message(item.getDefaultMessage())))
                    .collect(Collectors.toList());
            Map<String, List<ValidateError>> validateError = new HashMap<>(1);
            validateError.put("validateError", validateErrorList);
            return R.fail(Constants.REPONSE_STATUS_VALIDATA_CODE, validateError, "validate.error");
        }

        try {
            return eccWhiteService.insertEccWhite(eccWhiteDto, getUser());
        }catch (Exception e) {
            logger.error("New toolbox customization failed!", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, null,ConstantsEnum.SAVE_FAIL.getDesc() );
        }

    }

    /**
     * 修改保存工具箱Ecc白名单
     *
     * @param eccWhiteDto 更新数据
     * @return 更新结果
     */
    @PostMapping("/update")
    @MethodPermission("@dp.hasBtnPermission('editECC')")
    public R<Object> update(@RequestBody EccWhiteDto eccWhiteDto, BindingResult bindingResult) {

        if (bindingResult.hasErrors()) {
            List<ValidateError> validateErrorList = bindingResult.getFieldErrors().stream()
                    .map(item -> new ValidateError(item.getField(), MessageUtil.message(item.getDefaultMessage())))
                    .collect(Collectors.toList());
            Map<String, List<ValidateError>> validateError = new HashMap<>(1);
            validateError.put("validateError", validateErrorList);
            return R.fail(Constants.REPONSE_STATUS_VALIDATA_CODE, validateError,"validate.error");
        }

        try {
            return eccWhiteService.updateEccWhite(eccWhiteDto,getUser());
        }catch (Exception e) {
            logger.error("Modifying toolbox ecc white failed!", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, "", ConstantsEnum.UPDATE_FAIL.getDesc());
        }
    }

    /**
     * 更新工具箱Ecc白名单状态
     *
     * @param eccWhiteDto 更新数据
     * @return 更新结果
     */
    @PostMapping("/updateStatus")
    @MethodPermission("@dp.hasBtnPermission('stopECC') or @dp.hasBtnPermission('startECC')")
    public R<Object> updateStatus(@RequestBody EccWhiteDto eccWhiteDto,BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            List<ValidateError> validateErrorList = bindingResult.getFieldErrors().stream()
                    .map(item -> new ValidateError(item.getField(), MessageUtil.message(item.getDefaultMessage())))
                    .collect(Collectors.toList());
            Map<String, List<ValidateError>> validateError = new HashMap<>(1);
            validateError.put("validateError", validateErrorList);
            return R.fail(Constants.REPONSE_STATUS_VALIDATA_CODE, validateError,"validate.error");
        }
        try{
            eccWhiteService.updateStatus(eccWhiteDto,getUser());
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, ConstantsEnum.UPDATE_SUCCESS.getDesc());
        } catch (Exception e) {
            logger.error("Delete toolbox ecc white failed!", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, ConstantsEnum.UPDATE_FAIL.getDesc());
        }
    }

    /**
     * 根据ids删除工具箱Ecc白名单
     *
     * @param ids 主键列表
     * @return 删除结果
     */
    @PostMapping("/remove")
    @MethodPermission("@dp.hasBtnPermission('deleteECC')")
    public R<Void> remove(@RequestBody Long[] ids) {
        try{
            eccWhiteService.deleteEccWhiteByIds(ids);
            return R.ok(Constants.REPONSE_STATUS_SUSSCESS_CODE, ConstantsEnum.REMOVE_SUCCESS.getDesc());
        }catch (Exception e) {
            logger.error("Delete toolbox ecc white failed!", e);
            return R.fail(Constants.REPONSE_STATUS_FAIL_CODE, ConstantsEnum.REMOVE_FAIL.getDesc());
        }
    }

}

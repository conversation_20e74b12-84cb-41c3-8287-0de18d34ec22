<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.tools.mapper.AlarmToolsMapper">

    <resultMap type="com.ideal.tools.model.entity.AlarmToolsEntity" id="AlarmToolsResult">
            <result property="id" column="iid"/>
            <result property="alarmId" column="ialarm_id"/>
            <result property="toolsId" column="itools_id"/>
    </resultMap>

    <sql id="selectAlarmTools">
        select iid, ialarm_id, itools_id
        from ieai_tb_alarm_tools
    </sql>

    <select id="selectAlarmToolsList" parameterType="com.ideal.tools.model.entity.AlarmToolsEntity" resultMap="AlarmToolsResult">
        <include refid="selectAlarmTools"/>
        <where>
                        <if test="alarmId != null ">
                            and ialarm_id = #{alarmId}
                        </if>
                        <if test="toolsId != null ">
                            and itools_id = #{toolsId}
                        </if>
        </where>
    </select>

    <select id="selectAlarmToolsById" parameterType="Long"
            resultMap="AlarmToolsResult">
            <include refid="selectAlarmTools"/>
            where iid = #{id}
    </select>

    <select id="selectToolIdList" resultType="java.lang.Long">
        select itools_id from ieai_tb_alarm_tools
        <where>
            <if test="alarmId != null">
                and ialarm_id = #{alarmId}
            </if>
        </where>
    </select>

    <select id="selectAlarmIdsByToolIds" parameterType="String" resultType="java.lang.Long">
        select ialarm_id from ieai_tb_alarm_tools where itools_id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertAlarmTools" parameterType="com.ideal.tools.model.entity.AlarmToolsEntity">
        insert into ieai_tb_alarm_tools
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">iid,
                    </if>
                    <if test="alarmId != null">ialarm_id,
                    </if>
                    <if test="toolsId != null">itools_id,
                    </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">#{id},
                    </if>
                    <if test="alarmId != null">#{alarmId},
                    </if>
                    <if test="toolsId != null">#{toolsId},
                    </if>
        </trim>
    </insert>
    <insert id="batchInsertAlarmTools">
        insert into ieai_tb_alarm_tools (iid,ialarm_id,itools_id)
        values
        <foreach collection="toolIds" item="toolsId" separator=",">
            (#{id},#{alarmId}, #{toolsId})
        </foreach>
    </insert>


    <update id="updateAlarmTools" parameterType="com.ideal.tools.model.entity.AlarmToolsEntity">
        update ieai_tb_alarm_tools
        <trim prefix="SET" suffixOverrides=",">
                    <if test="alarmId != null">ialarm_id =
                        #{alarmId},
                    </if>
                    <if test="toolsId != null">itools_id =
                        #{toolsId},
                    </if>
        </trim>
        where iid = #{id}
    </update>

    <delete id="deleteAlarmToolsById" parameterType="Long">
        delete
        from ieai_tb_alarm_tools where iid = #{id}
    </delete>

    <delete id="deleteAlarmToolsByIds" parameterType="String">
        delete from ieai_tb_alarm_tools where iid in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteAlarmToolsByAlarmAndToolIds">
        delete from ieai_tb_alarm_tools where  ialarm_id = #{alarmId} and itools_id in
        <foreach collection="toolIds" item="toolsId" open="(" separator="," close=")">
            #{toolsId}
        </foreach>
    </delete>

    <delete id="deleteAlarmToolsByAlarmIds" parameterType="String">
        delete from ieai_tb_alarm_tools where ialarm_id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteAlarmToolsByToolsIds" parameterType="String">
        delete from ieai_tb_alarm_tools where itools_id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.tools.mapper.ToolsAgentInfoMapper">

    <resultMap type="com.ideal.tools.model.entity.ToolsAgentInfoEntity" id="ToolsAgentInfoResult">
            <result property="id" column="iid"/>
            <result property="deviceName" column="idevice_name"/>
            <result property="osName" column="ios_name"/>
            <result property="agentName" column="iagent_name"/>
            <result property="agentIp" column="iagent_ip"/>
            <result property="agentPort" column="iagent_port"/>
            <result property="agentState" column="iagent_state"/>
            <result property="sysAgentInfoId" column="isys_agent_info_id"/>
            <result property="tdToolsId" column="itd_tools_id"/>
            <result property="createTime" column="icreate_time"/>
            <result property="execUserName" column="iexec_user_name"/>
            <result property="ipLong" column="iip_long"/>
    </resultMap>

    <sql id="selectToolsAgentInfo">
        select iid, idevice_name, ios_name, iagent_name, iagent_ip, iagent_port, iagent_state, isys_agent_info_id, itd_tools_id, icreate_time, iexec_user_name, iip_long
        from ieai_tb_tools_agent_info
    </sql>
    
    <select id="selectToolsAgentInfoList" parameterType="com.ideal.tools.model.entity.ToolsAgentInfoEntity" resultMap="ToolsAgentInfoResult">
        <include refid="selectToolsAgentInfo"/>
        <where>
                        <if test="deviceName != null  and deviceName != ''">
                            and idevice_name like concat('%', #{deviceName}, '%')
                        </if>
                        <if test="osName != null  and osName != ''">
                            and ios_name like concat('%', #{osName}, '%')
                        </if>
                        <if test="agentName != null  and agentName != ''">
                            and iagent_name like concat('%', #{agentName}, '%')
                        </if>
                        <if test="agentIp != null  and agentIp != ''">
                            and iagent_ip = #{agentIp}
                        </if>
                        <if test="agentPort != null ">
                            and iagent_port = #{agentPort}
                        </if>
                        <if test="agentState != null ">
                            and iagent_state = #{agentState}
                        </if>
                        <if test="sysAgentInfoId != null ">
                            and isys_agent_info_id = #{sysAgentInfoId}
                        </if>
                        <if test="tdToolsId != null ">
                            and itd_tools_id = #{tdToolsId}
                        </if>
                        <if test="createTime != null ">
                            and icreate_time = #{createTime}
                        </if>
                        <if test="execUserName != null  and execUserName != ''">
                            and iexec_user_name like concat('%', #{execUserName}, '%')
                        </if>
                        <if test="ipLong != null ">
                            and iip_long = #{ipLong}
                        </if>
                        <if test="agentIpEnd != null ">
                            and iip_long <![CDATA[ <= ]]> #{agentIpEnd}
                        </if>
                        <if test="agentIpStart != null ">
                            and iip_long <![CDATA[ >= ]]> #{agentIpStart}
                        </if>

        </where>
    </select>
    
    <select id="selectToolsAgentInfoListb" parameterType="com.ideal.tools.model.entity.ToolsAgentInfoEntity" resultMap="ToolsAgentInfoResult">
        select iid, ititle_name as iagent_name, iagent_ip, iagent_port,iexec_user_name from ieai_tb_tools_commonagent_info
        <where>
                        <if test="agentName != null  and agentName != ''">
                            and ititle_name like concat('%', #{agentName}, '%')
                        </if>
                        <if test="agentIp != null  and agentIp != ''">
                            and iagent_ip = #{agentIp}
                        </if>
                        <if test="agentPort != null ">
                            and iagent_port = #{agentPort}
                        </if>
                        <if test="tdToolsId != null ">
                            and itd_scripttools_id = #{tdToolsId}
                        </if>
                        <if test="execUserName != null  and execUserName != ''">
                            and iexec_user_name like concat('%', #{execUserName}, '%')
                        </if>

        </where>
    </select>

    <select id="selectToolsAgentInfoById" parameterType="Long"
            resultMap="ToolsAgentInfoResult">
        <include refid="selectToolsAgentInfo"/>
        where iid = #{id}
    </select>

    <insert id="insertToolsAgentInfo" parameterType="com.ideal.tools.model.entity.ToolsAgentInfoEntity" useGeneratedKeys="true"
            keyProperty="id">
        insert into ieai_tb_tools_agent_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">iid,
                    </if>
                    <if test="deviceName != null">idevice_name,
                    </if>
                    <if test="osName != null">ios_name,
                    </if>
                    <if test="agentName != null">iagent_name,
                    </if>
                    <if test="agentIp != null and agentIp != ''">iagent_ip,
                    </if>
                    <if test="agentPort != null">iagent_port,
                    </if>
                    <if test="agentState != null">iagent_state,
                    </if>
                    <if test="sysAgentInfoId != null">isys_agent_info_id,
                    </if>
                    <if test="tdToolsId != null">itd_tools_id,
                    </if>
                    <if test="createTime != null">icreate_time,
                    </if>
                    <if test="execUserName != null">iexec_user_name,
                    </if>
                    <if test="ipLong != null">iip_long,
                    </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">#{id},
                    </if>
                    <if test="deviceName != null">#{deviceName},
                    </if>
                    <if test="osName != null">#{osName},
                    </if>
                    <if test="agentName != null">#{agentName},
                    </if>
                    <if test="agentIp != null and agentIp != ''">#{agentIp},
                    </if>
                    <if test="agentPort != null">#{agentPort},
                    </if>
                    <if test="agentState != null">#{agentState},
                    </if>
                    <if test="sysAgentInfoId != null">#{sysAgentInfoId},
                    </if>
                    <if test="tdToolsId != null">#{tdToolsId},
                    </if>
                    <if test="createTime != null">#{createTime},
                    </if>
                    <if test="execUserName != null">#{execUserName},
                    </if>
                    <if test="ipLong != null">#{ipLong},
                    </if>
        </trim>
    </insert>

    <update id="updateToolsAgentInfo" parameterType="com.ideal.tools.model.entity.ToolsAgentInfoEntity">
        update ieai_tb_tools_agent_info
        <trim prefix="SET" suffixOverrides=",">
                    <if test="deviceName != null">idevice_name =
                        #{deviceName},
                    </if>
                    <if test="osName != null">ios_name =
                        #{osName},
                    </if>
                    <if test="agentName != null">iagent_name =
                        #{agentName},
                    </if>
                    <if test="agentIp != null and agentIp != ''">iagent_ip =
                        #{agentIp},
                    </if>
                    <if test="agentPort != null">iagent_port =
                        #{agentPort},
                    </if>
                    <if test="agentState != null">iagent_state =
                        #{agentState},
                    </if>
                    <if test="sysAgentInfoId != null">isys_agent_info_id =
                        #{sysAgentInfoId},
                    </if>
                    <if test="tdToolsId != null">itd_tools_id =
                        #{tdToolsId},
                    </if>
                    <if test="createTime != null">icreate_time =
                        #{createTime},
                    </if>
                    <if test="execUserName != null">iexec_user_name =
                        #{execUserName},
                    </if>
                    <if test="ipLong != null">iip_long =
                        #{ipLong},
                    </if>
        </trim>
        where iid = #{id}
    </update>

    <delete id="deleteToolsAgentInfoById" parameterType="Long">
        delete
        from ieai_tb_tools_agent_info where iid = #{id}
    </delete>

    <delete id="deleteToolsAgentInfoByIds" parameterType="String">
        delete from ieai_tb_tools_agent_info where iid in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteToolsAgentInfoBySysAgentIds" parameterType="String">
        delete from ieai_tb_tools_agent_info where isys_agent_info_id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteToolsAgentInfoBySysAgentId"  parameterType="com.ideal.tools.model.entity.ToolsAgentInfoEntity" >
        delete from ieai_tb_tools_agent_info
        <where>
            <if test="sysAgentInfoId != null ">
                and isys_agent_info_id = #{sysAgentInfoId}
            </if>
            <if test="tdToolsId != null ">
                and itd_tools_id = #{tdToolsId}
            </if>
        </where>
    </delete>

    <delete id="deleteAgentToolId" parameterType="Long">
        delete
        from ieai_tb_tools_agent_info where itd_tools_id = #{tdToolsId}
    </delete>

    <select id="selectAgentIpByToolIdAndAgentIp" parameterType="com.ideal.tools.model.entity.ToolsAgentInfoEntity" resultType="String">
        select tai.iagent_ip from ieai_tb_tools_agent_info tai
        <where>
            <if test="agentIp != null and agentIp != '' and tdToolsId != null ">
               and tai.iagent_ip =#{agentIp} and tai.itd_tools_id like concat('%', (select ichild_ids from ieai_tb_tools_info tti where tti.istatus=3 and tti.iid = #{tdToolsId}), '%')
            </if>
        </where>
    </select>
    <select id="selectToolsAgentInfoListByIds" resultMap="ToolsAgentInfoResult">
        <include refid="selectToolsAgentInfo"/>
        <where>
            <if test="ids != null and ids.size() > 0">
                and itd_tools_id in
                <foreach item="id" collection="ids" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>
    <select id="selectAgentCountByToolId" resultType="Integer">
        select count(0) from ieai_tb_tools_agent_info where itd_tools_id = #{tdToolsId};
    </select>
</mapper>
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.tools.mapper.CommonToolsAgentInfoMapper">

    <resultMap type="com.ideal.tools.model.entity.CommonToolsAgentInfoEntity" id="CommonToolsAgentInfoResult">
        <result property="id" column="iid"/>
        <result property="titleName" column="ititle_name"/>
        <result property="agentIp" column="iagent_ip"/>
        <result property="agentPort" column="iagent_port"/>
        <result property="tdCommonToolsId" column="itd_commontools_id"/>
        <result property="tdScriptToolsId" column="itd_scripttools_id"/>
        <result property="createTime" column="icreate_time"/>
        <result property="execUserName" column="iexec_user_name"/>
    </resultMap>

    <sql id="selectCommonToolsAgentInfo">
        select iid, ititle_name, iagent_ip, iagent_port, itd_commontools_id, itd_scripttools_id, icreate_time, iexec_user_name
        from ieai_tb_tools_commonagent_info
    </sql>

    <select id="selectCommonToolsAgentInfoListByCommonToolsId" parameterType="com.ideal.tools.model.entity.CommonToolsAgentInfoEntity" resultMap="CommonToolsAgentInfoResult">
        <include refid="selectCommonToolsAgentInfo"/>
        <where>
            <if test="tdCommonToolsId != null  and tdCommonToolsId != ''">
                itd_commontools_id = #{tdCommonToolsId}
            </if>
        </where>
    </select>
    <select id="selectCommonToolsAgentInfoListByCommonToolsIdAndScriptToolsId" parameterType="com.ideal.tools.model.entity.CommonToolsAgentInfoEntity" resultMap="CommonToolsAgentInfoResult">
        <include refid="selectCommonToolsAgentInfo"/>
        <where>
            <if test="tdCommonToolsId != null  and tdCommonToolsId != ''">
                itd_commontools_id = #{tdCommonToolsId}
            </if>
            <if test="tdScriptToolsId != null  and tdScriptToolsId != ''">
                and itd_scripttools_id = #{tdScriptToolsId}
            </if>
        </where>
    </select>

    <insert id="insertCommonToolsAgentInfo" parameterType="com.ideal.tools.model.entity.CommonToolsAgentInfoEntity" useGeneratedKeys="true"
            keyProperty="id">
        insert into ieai_tb_tools_commonagent_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">iid,
            </if>
            <if test="titleName != null">ititle_name,
            </if>
            <if test="agentIp != null and agentIp != ''">iagent_ip,
            </if>
            <if test="agentPort != null">iagent_port,
            </if>
            <if test="tdCommonToolsId != null">itd_commontools_id,
            </if>
            <if test="tdScriptToolsId != null">itd_scripttools_id,
            </if>
            <if test="createTime != null">icreate_time,
            </if>
            <if test="execUserName != null">iexec_user_name
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},
            </if>
            <if test="titleName != null">#{titleName},
            </if>
            <if test="agentIp != null and agentIp != ''">#{agentIp},
            </if>
            <if test="agentPort != null">#{agentPort},
            </if>
            <if test="tdCommonToolsId != null">#{tdCommonToolsId},
            </if>
            <if test="tdScriptToolsId != null">#{tdScriptToolsId},
            </if>
            <if test="createTime != null">#{createTime},
            </if>
            <if test="execUserName != null">#{execUserName}
            </if>
        </trim>
    </insert>

    <select id="selectToolsAgentCountByCommonToolsid" resultType="Integer">
        select count(1) from ieai_tb_tools_commonagent_info where itd_commontools_id = #{tdCommonToolsId}
    </select>

    <select id="selectToolsAgentCountByCommonToolsIdAndScriptToolsId" resultType="Integer">
        select count(1) from ieai_tb_tools_commonagent_info where itd_commontools_id = #{tdCommonToolsId} and itd_scripttools_id = #{tdScriptToolsId}
    </select>

    <insert id="batchInsert" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO ieai_tb_tools_commonagent_info
        (itd_commontools_id, ititle_name, iagent_ip, itd_scripttools_id, iagent_port, iexec_user_name)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.tdCommonToolsId}, #{item.titleName}, #{item.agentIp},
            #{item.tdScriptToolsId}, #{item.agentPort}, #{item.execUserName})
        </foreach>
    </insert>

</mapper>
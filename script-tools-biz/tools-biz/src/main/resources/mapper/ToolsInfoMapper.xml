<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.tools.mapper.ToolsInfoMapper">

    <resultMap type="com.ideal.tools.model.entity.ToolsInfoEntity" id="ToolsInfoResult">
            <result property="id" column="iid"/>
            <result property="code" column="icode"/>
            <result property="name" column="iname"/>
            <result property="type" column="itype"/>
            <result property="classification" column="iclassification"/>
            <result property="scriptName" column="iscript_name"/>
            <result property="scriptIds" column="iscript_ids"/>
            <result property="scriptOperatingUser" column="iscript_operating_user"/>
            <result property="scriptType" column="iscript_type"/>
            <result property="effect" column="ieffect"/>
            <result property="usageScenario" column="iusage_scenario"/>
            <result property="scriptEditing" column="iscript_editing"/>
            <result property="estimateOperationalRisk" column="iestimate_operational_risk"/>
            <result property="status" column="istatus"/>
            <result property="keyword" column="ikeyword"/>
            <result property="businessSystemId" column="ibusiness_system_id"/>
            <result property="businessSystemName" column="ibusiness_system_name"/>
            <result property="businessSystemCode" column="ibusiness_system_code"/>
            <result property="matchIp" column="imatch_ip"/>
            <result property="firstDescribe" column="ifirst_describe"/>
            <result property="describe" column="idescribe"/>
            <result property="osType" column="ios_type"/>
            <result property="oneTypeId" column="ione_type_id"/>
            <result property="twoTypeId" column="itwo_type_id"/>
            <result property="threeTypeId" column="ithree_type_id"/>
            <result property="highRisk" column="ihigh_risk"/>
            <result property="childIds" column="ichild_ids"/>
            <result property="dangerCmd" column="idanger_cmd"/>
            <result property="deliveryStatus" column="idelivery_status"/>
            <result property="deliveryAuditorId" column="idelivery_auditor_id"/>
            <result property="deliveryReturnCause" column="idelivery_return_cause"/>
            <result property="deliveryReceptionTime" column="idelivery_reception_time"/>
            <result property="updatorId" column="iupdator_id"/>
            <result property="updatorName" column="iupdator_name"/>
            <result property="updateTime" column="iupdate_time"/>
            <result property="creatorId" column="icreator_id"/>
            <result property="creatorName" column="icreator_name"/>
            <result property="createTime" column="icreate_time"/>
	        <result property="delFlag" column="del_flag"/>
    </resultMap>

    <resultMap type="com.ideal.tools.model.entity.ToolsInfoCategoryEntity" id="ToolsInfoCategoryResult">
        <result property="id" column="iid"/>
        <result property="code" column="icode"/>
        <result property="name" column="iname"/>
        <result property="type" column="itype"/>
        <result property="scriptName" column="iscript_name"/>
        <result property="scriptIds" column="iscript_ids"/>
        <result property="scriptOperatingUser" column="iscript_operating_user"/>
	  <result property="scriptType" column="iscript_type"/>
            <result property="effect" column="ieffect"/>
            <result property="usageScenario" column="iusage_scenario"/>
            <result property="scriptEditing" column="iscript_editing"/>
            <result property="estimateOperationalRisk" column="iestimate_operational_risk"/>
        <result property="status" column="istatus"/>
        <result property="keyword" column="ikeyword"/>
        <result property="businessSystemId" column="ibusiness_system_id"/>
        <result property="businessSystemName" column="ibusiness_system_name"/>
        <result property="matchIp" column="imatch_ip"/>
        <result property="firstDescribe" column="ifirst_describe"/>
        <result property="describe" column="idescribe"/>
        <result property="osType" column="ios_type"/>

        <result property="oneTypeId" column="ione_type_id"/>
        <result property="oneTypeName" column="ione_type_name"/>

        <result property="twoTypeId" column="itwo_type_id"/>
        <result property="twoTypeName" column="itwo_type_name"/>

        <result property="threeTypeId" column="ithree_type_id"/>
        <result property="threeTypeName" column="ithree_type_name"/>

        <result property="highRisk" column="ihigh_risk"/>
        <result property="dangerCmd" column="idanger_cmd"/>
        <result property="childIds" column="ichild_ids"/>
        <result property="deliveryStatus" column="idelivery_status"/>
        <result property="deliveryAuditorId" column="idelivery_auditor_id"/>
        <result property="deliveryReturnCause" column="idelivery_return_cause"/>
        <result property="deliveryReceptionTime" column="idelivery_reception_time"/>
        <result property="updatorId" column="iupdator_id"/>
        <result property="updatorName" column="iupdator_name"/>
        <result property="updateTime" column="iupdate_time"/>
        <result property="creatorId" column="icreator_id"/>
        <result property="creatorName" column="icreator_name"/>
        <result property="createTime" column="icreate_time"/>
        <result property="approvalState" column="iapproval_state"/>
	<result property="delFlag" column="del_flag"/>
	<result property="classification" column="iclassification"/>
	<result property="num" column="num"/>
    </resultMap>

    <resultMap type="com.ideal.tools.model.entity.ToolsInfoEditEntity" id="ToolsInfoEditResult">
        <result property="id" column="iid"/>
        <result property="code" column="icode"/>
        <result property="name" column="iname"/>
        <result property="type" column="itype"/>
        <result property="scriptName" column="iscript_name"/>
        <result property="scriptIds" column="iscript_ids"/>
        <result property="scriptOperatingUser" column="iscript_operating_user"/>
        <result property="scriptType" column="iscript_type"/>
        <result property="effect" column="ieffect"/>
        <result property="usageScenario" column="iusage_scenario"/>
        <result property="scriptEditing" column="iscript_editing"/>
        <result property="estimateOperationalRisk" column="iestimate_operational_risk"/>
        <result property="status" column="istatus"/>
        <result property="keyword" column="ikeyword"/>
        <result property="businessSystemId" column="ibusiness_system_id"/>
        <result property="businessSystemName" column="ibusiness_system_name"/>
        <result property="businessSystemCode" column="ibusiness_system_code"/>
        <result property="matchIp" column="imatch_ip"/>
        <result property="firstDescribe" column="ifirst_describe"/>
        <result property="describe" column="idescribe"/>
        <result property="osType" column="ios_type"/>

        <result property="oneTypeId" column="ione_type_id"/>
        <result property="oneTypeName" column="ione_type_name"/>

        <result property="twoTypeId" column="itwo_type_id"/>
        <result property="twoTypeName" column="itwo_type_name"/>

        <result property="threeTypeId" column="ithree_type_id"/>
        <result property="threeTypeName" column="ithree_type_name"/>

        <result property="highRisk" column="ihigh_risk"/>
        <result property="dangerCmd" column="idanger_cmd"/>
        <result property="childIds" column="ichild_ids"/>
        <result property="deliveryStatus" column="idelivery_status"/>
        <result property="deliveryAuditorId" column="idelivery_auditor_id"/>
        <result property="deliveryReturnCause" column="idelivery_return_cause"/>
        <result property="deliveryReceptionTime" column="idelivery_reception_time"/>
        <result property="updatorId" column="iupdator_id"/>
        <result property="updatorName" column="iupdator_name"/>
        <result property="updateTime" column="iupdate_time"/>
        <result property="creatorId" column="icreator_id"/>
        <result property="creatorName" column="icreator_name"/>
        <result property="createTime" column="icreate_time"/>
        <result property="approvalState" column="iapproval_state"/>
        <result property="delFlag" column="del_flag"/>
        <result property="classification" column="iclassification"/>
        <result property="returnReason" column="ireturn_reason"/>
    </resultMap>

    <resultMap type="com.ideal.tools.model.entity.ToolsInfoEntity" id="ToolsInfoForKeywordResult">
        <result property="id" column="iid"/>
        <result property="keyword" column="ikeyword"/>
        <result property="businessSystemId" column="ibusiness_system_id"/>
    </resultMap>

    <resultMap type="com.ideal.tools.model.entity.ToolsInfoEntity" id="ToolsInfoForAlarmResult">
        <result property="id" column="iid"/>
        <result property="code" column="icode"/>
        <result property="name" column="iname"/>
        <result property="type" column="itype"/>
        <result property="classification" column="iclassification"/>
        <result property="scriptName" column="iscript_name"/>
        <result property="scriptIds" column="iscript_ids"/>
        <result property="scriptOperatingUser" column="iscript_operating_user"/>
        <result property="scriptType" column="iscript_type"/>
        <result property="effect" column="ieffect"/>
        <result property="usageScenario" column="iusage_scenario"/>
        <result property="scriptEditing" column="iscript_editing"/>
        <result property="estimateOperationalRisk" column="iestimate_operational_risk"/>
        <result property="status" column="istatus"/>
        <result property="keyword" column="ikeyword"/>
        <result property="businessSystemId" column="ibusiness_system_id"/>
        <result property="businessSystemName" column="ibusiness_system_name"/>
        <result property="businessSystemCode" column="ibusiness_system_code"/>
        <result property="matchIp" column="imatch_ip"/>
        <result property="firstDescribe" column="ifirst_describe"/>
        <result property="describe" column="idescribe"/>
        <result property="osType" column="ios_type"/>
        <result property="oneTypeId" column="ione_type_id"/>
        <result property="twoTypeId" column="itwo_type_id"/>
        <result property="threeTypeId" column="ithree_type_id"/>
        <result property="highRisk" column="ihigh_risk"/>
        <result property="childIds" column="ichild_ids"/>
        <result property="dangerCmd" column="idanger_cmd"/>
        <result property="deliveryStatus" column="idelivery_status"/>
        <result property="deliveryAuditorId" column="idelivery_auditor_id"/>
        <result property="deliveryReturnCause" column="idelivery_return_cause"/>
        <result property="deliveryReceptionTime" column="idelivery_reception_time"/>
        <result property="updatorId" column="iupdator_id"/>
        <result property="updatorName" column="iupdator_name"/>
        <result property="updateTime" column="iupdate_time"/>
        <result property="creatorId" column="icreator_id"/>
        <result property="creatorName" column="icreator_name"/>
        <result property="createTime" column="icreate_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="approvalState" column="iapproval_state"/>
    </resultMap>

    <sql id="selectToolsInfo">
        select iid, icode, iname, itype, iclassification, iscript_name, iscript_ids, iscript_operating_user, iscript_type, ieffect, iusage_scenario, iscript_editing, iestimate_operational_risk, istatus, ikeyword, ibusiness_system_id, ibusiness_system_name,ibusiness_system_code, imatch_ip, ifirst_describe, idescribe, ios_type, ione_type_id, itwo_type_id, ithree_type_id, ihigh_risk, ichild_ids, idelivery_status, idelivery_auditor_id, idelivery_return_cause, idelivery_reception_time, iupdator_id, iupdator_name, iupdate_time, icreator_id, icreator_name, icreate_time, idanger_cmd, del_flag
        from ieai_tb_tools_info
    </sql>
    <sql id="selectToolsEditInfo">
        select   a.iid,
            a.icode,
            a.iname,
            a.itype,
            a.iclassification,
            a.iscript_name,
            a.iscript_ids,
            a.iscript_operating_user,
            a.iscript_type, a.ieffect, a.iusage_scenario, a.iscript_editing, a.iestimate_operational_risk,
            a.istatus,
            a.ikeyword,
            a.ibusiness_system_id,
            a.ibusiness_system_name,
            a.ibusiness_system_code,
            a.imatch_ip,
            a.ifirst_describe,
            a.idescribe,
            a.ios_type,

            a.ione_type_id,
            b.iclassname as ione_type_name,

            a.itwo_type_id,
            c.iclassname as itwo_type_name,
            a.ithree_type_id,
            d.iclassname as ithree_type_name,
            a.ihigh_risk,
            a.idanger_cmd,
            a.ichild_ids,
            a.idelivery_status,
            a.idelivery_auditor_id,
            a.idelivery_return_cause,
            a.idelivery_reception_time,
            a.iupdator_id,
            a.iupdator_name,
            a.iupdate_time,
            a.icreator_id,
            a.icreator_name,
            a.icreate_time,
            a.del_flag,
            a.ireturn_reason from ieai_tb_tools_info  a
        LEFT JOIN ieai_tb_category b ON a.ione_type_id = b.iid

        LEFT JOIN ieai_tb_category c ON a.itwo_type_id = c.iid

        LEFT JOIN ieai_tb_category d ON a.ithree_type_id = d.iid

    </sql>
    <sql id="selectToolsInfoForKeyword">
        select iid, ikeyword, ibusiness_system_id
        from ieai_tb_tools_info
    </sql>

    <sql id="selectToolsInfoForAlarm">
        select a.iid, a.icode, a.iname, a.itype, a.iclassification, a.iscript_name, a.iscript_ids, a.iscript_operating_user, a.iscript_type, a.ieffect, a.iusage_scenario, a.iscript_editing, a.iestimate_operational_risk, a.istatus, a.ikeyword, a.ibusiness_system_id, a.ibusiness_system_name, a.imatch_ip, a.ifirst_describe, a.idescribe, a.ios_type, a.ione_type_id, a.itwo_type_id, a.ithree_type_id, a.ihigh_risk, a.ichild_ids, a.idelivery_status, a.idelivery_auditor_id, a.idelivery_return_cause, a.idelivery_reception_time, a.iupdator_id, a.iupdator_name, a.iupdate_time, a.icreator_id, a.icreator_name, a.icreate_time, a.idanger_cmd, a.del_flag ,b.iapproval_state
        from ieai_tb_tools_info a
        left join ieai_tb_audit b ON b.ibusiness_id = a.iid
        and b.itype in (1)  and b.iapply_time = (SELECT MAX(iapply_time) FROM ieai_tb_audit WHERE ibusiness_id = a.iid and itype in (1))
    </sql>

    <sql id="selectToolsInfoAll">
    SELECT
	a.iid,
	a.icode,
	a.iname,
	a.itype,
    a.iclassification,
	a.iscript_name,
	a.iscript_ids,
	a.iscript_operating_user,
	 a.iscript_type, a.ieffect, a.iusage_scenario, a.iscript_editing, a.iestimate_operational_risk,
	a.istatus,
	a.ikeyword,
	a.ibusiness_system_id,
	a.ibusiness_system_name,
	a.imatch_ip,
	a.ifirst_describe,
	a.idescribe,
	a.ios_type,
	
	a.ione_type_id,
	b.iclassname as ione_type_name,
	
	a.itwo_type_id,
		c.iclassname as itwo_type_name,
	a.ithree_type_id,
		d.iclassname as ithree_type_name,
	a.ihigh_risk,
    a.idanger_cmd,
	a.ichild_ids,
	a.idelivery_status,
	a.idelivery_auditor_id,
	a.idelivery_return_cause,
	a.idelivery_reception_time,
	a.iupdator_id,
	a.iupdator_name,
	a.iupdate_time,
	a.icreator_id,
	a.icreator_name,
	a.icreate_time,
	a.del_flag,
    a.iclassification,
    e.iapproval_state
FROM
	ieai_tb_tools_info a
	LEFT JOIN ieai_tb_category b ON a.ione_type_id = b.iid
	
	LEFT JOIN ieai_tb_category c ON a.itwo_type_id = c.iid			
	
	LEFT JOIN ieai_tb_category d ON a.ithree_type_id = d.iid

    LEFT JOIN (SELECT ibusiness_id,MAX(iapply_time) as max_apply_time
        FROM
            ieai_tb_audit
        WHERE
            itype IN (1,6,9)
        GROUP BY
            ibusiness_id
    ) latest_audit ON a.iid = latest_audit.ibusiness_id
    LEFT JOIN ieai_tb_audit e ON e.ibusiness_id = a.iid
        AND e.iapply_time = latest_audit.max_apply_time
        AND e.itype IN (1,6,9)
    </sql>
    <sql id="selectToolsInfoCategory">
    SELECT
	a.iid,
	a.icode,
	a.iname,
	a.itype,
    a.iclassification,
	a.iscript_name,
	a.iscript_ids,
	a.iscript_operating_user,
	 a.iscript_type, a.ieffect, a.iusage_scenario, a.iscript_editing, a.iestimate_operational_risk,
	a.istatus,
	a.ikeyword,
	a.ibusiness_system_id,
	a.ibusiness_system_name,
	a.imatch_ip,
	a.ifirst_describe,
	a.idescribe,
	a.ios_type,

	a.ione_type_id,
	b.iclassname as ione_type_name,

	a.itwo_type_id,
		c.iclassname as itwo_type_name,
	a.ithree_type_id,
		d.iclassname as ithree_type_name,
	a.ihigh_risk,
    a.idanger_cmd,
	a.ichild_ids,
	a.idelivery_status,
	a.idelivery_auditor_id,
	a.idelivery_return_cause,
	a.idelivery_reception_time,
	a.iupdator_id,
	a.iupdator_name,
	a.iupdate_time,
	a.icreator_id,
	a.icreator_name,
	a.icreate_time,
	a.del_flag,
	coalesce(case when h.iid is null AND m.iid is null THEN 0 END ,1)AS num
FROM
	ieai_tb_tools_info a
	LEFT JOIN ieai_tb_category b ON a.ione_type_id = b.iid

	LEFT JOIN ieai_tb_category c ON a.itwo_type_id = c.iid

	LEFT JOIN ieai_tb_category d ON a.ithree_type_id = d.iid

    LEFT JOIN ieai_tb_execute_history h on h.itool_id = a.iid
    LEFT JOIN ieai_tb_execute_monitor m on m.itool_id = a.iid

    </sql>

    <sql id="selectToolsInfoEdit">
        SELECT
            a.iid,
            a.icode,
            a.iname,
            a.itype,
            a.iclassification,
            a.iscript_name,
            a.iscript_ids,
            a.iscript_operating_user,
            a.iscript_type, a.ieffect, a.iusage_scenario, a.iscript_editing, a.iestimate_operational_risk,
            a.istatus,
            a.ikeyword,
            a.ibusiness_system_id,
            a.ibusiness_system_name,
            a.imatch_ip,
            a.ifirst_describe,
            a.idescribe,
            a.ios_type,

            a.ione_type_id,
            b.iclassname as ione_type_name,

            a.itwo_type_id,
            c.iclassname as itwo_type_name,
            a.ithree_type_id,
            d.iclassname as ithree_type_name,
            a.ihigh_risk,
            a.idanger_cmd,
            a.ichild_ids,
            a.idelivery_status,
            a.idelivery_auditor_id,
            a.idelivery_return_cause,
            a.idelivery_reception_time,
            a.iupdator_id,
            a.iupdator_name,
            a.iupdate_time,
            a.icreator_id,
            a.icreator_name,
            a.icreate_time,
            a.del_flag,
            e.iapproval_state
        FROM
            ieai_tb_tools_info a
                LEFT JOIN ieai_tb_category b ON a.ione_type_id = b.iid

                LEFT JOIN ieai_tb_category c ON a.itwo_type_id = c.iid

                LEFT JOIN ieai_tb_category d ON a.ithree_type_id = d.iid

                LEFT JOIN (SELECT ibusiness_id,MAX(iapply_time) as max_apply_time
                FROM
                    ieai_tb_audit
                WHERE
                    itype IN (1,6,9)
                GROUP BY
                    ibusiness_id
            ) latest_audit ON a.iid = latest_audit.ibusiness_id
                LEFT JOIN ieai_tb_audit e ON e.ibusiness_id = a.iid
                AND e.iapply_time = latest_audit.max_apply_time
                AND e.itype IN (1,6,9)
    </sql>

    <select id="selectToolsInfoList" parameterType="com.ideal.tools.model.entity.ToolsInfoEntity" resultMap="ToolsInfoResult">
        <include refid="selectToolsInfo"/>
        <where>
                        <if test="code != null  and code != ''">
                            and icode = #{code}
                        </if>
                        <if test="name != null  and name != ''">
                            and iname like concat('%', #{name}, '%')
                        </if>
                        <if test="type != null ">
                            and itype = #{type}
                        </if>
                        <if test="classification != null ">
                            and iclassification = #{classification}
                        </if>
                        <if test="scriptName != null  and scriptName != ''">
                            and iscript_name like concat('%', #{scriptName}, '%')
                        </if>
                        <if test="scriptIds != null  and scriptIds != ''">
                            and iscript_ids = #{scriptIds}
                        </if>
                        <if test="scriptOperatingUser != null  and scriptOperatingUser != ''">
                            and iscript_operating_user = #{scriptOperatingUser}
                        </if>
                        <if test="scriptType != null ">
                            and iscript_type = #{scriptType}
                        </if>
                        <if test="effect != null  and effect != ''">
                            and ieffect = #{effect}
                        </if>
                        <if test="usageScenario != null  and usageScenario != ''">
                            and iusage_scenario = #{usageScenario}
                        </if>
                        <if test="scriptEditing != null  and scriptEditing != ''">
                            and iscript_editing = #{scriptEditing}
                        </if>
                        <if test="estimateOperationalRisk != null ">
                            and iestimate_operational_risk = #{estimateOperationalRisk}
                        </if>
                        <if test="status != null ">
                            and istatus = #{status}
                        </if>
                        <if test="keyword != null  and keyword != ''">
                            and ikeyword = #{keyword}
                        </if>
                        <if test="businessSystemId != null ">
                            and ibusiness_system_id = #{businessSystemId}
                        </if>
                        <if test="businessSystemName != null  and businessSystemName != ''">
                            and ibusiness_system_name like concat('%', #{businessSystemName}, '%')
                        </if>
                        <if test="matchIp != null  and matchIp != ''">
                            and imatch_ip = #{matchIp}
                        </if>
                        <if test="firstDescribe != null  and firstDescribe != ''">
                            and ifirst_describe = #{firstDescribe}
                        </if>
                        <if test="describe != null  and describe != ''">
                            and idescribe = #{describe}
                        </if>
                        <if test="osType != null ">
                            and ios_type = #{osType}
                        </if>
                        <if test="oneTypeId != null ">
                            and ione_type_id = #{oneTypeId}
                        </if>
                        <if test="twoTypeId != null ">
                            and itwo_type_id = #{twoTypeId}
                        </if>
                        <if test="threeTypeId != null ">
                            and ithree_type_id = #{threeTypeId}
                        </if>
                        <if test="highRisk != null ">
                            and ihigh_risk = #{highRisk}
                        </if>
                        <if test="dangerCmd != null  and dangerCmd != ''">
                            and idanger_cmd = #{dangerCmd}
                        </if>
                        <if test="childIds != null  and childIds != ''">
                            and ichild_ids like concat('%', #{childIds}, '%')
                        </if>
                        <if test="deliveryStatus != null ">
                            and idelivery_status = #{deliveryStatus}
                        </if>
                        <if test="deliveryAuditorId != null ">
                            and idelivery_auditor_id = #{deliveryAuditorId}
                        </if>
                        <if test="deliveryReturnCause != null  and deliveryReturnCause != ''">
                            and idelivery_return_cause = #{deliveryReturnCause}
                        </if>
                        <if test="deliveryReceptionTime != null ">
                            and idelivery_reception_time = #{deliveryReceptionTime}
                        </if>
                        <if test="updatorId != null ">
                            and iupdator_id = #{updatorId}
                        </if>
                        <if test="updatorName != null  and updatorName != ''">
                            and iupdator_name like concat('%', #{updatorName}, '%')
                        </if>
                        <if test="updateTime != null ">
                            and iupdate_time = #{updateTime}
                        </if>
                        <if test="creatorId != null ">
                            and icreator_id = #{creatorId}
                        </if>
                        <if test="creatorName != null  and creatorName != ''">
                            and icreator_name like concat('%', #{creatorName}, '%')
                        </if>
                        <if test="createTime != null ">
                            and icreate_time = #{createTime}
                        </if>
                        <if test="delFlag != null ">
                            and del_flag=#{delFlag}
                        </if>
                        <if test="businessSystemIds != null">
                			and ibusiness_system_id in
                 			<foreach collection="businessSystemIds" item="businessSystemIdIt" open="(" close=")" separator=",">
                    		 #{businessSystemIdIt}
                 			</foreach>
             			</if>
             			<if test="notName != null ">
                			and iname!=#{notName}
            			</if>
        </where>
        ORDER BY iupdate_time DESC
    </select>
    <select id="selectToolsInfoListByStatus" parameterType="com.ideal.tools.model.entity.ToolsInfoEntity" resultMap="ToolsInfoResult">
        <include refid="selectToolsInfo"/>
        <where>
            <if test="code != null  and code != ''">
                and icode = #{code}
            </if>
            <if test="name != null  and name != ''">
                and iname like concat('%', #{name}, '%')
            </if>
            <if test="type != null ">
                and itype = #{type}
            </if>
            <if test="classification != null ">
                and iclassification = #{classification}
            </if>
            <if test="scriptName != null  and scriptName != ''">
                and iscript_name like concat('%', #{scriptName}, '%')
            </if>
            <if test="scriptIds != null  and scriptIds != ''">
                and iscript_ids = #{scriptIds}
            </if>
            <if test="scriptOperatingUser != null  and scriptOperatingUser != ''">
                and iscript_operating_user = #{scriptOperatingUser}
            </if>
            <if test="scriptType != null ">
                and iscript_type = #{scriptType}
            </if>
            <if test="effect != null  and effect != ''">
                and ieffect = #{effect}
            </if>
            <if test="usageScenario != null  and usageScenario != ''">
                and iusage_scenario = #{usageScenario}
            </if>
            <if test="scriptEditing != null  and scriptEditing != ''">
                and iscript_editing = #{scriptEditing}
            </if>
            <if test="estimateOperationalRisk != null ">
                and iestimate_operational_risk = #{estimateOperationalRisk}
            </if>
            <if test="keyword != null  and keyword != ''">
                and ikeyword = #{keyword}
            </if>
            <if test="businessSystemId != null ">
                and ibusiness_system_id = #{businessSystemId}
            </if>
            <if test="businessSystemName != null  and businessSystemName != ''">
                and ibusiness_system_name like concat('%', #{businessSystemName}, '%')
            </if>
            <if test="matchIp != null  and matchIp != ''">
                and imatch_ip = #{matchIp}
            </if>
            <if test="firstDescribe != null  and firstDescribe != ''">
                and ifirst_describe = #{firstDescribe}
            </if>
            <if test="describe != null  and describe != ''">
                and idescribe = #{describe}
            </if>
            <if test="osType != null ">
                and ios_type = #{osType}
            </if>
            <if test="oneTypeId != null ">
                and ione_type_id = #{oneTypeId}
            </if>
            <if test="twoTypeId != null ">
                and itwo_type_id = #{twoTypeId}
            </if>
            <if test="threeTypeId != null ">
                and ithree_type_id = #{threeTypeId}
            </if>
            <if test="highRisk != null ">
                and ihigh_risk = #{highRisk}
            </if>
            <if test="dangerCmd != null  and dangerCmd != ''">
                and idanger_cmd = #{dangerCmd}
            </if>
            <if test="childIds != null  and childIds != ''">
                and ichild_ids like concat('%', #{childIds}, '%')
            </if>
            <if test="deliveryStatus != null ">
                and idelivery_status = #{deliveryStatus}
            </if>
            <if test="deliveryAuditorId != null ">
                and idelivery_auditor_id = #{deliveryAuditorId}
            </if>
            <if test="deliveryReturnCause != null  and deliveryReturnCause != ''">
                and idelivery_return_cause = #{deliveryReturnCause}
            </if>
            <if test="deliveryReceptionTime != null ">
                and idelivery_reception_time = #{deliveryReceptionTime}
            </if>
            <if test="updatorId != null ">
                and iupdator_id = #{updatorId}
            </if>
            <if test="updatorName != null  and updatorName != ''">
                and iupdator_name like concat('%', #{updatorName}, '%')
            </if>
            <if test="updateTime != null ">
                and iupdate_time = #{updateTime}
            </if>
            <if test="creatorId != null ">
                and icreator_id = #{creatorId}
            </if>
            <if test="creatorName != null  and creatorName != ''">
                and icreator_name like concat('%', #{creatorName}, '%')
            </if>
            <if test="createTime != null ">
                and icreate_time = #{createTime}
            </if>
            <if test="delFlag != null ">
                and del_flag=#{delFlag}
            </if>
             <if test="businessSystemIds != null">
                 and ibusiness_system_id in
                 <foreach collection="businessSystemIds" item="businessSystemIdIt" open="(" close=")" separator=",">
                     #{businessSystemIdIt}
                 </foreach>
             </if>
             <if test="notName != null ">
                and iname!=#{notName}
            </if>
                and (istatus != 6 or istatus != 2 )
        </where>
        ORDER BY iupdate_time DESC
    </select>
    <select id="selectToolsInfoCategoryList" parameterType="com.ideal.tools.model.entity.ToolsInfoCategoryEntity" resultMap="ToolsInfoCategoryResult">
        <include refid="selectToolsInfoCategory"/>
        <where>
            <if test="code != null  and code != ''">
                and a.icode = #{code}
            </if>
            <if test="likeCode != null  and likeCode != ''">
                and a.icode like  concat('%', #{likeCode}, '%')
            </if>
            <if test="name != null  and name != ''">
                and a.iname like concat('%', #{name}, '%')
            </if>
            <if test="type != null ">
                and a.itype = #{type}
            </if>
            <if test="classification != null ">
                and a.iclassification = #{classification}
            </if>
            <if test="scriptName != null  and scriptName != ''">
                and a.iscript_name like concat('%', #{scriptName}, '%')
            </if>
            <if test="scriptIds != null  and scriptIds != ''">
                and a.iscript_ids = #{scriptIds}
            </if>
            <if test="scriptOperatingUser != null  and scriptOperatingUser != ''">
                and a.iscript_operating_user = #{scriptOperatingUser}
            </if>
	               <if test="scriptType != null ">
                            and a.iscript_type = #{scriptType}
                        </if>
                        <if test="effect != null  and effect != ''">
                            and a.ieffect = #{effect}
                        </if>
                        <if test="usageScenario != null  and usageScenario != ''">
                            and a.iusage_scenario = #{usageScenario}
                        </if>
                        <if test="scriptEditing != null  and scriptEditing != ''">
                            and a.iscript_editing = #{scriptEditing}
                        </if>
                        <if test="estimateOperationalRisk != null ">
                            and a.iestimate_operational_risk = #{estimateOperationalRisk}
                        </if>
            <if test="status != null ">
                and a.istatus = #{status}
            </if>
            <if test="keyword != null  and keyword != ''">
                and a.ikeyword = #{keyword}
            </if>
            <if test="businessSystemId != null ">
                and a.ibusiness_system_id = #{businessSystemId}
            </if>
            <if test="businessSystemName != null  and businessSystemName != ''">
                and a.ibusiness_system_name like concat('%', #{businessSystemName}, '%')
            </if>
            <if test="matchIp != null  and matchIp != ''">
                and a.imatch_ip = #{matchIp}
            </if>
            <if test="firstDescribe != null  and firstDescribe != ''">
                and a.ifirst_describe = #{firstDescribe}
            </if>
            <if test="describe != null  and describe != ''">
                and a.idescribe = #{describe}
            </if>
            <if test="osType != null ">
                and a.ios_type = #{osType}
            </if>
            <if test="oneTypeId != null ">
                and a.ione_type_id = #{oneTypeId}
            </if>
            <if test="twoTypeId != null ">
                and a.itwo_type_id = #{twoTypeId}
            </if>
            <if test="threeTypeId != null ">
                and a.ithree_type_id = #{threeTypeId}
            </if>
            <if test="highRisk != null ">
                and a.ihigh_risk = #{highRisk}
            </if>
            <if test="dangerCmd != null  and dangerCmd != ''">
                and a.idanger_cmd = #{dangerCmd}
            </if>
            <if test="childIds != null  and childIds != ''">
                and a.ichild_ids like concat('%', #{childIds}, '%')
            </if>
            <if test="deliveryStatus != null ">
                and a.idelivery_status = #{deliveryStatus}
            </if>
            <if test="deliveryAuditorId != null ">
                and a.idelivery_auditor_id = #{deliveryAuditorId}
            </if>
            <if test="deliveryReturnCause != null  and deliveryReturnCause != ''">
                and a.idelivery_return_cause = #{deliveryReturnCause}
            </if>
            <if test="deliveryReceptionTime != null ">
                and a.idelivery_reception_time = #{deliveryReceptionTime}
            </if>
            <if test="updatorId != null ">
                and a.iupdator_id = #{updatorId}
            </if>
            <if test="updatorName != null  and updatorName != ''">
                and a.iupdator_name like concat('%', #{updatorName}, '%')
            </if>
            <if test="updateTime != null ">
                and a.iupdate_time = #{updateTime}
            </if>
            <if test="creatorId != null ">
                and a.icreator_id = #{creatorId}
            </if>
            <if test="creatorName != null  and creatorName != ''">
                and a.icreator_name like concat('%', #{creatorName}, '%')
            </if>
            <if test="createTime != null ">
                and a.icreate_time = #{createTime}
            </if>
            <if test="approvalState != null ">
                and a.iapproval_state = #{approvalState}
            </if>
            <!-- 新增权限级别条件 -->
            <if test="permissionLevel != null">
                <choose>
                    <when test="permissionLevel == 0">
                        and (a.istatus = 3 or a.idelivery_status = 3)
                    </when>
                    <when test="permissionLevel == 1">
                        and (a.istatus = 3 or a.idelivery_status = 3)
                    </when>
                    <when test="permissionLevel == 2">
                        and a.istatus = 3
                    </when>
                </choose>
            </if>
            and a.del_flag=0
        </where>
        group by a.iid ORDER BY a.iupdate_time DESC
    </select>
    <!-- 工具编辑查询列表 -->
    <select id="selectToolsInfoEditList" parameterType="com.ideal.tools.model.entity.ToolsInfoEditEntity" resultMap="ToolsInfoEditResult">
        <include refid="selectToolsInfoEdit"/>
        <where>
             <if test="likeCode != null  and likeCode != ''">
                and a.icode like concat('%', #{likeCode}, '%')
            </if>
            <if test="likeName != null  and likeName != ''">
                and a.iname like concat('%', #{likeName}, '%')
            </if>
            <if test="code != null  and code != ''">
                and a.icode = #{code}
            </if>
            <if test="name != null  and name != ''">
                and a.iname = #{name}
            </if>
            <if test="type != null ">
                and a.itype = #{type}
            </if>
            <if test="classification != null ">
                and a.iclassification = #{classification}
            </if>
            <if test="scriptName != null  and scriptName != ''">
                and a.iscript_name like concat('%', #{scriptName}, '%')
            </if>
            <if test="scriptIds != null  and scriptIds != ''">
                and a.iscript_ids = #{scriptIds}
            </if>
            <if test="scriptOperatingUser != null  and scriptOperatingUser != ''">
                and a.iscript_operating_user = #{scriptOperatingUser}
            </if>
            <if test="scriptType != null ">
                and a.iscript_type = #{scriptType}
            </if>
            <if test="effect != null  and effect != ''">
                and a.ieffect = #{effect}
            </if>
            <if test="usageScenario != null  and usageScenario != ''">
                and a.iusage_scenario = #{usageScenario}
            </if>
            <if test="scriptEditing != null  and scriptEditing != ''">
                and a.iscript_editing = #{scriptEditing}
            </if>
            <if test="estimateOperationalRisk != null ">
                and a.iestimate_operational_risk = #{estimateOperationalRisk}
            </if>
            <choose>
                <when test="status == null">
                    and a.istatus != 6
                </when>
                <otherwise>
                    and a.istatus = #{status}
                </otherwise>
            </choose>
            <if test="keyword != null  and keyword != ''">
                and a.ikeyword = #{keyword}
            </if>
            <if test="businessSystemId != null ">
                and a.ibusiness_system_id = #{businessSystemId}
            </if>
            <if test="businessSystemName != null  and businessSystemName != ''">
                and a.ibusiness_system_name like concat('%', #{businessSystemName}, '%')
            </if>
            <if test="matchIp != null  and matchIp != ''">
                and a.imatch_ip = #{matchIp}
            </if>
            <if test="firstDescribe != null  and firstDescribe != ''">
                and a.ifirst_describe = #{firstDescribe}
            </if>
            <if test="describe != null  and describe != ''">
                and a.idescribe = #{describe}
            </if>
            <if test="osType != null ">
                and a.ios_type = #{osType}
            </if>
            <if test="oneTypeId != null ">
                and a.ione_type_id = #{oneTypeId}
            </if>
            <if test="twoTypeId != null ">
                and a.itwo_type_id = #{twoTypeId}
            </if>
            <if test="threeTypeId != null ">
                and a.ithree_type_id = #{threeTypeId}
            </if>
            <if test="highRisk != null ">
                and a.ihigh_risk = #{highRisk}
            </if>
            <if test="dangerCmd != null  and dangerCmd != ''">
                and a.idanger_cmd = #{dangerCmd}
            </if>
            <if test="childIds != null  and childIds != ''">
                and a.ichild_ids = #{childIds}
            </if>
            <if test="deliveryStatus != null ">
                and a.idelivery_status = #{deliveryStatus}
            </if>
            <if test="deliveryAuditorId != null ">
                and a.idelivery_auditor_id = #{deliveryAuditorId}
            </if>
            <if test="deliveryReturnCause != null  and deliveryReturnCause != ''">
                and a.idelivery_return_cause = #{deliveryReturnCause}
            </if>
            <if test="deliveryReceptionTime != null ">
                and a.idelivery_reception_time = #{deliveryReceptionTime}
            </if>
            <if test="updatorId != null ">
                and a.iupdator_id = #{updatorId}
            </if>
            <if test="updatorName != null  and updatorName != ''">
                and a.iupdator_name like concat('%', #{updatorName}, '%')
            </if>
            <if test="updateTime != null ">
                and a.iupdate_time = #{updateTime}
            </if>
            <if test="creatorId != null ">
                and a.icreator_id = #{creatorId}
            </if>
            <if test="creatorName != null  and creatorName != ''">
                and a.icreator_name like concat('%', #{creatorName}, '%')
            </if>
            <if test="createTime != null ">
                and a.icreate_time = #{createTime}
            </if>
            <if test="approvalState != null ">
                <choose>
                    <when test="approvalState == 0">
                        and e.iapproval_state = #{approvalState} OR e.iapproval_state IS NULL
                    </when>
                    <when test="approvalState == 1">
                        and e.iapproval_state = #{approvalState} and a.istatus != 1
                    </when>
                    <when test="permissionLevel != 0">
                        and e.iapproval_state = #{approvalState}
                    </when>
                </choose>
            </if>
            <!-- 新增权限级别条件 -->
            <if test="permissionLevel != null">
                <choose>
                    <when test="permissionLevel == 0">
                        and (a.istatus = 3 or a.idelivery_status = 3)
                    </when>
                    <when test="permissionLevel == 1">
                        and (a.istatus = 3 or a.idelivery_status = 3)
                    </when>
                    <when test="permissionLevel == 2">
                        and a.istatus = 3
                    </when>
                </choose>
            </if>
            <if test="deliveryReceptionTimeStart != null or deliveryReceptionTimeEnd != null ">
                and idelivery_reception_time <![CDATA[ >= ]]> #{deliveryReceptionTimeStart} and idelivery_reception_time <![CDATA[ <= ]]> #{deliveryReceptionTimeEnd}
            </if>
             <if test="businessSystemIds != null">
                 and a.ibusiness_system_id in
                 <foreach collection="businessSystemIds" item="businessSystemIdIt" open="(" close=")" separator=",">
                     #{businessSystemIdIt}
                 </foreach>
             </if>
            <!-- 筛选审批表最近数据 -->
            and a.del_flag = 0
        </where>
        ORDER BY a.iupdate_time DESC

    </select>

    <select id="selectToolsInfoById" parameterType="Long"
            resultMap="ToolsInfoResult">
            <include refid="selectToolsInfo"/>
            where iid = #{id}
    </select>
    <select id="selectToolsEditInfoById" parameterType="Long"
            resultMap="ToolsInfoEditResult">
        <include refid="selectToolsEditInfo"/>
        where a.iid = #{id}
    </select>
    <select id="selectToolsInfoCategoryListByIds" parameterType="java.util.Map" resultMap="ToolsInfoCategoryResult">
        <include refid="selectToolsInfoCategory"/>
        <where>
            <if test="entity != null">
            <if test="entity.code != null  and entity.code != ''">
                and a.icode = #{entity.code}
            </if>
            <if test="entity.name != null  and entity.name != ''">
                and a.iname like concat('%', #{entity.name}, '%')
            </if>
            <if test="entity.type != null ">
                and a.itype = #{entity.type}
            </if>
            <if test="entity.businessSystemId != null ">
                and a.ibusiness_system_id = #{entity.businessSystemId}
            </if>
            <if test="entity.businessSystemName != null  and entity.businessSystemName != ''">
                and a.ibusiness_system_name like concat('%', #{entity.businessSystemName}, '%')
            </if>
            <if test="entity.oneTypeId != null ">
                and a.ione_type_id = #{entity.oneTypeId}
            </if>
            <if test="entity.twoTypeId != null ">
                and a.itwo_type_id = #{entity.twoTypeId}
            </if>
            <if test="entity.threeTypeId != null ">
                and a.ithree_type_id = #{entity.threeTypeId}
            </if>
            <if test="entity.highRisk != null ">
                and a.ihigh_risk = #{entity.highRisk}
            </if><if test="entity.scriptName != null  and entity.scriptName != ''">
                and a.iscript_name like concat('%', #{entity.scriptName}, '%')
            </if>
            <if test="entity.scriptIds != null  and entity.scriptIds != ''">
                and a.iscript_ids = #{entity.scriptIds}
            </if>
            </if>
            <!-- 处理 ID 列表条件 -->
            <if test="ids != null and ids.size() > 0">
                AND a.iid IN
                <foreach collection="ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            and a.del_flag=0
        </where>
        group by a.iid ORDER BY a.iupdate_time DESC
    </select>

    <insert id="insertToolsInfo" parameterType="com.ideal.tools.model.entity.ToolsInfoEntity">
        insert into ieai_tb_tools_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null"> iid,
                    </if>
                    <if test="code != null">icode,
                    </if>
                    <if test="name != null">iname,
                    </if>
                    <if test="type != null">itype,
                    </if>
                    <if test="classification != null">iclassification,
                    </if>
                    <if test="scriptName != null">iscript_name,
                    </if>
                    <if test="scriptIds != null">iscript_ids,
                    </if>
                    <if test="scriptOperatingUser != null">iscript_operating_user,
                    </if>
                    <if test="scriptType != null">iscript_type,
                    </if>
                    <if test="effect != null">ieffect,
                    </if>
                    <if test="usageScenario != null">iusage_scenario,
                    </if>
                    <if test="scriptEditing != null">iscript_editing,
                    </if>
                    <if test="estimateOperationalRisk != null">iestimate_operational_risk,
                    </if>
                    <if test="status != null">istatus,
                    </if>
                    <if test="keyword != null">ikeyword,
                    </if>
                    <if test="businessSystemId != null">ibusiness_system_id,
                    </if>
                    <if test="businessSystemName != null">ibusiness_system_name,
                    </if>
                    <if test="businessSystemCode != null">ibusiness_system_code,
                    </if>
                    <if test="matchIp != null">imatch_ip,
                    </if>
                    <if test="firstDescribe != null">ifirst_describe,
                    </if>
                    <if test="describe != null">idescribe,
                    </if>
                    <if test="osType != null">ios_type,
                    </if>
                    <if test="oneTypeId != null">ione_type_id,
                    </if>
                    <if test="twoTypeId != null">itwo_type_id,
                    </if>
                    <if test="threeTypeId != null">ithree_type_id,
                    </if>
                    <if test="highRisk != null">ihigh_risk,
                    </if>
                    <if test="childIds != null">ichild_ids,
                    </if>
                    <if test="deliveryStatus != null">idelivery_status,
                    </if>
                    <if test="deliveryAuditorId != null">idelivery_auditor_id,
                    </if>
                    <if test="deliveryReturnCause != null">idelivery_return_cause,
                    </if>
                    <if test="deliveryReceptionTime != null">idelivery_reception_time,
                    </if>
                    <if test="updatorId != null">iupdator_id,
                    </if>
                    <if test="updatorName != null">iupdator_name,
                    </if>
                    <if test="updateTime != null">iupdate_time,
                    </if>
                    <if test="creatorId != null">icreator_id,
                    </if>
                    <if test="creatorName != null">icreator_name,
                    </if>
                    <if test="createTime != null">icreate_time,
                    </if>
                    <if test="dangerCmd != null">idanger_cmd,
                    </if>
                    <if test="delFlag != null">del_flag,
                    </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">#{id},
                    </if>
                    <if test="code != null">#{code},
                    </if>
                    <if test="name != null">#{name},
                    </if>
                    <if test="type != null">#{type},
                    </if>
                    <if test="classification != null">#{classification},
                    </if>
                    <if test="scriptName != null">#{scriptName},
                    </if>
                    <if test="scriptIds != null">#{scriptIds},
                    </if>
                    <if test="scriptOperatingUser != null">#{scriptOperatingUser},
                    </if>
                    <if test="scriptType != null">#{scriptType},
                    </if>
                    <if test="effect != null">#{effect},
                    </if>
                    <if test="usageScenario != null">#{usageScenario},
                    </if>
                    <if test="scriptEditing != null">#{scriptEditing},
                    </if>
                    <if test="estimateOperationalRisk != null">#{estimateOperationalRisk},
                    </if>
                    <if test="status != null">#{status},
                    </if>
                    <if test="keyword != null">#{keyword},
                    </if>
                    <if test="businessSystemId != null">#{businessSystemId},
                    </if>
                    <if test="businessSystemName != null">#{businessSystemName},
                    </if>
                    <if test="businessSystemCode != null">#{businessSystemCode},
                    </if>
                    <if test="matchIp != null">#{matchIp},
                    </if>
                    <if test="firstDescribe != null">#{firstDescribe},
                    </if>
                    <if test="describe != null">#{describe},
                    </if>
                    <if test="osType != null">#{osType},
                    </if>
                    <if test="oneTypeId != null">#{oneTypeId},
                    </if>
                    <if test="twoTypeId != null">#{twoTypeId},
                    </if>
                    <if test="threeTypeId != null">#{threeTypeId},
                    </if>
                    <if test="highRisk != null">#{highRisk},
                    </if>
                    <if test="childIds != null">#{childIds},
                    </if>
                    <if test="deliveryStatus != null">#{deliveryStatus},
                    </if>
                    <if test="deliveryAuditorId != null">#{deliveryAuditorId},
                    </if>
                    <if test="deliveryReturnCause != null">#{deliveryReturnCause},
                    </if>
                    <if test="deliveryReceptionTime != null">#{deliveryReceptionTime},
                    </if>
                    <if test="updatorId != null">#{updatorId},
                    </if>
                    <if test="updatorName != null">#{updatorName},
                    </if>
                    <if test="updateTime != null">#{updateTime},
                    </if>
                    <if test="creatorId != null">#{creatorId},
                    </if>
                    <if test="creatorName != null">#{creatorName},
                    </if>
                    <if test="createTime != null">#{createTime},
                    </if>
                    <if test="dangerCmd != null">#{dangerCmd},
                    </if>
                    <if test="delFlag != null">#{delFlag},
                    </if>
        </trim>
    </insert>

    <update id="updateToolsInfo" parameterType="com.ideal.tools.model.entity.ToolsInfoEntity">
        update ieai_tb_tools_info
        <trim prefix="SET" suffixOverrides=",">
                    <if test="code != null">icode =
                        #{code},
                    </if>
                    <if test="name != null">iname =
                        #{name},
                    </if>
                    <if test="type != null">itype =
                        #{type},
                    </if>
                    <if test="classification != null">iclassification =
                        #{classification},
                    </if>
                    <if test="scriptName != null">iscript_name =
                        #{scriptName},
                    </if>
                    <if test="scriptIds != null">iscript_ids =
                        #{scriptIds},
                    </if>
                    <if test="scriptOperatingUser != null">iscript_operating_user =
                        #{scriptOperatingUser},
                    </if>
                    <if test="scriptType != null">iscript_type =
                        #{scriptType},
                    </if>
                    <if test="effect != null">ieffect =
                        #{effect},
                    </if>
                    <if test="usageScenario != null">iusage_scenario =
                        #{usageScenario},
                    </if>
                    <if test="scriptEditing != null">iscript_editing =
                        #{scriptEditing},
                    </if>
                    <if test="estimateOperationalRisk != null">iestimate_operational_risk =
                        #{estimateOperationalRisk},
                    </if>
                    <if test="status != null">istatus =
                        #{status},
                    </if>
                    <if test="keyword != null">ikeyword =
                        #{keyword},
                    </if>
                    <if test="businessSystemId != null">ibusiness_system_id =
                        #{businessSystemId},
                    </if>
                    <if test="businessSystemName != null">ibusiness_system_name =
                        #{businessSystemName},
                    </if>
                    <if test="matchIp != null">imatch_ip =
                        #{matchIp},
                    </if>
                    <if test="firstDescribe != null">ifirst_describe =
                        #{firstDescribe},
                    </if>
                    <if test="describe != null">idescribe =
                        #{describe},
                    </if>
                    <if test="osType != null">ios_type =
                        #{osType},
                    </if>
                    <if test="oneTypeId != null">ione_type_id =
                        #{oneTypeId},
                    </if>
                    <if test="twoTypeId != null">itwo_type_id =
                        #{twoTypeId},
                    </if>
                    <if test="threeTypeId != null">ithree_type_id =
                        #{threeTypeId},
                    </if>
                    <if test="highRisk != null">ihigh_risk =
                        #{highRisk},
                    </if>
                    <if test="childIds != null">ichild_ids =
                        #{childIds},
                    </if>
                    <if test="deliveryStatus != null">idelivery_status =
                        #{deliveryStatus},
                    </if>
                    <if test="deliveryAuditorId != null">idelivery_auditor_id =
                        #{deliveryAuditorId},
                    </if>
                    <if test="deliveryReturnCause != null">idelivery_return_cause =
                        #{deliveryReturnCause},
                    </if>
                    <if test="deliveryReceptionTime != null">idelivery_reception_time =
                        ${@com.ideal.common.util.DbUtils@getCurrentTime()},
                    </if>
                    <if test="updatorId != null">iupdator_id =
                        #{updatorId},
                    </if>
                    <if test="updatorName != null">iupdator_name =
                        #{updatorName},
                    </if>
                    iupdate_time =
                        ${@com.ideal.common.util.DbUtils@getCurrentTime()},

                    <if test="creatorId != null">icreator_id =
                        #{creatorId},
                    </if>
                    <if test="creatorName != null">icreator_name =
                        #{creatorName},
                    </if>
                    <if test="createTime != null">icreate_time =
                        #{createTime},
                    </if>
                    <if test="dangerCmd != null">idanger_cmd =
                        #{dangerCmd},
                    </if>
                    <if test="delFlag != null">del_flag =
                        #{delFlag},
                    </if>
        </trim>
        where iid = #{id}
    </update>

    <delete id="deleteToolsInfoById" parameterType="Long">
        delete
        from ieai_tb_tools_info where iid = #{id}
    </delete>

    <delete id="deleteToolsInfoByIds" parameterType="String">
        delete from ieai_tb_tools_info where iid in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <select id="selectToolsInfoListByIds" parameterType="String" resultMap="ToolsInfoResult">
        <include refid="selectToolsInfo"/>
        where iid in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="selectToolsInfoListByIdsAndName" resultMap="ToolsInfoForAlarmResult">
        <include refid="selectToolsInfoForAlarm"/>
        <where>
            <if test="name !=null and name !=''">
                and a.iname like concat('%', #{name}, '%')
            </if>
            <if test="ids != null and ids.length > 0">
                and a.iid in
                <foreach item="id" collection="ids" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>
    <select id="selectToolsInfoCategoryById" parameterType="Long" resultMap="ToolsInfoCategoryResult">
        <include refid="selectToolsInfoAll"/>
        where a.iid = #{id}
    </select>

    <select id="selectToolsInfoListByBusinessSystemIds" parameterType="Long" resultMap="ToolsInfoForKeywordResult">
        <include refid="selectToolsInfoForKeyword"/>
        where ibusiness_system_id in
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <!--逻辑删除-->
    <update id="deleteByLogic" parameterType="String">
        UPDATE ieai_tb_tools_info SET
        del_flag = 1
        WHERE iid in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
        and istatus !=2
    </update>
    <!--查询分类是否存在-->
    <select id="selectToolsInfoByCategoryIds" parameterType="long[]" resultMap="ToolsInfoResult">
        <include refid="selectToolsInfo"/>
        WHERE del_flag=0 AND (
        ione_type_id IN
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
        OR itwo_type_id IN
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
        OR ithree_type_id IN
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
        )
    </select>

    <select id="selectAuditorIdByToolId" parameterType="Long" resultType="java.lang.Long">
      select ad.iauditor_id from ieai_tb_tools_info a
        left join ieai_tb_audit ad ON ad.ibusiness_id = a.iid
          and ad.itype in (1)  and ad.iapply_time = (SELECT MAX(iapply_time) FROM ieai_tb_audit WHERE ibusiness_id = a.iid and itype in (1))
       where a.iid = #{toolId}
    </select>

    <select id="getToolsChildListIdRefactorDownLine" parameterType="java.lang.Long" resultType="java.lang.String">
        select icode from ieai_tb_tools_info where ichild_ids like concat('%', #{toolId}, '%') and istatus != 6
    </select>

    <!--批量下线-->
    <update id="downToolsByIds" parameterType="String">
        UPDATE ieai_tb_tools_info SET
        istatus = 6
        WHERE iid in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!--批量物理删除-->
    <delete id="batchDelete">
        DELETE FROM ieai_tb_tools_info
        WHERE iid in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
        and istatus !=2
    </delete>

    <select id="selectToolsEditInfoByIds" resultMap="ToolsInfoEditResult">
        <include refid="selectToolsEditInfo"/>
        <where>
            <if test="ids != null and ids.size() > 0">
                and a.iid in
                <foreach item="id" collection="ids" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>
    <select id="selectToolsIdByType" resultType="java.lang.Long">
        SELECT iid FROM ieai_tb_tools_info WHERE 1=1
    </select>
    <select id="selectToolsInfoIsHaveDescTool" resultMap="ToolsInfoResult">
        <include refid="selectToolsInfo"/>
        <where>
            <if test="ids != null and ids.size() > 0">
                and iid in
                <foreach item="id" collection="ids" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectToolsInfoByCodes" parameterType="com.ideal.tools.model.entity.ToolsInfoEntity" resultMap="ToolsInfoResult">
        <include refid="selectToolsInfo"/>
        <where>
            <if test="codes != null and codes.size() > 0">
                and icode in
                <foreach item="code" collection="codes" open="(" separator="," close=")">
                    #{code}
                </foreach>
            </if>
        </where>
    </select>
    <select id="selectToolsChildsByIds" parameterType="String" resultMap="ToolsInfoResult">
        <include refid="selectToolsInfo"/>
        <where>
            <foreach item="id" collection="ids" open="" separator="OR" close="">
                ichild_ids like concat('%', #{id}, '%')
            </foreach>
        </where>
    </select>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.ideal</groupId>
        <artifactId>script-tools-biz</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>tools-biz</artifactId>
    <packaging>jar</packaging>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.ideal</groupId>
            <artifactId>tools-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ideal</groupId>
            <artifactId>engine-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ideal</groupId>
            <artifactId>engine-monitor</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-httpclient</groupId>
            <artifactId>commons-httpclient</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ideal</groupId>
            <artifactId>script-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ideal</groupId>
            <artifactId>studio-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ideal</groupId>
            <artifactId>xxl-job-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-core</artifactId>
            <version>5.8.27</version>
        </dependency>
    </dependencies>
</project>
# API接口文档（按路由菜单路径维度整理）

## 我的脚本(myScript)

| 功能名称 | 后端URL | 共用接口的菜单路由 |
| ------- | ------- | ------- |
| 获取脚本列表 | `/myScript/listMyScript` | - |
| 获取双重检查脚本标志 | `/myScript/getDoubleCheckScriptFlag` | double-check |
| 删除脚本 | `/myScript/deleteMyScript` | - |
| 获取脚本详情 | `/myScript/getScriptDetail` | task-apply, task-apply-duty, template-task, double-check, double-check |
| 保存脚本 | `/myScript/saveMyScript` | - |
| 更新脚本 | `/myScript/updateMyScript` | - |
| 获取删除标志 | `/myScript/getDeleteFlag` | - |
| 发布脚本 | `/myScript/publishScript` | - |
| 获取脚本编辑参数管理器 | `/manager/getParameterManagerForScriptEdit` | - |
| 获取脚本版本 | `/myScript/getScriptServiceVersionListForAllScript` | - |
| 有版本回退 | `/myScript/hasVersionRollBack` | - |
| 无版本回退 | `/myScript/noVersionRollBack` | - |
| 下载脚本 | `/myScript/scriptDownload` | - |
| 导出我的脚本介质 | `/myScript/myScriptExportReleaseMedia` | - |
| 获取用户列表 | `/myScript/getUserInfoList` | - |
| 创建者转移 | `/myScript/creatorTransfer` | - |
| 忽略脚本修订 | `/myScript/ignoreScriptRevision` | - |
| 获取执行器验证列表 | `/myScript/getExecutorValidationList` | - |
| 根据版本ID获取任务计数 | `/version/getTaskCountByVersionId` | - |
| 获取SQL显示标志 | `/myScript/getSqlShowFlag` | - |
| 上传附件 | `/attachment/uploadAttachment` | - |
| 下载附件 | `/attachment/downloadAttachment` | - |
| 获取附件信息列表 | `/attachment/getAttachmentInfoList` | - |
| 发送脚本 | `/script/issuerecord/sendScriptToAgents` | - |
| 获取发送记录 | `/script/issuerecord/list` | - |
| 根据UUID获取版本信息 | `/version/getInfoByVersionUuid` | - |
| 根据IDs获取版本ID | `/version/getVersionIdByIds` | - |
| 根据IDs获取版本信息 | `/version/getInfoVersionInfoList` | - |
| 导出介质 | `/releaseMedia/exportReleaseMedia` | task-apply |
| 获取脚本模板详情 | `/taskTemplate/getScriptTemplateDetail` | template-task |
| 删除任务模板 | `/taskTemplate/deleteTemplateTask` | template-task |
| 获取任务模板代理列表 | `/taskTemplate/queryTemplateAgentInfo` | template-task |
| 获取任务模板附件 | `/taskTemplate/getTaskTemplateAttachment` | template-task |
| 获取任务模板组列表 | `/taskTemplate/queryTemplateGroupInfo` | template-task |
| 创建克隆任务 | `/taskTemplate/createCloneTask` | template-task |
| 上传附件模板 | `/taskTemplate/uploadAttachmentTemplate` | template-task |
| 下载附件模板 | `/taskTemplate/downloadAttachmentTemplate` | template-task |
| 获取平台列表 | `/platform/platformList` | task-apply, task-apply-duty |
| 获取变量列表 | `/variablePublish/listVariablePForEdit` | - |
| 获取函数列表 | `/functionpublish/listFunctionpublishForScriptEdit` | - |
| 获取分类列表 | `/category/listMultiCategory` | - |
| 获取一级分类列表 | `/category/listFirstCategory` | - |
| 获取二级分类列表 | `/category/listNextCategory` | - |
| 保存共享脚本 | `/scriptShare/saveScriptVersionShare` | - |
| 删除共享脚本 | `/scriptShare/deleteScriptVersionShare` | - |
| 获取共享用户 | `/scriptShare/getShareUser` | - |
| 查询共享脚本数据 | `/scriptShare/selectShareScriptData` | - |
| 获取非共享组织管理树 | `/category/selectNotShareOrgManagementTree` | - |
| 获取输出消息 | `/runtime/getOutPutMessage` | execution-task |
| 获取实时输出消息 | `/taskExecute/getRealTimeOutPutMessage` | execution-task |
| 任务下运行的agent列表 | `/runtime/listTaskRuntime` | execution-task |
| 脚本任务启动 | `/taskExecute/scriptTaskStart` | execution-task |
| 根据审核关系ID获取信息ID | `/relation/selectInfoIdAndSrcScriptUuidByAuditRelationId` | double-check |
| 获取分类完整路径 | `/category/getCategoryFullPath` | category-service |
| 深度优先搜索获取分类列表 | `/category/selectCategoryListDFS` | category-service, key-commands, script-statement, time-task-manager, scheduled-task, all-script |
| 获取用户权限信息列表 | `/taskApply/queryUserInfoListByPermissionCode` | task-apply, task-apply-duty, service-rollout, double-check, double-check, history-task-statement |
| 更新使用状态 | `/taskAuthority/updateUseState` | - |
| 批量更新使用状态 | `/taskAuthority/batchUpdateUseState` | - |
| 检查是否存在运行任务 | `/taskAuthority/checkExistRunTask` | - |
| 删除脚本 | `/taskAuthority/deleteScript` | - |

## 参数管理(param-manage)

| 功能名称 | 后端URL | 共用接口的菜单路由 |
| ------- | ------- | ------- |
| 获取参数管理列表 | `/manager/listParameterManager` | - |
| 删除参数管理 | `/manager/removeParameterManager` | - |
| 保存参数管理 | `/manager/saveParameterManager` | - |
| 更新参数管理 | `/manager/updateParameterManager` | - |

## 类别维护(category-service)

| 功能名称 | 后端URL | 共用接口的菜单路由 |
| ------- | ------- | ------- |
| 获取分类列表 | `/category/listCategory` | - |
| 删除分类 | `/category/removelCategory` | - |
| 保存分类 | `/category/saveCategory` | - |
| 更新分类 | `/category/updateCategory` | - |
| 获取分类列表(无分页) | `/category/getCategoryList` | task-apply |
| 将分类分配给组织 | `/category/assignCategoryToOrg` | - |
| 获取分类组织关系 | `/category/getCategoryOrgRelations` | - |
| 将分类分配给用户 | `/category/assignCategoryToUser` | - |
| 获取分类用户关系 | `/category/getCategoryUserRelations` | - |
| 获取用户信息列表 | `/category/queryPermissionUserInfoList` | - |
| 获取用户信息分页 | `/category/queryPermissionUserInfoPage` | - |
| 选择组织管理树 | `/category/selectOrgManagementTree` | - |
| 选择非共享组织管理树 | `/category/selectNotShareOrgManagementTree` | - |
| 深度优先搜索获取分类列表 | `/category/selectCategoryListDFS` | myScript, key-commands, script-statement, time-task-manager, scheduled-task, all-script |
| 获取分类完整路径 | `/category/getCategoryFullPath` | myScript |
| 获取共享用户 | `/scriptShare/getShareUser` | - |
| 选择共享脚本数据 | `/scriptShare/selectShareScriptData` | - |
| 删除共享 | `/scriptShare/deleteScriptVersionShare` | - |

## 任务申请(task-apply)

| 功能名称 | 后端URL | 共用接口的菜单路由 |
| ------- | ------- | ------- |
| 获取任务申请列表 | `/taskApply/listTaskApply` | task-apply-duty |
| 获取任务模板列表 | `/taskTemplate/listCloneTask` | - |
| 获取代理信息 | `/groups/queryAgentInfoGroupRole` | template-task |
| 获取代理资源组 | `/groups/queryAgentGroupPageList` | template-task |
| 获取代理资源详情 | `/groups/queryAgentPageListByGroupId` | template-task |
| 脚本执行审核 | `/taskApply/scriptExecAuditing/` | double-check |
| 执行审核模板任务 | `/taskTemplate/execAuditTemplateTask/` | - |
| 保存审核模板任务 | `/taskTemplate/saveAuditTemplateTask/` | - |
| 获取审核员自身信息 | `/taskApply/getAuditorUserSelf` | - |
| 获取任务申请执行次数和成功率 | `/exectimeandsuccessrate/listTaskExecNumAndSuccessRate` | - |
| 获取任务执行次数和成功率 | `/exectime/getTotalAndSuccessRate` | - |
| 查询权限用户信息列表 | `/taskApply/queryPermissionUserInfoList` | - |
| 查询部门用户信息列表 | `/taskApply/queryDepartmentUserInfoList` | double-check |
| 导入服务器Excel | `/taskApply/importServerExcel` | - |
| 下载导入模板 | `/taskApply/downloadImportTemplate` | - |
| 获取来源 | `/taskApply/getSource` | - |
| 删除附件 | `/taskApply/deleteAttachment` | - |
| 重新审核 | `/taskApply/reAudit` | double-check |
| 获取审计详情 | `/taskApply/getAuditDetail` | execution-task, double-check |
| 通过审核关系ID获取脚本信息 | `/taskApply/getScriptInfoByAuditRelationId` | double-check |
| 获取平台列表 | `/platform/platformList` | myScript, task-apply-duty |
| 获取分类列表 | `/category/getCategoryList` | category-service |
| 导出介质 | `/releaseMedia/exportReleaseMedia` | myScript |
| 获取用户权限信息列表 | `/taskApply/queryUserInfoListByPermissionCode` | myScript, task-apply-duty, task-apply, service-rollout, double-check, double-check, history-task-statement |
| 保存常用任务 | `/taskTemplate/createCloneTaskFromTaskApply/` | task-apply-duty |
| 删除模板附件 | `/taskTemplate/deleteAttachmentTemplate` | task-apply-duty, template-task |

## 值班任务申请(task-apply-duty)

| 功能名称 | 后端URL | 共用接口的菜单路由 |
| ------- | ------- | ------- |
| 获取值班任务申请列表 | `/taskApply/listTaskApply` | task-apply |
| 获取值班人员信息 | `/dutyInfo/getDutyUserInfo` | - |
| 申请值班任务 | `/taskApply/applyDutyTask` | - |
| 获取值班列表 | `/dutyInfo/listDuty` | - |
| 获取值班详情 | `/dutyInfo/getDutyDetail` | - |
| 保存常用任务 | `/taskTemplate/createCloneTaskFromTaskApply/`  | task-apply                                                   |
| 删除模板附件 | `/taskTemplate/deleteAttachmentTemplate` | task-apply, template-task |
| 获取脚本信息 | `/myScript/getScriptDetail` | myScript, template-task, double-check, double-check |
| 获取平台列表 | `/platform/platformList` | myScript, task-apply |
| 获取用户权限信息列表 | `/taskApply/queryUserInfoListByPermissionCode` | myScript, task-apply, service-rollout, double-check, double-check, history-task-statement |

## 常用任务(template-task)

| 功能名称 | 后端URL | 共用接口的菜单路由 |
| ------- | ------- | ------- |
| 获取模板任务列表 | `/taskTemplate/listTemplateTasks` | - |
| 获取任务模板列表(克隆任务) | `/taskTemplate/listCloneTask` | - |
| 创建模板任务 | `/taskTemplate/saveTemplateTask` | - |
| 删除模板任务 | `/taskTemplate/deleteTemplateTask` | - |
| 获取模板任务组信息 | `/taskTemplate/queryTemplateGroupInfo` | - |
| 获取模板任务代理信息 | `/taskTemplate/queryTemplateAgentInfo` | - |
| 获取模板任务附件 | `/taskTemplate/getTaskTemplateAttachment` | - |
| 创建克隆任务 | `/taskTemplate/createCloneTask` | - |
| 获取脚本模板详情 | `/taskTemplate/getScriptTemplateDetail` | - |
| 上传附件模板 | `/taskTemplate/uploadAttachmentTemplate` | - |
| 下载附件模板 | `/taskTemplate/downloadAttachmentTemplate` | - |
| 执行审核模板任务 | `/taskTemplate/execAuditTemplateTask/` | - |
| 保存审核模板任务 | `/taskTemplate/saveAuditTemplateTask/` | - |
| 获取脚本详情 | `/myScript/getScriptDetail` | myScript, task-apply-duty, double-check, double-check |
| 获取代理信息 | `/groups/queryAgentInfoGroupRole` | task-apply |
| 获取代理资源组 | `/groups/queryAgentGroupPageList` | task-apply |
| 获取代理资源详情 | `/groups/queryAgentPageListByGroupId` | task-apply |
| 删除模板附件 | `/taskTemplate/deleteAttachmentTemplate` | task-apply, task-apply-duty |
| 获取审核员自身信息 | `/taskApply/getAuditorUserSelf` | task-apply |
| 获取用户权限信息列表 | `/taskApply/queryUserInfoListByPermissionCode` | myScript, task-apply, task-apply-duty, service-rollout, double-check, double-check, history-task-statement |
| 获取分类列表 | `/category/getCategoryList` | task-apply, category-service |
| 任务下运行的agent列表 | `/runtime/listTaskRuntime` | execution-task |


## 关键命令(key-commands)

| 功能名称 | 后端URL | 共用接口的菜单路由 |
| ------- | ------- | ------- |
| 获取关键命令列表 | `/cmd/listDangerCmd` | - |
| 删除关键命令 | `/cmd/removelDangerCmd` | - |
| 保存危险命令 | `/cmd/saveDangerCmd` | - |
| 更新危险命令 | `/cmd/updateDangerCmd` | - |
| 获取标签列表 | `/myScript/getLabelList` | - |
| 深度优先搜索获取分类列表 | `/category/selectCategoryListDFS` | myScript, category-service, script-statement, time-task-manager, scheduled-task, all-script |

## 参数验证(param-validation)

| 功能名称 | 后端URL | 共用接口的菜单路由 |
| ------- | ------- | ------- |
| 获取参数列表 | `/check/listParameterCheck` | - |
| 删除参数 | `/check/removeParameterCheck` | - |
| 保存参数 | `/check/saveParameterCheck` | - |
| 更新参数 | `/check/updateParameterCheck` | - |

## 任务监控(execution-task)

| 功能名称 | 后端URL | 共用接口的菜单路由 |
| ------- | ------- | ------- |
| 获取待执行任务列表 | `/taskExecute/listTaskReadyToExecute` | - |
| 获取脚本任务执行的agent信息 | `/info/getTaskBindAgentInfo` | - |
| 启动脚本任务 | `/taskExecute/scriptTaskStart` | myScript |
| 检查脚本任务前条件 | `/taskExecute/checkBefore` | - |
| 获取执行中任务列表 | `/taskExecute/listRunningScriptTasks` | - |
| 任务下运行的agent列表 | `/runtime/listTaskRuntime` | myScript, task-apply, template-task |
| 重试脚本服务Shell | `/taskExecute/retryScriptServiceShell` | - |
| 停止脚本任务 | `/taskExecute/stopTask` | - |
| 批量停止脚本任务 | `/taskExecute/batchStopScriptTask` | - |
| 取消脚本任务 | `/taskExecute/cancelTask` | - |
| 获取输出消息 | `/runtime/getOutPutMessage` | myScript |
| 获取实时输出消息 | `/taskExecute/getRealTimeOutPutMessage` | myScript |
| 脚本Shell杀死 | `/taskExecute/scriptShellKill` | - |
| 跳过脚本Shell | `/taskExecute/skipScriptShell` | - |
| 批量重试脚本服务Shell | `/taskExecute/batchRetryScriptServiceShell` | - |
| 获取执行历史任务列表 | `/taskExecute/listCompleteScriptTasks` | - |
| 获取任务审核详情 | `/taskApply/getAuditDetail` | task-apply, double-check |
| 导出执行历史任务Excel | `/taskExecute/exportAgentHisExcel` | - |
| 获取所有代理信息 | `/info/getTaskAllAgentInfo` | - |
| 获取任务组列表 | `/groups/listTaskGroups` | - |
| 脚本测试执行 | `/taskExecute/scriptTestExecution` | double-check |
| 根据UUID获取参数 | `/parameter/getParameterByUuid` | - |
| 根据任务ID获取参数 | `/parameter/getParameterByTaskId` | - |
| 深度优先搜索获取分类列表 | `/category/selectCategoryListDFS` | myScript, category-service, key-commands, script-statement, time-task-manager, scheduled-task, all-script |

## 服务投产(service-rollout)

| 功能名称 | 后端URL | 共用接口的菜单路由 |
| ------- | ------- | ------- |
| 获取服务发布列表 | `/script/product/list` | - |
| 获取用户权限信息列表 | `/taskApply/queryUserInfoListByPermissionCode` | myScript, task-apply, task-apply-duty, double-check, double-check, history-task-statement |
| 导入发布介质 | `/releaseMedia/importReleaseMedia` | - |

## 双人复核(double-check)

| 功能名称 | 后端URL | 共用接口的菜单路由 |
| ------- | ------- | ------- |
| 获取双重检查脚本标志 | `/myScript/getDoubleCheckScriptFlag` | myScript |
| 获取双重检查脚本详情 | `/myScript/getScriptDetail` | myScript, task-apply-duty, template-task, double-check |
| 根据审核关系ID获取信息ID | `/relation/selectInfoIdAndSrcScriptUuidByAuditRelationId` | myScript |
| 保存脚本 | `/myScript/saveMyScript` | myScript |
| 更新脚本 | `/myScript/updateMyScript` | myScript |
| 发布脚本 | `/myScript/publishScript` | myScript |
| 获取删除标志 | `/myScript/getDeleteFlag` | myScript |
| 获取用户权限信息列表 | `/taskApply/queryUserInfoListByPermissionCode` | myScript, task-apply, task-apply-duty, service-rollout, double-check, history-task-statement |
| 根据版本IDs获取版本ID | `/version/getVersionIdByIds` | myScript |
| 脚本测试执行 | `/taskExecute/scriptTestExecution` | execution-task |
| 获取双重检查列表 | `/approval/listDoubleCheck` | - |
| 获取双重检查跟踪 | `/approval/listDoubleCheckTrack` | - |
| 审批双重检查 | `/approval/approvalDoubleCheck` | - |
| 获取任务申请审计详情 | `/taskApply/getAuditDetail` | task-apply, execution-task |
| 任务申请脚本执行审计 | `/taskApply/scriptExecAuditing/` | task-apply |
| 任务申请重新审计 | `/taskApply/reAudit` | task-apply |
| 通过审计关系ID获取任务信息 | `/taskApply/getScriptInfoByAuditRelationId` | task-apply |
| 获取任务申请详情 | `/myScript/getScriptDetail` | myScript, task-apply-duty, template-task, double-check |
| 获取来源 | `/taskApply/getSource` | task-apply |
| 查询用户权限信息列表 | `/taskApply/queryPermissionUserInfoList` | myScript, task-apply, task-apply-duty, service-rollout, double-check, history-task-statement |
| 查询部门用户信息列表 | `/taskApply/queryDepartmentUserInfoList` | task-apply |



## 定时周期维护(time-task-manager)

| 功能名称 | 后端URL | 共用接口的菜单路由 |
| ------- | ------- | ------- |
| 获取定时任务列表 | `/taskExecute/listTimeTasks` | scheduled-task |
| 定时任务开关 | `/taskExecute/timeTaskSwitch` | scheduled-task |
| 定时任务杀死 | `/taskExecute/timeTaskKill` | scheduled-task |
| 更新定时任务CRON | `/taskExecute/updateTimeTaskCron` | scheduled-task |
| 导出定时任务 | `/taskExecute/export` | scheduled-task |
| 深度优先搜索获取分类列表 | `/category/selectCategoryListDFS` | myScript, category-service, key-commands, script-statement, scheduled-task, all-script |

## 长期未修改脚本报表(script-statement)

| 功能名称 | 后端URL | 共用接口的菜单路由 |
| ------- | ------- | ------- |
| 获取脚本报表列表 | `/statement/list` | - |
| 导出脚本报表 | `/statement/export` | - |
| 深度优先搜索获取分类列表 | `/category/selectCategoryListDFS` | myScript, category-service, key-commands, time-task-manager, scheduled-task, all-script |

## 历史任务报表(history-task-statement)

| 功能名称 | 后端URL | 共用接口的菜单路由 |
| ------- | ------- | ------- |
| 获取历史任务报表列表 | `/taskStatement/list` | - |
| 深度优先搜索获取分类列表 | `/category/selectCategoryListDFS` | myScript, category-service, key-commands, script-statement, time-task-manager, scheduled-task, all-script |
| 获取用户权限信息列表 | `/taskApply/queryUserInfoListByPermissionCode` | myScript, task-apply, task-apply-duty, service-rollout, double-check, double-check |

## 定时任务监控(scheduled-task)

| 功能名称 | 后端URL | 共用接口的菜单路由 |
| ------- | ------- | ------- |
| 获取定时任务列表 | `/taskExecute/listTimeTasks` | time-task-manager |
| 定时任务开关 | `/taskExecute/timeTaskSwitch` | time-task-manager |
| 定时任务杀死 | `/taskExecute/timeTaskKill` | time-task-manager |
| 更新定时任务CRON | `/taskExecute/updateTimeTaskCron` | time-task-manager |
| 导出定时任务 | `/taskExecute/export` | time-task-manager |
| 深度优先搜索获取分类列表 | `/category/selectCategoryListDFS` | myScript, category-service, key-commands, script-statement, time-task-manager, all-script |
| 获取执行中任务列表 | `/taskExecute/listRunningScriptTasks` | execution-task |
| 获取执行历史任务列表 | `/taskExecute/listCompleteScriptTasks` | execution-task |
| 停止脚本任务 | `/taskExecute/stopTask` | execution-task |
| 批量停止脚本任务 | `/taskExecute/batchStopScriptTask` | execution-task |
| 任务下运行的agent列表 | `/runtime/listTaskRuntime` | myScript, task-apply, execution-task, template-task |
| 获取脚本信息 | `/myScript/getScriptDetail` | myScript, task-apply-duty, template-task, double-check, double-check |
| 根据UUID获取版本信息 | `/version/getInfoByVersionUuid` | myScript |
| 获取输出消息 | `/runtime/getOutPutMessage` | myScript, execution-task |
| 获取实时输出消息 | `/taskExecute/getRealTimeOutPutMessage` | myScript, execution-task |

## 全部脚本(all-script)

| 功能名称 | 后端URL | 共用接口的菜单路由 |
| ------- | ------- | ------- |
| 获取全部脚本列表 | `/allScript/listAllScript` | - |
| 获取全部脚本详情 | `/allScript/getAllScriptDetail` | - |
| 获取全部脚本审计详情 | `/allScript/getAllScriptAuditDetail` | - |
| 深度优先搜索获取分类列表 | `/category/selectCategoryListDFS` | myScript, category-service, key-commands, script-statement, time-task-manager, scheduled-task |
| 获取全部脚本状态列表 | `/allScript/listAllScriptStatus` | - |
| 获取全部脚本状态详情 | `/allScript/getAllScriptStatusDetail` | - |
| 获取全部脚本状态审计详情 | `/allScript/getAllScriptStatusAuditDetail` | - |
| 获取全部脚本执行历史列表 | `/allScript/listAllScriptExecutionHistory` | - |
| 获取全部脚本执行历史详情 | `/allScript/getAllScriptExecutionHistoryDetail` | - |
| 获取全部脚本执行历史审计详情 | `/allScript/getAllScriptExecutionHistoryAuditDetail` | - |
| 导出全部脚本执行历史 | `/allScript/exportAllScriptExecutionHistory` | - |
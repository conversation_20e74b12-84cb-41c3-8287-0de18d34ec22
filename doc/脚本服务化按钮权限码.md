**后端URL菜单维度统计表：**

| 菜单               | 按钮名称          | v-hasPerm按钮icode     | 后端URL                                                   |
| ------------------ | ----------------- | ---------------------- | --------------------------------------------------------- |
| 我的脚本           | 删除              | removeScript           | /myScript/deleteMyScript                                  |
| 我的脚本           | 菜单              | myScript               |                                                           |
| 我的脚本           | 共享              | scriptShare            | /scriptShare/saveScriptVersionShare                       |
| 我的脚本           | 不修订            | confirmScript          | /myScript/ignoreScriptRevision                            |
| 我的脚本           | 测试              | scriptTestExecution    | /taskExecute/scriptTestExecution                          |
| 我的脚本           | 创建者转移        | creatorTransfer        | /myScript/creatorTransfer                                 |
| 我的脚本           | 投产介质          | exportReleaseMedia     | /myScript/myScriptExportReleaseMedia                      |
| 我的脚本           | 启用禁用          | updateUseState         | /taskAuthority/updateUseState                             |
| 我的脚本           | 下发              | sendScriptToAgents     | /script/issuerecord/sendScriptToAgents                    |
| 我的脚本           | 发布              | publishScript          | /myScript/publishScript                                   |
| 我的脚本           | 编辑              | updateScript           | /myScript/updateMyScript                                  |
| 我的脚本           | 查看版本-版本回退 | versionRollBack        | /myScript/hasVersionRollBack  /myScript/noVersionRollBack |
| 我的脚本           | 下载脚本          | scriptDownload         | /myScript/scriptDownload                                  |
| 我的脚本           | 新增              | saveScript             | /myScript/saveMyScript                                    |
| 枚举参数管理       | 删除              | enumParamsDelPerm      | /manager/removeParameterManager                           |
| 枚举参数管理       | 菜单              | param-manage           |                                                           |
| 枚举参数管理       | 编辑              | enumParamsEditPerm     | /manager/updateParameterManager                           |
| 枚举参数管理       | 新增              | enumParamsAddPerm      | /manager/saveParameterManager                             |
| 长期未修改脚本报表 | 菜单              | script-statement       |                                                           |
| 任务申请           | 下载脚本          | scriptDownload         | /myScript/scriptDownload                                  |
| 任务申请           | 常用任务          | cloneTask              | /taskTemplate/createCloneTask                             |
| 任务申请           | 投产介质          | exportReleaseMedia     | /releaseMedia/exportReleaseMedia                          |
| 任务申请           | 任务申请          | scriptExecAuditing     | /taskApply/scriptExecAuditing/                            |
| 任务申请           | 菜单              | task-apply             |                                                           |
| 历史任务报表       | 菜单              | history-task-statement |                                                           |
| 脚本分类           | 新增              | saveCategory           | /category/saveCategory                                    |
| 脚本分类           | 审核人授权        | catUserPermission      | /category/assignCategoryToUser                            |
| 脚本分类           | 部门授权          | catOrgPermission       | /category/assignCategoryToOrg                             |
| 脚本分类           | 菜单              | category-service       |                                                           |
| 脚本分类           | 删除              | removelCategory        | /category/removelCategory                                 |
| 脚本分类           | 编辑              | updateCategory         | /category/updateCategory                                  |
| 值班任务申请       | 菜单              | task-apply-duty        |                                                           |
| 关键命令           | 新增              | saveDangerCmd          | /cmd/saveDangerCmd                                        |
| 关键命令           | 菜单              | key-commands           |                                                           |
| 关键命令           | 编辑              | updateDangerCmd        | /cmd/updateDangerCmd                                      |
| 关键命令           | 删除              | removelDangerCmd       | /cmd/removelDangerCmd                                     |
| 常用任务           | 菜单              | template-task          |                                                           |
| 常用任务           | 常用克隆任务提交  | publishTemplate        | /taskTemplate/execAuditTemplateTask/                      |
| 常用任务           | 常用克隆任务保存  | saveTemplate           | /taskTemplate/saveAuditTemplateTask/                      |
| 常用任务           | 常用克隆任务删除  | removeTemplateTask     | /taskTemplate/deleteTemplateTask                          |
| 任务监控           | 菜单              | execution-task         |                                                           |
| 任务监控           | 执行中-查看-重试  | retryScript            | /taskExecute/retryScriptServiceShell                      |
| 任务监控           | 执行历史-导出     | excelExport            | /taskExecute/exportAgentHisExcel                          |
| 任务监控           | 执行中-查看-忽略  | skipScript             | /taskExecute/skipScriptShell                              |
| 任务监控           | 待执行-取消       | cancelTask             | /taskExecute/cancelTask                                   |
| 任务监控           | 待执行-执行、继续 | scriptTaskStart        | /taskExecute/scriptTaskStart                              |
| 任务监控           | 全部任务          | allTaskCheckBox        |                                                           |
| 任务监控           | 执行历史-克隆     | cloneTask              | /taskTemplate/createCloneTask                             |
| 任务监控           | 执行中-终止       | stopTask               | /taskExecute/stopTask                                     |
| 参数验证           | 删除              | removeParameterCheck   | /check/removeParameterCheck                               |
| 参数验证           | 菜单              | param-validation       |                                                           |
| 参数验证           | 新增              | saveParameterCheck     | /check/saveParameterCheck                                 |
| 参数验证           | 编辑              | updateParameterCheck   | /check/updateParameterCheck                               |
| 定时任务维护       | 停止              | scriptTimedTaskStop    | /taskExecute/timeTaskSwitch                               |
| 定时任务维护       | 终止              | scriptTimedTaskOver    | /taskExecute/timeTaskKill                                 |
| 定时任务维护       | 菜单              | time-task-manager      |                                                           |
| 定时任务维护       | 启动              | scriptTimedTaskStart   | /taskExecute/timeTaskSwitch                               |
| 定时任务监控       | 全部任务          | allTaskCheckBox        |                                                           |
| 定时任务监控       | 菜单              | scheduled-task         |                                                           |
| 定时任务监控       | 执行历史-克隆     | cloneTask              | /taskTemplate/createCloneTask                             |
| 定时任务监控       | 执行中-终止       | stopTask               | /taskExecute/stopTask                                     |
| 定时任务监控       | 执行中-查看-忽略  | skipScript             | /taskExecute/skipScriptShell                              |
| 定时任务监控       | 执行中-查看-重试  | retryScript            | /taskExecute/retryScriptServiceShell                      |
| 服务投产           | 菜单              | service-rollout        |                                                           |
| 服务投产           | 投产导入          | importReleaseMedia     | /releaseMedia/importReleaseMedia                          |
| 全部脚本           | 菜单              | all-script             |                                                           |

**后端URL被多个按钮共用的统计表：**

| 菜单（按钮名称）                                             | v-hasPerm按钮icode（空格分隔，如果code相同只留了一个） | 后端URL                              |
| ------------------------------------------------------------ | ------------------------------------------------------ | ------------------------------------ |
| 我的脚本-下载脚本 任务申请-下载脚本                          | scriptDownload                                         | /myScript/scriptDownload             |
| 任务申请-常用任务 任务监控-执行历史-克隆 定时任务监控-执行历史-克隆 | cloneTask                                              | /taskTemplate/createCloneTask        |
| 任务监控-执行中-查看-重试 定时任务监控-执行中-查看-重试      | retryScript                                            | /taskExecute/retryScriptServiceShell |
| 任务监控-执行中-查看-忽略 定时任务监控-执行中-查看-忽略      | skipScript                                             | /taskExecute/skipScriptShell         |
| 任务监控-执行中-终止 定时任务监控-执行中-终止                | stopTask                                               | /taskExecute/stopTask                |
| 定时任务维护-停止 定时任务维护-启动                          | scriptTimedTaskStop scriptTimedTaskStart               | /taskExecute/timeTaskSwitch          |
